/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_thread.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_03
  最近修改   :

******************************************************************************/
#include <unistd.h>
#include <sys/wait.h>
#include <sys/reboot.h>
#include <sys/prctl.h>
#include <sys/syscall.h>
#include <linux/capability.h>
#include "securec.h"
#include "hi_comdef.h"
#include "hi_errno.h"
#include "hi_typedef.h"
#include "os/hi_os_string.h"
#include "os/hi_os_err.h"
#include "os/hi_os_mem.h"
#include "os/hi_os_fileio.h"
#include "os/hi_os_thread.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

#define SYS_HIGHRISK_CAP (1ULL << CAP_CHECKPOINT_RESTORE)
#define bytes_to_bits(b) ((b) * 8)
#define HI_OS_CHILD_PROC_EXIT_STATUS 127

static void __thread_set_ambient(hi_ulong64 cap)
{
	int i;

	for (i = 0; i < bytes_to_bits(sizeof(cap)); i++)
		if ((0x1 << i) & cap)
			prctl(PR_CAP_AMBIENT, PR_CAP_AMBIENT_RAISE, i, 0, 0);
}

hi_int32 hi_os_set_cap(hi_ulong64 permitted, hi_ulong64 effective, hi_ulong64 inheritable)
{
	struct __user_cap_header_struct cap_header_data;
	cap_user_header_t cap_header = &cap_header_data;
	cap_user_data_t cap_data = NULL;

	cap_data = malloc(sizeof(*cap_data) * _LINUX_CAPABILITY_U32S_3);
	if (cap_data == NULL) {
		printf("[hi_os_set_cap] malloc fail\n");
		return HI_RET_FAIL;
	}
	memset_s(cap_header, sizeof(*cap_header), 0, sizeof(*cap_header));
	memset_s(cap_data, sizeof(*cap_data) * _LINUX_CAPABILITY_U32S_3, 0,
		sizeof(*cap_data)* _LINUX_CAPABILITY_U32S_3);

	cap_header->version = _LINUX_CAPABILITY_VERSION_3;
	syscall(SYS_capget, cap_header, cap_data);

	cap_header->version = _LINUX_CAPABILITY_VERSION_3;
	cap_data[0].permitted &= permitted;
	cap_data[0].effective = (effective & cap_data[0].permitted);
	cap_data[0].inheritable = (inheritable & cap_data[0].permitted);
	cap_data[1].permitted &= (permitted >> 32);
	cap_data[1].effective = ((effective >> 32) & cap_data[1].permitted);
	cap_data[1].inheritable = ((inheritable >> 32) & cap_data[1].permitted);
	if (syscall(SYS_capset, cap_header, cap_data) < 0) {
		printf("[hi_os_set_cap]SYS_capset fail, [%d]\n", errno);
		free(cap_data);
		return HI_RET_FAIL;
	}

	__thread_set_ambient(effective);
	free(cap_data);
	return HI_RET_SUCC;
}

hi_int32 hi_os_set_id(hi_uint32 uid, hi_uint32 gid)
{
	if (prctl(PR_SET_KEEPCAPS, 1, 0, 0, 0) < 0) {
		printf("set PR_SET_KEEPCAPS fail\n");
		return HI_RET_FAIL;
	}
	if (setgid(gid) < 0) {
		printf("setgid fail\n");
		return HI_RET_FAIL;
	}
	if (setuid(uid) < 0) {
		printf("setuid fail\n");
		return HI_RET_FAIL;
	}
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_os_execv
 功能描述  : 用来执行参数pucPath字符串所代表的文件路径，第二个参数利用数组指
             针来传递给执行文件。
*****************************************************************************/
hi_int32 hi_os_execv(const hi_char8 *pc_path, hi_char8* const a_pc_argv[])
{
    return execv(pc_path,a_pc_argv);
}

/*****************************************************************************
 函 数 名  : hi_os_execvp
 功能描述  : 从PATH 环境变量所指的目录中查找符合参数pcFile 的文件名，找到后便
            执行该文件，然后将第二个参数pucArgv传给该欲执行的文件。
 返 回 值  : hi_int32: 如果执行成功则函数不会返回，执行失败则直接返回-1，
             失败原因存于errno中。
*****************************************************************************/
hi_int32 hi_os_execvp(const hi_char8 *pc_file, hi_char8 *const a_pc_argv[])
{
    return execvp(pc_file,a_pc_argv);
}

/******************************************************************************
  函数名称  : hi_os_execcmd
  功能描述  : 执行SHELL命令，等待命令执行结束才返回
  返 回 值  : 返回0表示命令执行成功，返回-1表示命令执行失败
******************************************************************************/
hi_int32 hi_os_execcmd(hi_char8 *pc_command)
{
    hi_int32  pid = 0;
    hi_int32  status = 0;
    hi_char8 *argv[] = { "sh", "-c", pc_command, NULL };

    if (pc_command == NULL) {
        return -HI_RET_FAIL;
    }

    pid = hi_os_fork();
    if (pid < 0) {
        return -HI_RET_FAIL;
    } else if (pid == 0) {
         //子进程执行分支
        hi_os_execv("/bin/sh", argv);
        hi_os_exit(HI_OS_CHILD_PROC_EXIT_STATUS);
    }

    /* wait for child process return */
    while (hi_os_waitpid(pid, &status, 0) < 0) {
        if (hi_os_errno() != (hi_uint32)EINTR) {
            return -HI_RET_FAIL;
        }
    }
    return WIFEXITED(status) ? (HI_RET_SUCC) : (-HI_RET_FAIL);
}

hi_int32 hi_os_execcmd_cap(hi_uint32 uid, hi_uint32 gid, hi_ulong64 cap, hi_char8 *pc_command)
{
    hi_int32  pid = 0;
    hi_int32  status = 0;
    hi_char8 *argv[] = { "sh", "-c", pc_command, NULL };

    if (pc_command == NULL)
        return -HI_RET_FAIL;

    pid = hi_os_fork();
    if (pid < 0) {
        return -HI_RET_FAIL;
    } else if (pid == 0) {
         //子进程执行分支
        hi_os_set_id(uid, gid);
        if (uid != 0)
            cap &= ~SYS_HIGHRISK_CAP;
        hi_os_set_cap(cap, cap, cap);
        hi_os_execv("/bin/sh", argv);
        hi_os_exit(HI_OS_CHILD_PROC_EXIT_STATUS);
    }

    /* wait for child process return */
    while (hi_os_waitpid(pid, &status, 0) < 0) {
        if (hi_os_errno() != (hi_uint32)EINTR) {
            return -HI_RET_FAIL;
        }
    }
    return WIFEXITED(status) ? (HI_RET_SUCC) : (-HI_RET_FAIL);
}

/*****************************************************************************
 函 数 名  : hi_os_execcmd_cap_nohang
 功能描述  : 以特定用户和权限执行SHELL命令，不等待命令执行结束就返回
*****************************************************************************/
hi_int32 hi_os_execcmd_cap_nohang(hi_uint32 uid, hi_uint32 gid, hi_ulong64 cap, hi_char8 *pc_command)
{
    hi_int32 pid = 0;
    hi_char8 *argv[] = {"sh", "-c", pc_command, NULL};

    /**
     * 说明：
     * 1. 父进程第一次fork的子进程称为第一子进程
     * 2. 在子进程中fork产生的子进程称为第二子进程
     */

    if (pc_command == NULL)
        return -HI_RET_FAIL;

    pid = hi_os_fork();
    if (pid < 0)
        return -HI_RET_FAIL;

    if (pid == 0) {         /** 进入第一子进程 */
        pid = hi_os_fork(); /** 创建第二子进程执行命令 */
        if (pid < 0)
            return -HI_RET_FAIL;
        if (pid > 0) { /** 立即退出第一子进程，第二子进程将移交给init进程回收处理 */
            hi_os_execv("/bin/sh", NULL); /** 替换当前子进程 */
            hi_os_exit(HI_OS_CHILD_PROC_EXIT_STATUS);
        }

        /** 第二子进程执行命令 */
        hi_os_set_id(uid, gid);
        if (uid != 0)
            cap &= ~SYS_HIGHRISK_CAP;
        hi_os_set_cap(cap, cap, cap);
        hi_os_execv("/bin/sh", argv);
        hi_os_exit(HI_OS_CHILD_PROC_EXIT_STATUS);
    }

    /* wait for child process return */
    while (hi_os_waitpid(pid, NULL, 0) < 0) {
        if (hi_os_errno() != (hi_uint32)EINTR) {
            return -HI_RET_FAIL;
        }
    }
    return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_os_execcmd_nohang
 功能描述  : 执行SHELL命令，不等待命令执行结束就返回
*****************************************************************************/
hi_int32 hi_os_execcmd_nohang(hi_char8 *pc_command)
{
    hi_int32 pid = 0;
    hi_char8 *argv[4];

    /**
     * 说明：
     * 1. 父进程第一次fork的子进程称为第一子进程
     * 2. 在子进程中fork产生的子进程称为第二子进程
     */

    if (pc_command == NULL)
        return HI_RET_FAIL;

    pid = hi_os_fork();
    if (pid < 0)
        return HI_RET_FAIL;

    if (0 == pid) {         /** 进入第一子进程 */
        pid = hi_os_fork(); /** 创建第二子进程执行命令 */
        if (pid < 0)
            return -HI_RET_FAIL;
        if (pid > 0) { /** 立即退出第一子进程，第二子进程将移交给init进程回收处理 */
            hi_os_execv("/bin/sh", NULL); /** 替换当前子进程 */
            hi_os_exit(HI_OS_CHILD_PROC_EXIT_STATUS);
        }

        /** 第二子进程执行命令 */
        argv[0] = "sh";
        argv[1] = "-c";
        argv[2] = pc_command;
        argv[3] = 0;

        hi_os_execv("/bin/sh", argv);
        hi_os_exit(HI_OS_CHILD_PROC_EXIT_STATUS);
    }

    /* wait for child process return */
    while (hi_os_waitpid(pid, NULL, 0) < 0) {
        if (hi_os_errno() != (hi_uint32)EINTR) {
            return -HI_RET_FAIL;
        }
    }
    return pid;
}

hi_int32 hi_os_vcmd(const hi_char8 *format, ...)
{
    int ret;
    char buf[512] = ""; /* exec cmd max len 512 */

    HI_VA_LIST marker;

    va_start( marker, format );
    ret = vsnprintf_s(buf, sizeof(buf), sizeof(buf) - 1, format, marker);
    va_end(marker);
    if (ret >= 0) {
        ret = hi_os_execcmd(buf);
    }

    if (ret != HI_RET_SUCC) {
        printf("[ERR EXEC(%08x)]%s\n", ret, buf);
    }
    return ret;
}

hi_int32 hi_os_vcmd_cap(hi_uint32 uid, hi_uint32 gid, hi_ulong64 cap, const hi_char8 *format, ...)
{
    int ret;
    char buf[512] = ""; /* exec cmd max len 512 */

    HI_VA_LIST marker;

    va_start( marker, format );
    ret = vsnprintf_s(buf, sizeof(buf), sizeof(buf) - 1, format, marker);
    va_end(marker);
    if (ret >= 0)
        ret = hi_os_execcmd_cap(uid, gid, cap, buf);

    if (ret != HI_RET_SUCC)
        printf("[ERR EXEC(%08x)]%s\n", ret, buf);

    return ret;
}

/*****************************************************************************
 函 数 名  : hi_os_waitpid
 功能描述  : 暂时停止目前进程的执行，直到有信号来到或子进程
                            结束。如果在调用此函数时子进程已经结束，则此函数
                            会立即 返回子进程结束状态值。子进程的结束状态值
                            会由参数iStatus返回，而子进程的进程识别码也会一快返
                            回。如果不在意结束状态值，则参数iStatus 可以设成NULL。

 输入参数  : iPid为欲等待的子进程识别码， 其他数值意义如下：
                                pid<HI_RET_FAIL 等待进程组识别码为pid绝对值的任何子进程。
                                pid=HI_RET_FAIL 等待任何子进程，相当于wait（）。
                                pid=0 等待进程组识别码与目前进程相同的任何子进程。
                                pid>0 等待任何子进程识别码为pid的子进程。

                                参数option可以为0 或下面的OR组合：
                                1.WNOHANG 如果没有任何已经结束的子进程则马上返回，
                                不予以等待。
                                2.WUNTRACED 如果子进程进入暂停执行情况则马上返回，
                                但结束状态不予以理会。

 输出参数  :子进程的结束状态返回后存于iStatus，底下有几个宏可
                                判别结束情况：
                                1.WIFEXITED（status）如果子进程正常结束则为非0 值。
                                2.WEXITSTATUS（status）取得子进程exit（）返回的结束代码，
                                一般会先用WIFEXITED 来判断是否正常结束才能使用此宏。
                               3. WIFSIGNALED（status）如果子进程是因为信号而结束则此
                               宏值为 真
                                4.WTERMSIG（status） 取得子进程因信号而中止的信号代码，
                                一般 会先用WIFSIGNALED 来判断后才使用此宏。
                                5.WIFSTOPPED（status） 如果子进程处于暂停执行情况则此
                                宏值为真。一般只有使用WUNTRACED 时才会有此情况。
                                6.WSTOPSIG（status） 取得引发子进程暂停的信号代码，
                                一般会先用WIFSTOPPED 来判断后才使用此宏。
 返 回 值  : 如果执行成功则返回子进程识别码（PID），如果有错误
                           发生则返回-1。失败原因存于errno中。
*****************************************************************************/
hi_int32 hi_os_waitpid(hi_int32 i_pid,  hi_int32 *i_status, hi_int32 i_options)
{
     return waitpid(i_pid,i_status,i_options);
}

/*****************************************************************************
 函 数 名  : hi_os_fork
 功能描述  : 产生一个新的子进程，其子进程会复制父进程的数据与堆栈空间，并继承
            父进程的用户代码，组代码，环境变量、已打开 的文件代码、工作目录和
            资源限制等。Linux 使用copy-on-write （COW）技术，只有当其中一进程
            试图修改欲复制的空间时才会做真正的复制动作，由于这些继承的信息是复
            制而来，并非指相同的 内存空间，因此子进程对这些变量的修改和父进程
            并不会同步。此外，子进程不会继承父进程的文件锁定和未处理的信号。注意，
            Linux不保证子进程会比父进程先执行或晚执行，因此编写程序时要留意死锁
            或竞争条件的发生。
 返 回 值  : HI_PID_T:返回字符串的长度；指针为空，则返回0
*****************************************************************************/
hi_int32 hi_os_fork(hi_void)
{
     return fork();
}


/*****************************************************************************
 函 数 名  : hi_os_wait
 功能描述  : 暂时停止目前进程的执行，直到有信号来到或子进程结 束。如果在调用函
            数时子进程已经结束，则函数会立即返回子进程结束状态值。
 输入参数  : const hi_int32* i_status
 输出参数  : 子进程的结束状态值会由参数piStatus 返回，而子进程的进程识别码也会
            一快返回。如果不在意结束状态值，则参数piStatus可以设成NULL。子进程
            的结束状态值请参考waitpid（）。
 返 回 值  : HI_PID_T:如果执行成功则返回子进程识别码（PID），如果有错误发生
            则返回-1。失败原因存于errno中。
*****************************************************************************/
hi_int32 hi_os_wait(const hi_int32* pi_status)
{
    return wait((void *)pi_status);
}

hi_int32 hi_os_reboot(hi_int32 i_flag)
{
    return reboot(i_flag);
}

hi_int32 hi_os_pthread_attr_init(hi_os_thread_attr_s *pst_attr)
{
    if ( NULL == pst_attr)
    {
        return HI_RET_FAIL;
    }

    return pthread_attr_init((pthread_attr_t *)pst_attr);
}

hi_int32 hi_os_pthread_attr_destroy(hi_os_thread_attr_s *pst_attr)
{
    if ( NULL == pst_attr)
    {
        return HI_RET_FAIL;
    }

    return pthread_attr_destroy((pthread_attr_t *)pst_attr);
}

hi_int32 hi_os_pthread_attr_setdetachstate(hi_os_thread_attr_s *pst_attr, hi_int32 i_detachstate)
{
    if ( NULL == pst_attr)
    {
        return HI_RET_FAIL;
    }

   return pthread_attr_setdetachstate((pthread_attr_t *)pst_attr,i_detachstate);
}

hi_int32 hi_os_pthread_setcancelstate(hi_int32 i_state, hi_int32 *i_oldstate)
{
    if ( NULL == i_oldstate)
    {
      return HI_RET_FAIL;
    }

    return pthread_setcancelstate(i_state,i_oldstate);
}

hi_int32 hi_os_pthread_pthread_setcanceltype(hi_int32 i_type, hi_int32 *i_oldtype)
{
    if ( NULL == i_oldtype)
    {
      return HI_RET_FAIL;
    }

    return pthread_setcanceltype(i_type,i_oldtype);
}

/*****************************************************************************
 函 数 名  : hi_os_pthread_detach
 功能描述  : Indicate that the thread TH is never to be joined with PTHREAD_JOIN.
             The resources of TH will therefore be freed immediately when it
             terminates, instead of waiting for another thread to perform PTHREAD_JOIN
             on it.
*****************************************************************************/
hi_int32 hi_os_pthread_detach (HI_PTHREAD_T pst_thr)
{
    if (pst_thr == NULL) {
        return 0;
    }
    return pthread_detach(pst_thr);
}

/*****************************************************************************
 函 数 名  : hi_os_pthread_setschedparam
 功能描述  :  set the scheduling  policy and parameters of individual threads
              within a multi-threaded process to be retrieved and set. For
              SCHED_FIFO and SCHED_RR, the only required member of the sched_param
              structure is the pri-ority sched_priority. For SCHED_OTHER, the affected
              scheduling parameters are implementation-defined.
 输入参数  : HI_PTHREAD_T st_threadid
             hi_int32 i_policy;hi_os_sched_param_s *pst_param
*****************************************************************************/
hi_int32 hi_os_pthread_setschedparam(HI_PTHREAD_T st_threadid, hi_int32 i_policy,
                                    hi_os_sched_param_s *pst_param)
{
    return pthread_setschedparam(st_threadid, i_policy, (const struct sched_param *)(hi_void *)pst_param);
}

hi_int32 hi_os_getpriority(hi_int32 i_which, hi_int32 i_who)
{
     return getpriority(i_which,(hi_uint32)i_who);
}

hi_int32 hi_os_setpriority(hi_int32 i_which, hi_int32 i_who, hi_int32 i_pri)
{
     return setpriority(i_which,(hi_uint32)i_who,i_pri);
}

/*****************************************************************************
 函 数 名  : hi_os_pthread_create
 功能描述  : Create a thread with given attributes ATTR (or default attributes
             if ATTR is NULL), and call function START_ROUTINE with given
             arguments ARG.
*****************************************************************************/
hi_int32  hi_os_pthread_create (HI_PTHREAD_T *pst_threadp,
                               const hi_os_thread_attr_s *pst_attr,
                               hi_os_threadfun_t pf_startroutine,
                               hi_void *pv_arg)
{
    if ( NULL == pf_startroutine)
    {
        return HI_RET_FAIL;
    }

    return pthread_create((pthread_t *)pst_threadp,
                          (pthread_attr_t *)pst_attr,
                           pf_startroutine,
                           pv_arg);
}

/*****************************************************************************
 函 数 名  : hi_os_pthread_self
 功能描述  : Obtain the identifier of the current thread.
*****************************************************************************/
HI_PTHREAD_T  hi_os_pthread_self (hi_void)
{
    return pthread_self();
}

/*****************************************************************************
 函 数 名  : hi_os_pthread_exit
 功能描述  : Terminate calling thread.
*****************************************************************************/
hi_void hi_os_pthread_exit ( hi_void* pv_retval)
{
    if ( NULL == pv_retval)
    {
        return;
    }

    pthread_exit(pv_retval);
}

hi_int32 hi_os_pthread_cancel(HI_PTHREAD_T st_thread)
{
    return pthread_cancel(st_thread);
}

/*****************************************************************************
 函 数 名  : hi_os_pthread_join
 功能描述  : Make calling thread wait for termination of the thread TH.  The
             exit status of the thread is stored in *THREAD_RETURN, if THREAD_RETURN
             is not NULL.
*****************************************************************************/
hi_int32 hi_os_pthread_join (HI_PTHREAD_T st_thr, hi_void** ppv_threadreturn)
{
     return pthread_join(st_thr,ppv_threadreturn);
}

/*****************************************************************************
 函 数 名  : hi_os_sleep
 功能描述  : 秒级sleep
*****************************************************************************/
hi_uint32 hi_os_sleep(hi_uint32 ui_seconds)
{
     return sleep(ui_seconds);
}

/*****************************************************************************
 函 数 名  : hi_os_msleep
 功能描述  : 毫秒级sleep,用slect 实现
*****************************************************************************/
hi_uint32 hi_os_msleep(hi_uint32 ui_ms)
{
    return (hi_uint32)usleep((ui_ms*1000));
}

/*****************************************************************************
 函 数 名  : hi_os_usleep
 功能描述  : 微秒级sleep
*****************************************************************************/
hi_uint32 hi_os_usleep(hi_uint32 ui_usec)
{
    return (hi_uint32)usleep(ui_usec);
}

/*****************************************************************************
 函 数 名  : hi_os_getpid
 功能描述  : 获取当前线程ID
*****************************************************************************/
hi_uint32 hi_os_getpid(hi_void)
{
    return (hi_uint32)getpid();
}

/*****************************************************************************
 函 数 名  : hi_os_exit
 功能描述  : 退出
*****************************************************************************/
hi_void hi_os_exit(hi_int32 i_status)
{
     _exit(i_status);
}

hi_int32 hi_os_kill(hi_int32 i_pid, hi_int32 i_sig)
{
     return kill(i_pid,i_sig);
}

/******************************************************************************
  函数名称  : hi_os_rmdelimitor
  功能描述  : 把字符串中的'\0'去掉
  输入参数  :
              1.  *pc_buf: 待去除'\0'的字符串
  返 回 值  : 去除'\0'成功则返回真VOS_TRUE，
              去除'\0'失败则返回假VOS_FALSE，
******************************************************************************/
hi_uint32 hi_os_rmdelimitor( hi_char8 *pc_buf)
{
    hi_uint32  bRet = HI_TRUE;
    hi_uint32 ui_loop = 0;
    hi_uint32 ui_index = 0;

    if ( pc_buf == NULL )
    {
        return HI_FALSE;
    }

    while((*(pc_buf + ui_loop) != '\0') || (*(pc_buf + ui_loop + 1) != '\0'))
    {
        if(*(pc_buf + ui_loop) == '\0')
        {
            ui_loop++;
            continue;
        }
        *(pc_buf + ui_index) = *(pc_buf + ui_loop);
        ui_index++;
        ui_loop++;
    }
    *(pc_buf + ui_index) = '\0';

    return bRet;

}

/******************************************************************************
  函数名称  : hi_os_pidbyname
  功能描述  : 根据进程名得到进程号，如果有多个匹配则会列出所有匹配的进程ID
  返 回 值  : 返回值不为NULL表示查找成功；返回值为NULL表示查找失败
******************************************************************************/
hi_int32 hi_os_pidbyname( hi_char8 *name, hi_int32 begin)
{
    DIR  *dir = NULL;
    struct dirent *ent = NULL;
    HI_FILE_S  *pf_stat = NULL;
    hi_char8 file_name[HI_OS_SYS_READ_BUF_SIZE] = {0};
    hi_char8 buffer[HI_OS_SYS_READ_BUF_SIZE] = {0};
    hi_int32 pid;

    if (NULL == name || begin < 0) {
        return -1;
    }

    dir = opendir("/proc/");
    if (NULL == dir) {
        return -1;
    }

    while ((ent = readdir(dir)) != NULL) {
        /* 进程名字必须是数字 */
        pid = hi_os_strtol(ent->d_name, NULL, 0);
        if (pid <= begin) {
            continue;
        }

        (void)memset_s(buffer, sizeof(buffer), 0, sizeof(buffer));
        sprintf_s(file_name, sizeof(file_name), "/proc/%d/comm", pid);
        if ((pf_stat = hi_os_fopen(file_name, "r")) == NULL) {
            continue;
        }
        if (hi_os_fgets(buffer, sizeof(buffer),  pf_stat) == NULL) {
            hi_os_fclose(pf_stat);
            continue;
        }
        hi_os_fclose(pf_stat);

        hi_os_strim(buffer);
        if (hi_os_strcmp(buffer, name) == 0) {
            closedir(dir);
            return pid;
        }
    }
    closedir(dir);
    return -1;
}

hi_int32 hi_os_getpidbyname(hi_char8 *pc_name)
{
    return hi_os_pidbyname(pc_name, 0);
}

hi_int32 hi_os_pidbycmdline(hi_char8 *cmdline, hi_int32 begin)
{
    DIR *dir = NULL;
    struct dirent *ent = NULL;
    HI_FILE_S *fp_cmdline = NULL;
    hi_char8 file_cmdline_path[HI_OS_SYS_READ_BUF_SIZE] = {0};
    hi_char8 file_cmdline[HI_OS_SYS_READ_BUF_SIZE] = {0};
    hi_int32 pid;

    if (cmdline == NULL || begin < 0)
        return -1;

    dir = opendir("/proc/");
    if (dir == NULL)
        return -1;

    while ((ent = readdir(dir)) != NULL) {
        pid = hi_os_strtol(ent->d_name, NULL, 0);
        if (pid < begin)
            continue;

        (void)sprintf_s(file_cmdline_path, sizeof(file_cmdline_path), "/proc/%d/cmdline", pid);
        if ((fp_cmdline = hi_os_fopen(file_cmdline_path, "r")) == NULL) {
            continue;
        }
        if (hi_os_fgets(file_cmdline, sizeof(file_cmdline), fp_cmdline) == NULL) {
            hi_os_fclose(fp_cmdline);
            continue;
        }
        hi_os_fclose(fp_cmdline);

        if (hi_os_strncmp(file_cmdline, cmdline, strlen(cmdline)) == 0) {
            closedir(dir);
            return pid;
        }
    }
    closedir(dir);
    return -1;
}

hi_int32 hi_os_system(hi_char8 *pc_cmd)
{
    return system(pc_cmd);
}

/*****************************************************************************
 Function name: hi_os_prctl_name
 Description  : 修改子线程名
*****************************************************************************/
hi_int32 hi_os_prctl_name(const hi_char8 * pc_name)
{
    return prctl(PR_SET_NAME, (unsigned long)pc_name, 0, 0, 0);
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
