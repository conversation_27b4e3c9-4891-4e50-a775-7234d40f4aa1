/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_err.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_04
  最近修改   :

******************************************************************************/
#include <errno.h>
#include <string.h>
#include "hi_typedef.h"

#ifdef  __cplusplus
    extern "C"{
#endif
/*****************************************************************************
 函 数 名  : hi_os_errno
 功能描述  : 函数获取系统全局变量errno的值
 返 回 值  : 返回errno的值
*****************************************************************************/
hi_uint32 hi_os_errno(hi_void)
{
    return (hi_uint32)errno;
}

/*****************************************************************************
 函 数 名  : hi_os_errset
 功能描述  : 设置全局变量errno的值
 输入参数  : ui_new_err_no：要设置的新的错误码
*****************************************************************************/
hi_void hi_os_errset(hi_uint32 ui_new_err_no)
{
    errno = (hi_int32)ui_new_err_no;
    return;
}

/*****************************************************************************
 函 数 名  : hi_os_strerror
 功能描述  : 获取全局变量errno的值对应的字符串说明信息
 输入参数  : ui_err_no：要获取信息的errno的值
 返 回 值  :    返回错误码对应的字符串信息
*****************************************************************************/
hi_char8 *hi_os_strerror(hi_uint32 ui_err_no)
{
     return strerror((hi_int32)ui_err_no);
}

hi_char8 *hi_os_errnostr(void)
{
	return strerror(errno);
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif  /* end of __cplusplus */
