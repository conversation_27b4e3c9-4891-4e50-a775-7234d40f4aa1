/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_unistdio.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_05
  最近修改   :

******************************************************************************/
#include <unistd.h>
#include <fcntl.h>
#include "hi_typedef.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

hi_int32 hi_os_open(const hi_char8 *pc_pathname, hi_int32 i_flags, hi_uint32 ui_mode)
{
    return open(pc_pathname,i_flags,ui_mode);
}

hi_int32 hi_os_close(hi_int32 i_fd)
{
    return close(i_fd);
}

hi_int32 hi_os_creat(const hi_char8 *pc_pathname, hi_uint32 ui_mode)
{
    return creat(pc_pathname,ui_mode);
}

hi_int32 hi_os_read(hi_int32 i_fd, hi_void *pv_buf, hi_uint32 ui_count)
{
    return read(i_fd,pv_buf,ui_count);
}

hi_int32 hi_os_write(hi_int32 i_fd, const hi_void *pv_buf, hi_uint32 ui_count)
{
    return write(i_fd,pv_buf,ui_count);
}

hi_int32 hi_os_lseek(hi_int32 i_fildes, hi_int32 i_offset, hi_int32 i_whence)
{
    return lseek(i_fildes,i_offset,i_whence);
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
