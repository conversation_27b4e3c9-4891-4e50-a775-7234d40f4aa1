/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_array.c
  版 本 号   : 初稿
  作    者   : luocheng 00183967
  生成日期   :

******************************************************************************/
#include "hi_typedef.h"
#include "hi_errno.h"
#include "hi_comdef.h"
#include "os/hi_os_err.h"
#include "os/hi_os_mem.h"
#include "os/hi_os_array.h"
#include "os/hi_os_mutex.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

/*****************************************************************************
 Function name: hi_os_array_init
 Description  : 初始化一个静态数组
*****************************************************************************/
hi_uint32 hi_os_array_init(hi_os_static_array_s * pst_array,
                           hi_uint32 ui_count)
{
    hi_void * p_data = NULL;
    hi_uint32 ui_index = 0;
    hi_uint32 ui_return = 0;

    /* 参数检测，指针不能为空，队列元素个数不能为0，元素所占大小不为0 */
    if (( NULL == pst_array ) || (0 == ui_count))
    {
        return 0;
    }

    pst_array->ui_max_count = ui_count;
    pst_array->ui_current_count = 0;

    /* 申请数组占用的内存 */
    p_data = (hi_void *)hi_os_malloc(sizeof(hi_os_array_item_s) * ui_count);
    if (NULL == p_data)
    {
        return HI_RET_MALLOC_FAIL;
    }
    pst_array->pst_array = (hi_os_array_item_s *)p_data;

    /* 初始化静态链表 */
    for (ui_index = 0; ui_index < ui_count; ui_index++)
    {
        /* 初始化链表，每个节点指向下一个位置 */
        pst_array->pst_array[ui_index].ui_value
                        = (hi_uint32)(pst_array->pst_array + ui_index + 1);
        pst_array->pst_array[ui_index].ui_flag = HI_OS_ITME_UNUSED;
    }

    pst_array->pst_array[ui_index-1].ui_value = 0;  /* 最后一个指向空 */
    pst_array->pst_array[ui_index-1].ui_flag  = HI_OS_ITME_UNUSED;

    /* 初始化时，头指针指向第一个，尾指针指向最后一个 */
    pst_array->pst_header = &pst_array->pst_array[0];
    pst_array->pst_tailer = &pst_array->pst_array[ui_count-1];

    /* 初始化多线程锁 */
    ui_return = (hi_uint32)hi_os_pthread_mutex_init(&pst_array->st_locker, NULL);
    if (HI_RET_SUCC != ui_return)
    {
        hi_os_free(p_data);
        pst_array->pst_array = NULL;
        return ui_return;
    }

    pst_array->ui_flag = HI_TRUE;

    return HI_RET_SUCC;

}

/*****************************************************************************
 Function name: hi_os_array_dispose
 Description  : 释放一个静态数组，数组中的元素由使用者自已负责释放
 Input        : hi_os_static_array_s * pst_array
*****************************************************************************/
hi_uint32 hi_os_array_dispose(hi_os_static_array_s * pst_array)
{
    /* 参数检测，指针不能为空 */
    if (NULL == pst_array)
    {
        return HI_RET_NULLPTR;
    }

    (hi_void)hi_os_pthread_mutex_lock(&pst_array->st_locker);

    hi_os_free(pst_array->pst_array);
    pst_array->pst_array = NULL;
    pst_array->pst_header = NULL;
    pst_array->pst_tailer = NULL;
    pst_array->ui_current_count = 0;
    pst_array->ui_max_count = 0;
    (hi_void)hi_os_pthread_mutex_unlock(&pst_array->st_locker);

    (hi_void)hi_os_pthread_mutex_destroy(&pst_array->st_locker);

    return HI_RET_SUCC;

}

/*****************************************************************************
 Function name: hi_os_array_lock
 Description  : 给指定数组加锁
 Input        : hi_os_static_array_s * pst_array
*****************************************************************************/
hi_uint32 hi_os_array_lock(hi_os_static_array_s * pst_array)
{
    if ( NULL == pst_array )
    {
        return HI_RET_NULLPTR;
    }

    return (hi_uint32)hi_os_pthread_mutex_lock(&pst_array->st_locker);
}

/*****************************************************************************
 Function name: hi_os_array_ulock
 Description  : 给指定数组解锁
 Input        : hi_os_static_array_s * pst_array
*****************************************************************************/
hi_uint32 hi_os_array_ulock(hi_os_static_array_s * pst_array)
{
    if ( NULL == pst_array )
    {
        return HI_RET_NULLPTR;
    }

    return (hi_uint32)hi_os_pthread_mutex_unlock(&pst_array->st_locker);
}

/*****************************************************************************
 Function name: hi_os_array_add_item
 Description  : 在给定数组中添加一个新的元素
 Input        : hi_os_static_array_s * pst_array
                hi_uint32 * pui_index: 输出添加到数组中的索引
                hi_uint32 ui_value
*****************************************************************************/
hi_uint32 hi_os_array_add_item(hi_os_static_array_s * pst_array,
                              hi_uint32 *pui_index,
                              hi_uint32 ui_value)
{
    hi_uint32 ui_index = 0;

    if ((NULL == pst_array) || (NULL == pui_index))
    {
        return HI_RET_NULLPTR;
    }

    hi_os_pthread_mutex_lock(&pst_array->st_locker);

    if (  (pst_array->ui_current_count >= pst_array->ui_max_count)
        || (NULL == pst_array->pst_header))
    {
        /* 两种情况应该同时发生，表示空闲队列为空，无可用空间 */
        hi_os_pthread_mutex_unlock(&pst_array->st_locker);
        return (hi_uint32)HI_ERR_OS_ARRAY_FULL;
    }

    /* 取得索引 */
    ui_index = (hi_uint32)(pst_array->pst_header - pst_array->pst_array);

    /* 头指针指向下一个可用节点 */
    pst_array->pst_header = (hi_os_array_item_s*)pst_array->pst_array[ui_index].ui_value;

    if (NULL == pst_array->pst_header)
    {
        /* 如果头节点为空，则表示空闲队列为空，无可用空间 */
        pst_array->pst_tailer = NULL;
    }

    pst_array->pst_array[ui_index].ui_value = ui_value;
    pst_array->pst_array[ui_index].ui_flag = HI_OS_ITME_USED;
    pst_array->ui_current_count ++;
    *pui_index = ui_index;

    hi_os_pthread_mutex_unlock(&pst_array->st_locker);

    return HI_RET_SUCC;
}

/*****************************************************************************
 Function name: hi_os_array_get_item
 Description  : 从数组中取出第N个元素
 Input        : hi_os_static_array_s * pst_array
                hi_uint32 ui_index
                hi_uint32 * puiOutValue
*****************************************************************************/
hi_uint32 hi_os_array_get_item(hi_os_static_array_s * pst_array,
                              hi_uint32 ui_index,
                              hi_uint32 * puiOutValue)
{
    if ((NULL == pst_array) || (NULL == puiOutValue))
    {
        return HI_RET_NULLPTR;
    }

    if (ui_index > pst_array->ui_max_count)
    {
        return HI_RET_INVALID_PARA;
    }

    if ( HI_FALSE == pst_array->pst_array[ui_index].ui_flag )
    {
        return HI_RET_INVALID_PARA;
    }

    *puiOutValue = pst_array->pst_array[ui_index].ui_value;
    return HI_RET_SUCC;
}

/*****************************************************************************
 Function name: hi_os_array_check_item
 Description  : 判断第N个元素是否被占用
 Input        : hi_os_static_array_s * pst_array
                hi_uint32 ui_index
*****************************************************************************/
hi_uint32 hi_os_array_check_item(hi_os_static_array_s * pst_array,
                                 hi_uint32 ui_index)
{
    if (NULL == pst_array)
    {
        return HI_OS_ITME_UNUSED;
    }

    if (ui_index >= pst_array->ui_max_count)
    {
        return HI_OS_ITME_UNUSED;
    }

    return (pst_array->pst_array[ui_index].ui_flag);

}

/*****************************************************************************
 Function name: hi_os_array_release_item
 Description  : 释放第N个元素
 Input        : hi_os_static_array_s * pst_array
                hi_uint32 ui_index
*****************************************************************************/
hi_uint32 hi_os_array_release_item(hi_os_static_array_s * pst_array,
                                  hi_uint32 ui_index)
{
    if (NULL == pst_array)
    {
        return HI_RET_INVALID_PARA;
    }

    if (ui_index >= pst_array->ui_max_count)
    {
        return HI_RET_INVALID_PARA;
    }

    hi_os_pthread_mutex_lock(&pst_array->st_locker);

    /* 被释放的节点指向空 */
    pst_array->pst_array[ui_index].ui_value = 0;
    pst_array->pst_array[ui_index].ui_flag = HI_OS_ITME_UNUSED;

    if (NULL != pst_array->pst_tailer)
    {
        /* 尾指针为空，表示上次使用了最后一个节点 */
        pst_array->pst_tailer->ui_value = (hi_uint32)&pst_array->pst_array[ui_index];
    }

    /* 尾指针指向最后释放的节点 */
    pst_array->pst_tailer = &pst_array->pst_array[ui_index];

    /* 如果头指针为空，表示释放的节点为最后一个可用节点 */
    if (NULL == pst_array->pst_header)
    {
        /* 让头指针指向下一个可用节点，即当前释放的节点 */
        pst_array->pst_header = &pst_array->pst_array[ui_index];
    }

    if (pst_array->ui_current_count > 0)
    {
        pst_array->ui_current_count --;
    }

    hi_os_pthread_mutex_unlock(&pst_array->st_locker);

    return HI_RET_SUCC;

}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
