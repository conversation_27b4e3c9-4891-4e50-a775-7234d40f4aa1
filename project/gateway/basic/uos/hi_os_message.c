/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_message.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_02
  最近修改   :

******************************************************************************/
#include "hi_typedef.h"
#include "os/hi_os_msg.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */


hi_int32  hi_os_msgget(hi_int32 key, hi_int32 i_msgflg)
{
    return msgget(key,i_msgflg);
}

hi_int32 hi_os_msgctl (hi_int32 i_msqid, hi_int32 i_cmd, hi_os_msgidds_s *pst_buf)
{
    return msgctl(i_msqid,i_cmd,(struct msqid_ds *)(hi_void *)pst_buf);
}

hi_int32 hi_os_msgsnd(hi_int32 i_msqid, hi_os_msgbuf_s*pst_msg, hi_uint32 ui_msg, hi_int32 i_msgflg)
{
    return msgsnd(i_msqid,pst_msg,ui_msg,i_msgflg);
}

hi_int32  hi_os_msgrcv(hi_int32 i_msqid, hi_os_msgbuf_s *pst_msg, hi_uint32 ui_msg, hi_int32 i_msgtyp, hi_int32 i_msgflg)
{
    return msgrcv(i_msqid,pst_msg,ui_msg,i_msgtyp,i_msgflg);
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
