/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_signal.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_02
  最近修改   :

******************************************************************************/
#include "hi_typedef.h"
#include "hi_errno.h"
#include "os/hi_os_signal.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

static HI_PFUNCSIGPROC g_pf_custom_signal_callbacks[MAX_SINGAL_NUMBER] = {0};

hi_int32 hi_os_sigemptyset(hi_os_signal_set_s* pst_set)
{
    return  sigemptyset((sigset_t *)(hi_void *)pst_set);
}

hi_int32 hi_os_sigfillset(hi_os_signal_set_s *pst_set)
{
    return  sigfillset((sigset_t *)(hi_void *)pst_set);
}

hi_int32 hi_os_sigaddset(hi_os_signal_set_s *pst_set, hi_int32 i_sig_num)
{
    return  sigaddset((sigset_t *)(hi_void *)pst_set,i_sig_num);
}

hi_int32 hi_os_sigdelset(hi_os_signal_set_s *pst_set, hi_int32 i_sig_num)
{
    return  sigdelset((sigset_t *)(hi_void *)pst_set,i_sig_num);
}

hi_int32 hi_os_sigismember(const hi_os_signal_set_s *pst_set, hi_int32 i_sig_num)
{
    return  sigismember((sigset_t *)(hi_void *)pst_set,i_sig_num);
}

hi_int32 hi_os_sigaction(hi_int32 i_sig_num, const struct sigaction* pst_act, struct sigaction* pst_old_act)
{
    return  sigaction(i_sig_num, pst_act, pst_old_act);
}

hi_int32 hi_os_sigprocmask(hi_int32 i_how, const hi_os_signal_set_s *pst_set, hi_os_signal_set_s *pst_old_set)
{
    return  sigprocmask( i_how, (sigset_t *)(hi_void *)pst_set, (sigset_t *)(hi_void *)pst_old_set);
}

hi_int32 hi_os_sigpending(hi_os_signal_set_s *pst_set)
{
    return  sigpending((sigset_t *)(hi_void *)pst_set);
}

hi_int32 hi_os_sigsuspend(const hi_os_signal_set_s *pst_mask)
{
    return  sigsuspend((sigset_t *)(hi_void *)pst_mask);
}

hi_int32 hi_os_pthread_sigmask(hi_int32 i_how, const hi_os_signal_set_s  *pst_new_mask,  hi_os_signal_set_s  *pst_old_mask)
{
    return  pthread_sigmask( i_how, (sigset_t *)(hi_void *)pst_new_mask, (sigset_t *)(hi_void *)pst_old_mask);
}

hi_int32 hi_os_pthread_kill(HI_PTHREAD_T thread, hi_int32 i_sign_o)
{
    return    pthread_kill((pthread_t )thread, i_sign_o);
}

hi_int32 hi_os_sigwait(const hi_os_signal_set_s *pst_set, hi_int32 *pi_sig)
{
    return  sigwait( (sigset_t *)(hi_void *)pst_set, pi_sig);
}

hi_int32 hi_os_sigwaitinfo(const hi_os_signal_set_s *pst_set, HI_SIGINFO_T *pst_Info)
{
    return  sigwaitinfo( (sigset_t *)(hi_void *)pst_set, (siginfo_t *)(hi_void *)pst_Info);
}

hi_int32  hi_os_sigtimedwait(const  hi_os_signal_set_s  *pst_set,  HI_SIGINFO_T *pst_Info, const hi_os_time_spec_s *pst_timeout)
{
    return   sigtimedwait((sigset_t  *)(hi_void *)pst_set,  (siginfo_t *)(hi_void *)pst_Info,(struct timespec *)(hi_void *)pst_timeout);
}

hi_int32 hi_os_sigqueue(hi_int32 i_pid, hi_int32 i_sig, const hi_os_signal_val_u u_value)
{
    //return sigqueue(i_pid,i_sig, *( union sigval *)&u_value);
    return 0;//luocheng
}

hi_void hi_os_signaltrap(hi_int32 i_signal)
{
    /* 先处理用户注册的信息函数，可能有资源要释放 */
    if ((i_signal >= 0 ) && (i_signal < MAX_SINGAL_NUMBER))
    {
        if (g_pf_custom_signal_callbacks[i_signal])
        {
            g_pf_custom_signal_callbacks[i_signal](i_signal);
        }
    }

    /* 对于以下信号，打印栈信息，然后终止进程 */
    switch (i_signal)
    {
        case SIGINT:
        case SIGILL:
        case SIGABRT:
        case SIGFPE:
        case SIGSEGV:
        {
            hi_os_exit(HI_RET_FAIL);
            break;
        }
        default:
        {
            break;
        }
    }
    return;
}

hi_void* hi_os_signal(hi_int32 i_signal_info, HI_PFUNCSIGPROC pf_sig_proc)
{
    hi_void * pv_return = NULL;
    if ((i_signal_info >= 0 ) && (i_signal_info < MAX_SINGAL_NUMBER))
    {
        /* 只针对该范围内的信息做拦截 */
        g_pf_custom_signal_callbacks[i_signal_info] = pf_sig_proc;
        switch (i_signal_info)
        {
            case SIGINT:
            case SIGILL:
            case SIGABRT:
            case SIGFPE:
            case SIGSEGV:
            {
                /* 以上信号不处理，由默认的异常处理函数处理 */
                break;
            }
            default:
            {
                /* 其它信号还是挂上去 */
                pv_return = signal(i_signal_info, hi_os_signaltrap);
                break;
            }
        }
        return pv_return;
    }

    return signal(i_signal_info, pf_sig_proc);
}

/*****************************************************************************
 Function name: hi_os_signal_exception
 Description  : 进程要使用异常时输出函数调用关系时使用些函数挂信号处理函数
*****************************************************************************/
hi_void hi_os_signal_exception(hi_void)
{
    /* 直接将异常处理函数挂到信息号上 */

    /* 2. Interactive attention signal. */
    signal(SIGINT, hi_os_signaltrap);

    /* 4. Illegal instruction. */
    signal(SIGILL, hi_os_signaltrap);

    /* 6. Abnormal termination.  */
    signal(SIGABRT, hi_os_signaltrap);

    /* 8. Erroneous arithmetic operation */
    signal(SIGFPE, hi_os_signaltrap);

    /* 11. 段错误 */
    signal(SIGSEGV, hi_os_signaltrap);

    /* 15. 用户中断，可以认为不是异常 */
    //signal(SIGTERM, hi_os_signaltrap);

    /* 30. 用户信号，打印调用栈，调试使用 */
    signal(SIGUSR1, hi_os_signaltrap);

    return;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
