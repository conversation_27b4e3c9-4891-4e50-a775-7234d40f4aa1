/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_sharemem.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_02
  最近修改   :

******************************************************************************/
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <sys/mman.h>
#include <sys/shm.h>
#include "hi_typedef.h"
#include "os/hi_os_shm.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

hi_int32  hi_os_shm_open (const hi_uchar8 *puc_name, hi_int32 i_operateflag,  hi_uint32 ui_mode)
{
    //return shm_open(puc_name,i_operateflag,ui_mode);
    return 0;
}

hi_int32 hi_os_shm_unlink(const hi_uchar8 *puc_name)
{
    //return shm_unlink(puc_name);
    return 0;
}

hi_void*  hi_os_mmap(hi_void *pv_start, hi_uint32 ui_length, hi_int32 i_prot , hi_int32 i_flags, hi_int32 i_fd, hi_int32 i_offset)
{
    return mmap(pv_start,ui_length,i_prot,i_flags,i_fd,i_offset);
}

hi_int32 hi_os_munmap(const hi_void *pv_start, hi_uint32 ui_length)
{
    return munmap((void *)pv_start,ui_length);
}

hi_int32 hi_os_ftok(const hi_char8 *pc_pathname,hi_int32 i_proj_id)
{
    return ftok(pc_pathname,i_proj_id);
}

hi_int32 hi_os_shmget(hi_int32 i_key, hi_uint32 ui_size, hi_int32 i_shmflg)
{
    return shmget(i_key,ui_size,i_shmflg);
}

hi_void *hi_os_shmat(hi_int32 i_shmid, const hi_void *pv_shmaddr, hi_int32 i_shmflg)
{
    return (hi_void *)shmat(i_shmid,pv_shmaddr,i_shmflg);
}

hi_int32 hi_os_shmdt(const hi_void *pv_shmaddr)
{
    return shmdt(pv_shmaddr);
}

hi_int32 hi_os_shmctl(hi_int32 i_shmid, hi_int32 i_cmd, hi_os_shmidds_s *pst_buf)
{
    return shmctl(i_shmid,i_cmd,(struct shmid_ds *)(hi_void *)pst_buf);
}

char *hi_os_get_mmap_file(int *p_fd, const char *filepath, size_t max_size)
{
    char *mfile = NULL;
    *p_fd = open(filepath, O_RDWR | O_CREAT, 0666);
    if (*p_fd == -1) {
        printf("open failed, file[%s]\n", filepath);
        return NULL;
    }
    if (ftruncate(*p_fd, max_size) == -1) {
        printf("ftruncate failed.\n");
        close(*p_fd);
        return NULL;
    }
    mfile = mmap(NULL, max_size, PROT_WRITE, MAP_SHARED, *p_fd, 0);
    if (mfile == MAP_FAILED) {
        printf("mmap failed.\n");
        close(*p_fd);
        return NULL;
    }
    return mfile;
}

int hi_os_close_mmap_file(int *p_fd, char *mfile, size_t max_size, size_t actual_size)
{
    int ret = 0;
    if (munmap(mfile, max_size) == -1) {
        printf("munmap failed.\n");
        ret = -1;
    }
    ftruncate(*p_fd, actual_size);
    close(*p_fd);
    return ret;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
