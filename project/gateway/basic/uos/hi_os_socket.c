/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_socket.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_04
  最近修改   :

******************************************************************************/
#include <unistd.h>
#include <arpa/inet.h>
#include "securec.h"
#include "hi_typedef.h"
#include "hi_comdef.h"
#include "os/hi_os_socket.h"
#include "os/hi_os_string.h"

#ifdef  __cplusplus
#if  __cplusplus
extern "C"{
#endif
#endif

/*****************************************************************************
 函 数 名  : hi_os_fd_set
 功能描述  : 对于字符集的设置
 输入参数  : iFd为需要设置的字符集（表示可读可写等），
             pFdset表述字符集
*****************************************************************************/
hi_void hi_os_fd_set(hi_int32 i_fd, hi_os_fd_set_s *pst_fdset)
{
    FD_SET(((hi_uint32)i_fd),(fd_set *)(hi_void *)pst_fdset);
}

/*****************************************************************************
 函 数 名  : hi_os_fd_clr
 功能描述  : 清除指定字符集功能
 输入参数  : iFd为需要清除的字符集
                          pFdset表述字符集
*****************************************************************************/
hi_void hi_os_fd_clr(hi_int32 i_fd, hi_os_fd_set_s *pst_fdset)
{
    FD_CLR(((hi_uint32)i_fd),(fd_set *)(hi_void *)pst_fdset);
}

/*****************************************************************************
 函 数 名  : hi_os_fd_isset
 功能描述  : 看具体的文件描述符是否被set
 输入参数  : iFd为需要被测试的文件描述符
                          pFdset表示总共的文件描述符指针
*****************************************************************************/
hi_int32 hi_os_fd_isset(hi_int32 i_fd, hi_os_fd_set_s *pst_fdset)
{
    return (hi_int32)(FD_ISSET(((hi_uint32)i_fd),(fd_set *)(hi_void *)pst_fdset));
}

/*****************************************************************************
 函 数 名  : hi_os_fd_zero
 功能描述  : 清零对应的文件描述符
 输入参数  : pFdset表示文件描述符指针
*****************************************************************************/
hi_void hi_os_fd_zero(hi_os_fd_set_s *pst_fdset)
{
    FD_ZERO((fd_set *)(hi_void *)pst_fdset);
}

/*****************************************************************************
 函 数 名  : hi_os_socket
 功能描述  :建立一个socket通信
 输入参数  :i_af:指定使用何种的地址类型
                         i_type:通信类型
                         i_protocol:用来指定socket所使用的传输协议编号，
                         通常此参考不用管它，设为0即可。
*****************************************************************************/
HI_OS_SOCKET_T hi_os_socket( hi_int32 i_af, hi_int32 i_type,hi_int32 i_protocol )
{
    return socket(i_af,i_type,i_protocol);
}

/*****************************************************************************
 函 数 名  : hi_os_connect
 功能描述  :建立socket连接
 输入参数  :uiSockfd：套接字
                         serv_addr：服务器套接字地址
                         addrlen：套接字地址的长度
*****************************************************************************/
hi_int32 hi_os_connect(hi_uint32  ui_sockfd,  const hi_os_socket_addr_s *pst_servaddr, HI_OS_SOCKLEN_T ui_addrlen)
{
    return connect((hi_int32)ui_sockfd,(struct sockaddr *)(hi_void *)pst_servaddr,(hi_uint32)ui_addrlen);
}


/*****************************************************************************
 函 数 名  :hi_os_listen
 功能描述  :等待连接
 输入参数  :ui_socket:为已建好连接的socket,
            i_backlog:指定同时能处理的最大连接要求，如果连接数目达此上限则client端将收到ECONNREFUSED的错误。
 输出参数  :
 返 回 值  :成功则返回0，
            失败返回-1，错误原因存于errno
*****************************************************************************/
hi_int32 hi_os_listen( HI_OS_SOCKET_T ui_socket,hi_int32 i_backlog )
{
    return listen(ui_socket,i_backlog);
}

/*****************************************************************************
 函 数 名  : hi_os_bind
 功能描述  :绑定SOCKET
 输入参数  :ui_socket:套接字
                          pst_addr:绑定地址
                          i_addrlen:地址长度
 返 回 值  :   成功则返回0
               失败返回－1，错误原因存于errno 中。
*****************************************************************************/
hi_int32 hi_os_bind( HI_OS_SOCKET_T       ui_socket,
                      const hi_os_socket_addr_s *pst_addr,
                      hi_int32        i_addrlen )
{
    return bind(ui_socket, (struct sockaddr *)(hi_void *)pst_addr,  (hi_uint32)i_addrlen);
}

/*****************************************************************************
 函 数 名  : hi_os_accept
 功能描述  :接受SOCKET连接
 输入参数  :ui_socket:套接字
 输出参数  : pst_addr:连接成功后远程主机地址
                           i_addrlen:地址的长度
 返 回 值  :   成功则返回新的socket处理代码，
                         失败返回-1，错误原因存于errno中。
*****************************************************************************/
hi_int32 hi_os_accept( HI_OS_SOCKET_T       ui_socket,
                         hi_os_socket_addr_s *pst_addr,
                         hi_uint32       *p_addrlen )
{
    return (hi_int32)accept(ui_socket,(struct sockaddr *)(hi_void *)pst_addr,p_addrlen);
}

/*****************************************************************************
 函 数 名  : hi_os_select
 功能描述  :I/O多工机制
 输入参数  :i_width:的第一个参数是文件描述符集中要被检测的比特数，
                          这个值必须至少比待检测的最大文件描述符大1
                          pst_read_fds:指定了被读监控的文件描述符集
                          pst_write_fds:指定了被写监控的文件描述符集
                          pst_except_fds:指定了被例外条件监控的文件描述符集
                          pst_timeout:起了定时器的作用：到了指定的时间，
                          无论是否有设备准备好，都返回调用。
 输出参数  :
 返 回 值  :   成功则返回返回就绪的文件描述符个数；
                         经过了timeout时长后仍无设备准备好，返回值为0；
                         如果select被某个信号中断，它将返回-1并设置errno为EINTR。
                         如果出错，返回-1并设置相应的errno。
*****************************************************************************/

hi_int32 hi_os_select( hi_int32       i_width,
                            hi_os_fd_set_s         *pst_readfds,
                            hi_os_fd_set_s         *pst_writefds,
                            hi_os_fd_set_s         *pst_exceptfds,
                            const hi_os_timeval_s *pst_timeout )
{
    struct timeval tv;
    tv.tv_sec = pst_timeout->tv_sec;
    tv.tv_usec = pst_timeout->tv_usec;
    return select(i_width,(fd_set *)(hi_void *)pst_readfds,
        (fd_set *)(hi_void *)pst_writefds,
        (fd_set *)(hi_void *)pst_exceptfds,
        &tv);
}

/*****************************************************************************
 函 数 名  : HI_IoctlSocket
 功能描述  :SOCKET ioctl
 输入参数  :ui_socket:套接字
                         i_cmd:命令字
                         pv_arg: 参数
*****************************************************************************/
hi_int32 hi_os_ioctl( HI_OS_SOCKET_T ui_socket,hi_int32  i_cmd,hi_void *pv_arg )
{
   return ioctl(ui_socket,(hi_uint32)i_cmd,pv_arg);
}

/*****************************************************************************
 函 数 名  : hi_os_send
 功能描述  :用来将数据由指定的socket传给对方主机
 输入参数  :ui_socket:为已建好连接的socket,
                         pcBuf：指向欲连接的数据内容
                         ilen：数据长度
                         i_flags:标志位，一般设为0
*****************************************************************************/
HI_OS_SSIZE_T hi_os_send( HI_OS_SOCKET_T ui_socket,const hi_uchar8 *puc_buf,hi_int32 i_len,hi_int32 i_flags )
{
    return send(ui_socket,puc_buf,(hi_uint32)i_len,i_flags);
}

/*****************************************************************************
 函 数 名  : hi_os_sendto
 功能描述  :用来将数据由指定的socket传给对方主机
 输入参数  :ui_socket:套接字
                          i_len:可接收数据的最大长度
                          i_flags:标志位，一般设为0
                          pst_to:指定欲传送的网络地址
                          i_tolen:网络地址长度
 输出参数  :pBuf:存放接收的数据的空间
 返 回 值  :   成功则返回实际传送出去的字符数，
                         失败返回－1，错误原因存于errno 中。
*****************************************************************************/
HI_OS_SSIZE_T hi_os_sendto( HI_OS_SOCKET_T        ui_socket,
                         const hi_uchar8        *puc_buf,
                         hi_int32        i_len,
                         hi_int32        i_flags,
                         const hi_os_socket_addr_s *pst_to,
                         hi_int32        i_tolen )
{
    return  sendto(  ui_socket,  puc_buf,  (hi_uint32)i_len,  i_flags, (const struct sockaddr *)(hi_void *)pst_to,  (hi_uint32)i_tolen);
}

/*****************************************************************************
 函 数 名  : hi_os_send
 功能描述  :由指定的socket接收数据
 输入参数  :ui_socket:为已建好连接的socket,
                         pcBuf：指向欲连接的数据内容
                         ilen：数据长度
                         i_flags:标志位，一般设为0
*****************************************************************************/
HI_OS_SSIZE_T hi_os_recv( HI_OS_SOCKET_T ui_socket, hi_uchar8 *puc_buf, hi_int32 i_len, hi_int32 i_flags )
{
    return recv(ui_socket,puc_buf,(hi_uint32)i_len,i_flags);
}


/*****************************************************************************
 函 数 名  : hi_os_recvfrom
 功能描述  :用来接收远程主机经指定的socket 传来的数据，并把数据存到由参数buf 指向的内存空间
 输入参数  :ui_socket:套接字
                          i_len:可接收数据的最大长度
                          i_flags:标志位，一般设为0
                          pst_form:指定欲接收的网络地址
                          pui_formlen:网络地址长度
 输出参数  :pBuf:存放接收的数据的空间
 返 回 值  :   成功则返回字符串指针，失败则返回NULL。
*****************************************************************************/
HI_OS_SSIZE_T hi_os_recvfrom( HI_OS_SOCKET_T ui_socket,
                          hi_uchar8 *puc_buf,
                          hi_int32        i_len,
                          hi_int32        i_flags,
                          hi_os_socket_addr_s *pst_form,
                          hi_uint32       *pui_formlen )
{
    return (HI_OS_SSIZE_T)recvfrom( ui_socket, puc_buf,  (hi_uint32)i_len,  i_flags, (struct sockaddr *)(hi_void *)pst_form, pui_formlen);
}

/*****************************************************************************
 函 数 名  : hi_os_gethostname
 功能描述  :返回指定当前进程的host名称
 输入参数  :
                           ui_len:存放host名称的存储区大小
 输出参数  :pc_name:存放host名称的存储区
 返 回 值  :   成功返回0
                         失败返回错误码
*****************************************************************************/
hi_int32 hi_os_gethostname(hi_char8 *pc_name, hi_uint32 ui_len)
{
    return gethostname(pc_name,(hi_uint32)ui_len);
}

/*****************************************************************************
 函 数 名  : hi_os_getsockname
 功能描述  :获取当前描述符的名称
 输入参数  :ui_socket:套接字
 输出参数  : pName：套接字名字
                          pNamelen：套接字名字长度
 返 回 值  :   成功则返回0
                         如果出错，返回-1并设置相应的errno。
*****************************************************************************/
hi_int32 hi_os_getsockname(hi_int32 ui_socket, hi_os_socket_addr_s *pst_name, HI_OS_SOCKLEN_T *pst_namelen)
{
    return getsockname( ui_socket, (struct sockaddr *)(hi_void *)pst_name, (socklen_t *)(hi_void *)pst_namelen);
}

/*****************************************************************************
 函 数 名  : hi_os_getsockopt
 功能描述  :获取套接字的状态
 输入参数  :ui_socket:套接字
                         i_level:网络层
                         i_optname:   选项
                         pst_optlen:欲保存结果的内存地址的长度
 输出参数  : p_optval:指向欲保存结果的内存地址
 返 回 值  :   成功则返回0
                         如果出错，返回-1并设置相应的errno。
*****************************************************************************/

hi_int32  hi_os_getsockopt(HI_OS_SOCKET_T ui_socket, hi_int32 i_level, hi_int32 i_optname, hi_void *p_optval, HI_OS_SOCKLEN_T   *pst_optlen)
{
    return  getsockopt(ui_socket,  i_level,  i_optname, p_optval, (socklen_t*)pst_optlen);
}

/*****************************************************************************
 函 数 名  : hi_os_setsockopt
 功能描述  :设置套接字的状态
 输入参数  :ui_socket:套接字
                         i_level:欲设置的网络层
                         i_optname:  欲设置的选项
                         pst_optlen:欲设置的值的长度
                         p_optval:指向欲设置的值
 返 回 值  :   成功则返回0
                         如果出错，返回-1并设置相应的errno。
*****************************************************************************/

hi_int32  hi_os_setsockopt(HI_OS_SOCKET_T ui_socket,  hi_int32  i_level,  hi_int32  i_optname,
				const hi_void  *p_optval,   HI_OS_SOCKLEN_T ui_optlen)
{
    return setsockopt( ui_socket,  i_level, i_optname, p_optval,  ui_optlen);
}

/******************************************************************************
  函数名称  : hi_os_checkipaddr
  功能描述  : IP地址校验函数
  输入参数  :
              1.  *puc_ipaddr: 待校验的IP地址
  返 回 值  : pcIpAddr是IP地址则返回真HI_TRUE，
              pcIpAddr不是IP地址则返回假HI_FALSE，
******************************************************************************/
hi_uint32 hi_os_checkipaddr(hi_uchar8 *puc_ipaddr)
{
    hi_uint32    ui_cyc_var                       = 0;
    hi_uint32    ui_ret                          = HI_FALSE;
    hi_char8    *pc_token                        = NULL;
    hi_char8    *pc_last                         = NULL;
    hi_char8    *pc_end                          = NULL;
    hi_char8     a_c_buf[HI_OS_SYS_IP_LENGTH]    = {0};
    hi_int32     i_num                           = 0;

    if (NULL == puc_ipaddr)
    {
        return ui_ret;
    }

    if (strcpy_s(a_c_buf, sizeof(a_c_buf), (hi_char8*)puc_ipaddr) != EOK)
		return HI_FALSE;

    pc_token = hi_os_strtok_r(a_c_buf, ".", &pc_last);
    if ( NULL == pc_token)
    {
        return ui_ret;
    }

    /* start of 避免使用strtol函数时，将 "+123" 等效为 "123" 转换成整形123 */
    if (('+' == *pc_token) || ('-' == *pc_token))
    {
        return ui_ret;
    }

    /* end of 避免使用strtol函数时，将 "+123" 等效为 "123" 转换成整形123 */
    i_num = hi_os_strtol(pc_token, &pc_end, HI_OS_SYS_BASE_DEC);

    if (('\0' == *pc_end) && (HI_OS_SYS_IP_MAX_NUMBER >= i_num))
    {
        for (ui_cyc_var = 0; ui_cyc_var< (HI_OS_SYS_IP_BYTE - 1); ui_cyc_var++)
        {
            pc_token = hi_os_strtok_r(NULL, ".", &pc_last);
            if ( NULL == pc_token   )
            {
                break;
            }

            /* start of 避免使用strtol函数时，将 "+123" 等效为 "123" 转换成整形123 */
            if (('+' == *pc_token) || ('-' == *pc_token))
            {
                break;
            }

            /* end of 避免使用strtol函数时，将 "+123" 等效为 "123" 转换成整形123 */
            i_num = hi_os_strtol(pc_token, &pc_end, HI_OS_SYS_BASE_DEC);
            if ( ('\0' != *pc_end  ) || (i_num > HI_OS_SYS_IP_MAX_NUMBER) )
            {
                break;
            }
        }

        if ((HI_OS_SYS_IP_BYTE - 1) == ui_cyc_var  )
        {
            ui_ret = HI_TRUE;
        }
    }

    return ui_ret;
}

/*****************************************************************************
 函 数 名  : hi_os_inet_pton
 功能描述  : 将IP地址表示形式转换为网络序
 输入参数  : i_af   : 协议簇标志
             pc_src ：IP地址字符串
             pv_dst : 网络字节序数据缓存
*****************************************************************************/
hi_int32 hi_os_inet_pton(hi_int32 i_af, const hi_char8 *pc_src, hi_void *pv_dst)
{
    /* Convert from presentation format of an Internet number in buffer
       starting at CP to the binary network format and store result for
       interface type AF in buffer starting at BUF.  */

    return inet_pton (i_af, pc_src, pv_dst);
}

/*****************************************************************************
 函 数 名  : hi_os_inet_ntop
 功能描述  : 将网络序转换为IP地址表示形式
 输入参数  : i_af   : 协议簇标志
             pc_src ：IP地址字符串
             pv_dst : 网络字节序数据缓存
*****************************************************************************/
const hi_char8 * hi_os_inet_ntop(hi_int32 i_af, const hi_void *pv_src, hi_char8 *pc_dst, hi_uint32 ui_size)
{
    /* Convert a Internet address in binary network format for interface
       type AF in buffer starting at CP to presentation form and place
       result in buffer of length LEN astarting at BUF.  */

    return inet_ntop(i_af, pv_src, pc_dst, ui_size);
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif  /* end of __cplusplus */
