/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_os_fileio.h
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_08_06
  最近修改   :

******************************************************************************/
#ifndef __HI_OS_MATH_H__
#define __HI_OS_MATH_H__
#include <math.h>
#include "hi_typedef.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

static inline hi_double64 hi_os_pow(hi_double64 x, hi_double64 y)
{
	return pow(x, y);
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OS_MATH_H__ */
