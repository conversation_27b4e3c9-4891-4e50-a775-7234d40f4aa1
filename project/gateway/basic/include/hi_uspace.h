/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_uspace.h
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_09_23

******************************************************************************/
#ifndef __HI_USPACE_H__
#define __HI_USPACE_H__
#include "hi_basic.h"

#ifdef __cplusplus
#if __cplusplus
extern "C"{
#endif
#endif /* __cplusplus */

#define HI_NULL NULL

#define HI_INVALID_PTR(ptr)         if ((ptr) == HI_NULL) return HI_RET_NULLPTR;

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_USPACE_H__ */
