[A2]
0x0     ="0x64"
0x1     ="0x0"
0x2     ="0xCE"
0x3     ="0x0"
0x4     ="0x5F"
0x5     ="0x0"
0x6     ="0xD3"
0x7     ="0x0"
0x8     ="0x90"
0x9     ="0x88"
0xA     ="0x71"
0xB     ="0x48"
0xC     ="0x8E"
0xD     ="0x94"
0xE     ="0x73"
0xF     ="0x3C"
0x10    ="0xC3"
0x11    ="0x50"
0x12    ="0x1"
0x13    ="0xF4"
0x14    ="0xC1"
0x15    ="0x5C"
0x16    ="0x2"
0x17    ="0xEE"
0x18    ="0x7B"
0x19    ="0x84"
0x1A    ="0x27"
0x1B    ="0x10"
0x1C    ="0x62"
0x1D    ="0x20"
0x1E    ="0x31"
0x1F    ="0x2E"
0x20    ="0x31"
0x21    ="0x24"
0x22    ="0x0"
0x23    ="0x2"
0x24    ="0x27"
0x25    ="0x10"
0x26    ="0x0"
0x27    ="0x3"
0x28    ="0x0"
0x29    ="0x0"
0x2A    ="0x0"
0x2B    ="0x0"
0x2C    ="0x0"
0x2D    ="0x0"
0x2E    ="0x0"
0x2F    ="0x0"
0x30    ="0x0"
0x31    ="0x0"
0x32    ="0x0"
0x33    ="0x0"
0x34    ="0x0"
0x35    ="0x0"
0x36    ="0x0"
0x37    ="0x0"
0x38    ="0x0"
0x39    ="0x0"
0x3A    ="0x0"
0x3B    ="0x0"
0x3C    ="0x0"
0x3D    ="0x0"
0x3E    ="0x0"
0x3F    ="0x0"
0x40    ="0x0"
0x41    ="0x0"
0x42    ="0x0"
0x43    ="0x0"
0x44    ="0x3F"
0x45    ="0x80"
0x46    ="0x0"
0x47    ="0x0"
0x48    ="0x0"
0x49    ="0x0"
0x4A    ="0x0"
0x4B    ="0x0"
0x4C    ="0x1"
0x4D    ="0x0"
0x4E    ="0x0"
0x4F    ="0x0"
0x50    ="0x1"
0x51    ="0x0"
0x52    ="0x0"
0x53    ="0x0"
0x54    ="0x1"
0x55    ="0x0"
0x56    ="0x0"
0x57    ="0x0"
0x58    ="0x1"
0x59    ="0x0"
0x5A    ="0x0"
0x5B    ="0x0"
0x5C    ="0x0"
0x5D    ="0x0"
0x5E    ="0x0"
0x5F    ="0x0"
0x60    ="0x20"
0x61    ="0xE7"
0x62    ="0x7E"
0x63    ="0x24"
0x64    ="0x14"
0x65    ="0x69"
0x66    ="0x39"
0x67    ="0xE4"
0x68    ="0x0"
0x69    ="0x0"
0x6A    ="0x0"
0x6B    ="0x0"
0x6C    ="0x0"
0x6D    ="0x0"
0x6E    ="0x0"
0x6F    ="0x0"
0x70    ="0x0"
0x71    ="0x40"
0x72    ="0x0"
0x73    ="0x0"
0x74    ="0x0"
0x75    ="0x40"
0x76    ="0x0"
0x77    ="0x0"
0x78    ="0x0"
0x79    ="0x0"
0x7A    ="0xFF"
0x7B    ="0x0"
0x7C    ="0x0"
0x7D    ="0x0"
0x7E    ="0x0"
0x7F    ="0x0"
0x80    ="0xFF"
0x81    ="0xFF"
0x82    ="0xFF"
0x83    ="0xFF"
0x84    ="0xFF"
0x85    ="0xFF"
0x86    ="0xFF"
0x87    ="0xFF"
0x88    ="0xFF"
0x89    ="0xFF"
0x8A    ="0xFF"
0x8B    ="0xFF"
0x8C    ="0xFF"
0x8D    ="0xFF"
0x8E    ="0xFF"
0x8F    ="0xFF"
0x90    ="0xFF"
0x91    ="0xFF"
0x92    ="0xFF"
0x93    ="0xFF"
0x94    ="0xFF"
0x95    ="0xFF"
0x96    ="0xFF"
0x97    ="0xFF"
0x98    ="0xFF"
0x99    ="0xFF"
0x9A    ="0xFF"
0x9B    ="0xFF"
0x9C    ="0xFF"
0x9D    ="0xFF"
0x9E    ="0xFF"
0x9F    ="0xFF"
0xA0    ="0xFF"
0xA1    ="0xFF"
0xA2    ="0xFF"
0xA3    ="0xFF"
0xA4    ="0xFF"
0xA5    ="0xFF"
0xA6    ="0xFF"
0xA7    ="0xFF"
0xA8    ="0xFF"
0xA9    ="0xFF"
0xAA    ="0xFF"
0xAB    ="0xFF"
0xAC    ="0xFF"
0xAD    ="0xFF"
0xAE    ="0xFF"
0xAF    ="0xFF"
0xB0    ="0xFF"
0xB1    ="0xFF"
0xB2    ="0xFF"
0xB3    ="0xFF"
0xB4    ="0xFF"
0xB5    ="0xFF"
0xB6    ="0xFF"
0xB7    ="0xFF"
0xB8    ="0xFF"
0xB9    ="0xFF"
0xBA    ="0xFF"
0xBB    ="0xFF"
0xBC    ="0xFF"
0xBD    ="0xFF"
0xBE    ="0xFF"
0xBF    ="0xFF"
0xC0    ="0xFF"
0xC1    ="0xFF"
0xC2    ="0xFF"
0xC3    ="0xFF"
0xC4    ="0xFF"
0xC5    ="0xFF"
0xC6    ="0xFF"
0xC7    ="0xFF"
0xC8    ="0xFF"
0xC9    ="0xFF"
0xCA    ="0xFF"
0xCB    ="0xFF"
0xCC    ="0xFF"
0xCD    ="0xFF"
0xCE    ="0xFF"
0xCF    ="0xFF"
0xD0    ="0xFF"
0xD1    ="0xFF"
0xD2    ="0xFF"
0xD3    ="0xFF"
0xD4    ="0xFF"
0xD5    ="0xFF"
0xD6    ="0xFF"
0xD7    ="0xFF"
0xD8    ="0xFF"
0xD9    ="0xFF"
0xDA    ="0xFF"
0xDB    ="0xFF"
0xDC    ="0xFF"
0xDD    ="0xFF"
0xDE    ="0xFF"
0xDF    ="0xFF"
0xE0    ="0xFF"
0xE1    ="0xFF"
0xE2    ="0xFF"
0xE3    ="0xFF"
0xE4    ="0xFF"
0xE5    ="0xFF"
0xE6    ="0xFF"
0xE7    ="0xFF"
0xE8    ="0xFF"
0xE9    ="0xFF"
0xEA    ="0xFF"
0xEB    ="0xFF"
0xEC    ="0xFF"
0xED    ="0xFF"
0xEE    ="0xFF"
0xEF    ="0xFF"
0xF0    ="0xFF"
0xF1    ="0xFF"
0xF2    ="0xFF"
0xF3    ="0xFF"
0xF4    ="0xFF"
0xF5    ="0xFF"
0xF6    ="0xFF"
0xF7    ="0xFF"
0xF8    ="0xFF"
0xF9    ="0xC0"
0xFA    ="0xFF"
0xFB    ="0xFF"
0xFC    ="0xFF"
0xFD    ="0xC0"
0xFE    ="0xFF"
0xFF    ="0xFF"

[A2_TAB2]
0x80 = "0"
0x81 = "0"
0x82 = "0"
0x83 = "0"
0x84 = "0"
0x85 = "0"
0x86 = "0"
0x87 = "0"
0x88 = "0"
0x89 = "0"
0x8a = "0"
0x8b = "0"
0x8c = "0"
0x8d = "0"
0x8e = "0"
0x8f = "0"
0x90 = "0"
0x91 = "0"
0x92 = "0"
0x93 = "0"
0x94 = "0"
0x95 = "0"
0x96 = "0"
0x97 = "0"
0x98 = "0"
0x99 = "0"
0x9a = "0"
0x9b = "0"
0x9c = "0"
0x9d = "0"
0x9e = "0"
0x9f = "0" 
0xA0 = "6A"
0xA1 = "1A"
0xA2 = "70"
0xA3 = "9A"
0xA4 = "F5"
0xA5 = "A3"
0xA6 = "61" 
0xA7 = "41" 
0xA8 = "A4" 
0xA9 = "81"  
0xAA = "0"  
0xAB = "96"   
0xAC  = "C0"   
0xAD  = "F9"    
0xAE  = "FE"      
0xAF  = "96"     
0xB0  = "5"    
0xB1  = "4E"       
0xB2  = "0"     
0xB3  = "0"    
0xB4  = "0"     
0xB5  = "0"  
0xB6  = "0"        
0xB7  = "40"    
0xB8  = "0" 
0xB9  = "0"  
0xBA  ="A" 
0xBB  ="19"  
0xBC  ="1A"
0xBD  ="5"
0xBE  ="A8"
0xBF  ="0" 
0xC0  ="5E"
0xC1  ="2" 
0xC2  ="0"  
0xC3  ="A0" 
0xC4  ="0"  
0xC5  ="0"  
0xC6  ="0"
0xC7  = "3" 
0xC8  ="0"    
0xC9  ="FF"   
0xCA  ="FF"  
0xCB  ="FF"  
0xCC  ="FF"  
0xCD  ="FF"   
0xCE  ="FF" 
0xCF  ="FF" 
0xD0  ="FF" 
0xD1  ="A3" 
0xD2  ="01"
0xD3  ="44"
0xD4  ="BC"
0xD5  ="C8"
0xD6  ="00"
0xD7  ="A0"
0xD8  ="00"
0xD9  ="00"
0xDA  ="01"
0xDB  ="90"
0xDC  ="00"
0xDD  = "00"
0xDE  = "11" 
0xDF  = "D0"  
0xE0  = "0"  
0xE1  = "0" 
0xE2  = "3"
0xE3  = "0" 
0xE4  = "2"  
0xE5  = "6F"  
0xE6  = "FD" 
0xE7  = "80" 
0xE8  = "55"  
0xE9  = "0" 
0xEA  = "0" 
0xEB  = "0" 
0xEC  = "1A" 
0xED  = "D0" 
0xEE  = "0"  
0xEF  = "E0"  
0xF0  = "0"  
0xF1  = "1"   
0xF2  = "4D"  
0xF3  = "0"  
0xF4  = "CD"  
0xF5  = "80"  
0xF6  = "CF"  
0xF7  = "0" 
0xF8  = "FF"  
0xF9  = "C0" 
0xFA  = "FF" 
0xFB  = "FF" 
0xFC  = "FF"
0xFD  = "C0"
0xFE  = "FF"
0xFF  = "FF"

[BiasLUT]
0       = "1E"
1       = "0"
2       = "1E"
3       = "40"
4       = "1E"
5       = "80"
6       = "1E"
7       = "C0"
8       = "1F"
9       = "40"
10      = "1F"
11      = "80"
12      = "1F"
13      = "C0"
14      = "20"
15      = "0"
16      = "20"
17      = "80"
18      = "20"
19      = "C0"
20      = "21"
21      = "0"
22      = "21"
23      = "40"
24      = "21"
25      = "C0"
26      = "22"
27      = "40"
28      = "23"
29      = "0"
30      = "23"
31      = "C0"
32      = "24"
33      = "80"
34      = "25"
35      = "40"
36      = "26"
37      = "0"
38      = "26"
39      = "C0"
40      = "27"
41      = "40"
42      = "28"
43      = "0"
44      = "28"
45      = "C0"
46      = "29"
47      = "80"
48      = "2A"
49      = "40"
50      = "2B"
51      = "0"
52      = "2B"
53      = "C0"
54      = "2D"
55      = "40"
56      = "2F"
57      = "0"
58      = "30"
59      = "C0"
60      = "32"
61      = "80"
62      = "34"
63      = "40"
64      = "36"
65      = "0"
66      = "37"
67      = "C0"
68      = "39"
69      = "80"
70      = "3B"
71      = "40"
72      = "3D"
73      = "0"
74      = "3E"
75      = "C0"
76      = "40"
77      = "80"
78      = "44"
79      = "0"
80      = "47"
81      = "C0"
82      = "4B"
83      = "40"
84      = "4F"
85      = "0"
86      = "52"
87      = "80"
88      = "56"
89      = "40"
90      = "5D"
91      = "40"
92      = "64"
93      = "40"
94      = "6B"
95      = "40"
96      = "72"
97      = "40"
98      = "79"
99      = "40"
100     = "80"
101     = "40"
102     = "80"
103     = "40"
104     = "80"
105     = "40"
106     = "80"
107     = "40"
108     = "80"
109     = "40"
110     = "80"
111     = "40"
112     = "80"
113     = "40"
114     = "80"
115     = "40"
116     = "80"
117     = "40"
118     = "80"
119     = "40"
120     = "80"
121     = "40"
122     = "80"
123     = "40"
124     = "80"
125     = "40"
126     = "80"
127     = "40"

[MODLUT]
0       = "32"
1       = "0"
2       = "32"
3       = "40"
4       = "32"
5       = "80"
6       = "32"
7       = "C0"
8       = "33"
9       = "40"
10      = "33"
11      = "80"
12      = "33"
13      = "C0"
14      = "34"
15      = "0"
16      = "34"
17      = "80"
18      = "34"
19      = "C0"
20      = "35"
21      = "0"
22      = "35"
23      = "40"
24      = "35"
25      = "C0"
26      = "36"
27      = "40"
28      = "36"
29      = "C0"
30      = "37"
31      = "40"
32      = "37"
33      = "C0"
34      = "38"
35      = "40"
36      = "38"
37      = "C0"
38      = "39"
39      = "40"
40      = "39"
41      = "C0"
42      = "3A"
43      = "40"
44      = "3A"
45      = "C0"
46      = "3B"
47      = "40"
48      = "3B"
49      = "C0"
50      = "3C"
51      = "40"
52      = "3C"
53      = "C0"
54      = "3D"
55      = "C0"
56      = "3E"
57      = "C0"
58      = "3F"
59      = "C0"
60      = "41"
61      = "0"
62      = "42"
63      = "0"
64      = "43"
65      = "0"
66      = "44"
67      = "0"
68      = "45"
69      = "40"
70      = "46"
71      = "40"
72      = "47"
73      = "40"
74      = "48"
75      = "40"
76      = "49"
77      = "80"
78      = "4B"
79      = "80"
80      = "4D"
81      = "80"
82      = "4F"
83      = "C0"
84      = "51"
85      = "C0"
86      = "53"
87      = "C0"
88      = "56"
89      = "0"
90      = "59"
91      = "80"
92      = "5D"
93      = "40"
94      = "61"
95      = "0"
96      = "64"
97      = "C0"
98      = "68"
99      = "80"
100     = "6C"
101     = "40"
102     = "6C"
103     = "40"
104     = "6C"
105     = "40"
106     = "6C"
107     = "40"
108     = "6C"
109     = "40"
110     = "6C"
111     = "40"
112     = "6C"
113     = "40"
114     = "6C"
115     = "40"
116     = "6C"
117     = "40"
118     = "6C"
119     = "40"
120     = "6C"
121     = "40"
122     = "6C"
123     = "40"
124     = "6C"
125     = "40"
126     = "6C"
127     = "40"

[APDLUT]
0 = "0"
1 = "0"
2 = "0"
3 = "0"
4 = "0"
5 = "0"
6 = "0"
7 = "0"
8 = "0"
9 = "0"
10 = "0" 
11 = "0"
12 = "0"
13 = "0"
14 = "0"
15 = "0"
16 = "0"
17 = "0"
18 = "0"
19 = "0"
20 = "0"
21 = "0"
22 = "0"
23 = "0"
24 = "0"
25 = "0"
26 = "0"
27 = "0"
28 = "0"
29 = "0"
30 = "0"
31 = "0"
32 = "0"
33 = "0"
34 = "0"
35 = "0"
36 = "0"
37 = "0"
38 = "0"
39 = "0"
40 = "0"
41 = "0"
42 = "0"
43 = "0"
44 = "0"
45 = "0"
46 = "0"
47 = "0"
48 = "0"
49 = "0"
50 = "0"
51 = "0"
52 = "0"
53 = "0"
54 = "0"
55 = "0"
56 = "0"
57 = "0"
58 = "0"
59 = "0"
60 = "0"
61 = "0" 
62 = "0"
63 = "0"
0  =  "3f"
1  =  "42"
2  =  "44"
3  =  "46"
4  =  "48"
5  =  "4a"
6  =  "4c"
7  =  "4e"
8  =  "51"
9  =  "53"
10 =  "54"
11 =  "56"
12 =  "58"
13 =  "5a"
14 =  "5c"
15 =  "5d"
16 =  "5f"
17 =  "61"
18 =  "63"
19 =  "65"
20 =  "67"
21 =  "69"
22 =  "6c"
23 =  "6d"
24 =  "6e"
25 =  "70"
26 =  "72"
27 =  "73"
28 =  "75"
29 =  "77"
30 =  "79"
31 =  "7b"
32 =  "7d"
33 =  "7f"
34 =  "81"
35 =  "82"
36 =  "84"
37 =  "86"
38 =  "87"
39 =  "88"
40 =  "8a"
41 =  "8c"
42 =  "8e"
43 =  "90"
44 =  "92"
45 =  "93"
46 =  "95"
47 =  "97"
48 =  "99"
49 =  "9b"
50 =  "9b"
51 =  "9b"
52 =  "9b"
53 =  "9b"
54 =  "9b"
55 =  "9b"
56 =  "9b"
57 =  "9b"
58 =  "9b"
59 =  "9b"
60 =  "9b"
61 =  "9b"
62 =  "9b"
63 =  "9b"