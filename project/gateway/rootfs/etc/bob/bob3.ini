[A2]
0x0     ="0x64"
0x1     ="0x0"
0x2     ="0xCE"
0x3     ="0x0"
0x4     ="0x5F"
0x5     ="0x0"
0x6     ="0xD3"
0x7     ="0x0"
0x8     ="0x90"
0x9     ="0x88"
0xA     ="0x71"
0xB     ="0x48"
0xC     ="0x8E"
0xD     ="0x94"
0xE     ="0x73"
0xF     ="0x3C"
0x10    ="0xA6"
0x11    ="0x5"
0x12    ="0x1"
0x13    ="0xF4"
0x14    ="0x9C"
0x15    ="0x40"
0x16    ="0x2"
0x17    ="0xEE"
0x18    ="0x7B"
0x19    ="0x84"
0x1A    ="0x27"
0x1B    ="0x10"
0x1C    ="0x62"
0x1D    ="0x20"
0x1E    ="0x31"
0x1F    ="0x2E"
0x20    ="0x31"
0x21    ="0x24"
0x22    ="0x0"
0x23    ="0x1"
0x24    ="0x27"
0x25    ="0x10"
0x26    ="0x0"
0x27    ="0x3"
0x28    ="0x0"
0x29    ="0x0"
0x2A    ="0x0"
0x2B    ="0x0"
0x2C    ="0x0"
0x2D    ="0x0"
0x2E    ="0x0"
0x2F    ="0x0"
0x30    ="0x0"
0x31    ="0x0"
0x32    ="0x0"
0x33    ="0x0"
0x34    ="0x0"
0x35    ="0x0"
0x36    ="0x0"
0x37    ="0x0"
0x38    ="0x0"
0x39    ="0x0"
0x3A    ="0x0"
0x3B    ="0x0"
0x3C    ="0x0"
0x3D    ="0x0"
0x3E    ="0x0"
0x3F    ="0x0"
0x40    ="0x0"
0x41    ="0x0"
0x42    ="0x0"
0x43    ="0x0"
0x44    ="0x0"
0x45    ="0x0"
0x46    ="0x0"
0x47    ="0x0"
0x48    ="0x0"
0x49    ="0x0"
0x4A    ="0x0"
0x4B    ="0x0"
0x4C    ="0x0"
0x4D    ="0x0"
0x4E    ="0x0"
0x4F    ="0x0"
0x50    ="0x0"
0x51    ="0x0"
0x52    ="0x0"
0x53    ="0x0"
0x54    ="0x0"
0x55    ="0x0"
0x56    ="0x0"
0x57    ="0x0"
0x58    ="0x0"
0x59    ="0x0"
0x5A    ="0x0"
0x5B    ="0x0"
0x5C    ="0x0"
0x5D    ="0x0"
0x5E    ="0x0"
0x5F    ="0x0"
0x60    ="0x3A"
0x61    ="0x70"
0x62    ="0x77"
0x63    ="0x70"
0x64    ="0x17"
0x65    ="0xC"
0x66    ="0x2B"
0x67    ="0xB5"
0x68    ="0x0"
0x69    ="0x0"
0x6A    ="0x0"
0x6B    ="0x0"
0x6C    ="0x0"
0x6D    ="0x0"
0x6E    ="0x2"
0x6F    ="0x0"
0x70    ="0x0"
0x71    ="0x40"
0x72    ="0x0"
0x73    ="0x0"
0x74    ="0x1"
0x75    ="0x40"
0x76    ="0x0"
0x77    ="0x0"
0x78    ="0x0"
0x79    ="0x0"
0x7A    ="0x0"
0x7B    ="0x0"
0x7C    ="0x0"
0x7D    ="0x0"
0x7E    ="0x0"
0x7F    ="0x0"
0x80    ="0xFF"
0x81    ="0xFF"
0x82    ="0xFF"
0x83    ="0xFF"
0x84    ="0xFF"
0x85    ="0xFF"
0x86    ="0xFF"
0x87    ="0xFF"
0x88    ="0xFF"
0x89    ="0xFF"
0x8A    ="0xFF"
0x8B    ="0xFF"
0x8C    ="0xFF"
0x8D    ="0xFF"
0x8E    ="0xFF"
0x8F    ="0xFF"
0x90    ="0xFF"
0x91    ="0xFF"
0x92    ="0xFF"
0x93    ="0xFF"
0x94    ="0xFF"
0x95    ="0xFF"
0x96    ="0xFF"
0x97    ="0xFF"
0x98    ="0xFF"
0x99    ="0xFF"
0x9A    ="0xFF"
0x9B    ="0xFF"
0x9C    ="0xFF"
0x9D    ="0xFF"
0x9E    ="0xFF"
0x9F    ="0xFF"
0xA0    ="0xFF"
0xA1    ="0xFF"
0xA2    ="0xFF"
0xA3    ="0xFF"
0xA4    ="0xFF"
0xA5    ="0xFF"
0xA6    ="0xFF"
0xA7    ="0xFF"
0xA8    ="0xFF"
0xA9    ="0xFF"
0xAA    ="0xFF"
0xAB    ="0xFF"
0xAC    ="0xFF"
0xAD    ="0xFF"
0xAE    ="0xFF"
0xAF    ="0xFF"
0xB0    ="0xFF"
0xB1    ="0xFF"
0xB2    ="0xFF"
0xB3    ="0xFF"
0xB4    ="0xFF"
0xB5    ="0xFF"
0xB6    ="0xFF"
0xB7    ="0xFF"
0xB8    ="0xFF"
0xB9    ="0xFF"
0xBA    ="0xFF"
0xBB    ="0xFF"
0xBC    ="0xFF"
0xBD    ="0xFF"
0xBE    ="0xFF"
0xBF    ="0xFF"
0xC0    ="0xFF"
0xC1    ="0xFF"
0xC2    ="0xFF"
0xC3    ="0xFF"
0xC4    ="0xFF"
0xC5    ="0xFF"
0xC6    ="0xFF"
0xC7    ="0xFF"
0xC8    ="0xFF"
0xC9    ="0xFF"
0xCA    ="0xFF"
0xCB    ="0xFF"
0xCC    ="0xFF"
0xCD    ="0xFF"
0xCE    ="0xFF"
0xCF    ="0xFF"
0xD0    ="0xFF"
0xD1    ="0xFF"
0xD2    ="0xFF"
0xD3    ="0xFF"
0xD4    ="0xFF"
0xD5    ="0xFF"
0xD6    ="0xFF"
0xD7    ="0xFF"
0xD8    ="0xFF"
0xD9    ="0xFF"
0xDA    ="0xFF"
0xDB    ="0xFF"
0xDC    ="0xFF"
0xDD    ="0xFF"
0xDE    ="0xFF"
0xDF    ="0xFF"
0xE0    ="0xFF"
0xE1    ="0xFF"
0xE2    ="0xFF"
0xE3    ="0xFF"
0xE4    ="0xFF"
0xE5    ="0xFF"
0xE6    ="0xFF"
0xE7    ="0xFF"
0xE8    ="0xFF"
0xE9    ="0xFF"
0xEA    ="0xFF"
0xEB    ="0xFF"
0xEC    ="0xFF"
0xED    ="0xFF"
0xEE    ="0xFF"
0xEF    ="0xFF"
0xF0    ="0xFF"
0xF1    ="0xFF"
0xF2    ="0xFF"
0xF3    ="0xFF"
0xF4    ="0xFF"
0xF5    ="0xFF"
0xF6    ="0xFF"
0xF7    ="0xFF"
0xF8    ="0xFF"
0xF9    ="0xC0"
0xFA    ="0xFF"
0xFB    ="0xFF"
0xFC    ="0xFF"
0xFD    ="0xC0"
0xFE    ="0xFF"
0xFF    ="0xFF"

[A2_TAB2]
DIE_ID_3             = "0xFF"
DIE_ID_2             = "0xFF"
DIE_ID_1             = "0xFF"
DIE_ID_0             = "0xFF"
RESERVED_84          = "0xFF"
RESERVED_85          = "0xFF"
RESERVED_86          = "0xFF"
RESERVED_87          = "0xFF"
SAFE_MODE_STARTUP    = "0x6A"
I2C_CONTROL          = "0xA0"
I2C_CONTROL_2        = "0x18"
APC_CTRL             = "0x98"
EYEMAX_AMP_CTRL      = "0x2F"
EYEMAX_Q_CTRL        = "0xFF"
TX_CTRL              = "0x7"
TX_CTRL_0            = "0x1E"
TX_CTRL_1            = "0x66"
TX_TEMP_CTRL         = "0x5"
AUTO_ER_CTRL         = "0x33"
AUTO_ER_1            = "0x61"
AUTO_ER_2            = "0x45"
APCSET_DAC           = "0x90"
BIAS_DAC_MSB         = "0x53"
BIAS_DAC_LSB         = "0xC0"
MOD_DAC_MSB          = "0x60"
MOD_DAC_LSB          = "0xC0"
MD_MAX               = "0xFC"
BIAS_MAX             = "0xFE"
MOD_MAX              = "0xFF"
AUTO_ER_3            = "0x37"
TX_SD_CTRL           = "0x12"
TX_FAULT_CTRL        = "0x0"
ALM_WRN_CTRL_1       = "0x0"
ALM_WRN_CTRL_2       = "0x0"
ALM_WRN_CTRL_3       = "0x0"
ROGUE_ONU_CTRL       = "0x0"
ROGUE_ONU_TIME       = "0x0"
TX_MON_CTRL          = "0x0"
RESERVED_A6          = "0x0"
RX_CTRL_0            = "0x1A"
RX_CTRL_1            = "0x92"
RX_CTRL_2            = "0x0"
RX_LOS_DEBOUNCE_CTRL = "0x0"
RX_LOS_CTRL          = "0x89"
RX_LOS_LEVEL         = "0x19"
APD_CTRL_1           = "0xCA"
APD_CTRL_2           = "0x1D"
APD_CTRL_3           = "0x3F"
APD_GAIN             = "0x60"
APD_THRESHOLDS       = "0x54"
APD_RAMP_CTRL        = "0x12"
APD_DAC_MSB          = "0x5D"
APD_DAC_LSB          = "0x0"
APD_FAULT_CTRL       = "0x4"
SLEEP_CTRL           = "0x0"
ADC_CTRL             = "0xB"
RESERVED_B8          = "0x0"
RESERVED_B9          = "0x0"
RESERVED_BA          = "0x0"
RESERVED_BB          = "0x0"
RESERVED_BC          = "0x0"
RESERVED_BD          = "0x0"
RESERVED_BE          = "0x0"
RESERVED_BF          = "0x0"
SPARE_CTRL           = "0x0"
RESERVED_C1          = "0x0"
EEPROM_CTRL          = "0x1"
MEM_ACCESS_1         = "0x2"
MEM_ACCESS_2         = "0x54"
MEM_ACCESS_3         = "0xAD"
MEM_ACCESS_4         = "0xBA"
STATUS_0             = "0xA2"
PW1_3                = "0xFF"
PW1_2                = "0xFF"
PW1_0                = "0x6E"
PW1_0                = "0xFF"
PW2_3                = "0xFF"
PW2_2                = "0xFF"
PW2_1                = "0xFF"
PW2_0                = "0xFF"
RESERVED_D0          = "0x0"
RESERVED_D1          = "0x0"
TEMP_LINEAR_MSB      = "0x1"
TEMP_LINEAR_LSB      = "0x6A"
TEMP_OFFSET_MSB      = "0xBF"
TEMP_OFFSET_LSB      = "0x5A"
VCC_LINEAR_MSB       = "0x0"
VCC_LINEAR_LSB       = "0x9C"
VCC_OFFSET_MSB       = "0x0"
VCC_OFFSET_LSB       = "0x0"
TXBIAS_LINEAR_MSB    = "0x1"
TXBIAS_LINEAR_LSB    = "0x90"
TXBIAS_OFFSET_MSB    = "0x0"
TXBIAS_OFFSET_LSB    = "0x0"
TXPWR_LINEAR_MSB     = "0x1C"
TXPWR_LINEAR_LSB     = "0x20"
TXPWR_OFFSET_MSB     = "0xCC"
TXPWR_OFFSET_LSB     = "0x3"
RXPWR_QUADRATIC_MSB   = "0x8"
RXPWR_QUADRATIC_LSB   = "0x0"
RXPWR_LINEAR_MSB     = "0x0"
RXPWR_LINEAR_LSB     = "0xE4"
RXPWR_OFFSET_MSB     = "0xF1"
RXPWR_OFFSET_LSB     = "0xDA"
RAW_ADC_TEMP_MSB     = "0x6E"
RAW_ADC_TEMP_LSB     = "0x0"
RAW_ADC_IN_MSB       = "0x0"
RAW_ADC_IN_LSB       = "0x80"
RAW_ADC_TXBIAS_MSB   = "0x24"
RAW_ADC_TXBIAS_LSB   = "0x40"
RAW_ADC_TXPWR_MSB    = "0x2"
RAW_ADC_TXPWR_LSB    = "0x38"
RAW_ADC_RXPWR_MSB    = "0x0"
RAW_ADC_RXPWR_LSB    = "0xD7"
RAW_ADC_TXMOD_MSB    = "0x1D"
RAW_ADC_TXMOD_LSB    = "0x70"
RAW_ADC_VCCTX_MSB    = "0xC7"
RAW_ADC_VCCTX_LSB    = "0x0"
RAW_ADC_VCCRX_MSB    = "0xC3"
RAW_ADC_VCCRX_LSB    = "0x0"
RESERVED_F8          = "0x12"
RESERVED_F9          = "0x0"
LOT_INFO_3           = "0x0"
LOT_INFO_2           = "0x0"
RESERVED_FC          = "0x0"
RESERVED_FD          = "0x0"
LOT_INFO_1           = "0x0"
LOT_INFO_0           = "0x0"

[BiasLUT]
0       = "0x27"
1       = "0x0"
2       = "0x27"
3       = "0x0"
4       = "0x27"
5       = "0x40"
6       = "0x27"
7       = "0x80"
8       = "0x27"
9       = "0xC0"
10      = "0x27"
11      = "0xC0"
12      = "0x28"
13      = "0x0"
14      = "0x28"
15      = "0x40"
16      = "0x28"
17      = "0x80"
18      = "0x28"
19      = "0x80"
20      = "0x28"
21      = "0xC0"
22      = "0x29"
23      = "0x0"
24      = "0x29"
25      = "0x40"
26      = "0x29"
27      = "0x80"
28      = "0x29"
29      = "0x80"
30      = "0x29"
31      = "0xC0"
32      = "0x2A"
33      = "0x0"
34      = "0x2A"
35      = "0x40"
36      = "0x2A"
37      = "0x40"
38      = "0x2A"
39      = "0x80"
40      = "0x2A"
41      = "0xC0"
42      = "0x2B"
43      = "0x0"
44      = "0x2B"
45      = "0x0"
46      = "0x2B"
47      = "0x40"
48      = "0x2B"
49      = "0x80"
50      = "0x2B"
51      = "0xC0"
52      = "0x2C"
53      = "0x0"
54      = "0x2C"
55      = "0xC0"
56      = "0x2D"
57      = "0x80"
58      = "0x2E"
59      = "0x80"
60      = "0x2F"
61      = "0x40"
62      = "0x30"
63      = "0x0"
64      = "0x31"
65      = "0x0"
66      = "0x32"
67      = "0x40"
68      = "0x33"
69      = "0x80"
70      = "0x34"
71      = "0xC0"
72      = "0x36"
73      = "0x0 "
74      = "0x37"
75      = "0x40"
76      = "0x38"
77      = "0xC0"
78      = "0x3B"
79      = "0xC0"
80      = "0x3F"
81      = "0x0"
82      = "0x42"
83      = "0x40"
84      = "0x45"
85      = "0x80"
86      = "0x48"
87      = "0xC0"
88      = "0x4C"
89      = "0x0"
90      = "0x50"
91      = "0x40"
92      = "0x54"
93      = "0x80"
94      = "0x58"
95      = "0xC0"
96      = "0x5D"
97      = "0x0"
98      = "0x61"
99      = "0x40"
100     = "0x65"
101     = "0xC0"
102     = "0x65"
103     = "0xC0"
104     = "0x65"
105     = "0xC0"
106     = "0x65"
107     = "0xC0"
108     = "0x65"
109     = "0xC0"
110     = "0x65"
111     = "0xC0"
112     = "0x65"
113     = "0xC0"
114     = "0x65"
115     = "0xC0"
116     = "0x65"
117     = "0xC0"
118     = "0x65"
119     = "0xC0"
120     = "0x65"
121     = "0xC0"
122     = "0x65"
123     = "0xC0"
124     = "0x65"
125     = "0xC0"
126     = "0x65"
127     = "0xC0"

[MODLUT]
0       = "0x2D"
1       = "0x80"
2       = "0x2D"
3       = "0x80"
4       = "0x2D"
5       = "0x80"
6       = "0x2D"
7       = "0x80"
8       = "0x2D"
9       = "0x80"
10      = "0x2D"
11      = "0x80"
12      = "0x2D"
13      = "0x80"
14      = "0x2D"
15      = "0x80"
16      = "0x2D"
17      = "0x80"
18      = "0x2D"
19      = "0x80"
20      = "0x2D"
21      = "0x80"
22      = "0x2D"
23      = "0x80"
24      = "0x2D"
25      = "0x80"
26      = "0x2D"
27      = "0x80"
28      = "0x2D"
29      = "0x80"
30      = "0x2D"
31      = "0x80"
32      = "0x2D"
33      = "0x80"
34      = "0x2D"
35      = "0x80"
36      = "0x2D"
37      = "0x80"
38      = "0x2D"
39      = "0x80"
40      = "0x2D"
41      = "0x80"
42      = "0x2D"
43      = "0x80"
44      = "0x2D"
45      = "0x80"
46      = "0x2D"
47      = "0x80"
48      = "0x2D"
49      = "0x80"
50      = "0x2D"
51      = "0x80"
52      = "0x2D"
53      = "0x80"
54      = "0x2D"
55      = "0x80"
56      = "0x2D"
57      = "0xC0"
58      = "0x2E"
59      = "0x0"
60      = "0x2E"
61      = "0x0"
62      = "0x2E"
63      = "0x40"
64      = "0x2E"
65      = "0x80"
66      = "0x2E"
67      = "0xC0"
68      = "0x2F"
69      = "0x40"
70      = "0x2F"
71      = "0x80"
72      = "0x30"
73      = "0x0"
74      = "0x30"
75      = "0x40"
76      = "0x30"
77      = "0xC0"
78      = "0x31"
79      = "0x0"
80      = "0x31"
81      = "0x80"
82      = "0x31"
83      = "0xC0"
84      = "0x32"
85      = "0x40"
86      = "0x32"
87      = "0x80"
88      = "0x33"
89      = "0x0"
90      = "0x33"
91      = "0xC0"
92      = "0x34"
93      = "0x80"
94      = "0x35"
95      = "0x40"
96      = "0x36"
97      = "0x0 "
98      = "0x36"
99      = "0xC0"
100     = "0x37"
101     = "0xC0"
102     = "0x37"
103     = "0xC0"
104     = "0x37"
105     = "0xC0"
106     = "0x37"
107     = "0xC0"
108     = "0x37"
109     = "0xC0"
110     = "0x37"
111     = "0xC0"
112     = "0x37"
113     = "0xC0"
114     = "0x37"
115     = "0xC0"
116     = "0x37"
117     = "0xC0"
118     = "0x37"
119     = "0xC0"
120     = "0x37"
121     = "0xC0"
122     = "0x37"
123     = "0xC0"
124     = "0x37"
125     = "0xC0"
126     = "0x37"
127     = "0xC0"

[APDLUT]
0 = "0"
1 = "0"
2 = "0"
3 = "0"
4 = "0"
5 = "0"
6 = "0"
7 = "0"
8 = "0"
9 = "0"
10 = "0" 
11 = "0"
12 = "0"
13 = "0"
14 = "0"
15 = "0"
16 = "0"
17 = "0"
18 = "0"
19 = "0"
20 = "0"
21 = "0"
22 = "0"
23 = "0"
24 = "0"
25 = "0"
26 = "0"
27 = "0"
28 = "0"
29 = "0"
30 = "0"
31 = "0"
32 = "0"
33 = "0"
34 = "0"
35 = "0"
36 = "0"
37 = "0"
38 = "0"
39 = "0"
40 = "0"
41 = "0"
42 = "0"
43 = "0"
44 = "0"
45 = "0"
46 = "0"
47 = "0"
48 = "0"
49 = "0"
50 = "0"
51 = "0"
52 = "0"
53 = "0"
54 = "0"
55 = "0"
56 = "0"
57 = "0"
58 = "0"
59 = "0"
60 = "0"
61 = "0" 
62 = "0"
63 = "0"
0  =  "0x55"
1  =  "0x0"
2  =  "0x56"
3  =  "0x0"
4  =  "0x56"
5  =  "0x80"
6  =  "0x57"
7  =  "0x0"
8  =  "0x57"
9  =  "0x80"
10 =  "0x58"
11 =  "0x0"
12 =  "0x58"
13 =  "0x80"
14 =  "0x59"
15 =  "0x80"
16 =  "0x5A"
17 =  "0x0"
18 =  "0x5A"
19 =  "0x80"
20 =  "0x5B"
21 =  "0x0"
22 =  "0x5B"
23 =  "0x80"
24 =  "0x5C"
25 =  "0x0"
26 =  "0x5D"
27 =  "0x0"
28 =  "0x5D"
29 =  "0x80"
30 =  "0x5E"
31 =  "0x0"
32 =  "0x5E"
33 =  "0x80"
34 =  "0x5F"
35 =  "0x0"
36 =  "0x5F"
37 =  "0x80"
38 =  "0x60"
39 =  "0x0"
40 =  "0x61"
41 =  "0x0"
42 =  "0x61"
43 =  "0x80"
44 =  "0x62"
45 =  "0x0"
46 =  "0x62"
47 =  "0x80"
48 =  "0x63"
49 =  "0x0"
50 =  "0x63"
51 =  "0x80"
52 =  "0x64"
53 =  "0x80"
54 =  "0x65"
55 =  "0x0"
56 =  "0x65"
57 =  "0x80"
58 =  "0x66"
59 =  "0x0"
60 =  "0x66"
61 =  "0x80"
62 =  "0x67"
63 =  "0x0"