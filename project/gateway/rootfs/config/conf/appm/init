#! /bin/sh
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/lib/xtables
export XTABLES_LIBDIR=$XTABLES_LIBDIR:/usr/lib/xtables

echo "appm init begin"

mkdir -p /tmp/run
mkdir -p /log/files
mkdir -p /var/lib/ebtables

touch /tmp/run/xtables.lock
touch /var/lib/ebtables/lock

# ismod global kernel module
insmod /lib/hisilicon/ko/hi_kipc.ko
let "timeout=100"
until [ -c /dev/hi_ipc ]
do
	if [ $timeout -le 0 ]; then
		echo "ipc module init fail!!!!!!!!!!!!!!"
		break
	fi
	timeout=`expr $timeout - 1`
	usleep 100000
done
chown system:system /dev/hi_ipc
insmod /lib/hisilicon/ko/hi_kmsgcenter.ko

# init appm &  ipc
mkdir /var/.appm
mkdir -m 770 /var/.ipc

# init ucm
factory_path=/usr/local/factory
target=/var/.ucm/xml

mkdir -p $target
cp -rf $factory_path/sysinfo.xml $target/
cp -rf $factory_path/board.xml $target/

iptables-restore /config/conf/appm/ipt_init
ip6tables-restore /config/conf/appm/ip6t_init

insmod /lib/hisilicon/ko/hi_kport_mirror.ko
