#! /bin/sh

mode=$(hi_cfm get sysinfo.mode)
[ "$mode" == "Router" ] && {
	echo "Router mode"
	insmod /lib/modules/`uname -r`/ip_set.ko
	insmod /lib/modules/`uname -r`/ip_set_hash_mac.ko
	insmod /lib/modules/`uname -r`/xt_set.ko
}
[ "$mode" == "HGU" ] && {
	echo "HGU mode"
	# hi_xt_fwd.ko custom_tgt depends on nf_reject_ipv6.ko
	insmod /lib/modules/`uname -r`/nf_reject_ipv6.ko
	insmod /lib/modules/`uname -r`/ip6t_REJECT.ko
}

# hi_xt_fwd.ko depends on ip_set.ko, the ipset function of gw is compiled into the kernel
insmod /lib/hisilicon/ko/hi_netif.ko
# hi_xt_fwd.ko custom_tgt depends on hi_netif.ko
insmod /lib/hisilicon/ko/hi_xt_fwd.ko
insmod /lib/hisilicon/ko/hi_kmc.ko
let "timeout=100"
until [ -c /dev/hi_domain ]
do
	if [ $timeout -le 0 ]; then
		echo "xt_fwd module init fail!!!"
		break
	fi
	timeout=`expr $timeout - 1`
	usleep 100000
done
chown system:system /dev/hi_domain
