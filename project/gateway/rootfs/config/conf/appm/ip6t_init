# Generated by ip6tables-save v1.8.2 on Thu Jan  1 08:01:57 1970
*nat
:PREROUTING ACCEPT [0:0]
:INPUT ACCEPT [0:0]
:OUTPUT ACCEPT [0:0]
:POSTROUTING ACCEPT [0:0]
COMMIT
# Completed on Thu Jan  1 08:01:57 1970
# Generated by ip6tables-save v1.8.2 on Thu Jan  1 08:01:57 1970
*mangle
:PREROUTING ACCEPT [0:0]
:INPUT ACCEPT [0:0]
:FORWARD ACCEPT [0:0]
:OUTPUT ACCEPT [0:0]
:POSTROUTING ACCEPT [0:0]
:HI_IPFILTER_UP_CHAIN - [0:0]
:HI_IPFILTER_DN_CHAIN - [0:0]
:HI_QOS_CHAIN_0 - [0:0]
:HI_QOS_CHAIN_1 - [0:0]
:HI_QOS_CHAIN_2 - [0:0]
:HI_QOS_CHAIN_3 - [0:0]
:HI_QOS_CHAIN_4 - [0:0]
:HI_QOS_CHAIN_5 - [0:0]
:HI_QOS_CHAIN_6 - [0:0]
:HI_QOS_CHAIN_7 - [0:0]
:HI_DL_QOS_CHAIN_0 - [0:0]
:HI_DL_QOS_CHAIN_1 - [0:0]
:HI_DL_QOS_CHAIN_2 - [0:0]
:HI_DL_QOS_CHAIN_3 - [0:0]
:HI_DL_QOS_CHAIN_4 - [0:0]
:HI_DL_QOS_CHAIN_5 - [0:0]
:HI_DL_QOS_CHAIN_6 - [0:0]
:HI_DL_QOS_CHAIN_7 - [0:0]
:HI_SSID_INTERNET - [0:0]
-A FORWARD -i br0 -j "HI_IPFILTER_UP_CHAIN"
-A FORWARD -i wan+ -j "HI_IPFILTER_DN_CHAIN"
-A FORWARD -i ppp+ -j "HI_IPFILTER_DN_CHAIN"
-A FORWARD -i br0 -j "HI_QOS_CHAIN_0"
-A FORWARD -i br0 -j "HI_QOS_CHAIN_1"
-A FORWARD -i br0 -j "HI_QOS_CHAIN_2"
-A FORWARD -i br0 -j "HI_QOS_CHAIN_3"
-A FORWARD -i br0 -j "HI_QOS_CHAIN_4"
-A FORWARD -i br0 -j "HI_QOS_CHAIN_5"
-A FORWARD -i br0 -j "HI_QOS_CHAIN_6"
-A FORWARD -i br0 -j "HI_QOS_CHAIN_7"
-A FORWARD -o br0 -j "HI_DL_QOS_CHAIN_0"
-A FORWARD -o br0 -j "HI_DL_QOS_CHAIN_1"
-A FORWARD -o br0 -j "HI_DL_QOS_CHAIN_2"
-A FORWARD -o br0 -j "HI_DL_QOS_CHAIN_3"
-A FORWARD -o br0 -j "HI_DL_QOS_CHAIN_4"
-A FORWARD -o br0 -j "HI_DL_QOS_CHAIN_5"
-A FORWARD -o br0 -j "HI_DL_QOS_CHAIN_6"
-A FORWARD -o br0 -j "HI_DL_QOS_CHAIN_7"
COMMIT
# Completed on Thu Jan  1 08:01:57 1970
# Generated by ip6tables-save v1.8.2 on Thu Jan  1 08:01:57 1970
*filter
:INPUT DROP [0:0]
:FORWARD DROP [0:0]
:OUTPUT ACCEPT [0:0]
:badpacket_level - [0:0]
:firewall_blacklist - [0:0]
:firewall_blacklist_forword - [0:0]
:firewall_safedos - [0:0]
:firewall_safedos_forword - [0:0]
:firewall_whitelist - [0:0]
:firewall_whitelist_forword - [0:0]
:firewall_whitelist_lan - [0:0]
:in_nes_filter - [0:0]
:incoming_filter - [0:0]
:input_blacklist - [0:0]
:input_firewall_white - [0:0]
:input_wan - [0:0]
:WAN_ICMP_WHITELIST - [0:0]
:log - [0:0]
:service_active - [0:0]
:service_active_forword - [0:0]
:service_blacklist - [0:0]
:service_blacklist_forword - [0:0]
:service_white_global - [0:0]
:service_white_spec - [0:0]
:service_whitelist - [0:0]
:service_whitelist_forword - [0:0]
:wan_accept_templet - [0:0]
:safedos_ddostcp - [0:0]
-A INPUT -j in_nes_filter
-A INPUT -j incoming_filter
-A INPUT -j firewall_blacklist
-A INPUT -j firewall_safedos
-A INPUT -j service_blacklist
-A INPUT -j service_active
-A INPUT -j service_whitelist
-A INPUT -j firewall_whitelist
-A FORWARD -j firewall_blacklist_forword
-A FORWARD -j firewall_safedos_forword
-A FORWARD -j service_blacklist_forword
-A FORWARD -j service_active_forword
-A FORWARD -j service_whitelist_forword
-A FORWARD -j firewall_whitelist_forword
-A OUTPUT -j incoming_filter
-A badpacket_level -m state --state INVALID -j DROP
-A badpacket_level -p tcp -m tcp --tcp-flags SYN,ACK SYN,ACK -m state --state NEW -j DROP
-A badpacket_level ! -i br0 -p tcp -m tcp ! --tcp-flags FIN,SYN,RST,ACK SYN -m state --state NEW -j DROP
-A badpacket_level -j RETURN
-A firewall_blacklist -j log
-A firewall_blacklist -j input_blacklist
-A firewall_whitelist -j firewall_whitelist_lan
-A firewall_whitelist -j input_firewall_white
-A service_active_forword -p tcp -m tcp --tcp-flags SYN,RST SYN -j TCPMSS --clamp-mss-to-pmtu
-A firewall_whitelist_forword -i lxcbr0 -j ACCEPT
-A firewall_whitelist_forword -i br0 -j ACCEPT
-A firewall_whitelist_forword ! -i br0,lo -m state --state RELATED,ESTABLISHED -j ACCEPT
-A in_nes_filter -p tcp -m tcp --dport 53 -j DROP
-A in_nes_filter -i br0 -p udp -m udp --sport 53 -j DROP
-A in_nes_filter -p udp -m udp --sport 53 -m state --state NEW -j DROP
-A in_nes_filter -p tcp -m multiport --dports 8080,5431,2468,49150:49155 -m state --state ESTABLISHED -m string --string "User-Agent: Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0)" --algo bm --to 65535 -m string --string "GET / HTTP/1.1" --algo bm --to 65535 -m string ! --string "%" --algo bm --to 65535 -m string ! --string "Referer:" --algo bm --to 65535 -j ACCEPT
-A in_nes_filter -p tcp -m multiport --dports 8080,5431,2468,49150:49155 -m state --state ESTABLISHED -m string --string "User-Agent: Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1; Trident/4.0)" --algo bm --to 65535 -m string --string "Pragma: no-cache" --algo bm --to 65535 -j DROP
-A input_blacklist ! -i br0 -p udp -m udp --dport 54321 -j DROP
-A input_blacklist ! -i br0 -p tcp -m tcp --dport 53 -j DROP
-A input_blacklist -i wan+ -p tcp -m tcp --dport 8080 -j DROP
-A input_blacklist -i ppp+ -p tcp -m tcp --dport 8080 -j DROP
-A input_blacklist -i wan+ -p tcp -m tcp --dport 80 -j DROP
-A input_blacklist -i ppp+ -p tcp -m tcp --dport 80 -j DROP
-A input_firewall_white -i lo -j ACCEPT
-A input_firewall_white -d fe80::/64 -j ACCEPT
-A input_firewall_white -d ff00::/8 -j ACCEPT
-A input_firewall_white ! -i br0 -j input_wan
-A input_firewall_white -i wan+ -p ipv6-icmp -j ACCEPT
-A input_firewall_white -i ppp+ -p ipv6-icmp -j ACCEPT
-A input_firewall_white -i vxlan+ -p ipv6-icmp -j ACCEPT
-A input_firewall_white -i br0 -p udp -m udp --sport 68 --dport 67 -j ACCEPT
-A input_firewall_white -p igmp -m state --state NEW -j ACCEPT
-A input_wan -p ipv6-icmp -j WAN_ICMP_WHITELIST
-A input_wan -m state --state RELATED,ESTABLISHED -j ACCEPT
-A input_wan -p udp -m udp --dport 546:547 -j ACCEPT
-A log -i wan+ -p tcp -m tcp --dport 21 --tcp-flags SYN,RST,ACK SYN -j LOG --log-prefix "Recv a FTP Req "
-A log -i ppp+ -p tcp -m tcp --dport 21 --tcp-flags SYN,RST,ACK SYN -j LOG --log-prefix "Recv a FTP Req "
-A log -i br0 -p tcp -m tcp --dport 21 --tcp-flags SYN,RST,ACK SYN -j LOG --log-prefix "Recv a FTP Req "
-A service_whitelist -j service_white_spec
-A service_whitelist -j service_white_global
-A wan_accept_templet -i wan+ -j ACCEPT
-A wan_accept_templet -i ppp+ -j ACCEPT
-A wan_accept_templet -i br0 -j DROP
-A safedos_ddostcp -p tcp -m state --state RELATED,ESTABLISHED -j RETURN
-A safedos_ddostcp -p tcp --syn -m recent --name PortScan --rcheck --seconds 3600 --hitcount 50 -j DROP
-A safedos_ddostcp -p tcp --syn -m recent --name PortScan --set
COMMIT
# Completed on Thu Jan  1 08:01:57 1970
