[settings]
functionname=hi_ctcoam_set_additional_stat;
attributenum=2;
attributeflag = 1 ;   //0:close 1:open

[parameter]
attribute=alias<ui_port>  type<uint>  range<0, 5>              default<0>;
attribute=alias<ui_data>  type<uint>  range<0, 0xffffffff>     default<0>;

[help]
typedef enum {
    HI_LSW_PORT_UNI_ETH0_E   = 0,
    HI_LSW_PORT_UNI_ETH1_E   = 1,
    HI_LSW_PORT_UNI_ETH2_E   = 2,
    HI_LSW_PORT_UNI_ETH3_E   = 3,
    HI_LSW_PORT_UNI_ETH4_E   = 4,
    HI_LSW_PORT_NNI_PON_E    = 5,
} hi_lsw_port_e;
