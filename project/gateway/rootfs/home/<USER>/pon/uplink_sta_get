[settings]
functionname=hi_sal_pon_uplink_status_get;  
attributeflag = 1 ;   //0:close 1:open

[parameter]                                                                     
attribute=alias<up_protocol>  	type<uint>    	range<0x1,0x2>     		  default<1>;
attribute=alias<pon_link>  		type<uint>    	range<0x0,0x8>			  default<0>;
attribute=alias<link_time> 		type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<fec_cap> 		type<uint>    	range<0x0,0x1>	          default<0>;
attribute=alias<fec_sta> 		type<uint>    	range<0x0,0x1>            default<0>;
attribute=alias<tx_power> 		type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<rx_power> 		type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<temprature> 	type<uint>      range<0x0,0xffffffff>     default<0>;
attribute=alias<voltage>  	    type<uint>      range<0x0,0xffffffff>     default<0>;
attribute=alias<ibias>  		type<uint>      range<0x0,0xffffffff>     default<0>;

[help]
usage: 

