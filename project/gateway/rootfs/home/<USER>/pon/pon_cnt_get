[settings]
functionname=hi_sal_pon_stats_get;  
attributeflag = 1 ;   //0:close 1:open

[parameter]                                                                     
attribute=alias<bytes_sent>  		        type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<bytes_received>  		    type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<packets_sent>  		        type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<packets_received>  		    type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<sunicast_packets>  		    type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<runicast_packets>  		    type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<smulticast_packets>  		type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<rmulticast_packets>  		type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<sbroadcast_packets>  		type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<rbroadcast_packets>  		type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<fec_error>  		        type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<fcs_error>  		        type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<hec_error>  		        type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<drop_packets>  		        type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<spause_packets>  		    type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;
attribute=alias<rpause_packets>  		    type<uint>    	range<0x0,0xFFFFFFFF>     default<0>;

[help]
usage: 

