[settings]
functionname=hi_auto_sensing_adp_set_attr;  
attributeprev = hi_auto_sensing_adp_get_attr;  
attributenum=5;
attributeflag = 1 ;   //0:close 1:open

[parameter]
attribute=alias<en>              type<uint>   range<0x0,0x1>           default<0x0>;
attribute=alias<chk_time>        type<uint>   range<0x0,0xffffffff>    default<0x5>;
attribute=alias<wait_time>       type<uint>   range<0x0,0xffffffff>    default<0x5>;
attribute=alias<los_cnt>         type<uint>   range<0x0,0xffffffff>    default<0x5>;
attribute=alias<reg_check_time>  type<uint>   range<0x0,0xffffffff>    default<0x1e>;
[help]

