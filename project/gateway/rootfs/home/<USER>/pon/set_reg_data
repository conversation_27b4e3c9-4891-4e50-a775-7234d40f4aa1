[settings]
functionname=hi_bob_set_reg;
attributenum=3;
attributeflag=1;

[parameter]
attribute=alias<table>      type<uint>      range<0x0,0xff>      default<0x0>;
attribute=alias<reg>        type<uint>      range<0x0,0xff>      default<0x80>;
attribute=alias<val>        type<uint>      range<0x0,0xff>      default<0x0>;

[help]
HI_BOB_TAB_A2_LOW_E  = 0x0
HI_BOB_TAB_A2_HIGH_E = 0x1
HI_BOB_TAB_CTRL_E    = 0x2
HI_BOB_TAB_BIAS_E    = 0x3
HI_BOB_TAB_MOD_E     = 0x4
HI_BOB_TAB_APD_E     = 0x5