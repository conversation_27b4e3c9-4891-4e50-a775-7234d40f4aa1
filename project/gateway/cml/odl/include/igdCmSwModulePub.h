#ifndef IGD_CM_SOFTWAREMOD_H
#define IGD_CM_SOFTWAREMOD_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_osgi_env.h"

/***********************JSON服务器地址环境表*********************************/
word32 igdCmSwmGlbAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmGlbAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmGlbAttrInit(void);


/***********************插件运行环境配置表*********************************/
word32 igdCmSwmExecEnvAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSwmExecEnvAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmSwmExecEnvAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmExecEnvAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmExecEnvAttrEntryNumGet(uword32 *entrynum);
word32 igdCmSwmExecEnvAttrAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmExecEnvAttrInit(void);

/***********************发布单元表*********************************/
word32 igdCmSwmDeploymentUnitInfoGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDeploymentUnitInfoSet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDeploymentUnitEntryNumGet(uword32 *entrynum);
word32 igdCmSwmDeploymentUnitAllIndexGet(uword8 *pucInfo, uword32 len);

/***********************运行单元信息表*********************************/
word32 igdCmSwmExecutionUnitInfoGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmExecutionUnitInfoSet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmExecutionUnitEntryNumGet(uword32 *entrynum);
word32 igdCmSwmExecutionUnitAllIndexGet(uword8 *pucInfo, uword32 len);

/***********************插件权限表*********************************/
word32 igdCmSwmDuPermissionAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDuPermissionAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDuPermissionAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDuPermissionAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDuPermissionAttrEntryNumGet(uword32 *entrynum);
word32 igdCmSwmDuPermissionAttrAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDuPermissionAttrInit(void);

/***********************插件权限能力集表*********************************/
word32 igdCmSwmApiCapabilitesAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSwmApiCapabilitesAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmSwmApiCapabilitesAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmApiCapabilitesAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmApiCapabilitesAttrEntryNumGet(uword32 *entrynum);
word32 igdCmSwmApiCapabilitesAttrAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmApiCapabilitesAttrInit(void);

word32 igdCmSwmDuPermissionApiAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDuPermissionApiDel(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDuPermissionApiSet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDuPermissionApiGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDuPermissionApiEntryNumGet(uword32 *entrynum);
word32 igdCmSwmDuPermissionApiAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmSwmDuPermissionApiInit(void);

uword32 igdCmSwmModuleInit(void);
void  igdCmSwmModuleExit(void);

#ifdef CONFIG_WITH_CMCC_OSGI
void cm_swm_oper_init(void);
#else
static inline void cm_swm_oper_init(void) { }
#endif

#endif/*IGD_CM_SOFTWAREMOD_H*/
