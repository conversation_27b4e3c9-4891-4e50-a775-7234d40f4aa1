/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2024-04-24
 */
#ifndef HI_ODL_WAN_ACCESS_H
#define HI_ODL_WAN_ACCESS_H
#include "hi_basic.h"
#include "hi_odl_tab_method.h"

#if defined(CONFIG_WITH_WAN_PING_WHITELIST)
void hi_odl_init_wan_access(void);

int32_t hi_odl_wan_ping_resp_apply(IgdWanConnectionAttrConfTab *old,
				IgdWanConnectionAttrConfTab *new,
				enum hi_odl_apply_act act);
#else
static inline void hi_odl_init_wan_access(void)
{

}

static inline int32_t hi_odl_wan_ping_resp_apply(IgdWanConnectionAttrConfTab *old,
				IgdWanConnectionAttrConfTab *new,
				enum hi_odl_apply_act act)
{
    return 0;
}
#endif
int32_t hi_odl_wan_ipforward_apply(IgdWanConnectionAttrConfTab *old,
				IgdWanConnectionAttrConfTab *new,
				enum hi_odl_apply_act act);

int32_t hi_odl_wan_updata_option(IgdWanConnectionAttrConfTab *wan_conn);

void igd_wan_ppp_err_proc(IgdWanConnectionStateInfoTab *wanconn_state_info, hi_wan_conn_info_s *wan_info);

#endif /* CONFIG_WITH_WAN_PING_WHITELIST */
