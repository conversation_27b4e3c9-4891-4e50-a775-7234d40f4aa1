/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef IGD_CM_HARD_ACC_CANCEL_H
#define IGD_CM_HARD_ACC_CANCEL_H

extern word32 igdCmHardAccCancelAttrAdd(uword8 *info, uword32 len);
extern word32 igdCmHardAccCancelAttrDel(uword8 *info, uword32 len);
extern word32 igdCmHardAccCancelAttrSet(uword8 *info, uword32 len);
extern word32 igdCmHardAccCancelAttrGet(uword8 *info, uword32 len);
extern word32 igdCmHardAccCancelAttrEntryNumGet(uword32 *entrynum);
extern word32 igdCmHardAccCancelAttrAllIndex(uword8 *info, uword32 len);
extern word32 igdCmHardAccCancelInit(void);

void cm_hard_acc_cancel_oper_init(void);

#endif
