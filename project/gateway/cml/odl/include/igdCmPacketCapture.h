/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: Remote packet capture
 * Author: hsan
 * Create: 2023-6-13
 * History:
 */

#ifndef IGD_CM_PACKETCAPTURE_H
#define IGD_CM_PACKETCAPTURE_H
#include <igdGlobalTypeDef.h>
#include "igdCmFeatureDef.h"
#include "hi_odl_tab_packet_capture.h"

extern word32 igdCmPacketCaptureSet(uword8 *pucInfo, uword32 len);
extern word32 igdCmPacketCaptureGet(uword8 *pucInfo, uword32 len);
extern word32 igdCmPacketCaptureInit(void);

void cm_packet_capture_oper_init(void);

#endif
