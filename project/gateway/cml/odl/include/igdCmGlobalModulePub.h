#ifndef IGD_CM_GLOBAL_MODULE_PUB_H
#define IGD_CM_GLOBAL_MODULE_PUB_H
#include "igdCmApi.h"
#include <igdGlobalTypeDef.h>
#include "hi_basic.h"

/***************************设备能力集信息表*********************************/
/*表相关宏*/

/*业务逻辑处理函数声明*/
word32 igdCmGlobalDevCapbilityGet(uword8 *pucInfo, uword32 len);

/*操作库函数声明*/


word32 igdCmGlobalDevInfoSet(uword8 *pucInfo, uword32 len);
word32 igdCmGlobalDevInfoGet(uword8 *pucInfo, uword32 len);
word32 igdCmGlobalProductMode(void);
word32 igdCmGlobalUplinkType(void);
word32 igdCmGlobalIsRouterBridge(void);
word32 igdCmGlobalIsGatewaySlaveBridge(void);
static inline word32 igdCmGlobalIsRouter(void)
{
	return igdCmGlobalProductMode() == DEVICE_TYPE_INTELLIGENT_ROUTER;
}
static inline word32 igdCmGlobalIsGatewaySlave(void)
{
	return igdCmGlobalProductMode() == DEVICE_TYPE_INTELLIGENT_GATEWAY_SLAVE;
}

/*业务逻辑处理函数声明*/
word32 igdCmGlobalAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmGlobalAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmGlobalAttrInit(void);
word32 igdCmGlobalRouterModeGet(void);
/*操作库函数声明*/

/*业务逻辑处理函数声明*/
word32 igdCmGlobalStatusInfoGet(uword8 *pucInfo, uword32 len);
word32 init_regstatus(void);
void exit_regstatus(void);

/*业务逻辑处理函数声明*/
word32 igdCmSysCMDConfigSet(uword8 *pucInfo, uword32 len);

/*业务逻辑处理函数声明*/
word32 igdCmGlobalLoidItmsRegStatusGet(uword8 *pucInfo, uword32 len);

/*业务逻辑处理函数声明*/
word32 igdCmFactoryAttrGet(uword8 *pucInfo, uword32 len);
/***************************出厂信息表*********************************/

/*业务逻辑处理函数声明*/
word32 igdCmOmciAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmOmciAttrSet(uword8 *pucInfo, uword32 len);

void cm_global_oper_init(void);

/***************************全局属性表*********************************/
uword32 igdCmGlobalModuleInit(void);
void  igdCmGlobalModuleExit(void);

#endif/*IGD_CM_GLOBAL_MODULE_PUB_H*/
