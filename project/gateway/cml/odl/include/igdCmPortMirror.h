/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.
 * Description: Port Mirror
 * Author: hsan
 * Create: 2023-7
 * History:
 */

#ifndef IGD_CM_PORTMIIROR_H
#define IGD_CM_PORTMIIROR_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_port_mirror.h"

extern word32 igdCmPortMirrorSet(uword8 *pucInfo, uword32 len);
extern word32 igdCmPortMirrorGet(uword8 *pucInfo, uword32 len);
extern word32 igdCmPortMirrorInit(void);

void cm_port_mirror_oper_init(void);

#endif
