/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef IGD_CM_APP_FILTER_H
#define IGD_CM_APP_FILTER_H

#include <igdGlobalTypeDef.h>

extern word32 igdCmAppFilterAttrAdd(uword8 *info, uword32 len);
extern word32 igdCmAppFilterAttrDel(uword8 *info, uword32 len);
extern word32 igdCmAppFilterAttrSet(uword8 *info, uword32 len);
extern word32 igdCmAppFilterAttrGet(uword8 *info, uword32 len);
extern word32 igdCmAppFilterAttrEntryNumGet(uword32 *entrynum);
extern word32 igdCmAppFilterAttrAllIndex(uword8 *info, uword32 len);
extern word32 igdCmAppFilterInit(void);
extern word32 igdCmAppFilterExit(void);

void cm_app_filter_oper_init(void);

#endif
