/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef IGD_ODL_TAB_COMMON_H
#define IGD_ODL_TAB_COMMON_H

#include <stdio.h>
#include <string.h>
#include <stdint.h>

int32_t igdCmTabCommonAttrAdd(int32_t tabid, int32_t max_entry, void *conf_entry, void *tmp_entry);
int32_t igdCmTabCommonAttrDel(int32_t tabid, uint32_t entry_index, void *tmp_entry);
int32_t igdCmTabCommonAttrAllIndex(
	int32_t tabid, uint32_t *entry_indexs, uint32_t sizeof_entry_indexs, void *tmp_entry);
int32_t igdCmTabCommonGetByEntryIndex(int32_t table_id, uint32_t entry_index, void *tmp_entry);

#define RETURN_IF_CHECK_LEN_FAILD(__data_len, __sizeof_struct, __struct_name)				 \
	do {																					  \
		if (__data_len != __sizeof_struct) {												  \
			CM_ERR("len %d not match struct len of " #__struct_name ". failed.", __data_len); \
			return IGD_CM_OPERATE_PARAM_INVALID;											  \
		}																					 \
	} while (0)

#define RET_IF_CHECK_FAILD(__expr, __errcode) \
	do {									  \
		if (!(__expr))						\
			return (__errcode);			   \
	} while (0)

#define RET_IF(__expr, __errcode) \
	do {						  \
		if ((__expr))			 \
			return (__errcode);   \
	} while (0)

#define RET_IF_NULL(__ptr, __errcode) \
	do {							  \
		if ((__ptr) == NULL)		  \
			return (__errcode);	   \
	} while (0)

#define CHECK_IF_NULL(__ptr) \
	do {					 \
		if ((__ptr) == NULL) \
			return;		  \
	} while (0)

#define CHECK_IF(__expr) \
	do {				 \
		if ((__expr))	\
			return;	  \
	} while (0)

#define CHECK_IF_FAILD(__expr) \
	do {					   \
		if (!(__expr))		 \
			return;			\
	} while (0)

#endif /* IGD_ODL_TAB_COMMON_H */
