/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igdCmEmuSpeed.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_CM_EMU_SPEED_H
#define IGD_CM_EMU_SPEED_H
#include "os/hi_os_time.h"
#include "hi_odl_tab_emu_speed.h"

/***************************下载诊断统计表*********************************/
extern word32 igdCmPMDownloadDiagAttrSet(uword8 *pucInfo, uword32 len);

extern word32 igdCmPMDownloadDiagAttrGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmPMDownloadDiagAttrStart(void);

extern word32 igdCmPMDownloadDiagAttrStop(void);

extern word32 igdCmPMDownloadDiagAttrInit(void);

extern word32 igdCmPMDownloadDiagStatisticsAttrGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmPMUploadDiagAttrSet(uword8 *pucInfo, uword32 len);

extern word32 igdCmPMUploadDiagAttrGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmPMUploadDiagAttrStart(void);

extern word32 igdCmPMUploadDiagAttrStop(void);

extern word32 igdCmPMUploadDiagAttrInit(void);

extern word32 igdCmPMUploadDiagStatisticsAttrGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmPMUploadDiagNotify(uword8 *pv_data);

/* anhui : Download diagnose after pppoe emulation */
extern word32 igdCmPMDownloadPPPNotify(uword8 *pv_data);

extern word32 igdCmPMDownloadPPPStart(void);

extern word32 igdCmPMDownloadPPPStop(void);

extern word32 igdCmPMDownloadPPPGet(uword8 *pucInfo, uword32 len);

/*******************************DNS报文限速********************************/
word32 igdCmDbusDnsSpeedLimitAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmDbusDnsSpeedLimitAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmDbusDnsSpeedLimitInit(void);

int32_t igd_cm_http_speed_test_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_http_speed_test_attr_get(uint8_t *info, uint32_t len);
int32_t igd_cm_http_speed_test_attr_start(void);
int32_t igd_cm_http_speed_test_attr_stop(void);

int32_t igd_cm_rms_speed_test_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_rms_speed_test_attr_get(uint8_t *info, uint32_t len);
int32_t igd_cm_rms_speed_test_attr_start(void);
int32_t igd_cm_rms_speed_test_attr_stop(void);

void cm_emu_speed_oper_init(void);

#endif /* SRC_ODL_INCLUDE_IGDCMEMUSPEED_H_ */
