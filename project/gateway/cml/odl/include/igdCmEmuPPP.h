/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igdCmEmuPPP.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_CM_EMU_PPP_H
#define IGD_CM_EMU_PPP_H

/*业务逻辑处理函数声明*/
extern word32 igdCmPMPPPOEEmulatorAttrSet(uword8 *pucInfo, uword32 len);

extern word32 igdCmPMPPPOEEmulatorAttrGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmPMPPPOEEmulatorAttrInit(void);

extern word32 igdCmPMPPPOEEmulatorAttrStart(void);

extern word32 igdCmPMPPPOEEmulatorAttrStop(void);

extern word32 igdCmEmuPPPAttrSet(uword8 *pucInfo, uword32 len);

extern word32 igdCmEmuPPPAttrGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmEmuPPPInit(void);

extern word32 igdCmEmuPPPStart(void);

extern word32 igdCmEmuPPPStop(void);

extern word32 igdCmEmuStop(void);

extern word32 igdEmuPPPProcNotify(uword8 *pv_data);

#endif /* SRC_ODL_INCLUDE_IGDCMEMUPPP_H_ */
