/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co.(Ltd. 2024-2024. All rights reserved.
 * Description: macbind odl uspace header
 * Author: HSAN
 * Create: 2024-6-11
 */

#ifndef HI_ODL_MACBIND_H
#define HI_ODL_MACBIND_H

#include "hi_uspace.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#ifdef CONFIG_WITH_MACBIND
int32_t hi_odl_macbind_judge_act(void);
int32_t hi_odl_macbind_init(void);
#else
static inline int32_t hi_odl_macbind_judge_act(void)
{
	return HI_RET_SUCC;
}
static inline int32_t hi_odl_macbind_init(void)
{
	return HI_RET_SUCC;
}
#endif

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* HI_ODL_MACBIND_H */

