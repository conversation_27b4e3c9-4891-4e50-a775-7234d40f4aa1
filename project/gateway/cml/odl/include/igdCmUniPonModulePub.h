#ifndef IGD_CM_DOWNLINK_PON_MODULE_PUB_H
#define IGD_CM_DOWNLINK_PON_MODULE_PUB_H
#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_uni_pon_info.h"

uint32_t igd_cm_pon_config_info_get(uint8_t *pucInfo, uint32_t len);
uint32_t igd_cm_pon_config_info_set(uint8_t *pucInfo, uint32_t len);
uint32_t igd_cm_pon_config_info_init(void);
uint32_t igd_cm_opt_module_parameter_info_get(uint8_t *pucInfo, uint32_t len);
uint32_t igd_cm_opt_module_parameter_info_set(uint8_t *pucInfo, uint32_t len);
uint32_t igd_cm_opt_module_parameter_info_init(void);
uint32_t igd_cm_connected_ont_info_add(uint8_t *pucInfo, uint32_t len);
uint32_t igd_cm_connected_ont_info_del(uint8_t *pucInfo, uint32_t len);
uint32_t igd_cm_connected_ont_info_get(uint8_t *pucInfo, uint32_t len);
uint32_t igd_cm_connected_ont_info_set(uint8_t *pucInfo, uint32_t len);
uint32_t igd_cm_connected_ont_info_num_get(uint32_t *entrynum);
uint32_t igd_cm_connected_ont_all_info_get(uint8_t *pucInfo, uint32_t len);
uint32_t igd_cm_connected_ont_info_init(void);
void idg_cm_uni_pon_info_init(void);
void cm_uni_pon_oper_init(void);

#endif