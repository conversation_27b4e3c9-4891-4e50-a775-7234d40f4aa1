/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: uplink info
 * Author: hsan
 * Create: 2023-03-12
 * History:
 */
#ifndef IGD_CM_UPLINK_H
#define IGD_CM_UPLINK_H
#include "igdGlobalTypeDef.h"
#include "hi_odl_tab_uplink.h"

/***************************上行数据统计表*********************************/
word32 igdCmUplinkRateConfigSet(uword8 *pucInfo, uword32 len);
word32 igdCmUplinkRateConfigGet(uword8 *pucInfo, uword32 len);
word32 igdCmUplinkStatsGet(uword8 *pucInfo, uword32 len);
word32 igdCmUplinkRatesGet(uword8 *pucInfo, uword32 len);
word32 igdCmUplinkMacGet(uword8 *mac, uword8 len);
word32 igdCmUplinkInit(void);

typedef struct {
	uword32 ulStateAndIndex;
	char interface[0x10];
	uword32 reportInterval;
	uword32 sampleInterval;
	uword32  ulBitmap;
} __PACK__ igd_uplink_rate_interval;

int32_t uplink_is_ready(void);
void uplink_detect_enable(int32_t enable);
void uplink_detect_step_refresh(void);
int32_t uplink_get_uplink_type(void);
int32_t uplink_get_eth_status(void);

int32_t igd_cm_uplink_cfg_set(uint8_t *info, uint32_t size);
int32_t igd_cm_uplink_cfg_get(uint8_t *info, uint32_t size);
int32_t igd_cm_uplink_cfg_init(void);

void igdRouteChangeUpDev(word32 uplink);
void igdRouteUpdateUpStatus(word32 linkid, word32 status);

void cm_uplink_oper_init(void);

#endif
