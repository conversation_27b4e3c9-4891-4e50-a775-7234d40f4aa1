#ifndef IGD_CM_EM_MODULE_PUB_H
#define IGD_CM_EM_MODULE_PUB_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_easymesh.h"
#include "igdCmFeatureDef.h"

enum cm_em_ctrl_val {
	CM_EM_BASIC_AUTH_MODE_OPEN = 0,
	CM_EM_BASIC_AUTH_MODE_WPA2PSK,
	CM_EM_BASIC_AUTH_MODE_WPA3SAE,
	CM_EM_BASIC_AUTH_MODE_WPA2WPA3,
	CM_EM_BASIC_AUTH_MODE_WPA,
	CM_EM_BASIC_AUTH_MODE_WPAWPA2,
	CM_EM_BASIC_AUTH_MODE_SHARED,
	CM_EM_BASIC_AUTH_MODE_WPA_ENTERPRISE,
	CM_EM_BASIC_AUTH_MODE_WPA2_ENTERPRISE,
	C<PERSON>_EM_BASIC_AUTH_MODE_WPA_WPA2_ENTERPRISE,
};

int32_t igd_cm_em_module_pre_init(void);
int32_t igd_cm_em_module_post_init(void);

/* controller基本设置 */
word32 igdCmEmBasicConfigSet(uword8 *pucInfo, uword32 len);
word32 igdCmEmBasicConfigGet(uword8 *pucInfo, uword32 len);
uint8_t igd_cm_em_get_basic_auth_type(uint8_t val);

/* 漫游策略 */
word32 igdCmEmSteeringCfgSet(uword8 *pucInfo, uword32 len);
word32 igdCmEmSteeringCfgGet(uword8 *pucInfo, uword32 len);
word32 igdCmEmMandateSteeringSet(uword8 *pucInfo, uword32 len);

/* 业务逻辑处理函数 */
word32 igdCmEmGetDevList(uword8 *pucInfo, uword32 len);

/* 业务处理函数 */
word32 igdCmEmApTopologyGet(uword8 *pucInfo, uword32 len);

/* 业务处理函数 */
word32 igdCmEmTopoClientGet(uword8 *pucInfo, uword32 len);

/* 业务处理函数 */
word32 igdCmEmChannelSelectSet(uword8 *pucInfo, uword32 len);
word32 igdCmEmChannelSelectGet(uword8 *pucInfo, uword32 len);

/* CGI层调用此接口同步黑白名单到easymesh网络 */
word32 igdCmEmMacFilterSyncSet(uword8 *pucInfo, uword32 len);
word32 IgdCmSendMacFilterToEm(uword8 *alMac);

/* CGI层调用此接口触发指定BSSID执行WPS */
word32 igdCmEmTriggerWpsEvent(uword8 *pucInfo, uword32 len);
word32 igdCmSetAgentUnbind(uword8 *pucInfo, uword32 len);

void cm_easymesh_oper_init(void);

#endif
