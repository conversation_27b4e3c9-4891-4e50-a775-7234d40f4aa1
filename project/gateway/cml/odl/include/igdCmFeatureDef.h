#ifndef IGD_CM_FEATURE_DEF_H
#define IGD_CM_FEATURE_DEF_H
#define CONFIG_WITH_SAMBA
/* totoal config list
CONFIG_WITH_WLAN_ASSOCIATE_SSID
CONFIG_WITH_WLAN_REPEATER
CONFIG_WITH_EASYMESH depend CONFIG_WITH_WLAN_ASSOCIATE_SSID  CONFIG_WITH_WLAN_REPEATER

 CONFIG_ISP_CMCC
 CONFIG_WITH_USB

CONFIG_WITH_FTPD
CONFIG_WITH_VOIP

CONFIG_WITH_NAS_STORAGE
CONFIG_WITH_DNS_SPEEDLIMIT

CONFIG_WITH_CTC_IPC_SERVICE
CONFIG_WITH_CTC_OBSOLETE_DPI
CONFIG_WITH_VPN
CONFIG_WITH_CTC_SYSDIAG
CONFIG_WITH_DATA_UPLOAD


CONFIG_WITH_SHARE_CFG
CONFIG_WITH_L2DETECT
CONFIG_WITH_CMCC_OSGI

CONFIG_IGMP_SNOOPING_FLOODING

CONFIG_WITH_WLAN_BANDSTEERING
CONFIG_WITH_WLAN_BEACONT_VSIE
*/
#define CONFIG_WITH_SHARE_CFG
#define CONFIG_WITH_SERVICE_ACCONT
#define CONFIG_WITH_FTPD
#define CONFIG_IGMP_SNOOPING_FLOODING

#define CONFIG_WITH_WLAN_SSID_FILTER_LIST

#define CONFIG_WITH_WLAN_ASSOCIATE_SSID
#define CONFIG_WITH_WLAN_REPEATER
#define CONFIG_ISP_CTC_SMART_HGU
#define CONFIG_WITH_WLAN_BEACON_VISE
#define CONFIG_WITH_WLAN_BANDSTEERING
#define CONFIG_WITH_WLAN_KVR
#define CONFIG_WITH_CMCC_UBUS

#endif
