/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igdCmEmuMc.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_CM_INCOMING_FILTER_H
#define IGD_CM_INCOMING_FILTER_H
#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_incomming_filter.h"

/*业务逻辑处理函数声明*/
word32 igdCmSecureIncomingFilterSet(uword8 *pucInfo, uword32 len);

/*业务逻辑处理函数声明*/
word32 igdCmDbusIncomingFilterAttrAdd(uword8 *info, uword32 len);
word32 igdCmDbusIncomingFilterAttrDel(uword8 *info, uword32 len);
word32 igdCmDbusIncomingFilterInit();

void cm_incoming_filter_oper_init(void);

#endif /* IGD_CM_INCOMING_FILTER_H */
