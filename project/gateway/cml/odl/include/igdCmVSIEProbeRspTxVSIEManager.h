/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef IGD_CM_VSIE_PROBE_RSP_TX_VSIE_MANAGER_H
#define IGD_CM_VSIE_PROBE_RSP_TX_VSIE_MANAGER_H

extern word32 igdCmVSIEProbeRspTxVSIEManagerAttrSet(uword8 *info, uword32 len);
extern word32 igdCmVSIEProbeRspTxVSIEManagerAttrGet(uword8 *info, uword32 len);
extern word32 igdCmVSIEProbeRspTxVSIEManagerInit(void);

void cm_vsie_probe_rsp_tx_vsie_manager_oper_init(void);

#endif
