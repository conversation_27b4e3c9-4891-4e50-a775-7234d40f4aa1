/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igdCmTransferServices.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_CM_TRANSFERSERVICES_H
#define IGD_CM_TRANSFERSERVICES_H
#include "hi_odl_tab_transfer_service.h"
extern word32 igdCmTransferSerForwardSet(uword32 *pucInfo, uword32 len);
extern word32 igdCmTransferSerForwardAdd(uword32 *pucInfo, uword32 len);
extern word32 igdCmTransferSerForwardGet(uword32 *pucInfo, uword32 len);
extern word32 igdCmTransferSerForwardDel(uword8 *pucInfo, uword32 len);
extern word32 igdCmTransferSerForwardEntryGetNum(uword32 *pulEntryNum);
extern word32 igdCmTransferSerForwardEntryGetAll(uword8 *pucInfo, uword32 len);
extern word32 igdCmTransferSerForwardEntryGetAllIndex(uword8 *pucInfo, uword32 len);

/*=======================Mirror Class Start =============================================*/
extern word32 igdCmTransferSerMirrorSet(uword32 *pucInfo, uword32 len);
extern word32 igdCmTransferSerMirrorAdd(uword32 *pucInfo, uword32 len);
extern word32 igdCmTransferSerMirrorGet(uword32 *pucInfo, uword32 len);

extern word32 igdCmTransferSerMirrorDel(uword8 *pucInfo, uword32 len);

extern word32 igdCmTransferSerMirrorEntryGetNum(uword32 *pulEntryNum);

extern word32 igdCmTransferSerMirrorEntryGetAll(uword8 *pucInfo, uword32 len);
extern word32 igdCmTransferSerMirrorEntryGetAllIndex(uword8 *pucInfo, uword32 len);


extern word32 igdCmTransferServicesInit(void);

/*=======================TrafficMonitor Class Start =============================================*/
word32 igdCmTranferSerMonitorAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmTranferSerMonitorAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmTranferSerMonitorAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmTranferSerMonitorAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmTranferSerMonitorAttrEntryNumGet(uword32 *entrynum);
word32 igdCmTranferSerMonitorAttrAllIndexGet(uword8 *pucInfo, uword32 len);
word32 igdCmTranferSerMonitorAttrInit(void);

/*=======================Traffic Detail Process Service Class Start =========================*/
extern word32 igdCmTrafficDetailProcServiceAdd(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficDetailProcServiceSet(unsigned char *pucInfo, unsigned int len);
extern word32 igdCmTrafficDetailProcServiceDel(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficDetailProcServiceDelAll(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficDetailProcServiceGet(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficDetailProcServiceGetAll(unsigned char *pucInfo, unsigned int len);
extern word32 igdCmTrafficDetailProcServiceGetEntryNum(uword32 *entrynum);
extern word32 igdCmTrafficDetailProcServiceGetAllIndex(unsigned char *pucInfo, unsigned int len);


/*=======================Traffic Qos Service Class Start ===========================*/
extern word32 igdCmTrafficFlowAdd(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficFlowSet(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficFlowGet(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficFlowDel(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficFlowDelAll(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficFlowGetAll(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficFlowGetAllIndex(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficFlowGetEntryNum(uword32 *entrynum);

/* Traffic Qos flow statistic */
extern word32 igdCmTrafficFlowStatsGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmTrafficQosFlowAttrAdd(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficQosFlowAttrDel(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficQosFlowAttrDelAll(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficQosFlowAttrGet(uword8 *pucInfo, uword32 len);
extern word32 igdCmTrafficQosFlowAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmTrafficQosFlowAttrInit(void);

word32 igdCmTranferSerMonitorAttrGetAll_Test();

word32 igdCmTransferServicesAllInit(void);

#ifdef CONFIG_WITH_SMART_QOS
void cm_transfer_service_oper_init(void);
#else
static inline void cm_transfer_service_oper_init(void) { }
#endif

#endif
