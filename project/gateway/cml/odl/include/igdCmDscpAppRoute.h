/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef IGD_CM_DSCP_APP_ROUTE_H
#define IGD_CM_DSCP_APP_ROUTE_H

#include <igdGlobalTypeDef.h>

extern word32 igdCmDscpAppRouteAttrAdd(uword8 *info, uword32 len);
extern word32 igdCmDscpAppRouteAttrDel(uword8 *info, uword32 len);
extern word32 igdCmDscpAppRouteAttrSet(uword8 *info, uword32 len);
extern word32 igdCmDscpAppRouteAttrGet(uword8 *info, uword32 len);
extern word32 igdCmDscpAppRouteAttrEntryNumGet(uword32 *entrynum);
extern word32 igdCmDscpAppRouteAttrAllIndex(uword8 *info, uword32 len);
extern word32 igdCmDscpAppRouteInit(void);
extern word32 igdCmDscpAppRouteExit(void);

void cm_scp_app_route_oper_init(void);

int read_data_from_binary_file(const char *filepath, char *data, int data_size);
int write_data_to_binary_file(const char *filepath, char *data, int data_size);
void remove_common_elements(char *old, const char *new);
int add_elements(char *old, int old_size, char *new);

#endif
