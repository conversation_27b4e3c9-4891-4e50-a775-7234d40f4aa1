#ifndef IGD_CM_PM_MODULE_PUB_H
#define IGD_CM_PM_MODULE_PUB_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_monitor_collector.h"
#include "hi_odl_tab_ext_device.h"
#include "hi_odl_tab_emu_httpdownload.h"
#include "hi_odl_tab_client_download.h"
#include "hi_odl_tab_storage.h"

/***************************状态上报监控基本属性表*********************************/
word32 igdCmPMMonitorCfgAdd(uword8 *pucInfo, uword32 len);
word32 igdCmPMMonitorCfgDel(uword8 *pucInfo, uword32 len);
word32 igdCmPMMonitorCfgSet(uword8 *pucInfo, uword32 len);
word32 igdCmPMMonitorCfgGet(uword8 *pucInfo, uword32 len);
word32 igdCmPMMonitorCfgEntryNumGet(uword32 *entrynum);
word32 igdCmPMGetallMonitorCfgInfo(uword8 *pucInfo, uword32 len);

word32 igdCmPMMonitorCollectorAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmPMMonitorCollectorAttrGet(uword8 *pucInfo, uword32 len);

/* 下载测试（电信）*/
word32 igdCmPMHttpFtpDownloadTestAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmPMHttpFtpDownloadTestAttrGet(uword8 *pucInfo, uword32 len);


/***************************FTP Client 远程下载*********************************/
word32 igdCmPMFtpClientDownloadAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmPMFtpClientDownloadAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmPMFtpClientDownloadAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmPMFtpClientDownloadAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmPMFtpClientDownloadCfgEntryNumGet(uword32 *entrynum);
word32 igdCmPMFtpClientDownloadConfigInfo(uword8 *pucInfo, uword32 len);

/*********************************FTP Client 取消下载***************************************/

/***************************USB 全局配置属性表*********************************/
word32 igdCmPMUsbGlobalCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmPMUsbGlobalCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmPMUsbGlobalCfgAttrInit(void);

/***************************接入USB配置信息属性表*********************************/
word32 igdCmPMUsbCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmPMUsbCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmPMUsbCfgAttrInit(void);

/***************************USB 设备消息表*******************************************/
word32 igdCmBundleNotifyCfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmBundleNotifyCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmBundleNotifyCfgAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmBundleNotifyCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmBundleNotifyCfgEntryNumGet(uword32 *pulEntryNum);

/***************************USB 串口设备HANDLE*******************************************/
word32 igdCmPMUsbSerialHandleCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmPMUsbSerialHandleCfgAttrGet(uword8 *pucInfo, uword32 len);

/***************************USB 串口设备数据操作****************************************/
word32 igdCmPMUsbSerialDataCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmPMUsbSerialDataCfgAttrGet(uword8 *pucInfo, uword32 len);

/***************************USB 串口属性****************************************/
word32 igdCmPMUsbSerialAttrCfgSet(uword8 *pucInfo, uword32 len);


/***************************usb设备属性表*********************************/
word32 igdCmStorageUSBAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmStorageUSBAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmStorageUSBAttrGetEntryNum(uword32 *entrynum);
word32 igdCmStorageUSBAttrGetAllEntry(uword8 *pucInfo, uword32 len);


/***************************usb设备DEVNAME表*********************************/
word32 igdCmStorageUSBDevNameGetAllEntry(uword8 *pucInfo, uword32 len);

/***************************NASaccess 信息表**************************/
word32 igdCmStorageNASOperation(uword8 *pucInfo, uword32 len);

/*****************************Samba用户参数属性表*************************************/
word32 igdCmPMSambaUserAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmPMSambaUserAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmPMSambaUserAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmPMSambaUserAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmPMSambaUserEntryNumGet(uword32 *entrynum);
word32 igdCmPMSambaUserGetallIndex(uword8 *pucInfo, uword32 len);
word32 igdCmPMSambaUserAttrInit(void);

uword32 igdCmPMModuleInit(void);
void cm_pm_monitor_oper_init(void);

#endif/*IGD_CM_PM_MODULE_PUB_H*/
