#ifndef IGD_CM_AWIFI_MODULE_PUB_H
#define IGD_CM_AWIFI_MODULE_PUB_H
#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_awifi.h"

#define AWIFI_VERSION_FILE_PATH "/usr/awifi/version"
#define AWIFI_VERSIONBAK_FILE_PATH "/usr/bin/awifi/version"
#define AWIFI_CONFIG_FILE_PATH "/usr/awifi/awifi.conf"
#define AWIFI_CONFIGBAK_FILE_PATH "/usr/bin/awifi/awifi.conf"

uword32 igdCmAwifiPPPoEDomainAttrSet(uword8 *pucInfo, uword32 len);
uword32 igdCmAwifiPPPoEDomainAttrGet(uword8 *pucInfo, uword32 len);

uword32 igdCmAwifiDefaultServerAttrSet(uword8 *pucInfo, uword32 len);
uword32 igdCmAwifiDefaultServerAttrGet(uword8 *pucInfo, uword32 len);

uword32 igdCmAwifiAutoUpgradeAttrSet(uword8 *pucInfo, uword32 len);
uword32 igdCmAwifiAutoUpgradeAttrGet(uword8 *pucInfo, uword32 len);

uword32 igdCmAwifiFirmwareInfoGet(uword8 *pucInfo, uword32 len);
uword32 igdCmAwifiFirmwareInfoSet(uword8 *pucInfo, uword32 len);

uword32 igdCmAwifiLanAuthConfSet(uword8 *pucInfo, uword32 len);
uword32 igdCmAwifiLanAuthConfGet(uword8 *pucInfo, uword32 len);

uword32 igdCmAwifiCustomSiteServerConfSet(uword8 *pucInfo, uword32 len);
uword32 igdCmAwifiCustomSiteServerConfGet(uword8 *pucInfo, uword32 len);
#endif
