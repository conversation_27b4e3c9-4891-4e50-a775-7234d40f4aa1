#ifndef IGD_CM_TIMER_MODULE_PUB_H
#define IGD_CM_TIMER_MODULE_PUB_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_timer.h"

word32 igdCmSleepTimerStateGet(uword8 *pucInfo, uword32 len);

word32 igdCmSleepTimerCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSleepTimerCfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSleepTimerCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSleepTimerCfgAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmSleepTimerEntryNumGet(uword32 *pulEntryNum);
word32 igdCmSleepTimerEntryGetAll(uword8 *pucInfo, uword32 len);
/***************************WiFi Timer属性表 单实例*********************************/
word32 igdCmWifiTimerBASICSet(uword8 *pucInfo, uword32 len);
word32 igdCmWifiTimerBASICGet(uword8 *pucInfo, uword32 len);

word32 igdCmWiFiTimerCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimerCfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimerCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimerCfgAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimerEntryNumGet(uword32 *pulEntryNum);
word32 igdCmWiFiTimerEntryGetAll(uword8 *pucInfo, uword32 len);

/***************************WiFi Timer1属性表 多实例*********************************/
word32 igdCmWiFiTimer1CfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimer1CfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimer1CfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimer1CfgAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimer1EntryNumGet(uword32 *pulEntryNum);
word32 igdCmWiFiTimer1EntryGetAll(uword8 *pucInfo, uword32 len);

/***************************LED Timer 属性表 单实例*********************************/
word32 igdCmLedTimerCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmLedTimerCfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmLedTimerCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmLedTimerCfgAttrDel(uword8 *pucInfo, uword32 len);

word32 igdCmNetworkTimerCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmNetworkTimerCfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmNetworkTimerCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmNetworkTimerCfgAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmNetworkTimerEntryNumGet(uword32 *pulEntryNum);
word32 igdCmNetworkTimerEntryGetAll(uword8 *pucInfo, uword32 len);

uword32 igdCmTimerlModuleInit(void);
void  igdCmTimerModuleExit(void);

void cm_timer_oper_init(void);

#endif/*IGD_CM_TIMER_MODULE_PUB_H*/
