#ifndef IGD_CM_WLAN_MODULE_PUB_H
#define IGD_CM_WLAN_MODULE_PUB_H

#include <igdGlobalTypeDef.h>
#include <igdCmFeatureDef.h>
#include "hi_odl_tab_wlan.h"
#include "hi_odl_asyn_rt.h"

#define WIFI_TRACE(fmt,...) \
	printf("%s()-%d: " fmt,(char*)__FUNCTION__,__LINE__,##__VA_ARGS__)

/***************************无线通用功能属性表************************************/
word32 igdCmWlanGlobalCfgAttrInit(void);
word32 igdCmWlanGlobalCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanGlobalCfgAttrTr069Set(uword8 *pucInfo, uword32 len);
word32 igdCmWlanGlobalCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanGlobalCfgAttrGetNum(uword32 *pulEntryNum);

word32 igdCmWlanMldAttrInit(void);
word32 igdCmWlanMldAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanMldAttrGet(uword8 *pucInfo, uword32 len);

int32_t igdCmWlanGlobalCfgAttrFillEntryImpl(IgdWLANGlobalAttrCfgTab *wlan_global_attr_cfg,
	IgdWLANGlobalAttrCfgTab *target_entry, uint32_t entry_index, cm_attr_op_type cfg_attr_op);

/***************************无线网络状态信息属性表************************************/
word32 igdCmWlanGlobalInfoAttrQry(uword8 *pucInfo, uword32 len);

/***************************无线网络包统计属性表*********************************/

word32 igdCmWlanSsidPktStatQry(uword8 *pucInfo, uword32 len);

/***************************无线SSID属性表*********************************/
word32 igdCmWlanSsidCfgAttrInit(void);
word32 igdCmWlanSsidCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidCfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidCfgAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidCfgEntryGetNum(uword32 *pulEntryNum);
word32 igdCmWlanSsidCfgEntryGetAll(uword8 *pucInfo, uword32 len);  //IgdWLANSsidEntryCfgTab

int32_t igdCmWlanSsidCfgAttrFillEntryImpl(IgdWLANSsidAttrCfgTab *wlan_ssid_attr_cfg, IgdWLANSsidAttrCfgTab *target_entry,
	uint32_t entry_index, cm_attr_op_type cfg_attr_op);
bool igdCmWlanSsidFindEntryIndex(
	const IgdWLANSsidAttrCfgTab *attr_cfg, uint32_t *entry_index, IgdWLANSsidAttrCfgTab *found_entry);
void IgdCmWlanSsidCfgWepKeySplit(const word8 *str, word8 *deststr, word32 totalLength, word32 length);

int32_t igd_cm_wlan_ssid_cfg_entry_get_all_index(uint8_t *in_arg, uint32_t len);

/***************************无线SSID属性表*********************************/

/***************************无线AP关联的当前设备的信息属性表*********************************/
word32 igdCmWlanApStaInfoGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanApStaInfoGetEntryNum(uword32 *entrynum);
word32 igdCmWlanApStaInfoEntryQry(uword8 *pucInfo, uword32 len);
uint32_t igd_cm_sta_info_query(void *arg);

/*******************************无线AP邻居AP的信息属性表*************************************/
word32 igdCmWlanApNeighborInfoEntryQry(uword8 *pucInfo, uword32 len);


/***************************无线WLAN共享功能属性表************************************/
word32 igdCmWlanSsidShareCfgAttrInit(void);
word32 igdCmWlanSsidShareCfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidShareCfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidShareCfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidShareCfgAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidShareCfgEntryGetNum(uword32 *pulEntryNum);
word32 igdCmWlanSsidShareCfgEntryGetAll(uword8 *pucInfo, uword32 len);
void igdCmWlanAssociateConnStart(void);
/***************************无线WLAN共享功能属性表************************************/

#if defined(CONFIG_WITH_EASYMESH)
word32 igdCmEmFhSSID(word32 band);
word32 igdCmEmBhSSID(word32 band);


word32 igdWLANEasymeshAttrSet(uword8 *pucInfo, uword32 len);
word32 igdWLANEasymeshAttrGet(uword8 *pucInfo, uword32 len);
uint32_t igdIsControllerEnable(void);

word32 igdCmWlanStaWpsProcess(uword8 *pucInfo, uword32 len);

word32 igdCmCtrlUnbindAgent(uword8 *pucInfo, uword32 len);
void igdCmWlanSetDevMode();

/* ****** easymesh reference end ******* */
#endif

#if defined(CONFIG_WITH_WLAN_ASSOCIATE_SSID)
/* 中继模式连接SSID信息 */
word32 igdCmWlanAssociateSSIDAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanAssociateSSIDAttrSet(uword8 *pucInfo, uword32 len);
void hi_odl_wlan_clear_assoc_ssid_attr(void);
word32 igdCmWlanAssociateSSIDAttrUpdate(uword8 *pucInfo, uword32 len);
word32 igdCmWlanAssociateSSIDLeave(uword8 *pucInfo, uword32 len);
word32 igdCmWlanAssociateSSIDInit(void);

/* igdCmCli.c 有用到的函数 */
uword32 igdCmWlanControlSSIDStateByBit(uword8 *pucInfo, uword32 len);
uword32 igdCmWlanModuleInit(void);
word32 igdCmRouterUplinkMonitorInit(void);

word32 igdCmWlanIndexGet(uword32 ulIndex);
word32 igdCmWlanSSIDIndexGet(word32 ulIndex);

word32 igdCmWlanConnectApResultGet(uword8 *pucInfo, uword32 len);

/*******************************WIFI上行功能信息属性表*************************************/
word32 igdCmWlanRepeaterModeSet(uword8 *pucInfo, uword32 len);
#endif

#if defined(CONFIG_WITH_WLAN_SSID_FILTER_LIST)
/***************************无线SSID过滤MAC列表************************************/
word32 igdCmWlanSsidFilterListAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidFilterListDel(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidFilterListEntryNumGet(uword32 *entrynum);
word32 igdCmWlanSsidFilterListAllGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidFilterListAllIndexGet(uword8 *pucInfo, uword32 len);

/***************************无线SSID过滤MAC控制属性表************************************/
word32 igdCmWlanSsidCfgRestor();
word32 igdCmWlanSsidFilterListSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanSsidFilterListGet(uword8 *pucInfo, uword32 len);
#endif

#if defined(CONFIG_WITH_WLAN_BANDSTEERING)
/********************无线频率自动转换属性表*****************************/
word32 igdCmWlanBandSteeringAttrInit(void);
word32 igdCmWlanBandSteeringAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanBandSteeringAttrGet(uword8 *pucInfo, uword32 len);
/********************无线频率自动转换属性表*****************************/
#endif

#if defined(CONFIG_WITH_WLAN_BEACON_VISE)
/********************无线SSID Beacon帧扩展VSIE服务管理表*****************************/
word32 igdCmWlanBTVSIEMngInit(void);
word32 igdCmWlanBTVSIEMngSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanBTVSIEMngGet(uword8 *pucInfo, uword32 len);
/********************无线SSID Beacon帧扩展VSIE服务管理表*****************************/

/********************无线SSID Beacon帧扩展VSIE服务属性表*****************************/
word32 igdCmWlanBTVSIEAttrInit(void);
word32 igdCmWlanBTVSIEAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWlanBTVSIEAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmWlanBTVSIEAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanBTVSIEAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanBTVSIEAttrEntryNum(uword32 *pulEntryNum);
word32 igdCmWlanBTVSIEAttrAllEntry(uword8 *pucInfo, uword32 len);
word32 igdCmWlanBTVSIEAttrAllIndex(uword8 *pucInfo, uword32 len);

/********************无线SSID Probe帧扩展VSIE服务管理表*****************************/
word32 igdCmWlanPRVSIEMngInit(void);
word32 igdCmWlanPRVSIEMngSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIEMngGet(uword8 *pucInfo, uword32 len);

/********************无线SSID Probe帧扩展VSIE服务属性表*****************************/
word32 igdCmWlanPRVSIEAttrInit(void);
word32 igdCmWlanPRVSIEAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIEAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIEAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIEAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIEAttrEntryNum(uword32 *pulEntryNum);
word32 igdCmWlanPRVSIEAttrAllEntry(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIEAttrAllIndex(uword8 *pucInfo, uword32 len);

/********************无线SSID Probe帧扩展VSIE记录管理表*****************************/
word32 igdCmWlanPRVSIERecMngInit(void);
word32 igdCmWlanPRVSIERecMngSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIERecMngGet(uword8 *pucInfo, uword32 len);

/********************无线SSID Probe帧扩展VSIE记录属性表*****************************/
word32 igdCmWlanPRVSIERecInit(void);
word32 igdCmWlanPRVSIERecAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIERecDel(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIERecGet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIERecEntryGetNum(uword32 *pulEntryNum);
word32 igdCmWlanPRVSIERecAllIndex(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRVSIERecAllEntry(uword8 *pucInfo, uword32 len);

word32 igdCmWlanPRSVSIEAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRSVSIEAttrGet(uword8 *pucInfo, uword32 len);
#endif

/*******************************WIFI漫游功能信息属性表*************************************/
word32 igdCmWlanRoamingAttrCfgSet(uword8 *pucInfo, uword32 len);
word32 igdCmWlanRoamingAttrCfgGet(uword8 *pucInfo, uword32 len);

word32 igdCm5gPreferConfigSet(uword8 *pucInfo, uword32 len);

int32_t igd_cm_wlancfg_wps_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_wps_attr_get(uint8_t *info, uint32_t len);

int32_t igd_cm_wlancfg_wps_registrar_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_wps_registrar_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_wps_registrar_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_wlancfg_wps_registrar_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_wlancfg_wps_registrar_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_wps_registrar_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_wps_registrar_attr_get(uint8_t *info, uint32_t len);

int32_t igd_cm_wlancfg_ap_wmm_parameter_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_ap_wmm_parameter_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_ap_wmm_parameter_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_wlancfg_ap_wmm_parameter_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_wlancfg_ap_wmm_parameter_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_ap_wmm_parameter_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_ap_wmm_parameter_attr_get(uint8_t *info, uint32_t len);

int32_t igd_cm_wlancfg_sta_wmm_parameter_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_sta_wmm_parameter_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_sta_wmm_parameter_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_wlancfg_sta_wmm_parameter_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_wlancfg_sta_wmm_parameter_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_sta_wmm_parameter_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_wlancfg_sta_wmm_parameter_attr_get(uint8_t *info, uint32_t len);

int32_t igd_cm_wmm_configuration_attr_init(void);
int32_t igd_cm_wmm_configuration_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_wmm_configuration_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_wmm_configuration_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_wmm_configuration_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_wmm_configuration_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_wmm_configuration_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_wmm_configuration_attr_get(uint8_t *info, uint32_t len);

int32_t igd_cm_wmm_service_ipset_attr_add(uint8_t *info, uint32_t len);
int32_t igd_cm_wmm_service_ipset_attr_del(uint8_t *info, uint32_t len);
int32_t igd_cm_wmm_service_ipset_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_wmm_service_ipset_attr_all_index_get(uint32_t *info, uint32_t len);
int32_t igd_cm_wmm_service_ipset_attr_all_entry_get(uint8_t *info, uint32_t len);
int32_t igd_cm_wmm_service_ipset_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_wmm_service_ipset_attr_get(uint8_t *info, uint32_t len);

uword32 igdCmWlanModuleInit(void);
void  igdCmWlanModuleExit(void);
word32 igdCmWlanPRSVSIEAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmWlanPRSVSIEAttrAllEntry(uword8 *pucInfo, uword32 len);

void cm_wlan_oper_init(void);

/***************************漫游优化属性表************************************/
int32_t igd_cm_wlan_roaming_optimization_attr_set(uint8_t *info, uint32_t len);
int32_t igd_cm_wlan_roaming_optimization_attr_get(uint8_t *info, uint32_t len);

#endif/*IGD_CM_WLAN_MODULE_PUB_H*/
