/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: igdCmEmuPing.h
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef IGD_CM_EMU_PING_H
#define IGD_CM_EMU_PING_H
#include "hi_odl_tab_emu_ping.h"

extern word32 igdCmEmuIPPingSet(uword32 *pucInfo, uword32 len);

extern word32 igdCmEmuIPPingGet(uword32 *pucInfo, uword32 len);

extern word32 igdCmEmuIPPingGetStatics(uword32 *pucInfo, uword32 len);

extern word32 igdCmEmuIPPingInit(void);

extern word32 igdCmEmuIPPingStart(void);

extern word32 igdCmEmuIPPingStop(void);

extern word32 igdCmEmuCTPingSet(uword8 *pucInfo, uword32 len);

extern word32 igdCmEmuCTPingGet(uword8 *pucInfo, uword32 len);

extern word32 igdCmEmuCTPingStart(uword32 instid);

extern word32 igdCmEmuCTPingStop(uword32 instid);

extern word32 igdEmuCTPingProcNotify(uword8 *pv_data);

extern word32 igdEmuCTPingInit(void);

extern word32 igdCmDetectTCPPingSet(uword32 *pucInfo, uword32 len);

extern word32 igdCmDetectTCPPingGet(uword32 *pucInfo, uword32 len);

void cm_emu_ipping_oper_init(void);

#endif /* SRC_ODL_INCLUDE_IGDCMEMUPING_H_ */
