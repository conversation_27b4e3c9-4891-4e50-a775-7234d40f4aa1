#ifndef IGD_CM_SYSMNG_MODULE_PUB_H
#define IGD_CM_SYSMNG_MODULE_PUB_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_sysmng.h"


/***************************用户账号信息表*********************************/
word32 igdCmSysmngAccountAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngAccountAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngAccountAttrInit(void);


/***************************日志属性表*********************************/
word32 igdCmSysmngSyslogAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngSyslogAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngSyslogAttrInit(void);
word32 igdCmSysmngSysDataWrite(uword8 *pucInfo, uword32 len);


/***************************告警基本属性表*********************************/
word32 igdCmSysmngAlarmAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngAlarmAttrGet(uword8 *pucInfo, uword32 len);

/***************************告警参数属性表*********************************/
word32 igdCmSysmngAlarmConfigAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngAlarmConfigDel(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngAlarmConfigSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngAlarmConfigGet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngAlarmConfigEntryNumGet(uword32 *entrynum);
word32 igdCmSysmngGetallAlarmConfigInfo(uword8 *pucInfo, uword32 len);


/***************************参数监控基本属性表*********************************/
word32 igdCmSysmngMonitorAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngMonitorAttrGet(uword8 *pucInfo, uword32 len);

/***************************监控参数属性表*********************************/
word32 igdCmSysmngMonitorConfigAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngMonitorConfigDel(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngMonitorConfigSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngMonitorConfigGet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngMonitorConfigEntryNumGet(uword32 *entrynum);
word32 igdCmSysmngGetallMonitorConfigInfo(uword8 *pucInfo, uword32 len);

/***************************Ping基本属性表*********************************/
word32 igdCmSysmngPingAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngPingAttrGet(uword8 *pucInfo, uword32 len);

/***************************Ping参数属性表*********************************/
word32 igdCmSysmngPingConfigAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngPingConfigDel(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngPingConfigSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngPingConfigGet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngPingConfigEntryNumGet(uword32 *entrynum);
word32 igdCmEmuCTPingEntryNumGet(uword32 *entrynum);

/***************************环路检测全局属性表*********************************/
word32 igdCmSysmngLoopbackAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngLoopbackAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngLoopbackAttrInit(void);

/***************************环路检测状态信息表*********************************/
word32 igdCmSysmngLoopbackStateGet(uword8 *pucInfo, uword32 len);

/*************************** IPPing Diagnostics参数属性表 *********************************/
word32 igdCmSysmngIPPingDiagAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngIPPingDiagAttrGet(uword8 *pucInfo, uword32 len);


/***************************固件升级*********************************/
word32 igdCmSysmngUpgradeInfoGet(uword8 *pucInfo, uword32 len);

/***************************DBUS 升级配置数据属性表 *********************************/
word32 igdCmSysmngDbusUpgradeAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngDbusUpgradeAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmDbusFirmwareClearUpgradeStatus(void);

/***************************中间件升级*********************************/
word32 igdCmSysmngUpgradeFirmwareSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngUpgradeFirmwareGet(uword8 *pucInfo, uword32 len);


/***************************Memory 告警基本属性表*********************************/
word32 igdCmSysmngMemAlarmAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngMemAlarmAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngMemAlarmAttrInit(void);

/*************************** 网关质量诊断*********************************/
word32 igdCmSysmngOMDiagAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngOMDiagAttrGet(uword8 *pucInfo, uword32 len);

word32 igdCmSysmngOMDiagResourceAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngOMDiagResourceAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngOMDiagResourceAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngOMDiagResourceAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngOMDiagResourceAttrNumGet(uword32 *entrynum);
word32 igdCmSysmngOMDiagResourceAttrGetallIndex(uword8 *pucInfo, uword32 len);

word32 igdCmSysmngOMDiagProcessAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngOMDiagProcessAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngOMDiagProcessAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngOMDiagProcessAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmSysmngOMDiagProcessAttrNumGet(uword32 *entrynum);
word32 igdCmSysmngOMDiagProcessAttrGetallIndex(uword8 *pucInfo, uword32 len);

word32 igdCmTestModeSet(uword8 *pucInfo, uword32 len);
word32 igdCmTestModeGet(uword8 *pucInfo, uword32 len);

#define TEST_MODE_SHIFIT_DDR_UP_LV3     (13)
#define TEST_MODE_SHIFIT_DDR_UP_LV2     (12)
#define TEST_MODE_SHIFIT_DDR_UP_LV1     (11)
#define TEST_MODE_SHIFIT_DDR_UP_LV0     (10)
#define TEST_MODE_SHIFIT_CPU_UP_LV2     (9)
#define TEST_MODE_SHIFIT_CPU_UP_LV1     (8)
#define TEST_MODE_SHIFIT_CPU_UP_LV0     (7)
#define TEST_MODE_SHIFIT_KILL           (5)
#define TEST_MODE_SHIFIT_ANTI           (4)
#define TEST_MODE_SHIFIT_AIR_PERF       (2)
#define TEST_MODE_SHIFIT_CABLE_LATENCY  (1)
#define TEST_MODE_SHIFIT_CABLE_PREF     (0)

int32_t igd_cm_sysmng_alarm_info_attr_entry_num_get(uint32_t *entry_num);
int32_t igd_cm_sysmng_alarm_info_attr_all_entry_get(uint8_t *info, uint32_t len);

uword32 igdCmSysMngModuleInit(void);
void  igdCmSysMngModuleExit(void);
void cm_sysmng_oper_init(void);

#endif/*IGD_CM_SYSMNG_MODULE_PUB_H*/
