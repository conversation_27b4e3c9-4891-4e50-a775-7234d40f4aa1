/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: cloudvr
 * Author: hsan
 * Create: 2023-12-15
 * History:
 */
#ifndef IGD_CM_CLOUDVR_H
#define IGD_CM_CLOUDVR_H

#include <igdGlobalTypeDef.h>
#include "hi_odl_tab_cloudvr.h"

extern word32 igdCmCloudVRSet(uword8 *pucInfo, uword32 len);
extern word32 igdCmCloudVRGet(uword8 *pucInfo, uword32 len);
extern word32 igdCmCloudVRInit(void);

void cm_cloudvr_oper_init(void);

#endif