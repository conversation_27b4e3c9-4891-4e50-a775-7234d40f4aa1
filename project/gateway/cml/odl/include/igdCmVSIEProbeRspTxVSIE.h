/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef IGD_CM_VSIE_PROBE_RSP_TX_VSIE_H
#define IGD_CM_VSIE_PROBE_RSP_TX_VSIE_H

extern word32 igdCmVSIEProbeRspTxVSIEAttrAdd(uword8 *info, uword32 len);
extern word32 igdCmVSIEProbeRspTxVSIEAttrDel(uword8 *info, uword32 len);
extern word32 igdCmVSIEProbeRspTxVSIEAttrSet(uword8 *info, uword32 len);
extern word32 igdCmVSIEProbeRspTxVSIEAttrGet(uword8 *info, uword32 len);
extern word32 igdCmVSIEProbeRspTxVSIEAttrEntryNumGet(uword32 *entrynum);
extern word32 igdCmVSIEProbeRspTxVSIEAttrAllIndex(uword8 *info, uword32 len);
extern word32 igdCmVSIEProbeRspTxVSIEInit(void);

void cm_vsie_probe_rsp_tx_vsie_oper_init(void);

#endif
