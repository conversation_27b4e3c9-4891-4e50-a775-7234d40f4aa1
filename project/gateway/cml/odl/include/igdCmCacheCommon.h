/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef _IGD_CM_CACHE_COMMON_H
#define _IGD_CM_CACHE_COMMON_H

#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

/*********** IGD_WLAN_SSID_CFG_ATTR_TAB ********************/
int32_t idgCmWlanSsidCfgAttrCacheNew(uint8_t *cache, uint32_t inst, uint32_t len);
int32_t igdCmWlanSsidCfgAttrCacheMerge(const uint8_t *cfg, uint8_t *cache, uint32_t inst, uint32_t len);
uint64_t idgCmWlanSsidCfgAttrBitmapGet(const uint8_t *info, uint32_t len);
void idgCmWlanSsidCfgAttrBitmapSet(uint64_t bitmap, uint8_t *info, uint32_t len);

/*********** IGD_WLAN_SSID_CFG_ATTR_TAB ********************/
int32_t igdCmWlanGlobalCfgAttrCacheNew(uint8_t *cache, uint32_t inst, uint32_t len);
int32_t igdCmWlanGlobalCfgAttrCacheMerge(const uint8_t *cfg, uint8_t *cache, uint32_t inst, uint32_t len);
uint64_t igdCmWlanGlobalCfgAttrBitmapGet(const uint8_t *info, uint32_t len);
void igdCmWlanGlobalCfgAttrBitmapSet(uint64_t bitmap, uint8_t *info, uint32_t len);

/*********** IGD_SECUR_URL_FIREWALL_ATTR_TAB ********************/
int32_t igdCmSecureFirewallAttrCacheNew(uint8_t *cache, uint32_t inst, uint32_t len);
int32_t igdCmSecureFirewallAttrCacheMerge(const uint8_t *cfg, uint8_t *cache, uint32_t inst, uint32_t len);
uint64_t igdCmSecureFirewallAttrBitmapGet(const uint8_t *info, uint32_t len);
void igdCmSecureFirewallAttrBitmapSet(uint64_t bitmap, uint8_t *info, uint32_t len);

#endif /* _IGD_CM_CACHE_COMMON_H */