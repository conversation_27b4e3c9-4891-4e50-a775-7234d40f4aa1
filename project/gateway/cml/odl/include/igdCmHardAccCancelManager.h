/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef IGD_CM_HARD_ACC_CANCEL_MANAGER_H
#define IGD_CM_HARD_ACC_CANCEL_MANAGER_H

extern word32 igdCmHardAccCancelManagerAttrSet(uword8 *info, uword32 len);
extern word32 igdCmHardAccCancelManagerAttrGet(uword8 *info, uword32 len);
extern word32 igdCmHardAccCancelManagerInit(void);

void cm_hard_acc_cancel_manager_oper_init(void);

#endif
