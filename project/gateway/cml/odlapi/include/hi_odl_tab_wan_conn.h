/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab wan connection obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_WAN_CONN_H
#define HI_ODL_TAB_WAN_CONN_H
#include "hi_odl_basic_type.h"


/***************************Wan连接信息总览*********************************/
typedef struct {
	uword32 ulStateAndIndex;
#define WAN_INFO_STING_LEN (800)
	uword8 aucWanInfo[WAN_INFO_STING_LEN];

#define WAN_INFO_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdWanInfoTab;

/***************************Wan接口属性*********************************/
typedef struct {
    uword32 ulStateAndIndex;
    uword32 tcp_timeout;
    uword32 udp_timeout;
#define WAN_COMM_INTERFACE_INTERNET_DISABLE (0)
#define WAN_COMM_INTERFACE_INTERNET_ENABLE (1)
#define WAN_COMM_INTERFACE_INTERNET_DISABLE_PORTAL (2)
    uword8 ucInternetEnable;
    uword8 ucPad[CM_THREE_PADS];
    word32 lrnCfg;
#define WAN_COMM_INTERFACE_ATTR_MASK_INTERNETEN (0x01)
#define WAN_COMM_INTERFACE_ATTR_MASK_PACKET_AGE (0x02)
#define WAN_COMM_INTERFACE_ATTR_MASK_LRNCFG (0x04)
#define WAN_COMM_INTERFACE_ATTR_MASK_ALL (0x07)
    uword32 ulBitmap;
} __PACK__ IgdWanCommInterfaceAttrConfTab;

/*************************** Wan接口信息 *********************************/
typedef struct {
	uword32 ulStateAndIndex;
#define WAN_COMM_INTERFACE_WAN_ACCESS_TYPE_DSL (0)
#define WAN_COMM_INTERFACE_WAN_ACCESS_TYPE_ETHERNET (1)
#define WAN_COMM_INTERFACE_WAN_ACCESS_TYPE_POTS (2)
#define WAN_COMM_INTERFACE_WAN_ACCESS_TYPE_EPON (3)
#define WAN_COMM_INTERFACE_WAN_ACCESS_TYPE_GPON (4)
#define WAN_COMM_INTERFACE_WAN_ACCESS_TYPE_10G_EPON (5)
#define WAN_COMM_INTERFACE_WAN_ACCESS_TYPE_XGPON (6)
#define WAN_COMM_INTERFACE_WAN_ACCESS_TYPE_XGSPON (7)
#define WAN_COMM_INTERFACE_WAN_ACCESS_TYPE_GE (8)
#define WAN_COMM_INTERFACE_WAN_ACCESS_TYPE_WCDMA (9)

	uword8 ucWanAccessType;
#define WAN_COMM_INTERFACE_PHYSICAL_LINK_STATUS_INITIALIZING (0)
#define WAN_COMM_INTERFACE_PHYSICAL_LINK_STATUS_UNAVAILABLE (1)
#define WAN_COMM_INTERFACE_PHYSICAL_LINK_STATUS_UP (2)
#define WAN_COMM_INTERFACE_PHYSICAL_LINK_STATUS_DOWN (3)
	uword8 ucPhyLinkStatus;
	uword8 aucPad[CM_TWO_PADS];

	uword32 ulUpMaxRate;
	uword32 ulDownMaxRate;

#define WAN_COMM_INTERFACE_PROVIDER_NAME_LEN (64)
	uword8 aucProvider[WAN_COMM_INTERFACE_PROVIDER_NAME_LEN];

	uword64 lTotalByteSend;
	uword64 lTotalByteRecv;
	uword64 lTotalPacketsSend;
	uword64 lTotalPacketsRecv;
	uword32 crcError;

	uword32 ulConnMax;
	uword32 ulActiveConnNum;

	word32 lLinkSpeed;   /* 协商速率 */
	uword64 ul_mc_valid_pkts; // 组播有效流量统计
	uword64 ul_mc_valid_bytes;

#define WAN_COMM_INTERFACE_INFO_MASK_BIT0_ACCESS_TYPE (0x01)
#define WAN_COMM_INTERFACE_INFO_MASK_BIT1_PHY_LINK_STATUS (0x02)
#define WAN_COMM_INTERFACE_INFO_MASK_BIT2_UP_MAX_RATE (0x04)
#define WAN_COMM_INTERFACE_INFO_MASK_BIT3_DOWN_MAX_RATE (0x08)
#define WAN_COMM_INTERFACE_INFO_MASK_BIT4_PROVIDER (0x10)
#define WAN_COMM_INTERFACE_INFO_MASK_BIT5_BYTES_SEND (0x20)
#define WAN_COMM_INTERFACE_INFO_MASK_BIT6_BYTES_RECV (0x40)
#define WAN_COMM_INTERFACE_INFO_MASK_BIT7_PACKETS_SEND (0x80)
#define WAN_COMM_INTERFACE_INFO_MASK_BIT8_PACKETS_RECV (0x100)
#define WAN_COMM_INTERFACE_INFO_MASK_BIT9_CONN_MAX_NUM (0x200)
#define WAN_COMM_INTERFACE_INFO_MASK_BIT10_ACTIVE_CONN_NUM (0x400)
#define WAN_COMM_INTERFACE_INFO_MASK_ALL (0x7ff)
	uword32 ulBitmap;
} __PACK__ IgdWanCommInterfaceInfoTab;


/***************************Wan连接*********************************/
/* IGD_WAN_CONNECTION_ATTR_TAB */
#define IGD_WAN_CONNECTION_RECORD_NUM (8)

#define IGD_WAN_IP_CONNECTION_NUM_MAX (1)
#define IGD_WAN_PPP_CONNECTION_NUM_MAX (1)

typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 ucConDevIndex; /*WANConnectionDevice 索引*/
	uword8 ucXPConIndex; /*IP或PPP Connection 索引*/
#define WAN_CONNECTION_OPT_DISABLE (0)
#define WAN_CONNECTION_OPT_ENABLE (1)
#define WAN_CONNECTION_OPT_HIDE (2)
	uword8 ucOptEnable; /* 是否允许操作，默认允许 */

	/*VLAN Info*/
#define WAN_CONNECTION_VLAN_CONFIG_DISABLE (0)
#define WAN_CONNECTION_VLAN_CONFIG_ENABLE (1)
	uword8 ucVlanEnable;/*本参数在管理通道中是不允许修改的*/
#define WAN_CONNECTION_VLAN_DISABLE (0)
#define WAN_CONNECTION_VLAN_RESERVE (1)
#define WAN_CONNECTION_VLAN_OVERRIDE (2)
	uword8 ucVlanMode; /*是否启用Vlan标志*/
#define WAN_CONNECTION_8021P_MIN (0)
#define WAN_CONNECTION_8021P_MAX (7)
	uword8 uc8021pMark; /*接口的802.1q，取值范围0-7*/
#define WAN_CONNECTION_DSCP_DISABLE (0)
#define WAN_CONNECTION_DSCP_ENABLE (1)
	uword8 ucDscpEnable; /*DSCP使能*/

#define WAN_CONNECTION_DSCP_MIN (0)
#define WAN_CONNECTION_DSCP_MAX (63)
	uword8 ucDscpValue; /*DSCP取值*/
	uword8 aucPad[CM_THREE_PADS];


#define WAN_CONNECTION_VLANID_MIN (1)
#define WAN_CONNECTION_VLANID_MAX (4095)
	uword32 ulVlanIdMark; /* VlanID*/
	/*VLAN Info*/

#define WAN_CONNECTION_XP_CONNECTION_DISABLE (0)
#define WAN_CONNECTION_XP_CONNECTION_ENABLE (1)
#define WAN_CONNECTION_XP_CONNECTION_RESET (2) /* only set */
	uword8  ucEnable; /*是否使能IP或者PPP connection 实例*/
#define WAN_CONNECTION_TYPE_UNCONFIGURED (0)
#define WAN_CONNECTION_TYPE_IP_ROUTED (1)
#define WAN_CONNECTION_TYPE_IP_BRIDGED (2)
#define WAN_CONNECTION_TYPE_DHCP_SPOOFED (3)
#define WAN_CONNECTION_TYPE_PPPOE_BRIDGED (4)
#define WAN_CONNECTION_TYPE_PPPOE_RELAY (5)
#define WAN_CONNECTION_TYPE_PPTP_RELAY (6)
#define WAN_CONNECTION_TYPE_L2TP_RELAY (7)
	uword8 ucConnectionType; /* 连接模式 路由、桥；路由不做区分，都选IP_ROUTED,桥作区分，IP桥选IP_BRIDGED，PPP桥选择PPPOE_BRIDGED*/
#define WAN_CONNECTION_ADDRESSING_TYPE_STATIC (1)
#define WAN_CONNECTION_ADDRESSING_TYPE_DHCP (2)
#define WAN_CONNECTION_ADDRESSING_TYPE_PPPOE (3)
#define WAN_CONNECTION_ADDRESSING_TYPE_NONE (4)
	uword8 ucAddressingType; /* static/dhcp/pppoe，pppoe是为了区分路由的PPP连接，须注意*/
#define WAN_CONNECTION_NAT_DISABLE (0)
#define WAN_CONNECTION_NAT_ENABLE (1)
	uword8 ucNATEnabled; /*是否启动NAT*/

#define WAN_CONNECTION_SERVICE_TR069 (0x01)
#define WAN_CONNECTION_SERVICE_VOIP (0x02)
#define WAN_CONNECTION_SERVICE_INTERNET (0x04)
#define WAN_CONNECTION_SERVICE_OTHER (0x08)
#define WAN_CONNECTION_SERVICE_IPTV (0x10)
#define WAN_CONNECTION_SERVICE_OTT (0x20)

#define WAN_CONNECTION_SERVICE_SPECIAL_WAN1 (0x40)
#define WAN_CONNECTION_SERVICE_SPECIAL_WAN2 (0x80)
#define WAN_CONNECTION_SERVICE_SPECIAL_WAN3 (0x100)
#define WAN_CONNECTION_SERVICE_SPECIAL_WAN4 (0x200)
#define WAN_CONNECTION_SPECIAL_SERVICE_VR (0x400)

	uint32_t ucServiceList; /*业务类型,具体的业务类型根据bitmap组合*/
#define WAN_CONNECTION_SERVICE_ALIAS_LEN (64)
	word8 aucServiceAlias[WAN_CONNECTION_SERVICE_ALIAS_LEN]; /*本WAN连接承载业务别名，用于备注本WAN连接自定义用途，默认为空，重启不丢失*/

	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN]; /*Wan连接名称*/
#define WAN_CONNECTION_INTERFACE_NAME_LEN (64)
	word8 aucWanIfName[WAN_CONNECTION_INTERFACE_NAME_LEN]; /*Wan物理接口名*/

#define WAN_CONNECTION_BIND_PORT_LAN1 (0x01)
#define WAN_CONNECTION_BIND_PORT_LAN2 (0x02)
#define WAN_CONNECTION_BIND_PORT_LAN3 (0x04)
#define WAN_CONNECTION_BIND_PORT_LAN4 (0x08)
#define WAN_CONNECTION_BIND_PORT_LAN5 (0x10)
#define WAN_CONNECTION_BIND_PORT_LAN6 (0x20)
#define WAN_CONNECTION_BIND_PORT_LAN7 (0x40)
#define WAN_CONNECTION_BIND_PORT_LAN8 (0x80)
#define WAN_CONNECTION_BIND_PORT_SSID1 (0x100)
#define WAN_CONNECTION_BIND_PORT_SSID2 (0x200)
#define WAN_CONNECTION_BIND_PORT_SSID3 (0x400)
#define WAN_CONNECTION_BIND_PORT_SSID4 (0x800)
#define WAN_CONNECTION_BIND_PORT_SSID5 (0x1000)
#define WAN_CONNECTION_BIND_PORT_SSID6 (0x2000)
#define WAN_CONNECTION_BIND_PORT_SSID7 (0x4000)
#define WAN_CONNECTION_BIND_PORT_SSID8 (0x8000)
#define WAN_CONNECTION_BIND_PORT_SSID9 (0x10000)
#define WAN_CONNECTION_BIND_PORT_SSID10 (0x20000)
#define WAN_CONNECTION_BIND_PORT_SSID11 (0x40000)
#define WAN_CONNECTION_BIND_PORT_SSID12 (0x80000)
#define WAN_CONNECTION_BIND_PORT_SSID13 (0x100000)
#define WAN_CONNECTION_BIND_PORT_SSID14 (0x200000)
#define WAN_CONNECTION_BIND_PORT_SSID15 (0x400000)
#define WAN_CONNECTION_BIND_PORT_SSID16 (0x800000)
	uword32 ulBindPort; /*wan连接绑定的端口，绑定多个端口时置对应掩码即可*/
	uword32 ulAutoDisconnectTime; /*自动掉线时间*/
	uword32 ulIdleDisconnectTime; /*空闲掉线时间*/
	uword32 ulWarnDisconnectDelay; /*掉线状态变为删除连接的延迟时间*/

	/*ppp connection only.*/
#define WAN_CONNECTION_PPP_USERNAME_LEN (32)
#define WAN_CONNECTION_PPP_PASSWORD_LEN (32)
	word8 aucUsername[WAN_CONNECTION_PPP_USERNAME_LEN]; /*拨号账户*/
	word8 aucPassword[WAN_CONNECTION_PPP_PASSWORD_LEN]; /*拨号密码*/
	word8 aucPppoeAcName[WAN_CONNECTION_PPP_USERNAME_LEN]; /*PPPoE Access Concentrator*/
	word8 aucPppoeServiceName[WAN_CONNECTION_PPP_PASSWORD_LEN]; /*PPPoE Service Name*/

#define WAN_CONNECTION_PPP_AUTH_PROTOCOL_AUTO (0)
#define WAN_CONNECTION_PPP_AUTH_PROTOCOL_PAP (1)
#define WAN_CONNECTION_PPP_AUTH_PROTOCOL_CHAP (2)
	uword8 ucPPPAuthProtocol; /*PPP认证协议*/
	uword8 ucBRoute;

#define WAN_PPP_MANUAL_DIAL_IDLE       (0)
#define WAN_PPP_MANUAL_DIAL_CONNECT    (1)
#define WAN_PPP_MANUAL_DIAL_DISCONNECT (2)
	uword8 ucManualDialAction;
	uword8 ucManualDialResult;

	uword32 ulDialDemend; /*拨号间隔*/
	uword32 ulMaxMRUSize;
	/*ppp connection only.*/

	/* ip connection only.*/
	uword8 aucSubnetMask[CM_IP_ADDR_LEN]; /*网络掩码，获取地址方式为Static时可配置*/
	uword8 aucDefaultGateway[CM_IP_ADDR_LEN]; /*默认网关，获取地址方式为Static时可配置*/
	uword8 aucExternalIPAddress[CM_IP_ADDR_LEN]; /*IP地址，获取地址方式为Static时可配置*/
	/* ip connection only.*/
#define WAN_CONNECTION_MTU_MIN (0)
#define WAN_CONNECTION_MTU_MAX (1920)
	uword32 ulMaxMTUSize; /*允许本地发送的以太最大帧长*/

#define WAN_CONNECTION_DNS_DISABLE (0)
#define WAN_CONNECTION_DNS_ENABLE (1)
	uword8 ucDNSEnabled; /*启用DNS，默认:enable*/
#define WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_DISABLE (0)
#define WAN_CONNECTION_DNS_OVERRIDE_ALLOWED_ENABLE (1)
	uword8 ucDNSOverrideAllowed; /*是否允许人工配置DNS，默认:disable*/
#define WAN_CONNECTION_MAC_ADDR_OVERRIDE_ALLOWED_DISABLE (0)
#define WAN_CONNECTION_MAC_ADDR_OVERRIDE_ALLOWED_ENABLE (1)
	uword8 ucMACAddressOverride; /*MAC地址是否可以改写，如果设置为disable，则WAN连接的MAC地址只能使用默认值*/
#define WAN_8021P_MARK_DISABLE (0)
#define WAN_8021P_MARK_ENABLE (1)
	uword8 uc8021pMarkEnable; /*802.1的使能位，默认:enable*/

#define WAN_CONNECTION_DNS_SERVERS_LEN (256)
	word8 aucDNSServers[WAN_CONNECTION_DNS_SERVERS_LEN]; /* DNS Servers，多个DNS服务器以逗号隔开*/

	word8 aucMACAddress[CM_MAC_ADDR_LEN]; /*WAN连接的MAC地址，如果MAC地址允许被改写，该值才有效。*/
#define WAN_CONNECTION_TRIGGER_ON_COMMAND (1)
#define WAN_CONNECTION_TRIGGER_ALWAYS_ON (2)
#define WAN_CONNECTION_TRIGGER_MANUAL (3)
	uword8 ucConnectionTrigger; /*触发建立连接的类型*/
#define WAN_CONNECTION_ROUTE_PROTOCOL_RX_OFF (0)
#define WAN_CONNECTION_ROUTE_PROTOCOL_RIPV1 (1)
#define WAN_CONNECTION_ROUTE_PROTOCOL_RIPV2 (2)
#define WAN_CONNECTION_ROUTE_PROTOCOL_OSPF (3)
	uword8 ucRouteProtocolRx; /*支持的路由协议，默认: OFF*/

	uword32 ulShapingRate; /*出口Shaping速率*/
	uword32 ulShapBurstRate; /*支持的突发大小*/

#define WAN_CONNECTION_IP_FORWARD_LIST_LEN (128)
	word8 aucIPForwardList[WAN_CONNECTION_IP_FORWARD_LIST_LEN];

#define WAN_CONNECTION_DOMIAN_FORWARD_LIST_LEN (64)
	word8 aucDomainForwardList[WAN_CONNECTION_DOMIAN_FORWARD_LIST_LEN];

#define WAN_CONNECTION_LAN_INTERFACE_DISABLE (0)
#define WAN_CONNECTION_LAN_INTERFACE_ENABLE (1)
	uword8 ucLanInterfaceDHCPEnable; /* 是否启用本WAN连接DHCP Server功能，缺省值：enable。注：在建立“OTHER”属性WAN连接时，本参数应自动设置为：disable。*/
#define WAN_CONNECTION_IP_MODE_V4 (1)
#define WAN_CONNECTION_IP_MODE_V6 (2)
#define WAN_CONNECTION_IP_MODE_V4_AND_V6 (3)
	uword8 ucIPMode; /* ipv4、ipv6、ipv4和ipv6 */
#define WAN_CONNECTION_PROXY_DISABLE (0)
#define WAN_CONNECTION_PROXY_ENABLE (1)
	uword8 ucProxyEnable; /* 使能pppoe代理*/
#define WAN_CONNECTION_USER_MIN (1)
#define WAN_CONNECTION_USER_MAX (16)
	uword8 ucMaxUser; /*可代理最大用户数*/

#define WAN_CONNECTION_MULTICAST_VLAN_MIN (0)
#define WAN_CONNECTION_MULTICAST_VLAN_MAX (4094)
	word32 ulMulticastVlan; /* 组播vlan */

	/* ipv6*/
#define WAN_CONNECTION_IPV6_CONFIG_DISABLE (0)
#define WAN_CONNECTION_IPV6_CONFIG_ENABLE (1)
	uword8 ucIPv6Enable; /*IPv6是否使能，没有使用*/
#define WAN_CONNECTION_GETWAY_ACHIEVE_AUTO (0)
#define WAN_CONNECTION_GETWAY_ACHIEVE_STATIC (1)
	uword8 ucGetwayAchieve; /*无状态/手动*/
#define WAN_CONNECTION_DNS_ACHIEVE_AUTO (0)
#define WAN_CONNECTION_DNS_ACHIEVE_STATIC (1)
#define WAN_CONNECTION_DNS_ACHIEVE_DHCPV6 (2)
	uword8 ucDNSAchieve; /* DNS获取方式：无状态/手动/DHCPV6*/

#define WAN_CONNECTION_IPV6_ADDR_ORIGIN_NONE (0)
#define WAN_CONNECTION_IPV6_ADDR_ORIGIN_STATIC (1)
#define WAN_CONNECTION_IPV6_ADDR_ORIGIN_DHCPV6 (2)
#define WAN_CONNECTION_IPV6_ADDR_ORIGIN_PREFIX_DERIVED (3)
#define WAN_CONNECTION_IPV6_ADDR_ORIGIN_AUTO_CONFIGURED (4)
	uword8 ucIPv6AddressOrigin; /*IPv6获取地址方式，默认值:DHCPv6*/
#define WAN_CONNECTION_IPV6_PREFIX_DELEGATION_DISABLE (0)
#define WAN_CONNECTION_IPV6_PREFIX_DELEGATION_ENABLE (1)
	uword8 ucIPv6PrefixDelegationEnabled; /*WAN连接是否获得Prefix Delegation*/
#define WAN_CONNECTION_DSLITE_DISABLE (0)
#define WAN_CONNECTION_DSLITE_ENABLE (1)
	uword8 ucDsliteEnable; /*是否启用DS-lite功能，缺省:disable*/
#define WAN_CONNECTION_AFTR_MODE_AUTO (0)
#define WAN_CONNECTION_AFTR_MODE_STATIC (1)
	uword8 ucAftrMode; /*AFTR配置模式，0：为自动获取方式；1：为配置方式，应与IPv6配置方式保持一致，缺省值：0*/

	uword8 ucIPv6AddrPrefix;				 /*IPv6地址前缀，仅当Static时可配置*/
	word8 aucIPv6Address[CM_IPV6_ADDR_LEN]; /*IPv6地址，仅当Static时可配置*/
#define WAN_CONNECTION_IPV6_ADDR_ALIAS_LEN (64)
	word8 aucIPv6AddressAlias[WAN_CONNECTION_IPV6_ADDR_ALIAS_LEN]; /*WAN口地址别名*/
#define WAN_CONNECTION_IPV6_DNS_SERVERS_LEN (256)
	word8 aucIPv6DNSServers[WAN_CONNECTION_IPV6_DNS_SERVERS_LEN]; /*IPv6 DNS Servers，多个以逗号隔开*/

#define WAN_CONNECTION_IPV6_PREFIX_ORIGIN_NONE (1)
#define WAN_CONNECTION_IPV6_PREFIX_ORIGIN_STATIC (2)
#define WAN_CONNECTION_IPV6_PREFIX_ORIGIN_PREFIX_DELEGATION (3)
#define WAN_CONNECTION_IPV6_PREFIX_ORIGIN_RA (4)
	uword8 ucIPv6PrefixOrigin; /*前缀地址分配机制，默认值、Static、None*/
	uword8 aucPad3[CM_THREE_PADS];

#define WAN_CONNECTION_IPV6_PREFIX_LEN (40)
	word8 aucIPv6Prefix[WAN_CONNECTION_IPV6_PREFIX_LEN]; /*IPv6地址前缀*/
#define WAN_CONNECTION_IPV6_DEFAULT_GATEWAY_LEN (40)
	word8 aucDefaultIPv6Gateway[WAN_CONNECTION_IPV6_DEFAULT_GATEWAY_LEN]; /*默认IPv6网关*/
	word8 aucIPv6DomainName[CM_DOMAIN_NAME_LEN]; /*IPv6域名*/
#define WAN_CONNECTION_AFTR_LEN (64)
	word8 aucAftr[WAN_CONNECTION_AFTR_LEN];/*AFTR地址，Aftr配置模式为1时，需要配置的AFTR地址；可为域名方式或者IP地址方式。*/
#define WAN_CONNECTION_IPV6_PREFIX_ALIAS_LEN (64)
	word8 aucIPv6PrefixAlias[WAN_CONNECTION_IPV6_PREFIX_ALIAS_LEN]; /*前缀地址别名*/
	uword32 ulIPv6PrefixPltime;/*公告前缀的preferred lifetime，单位：秒*/
	uword32 ulIPv6PrefixVltime;/*公告前缀的valid lifetime，单位：秒*/
#define WAN_CONNECTION_NPTV6_DISABLE (0)
#define WAN_CONNECTION_NPTV6_ENABLE (1)
	uword8 ucNPTv6Enable;
#define WAN_PPP_MIX_DISABLE (0)
#define WAN_PPP_MIX_ENABLE (1)
	uword8 ucPPPMixEnable;
#define WAN_CONNECTION_MLD_PROXY_DISABLE (0)
#define WAN_CONNECTION_MLD_PROXY_ENABLE  (1)
	uword8 ucMLDProxyEnable;
#define WAN_CONNECTION_COULD_CP_DISABLE (0)
#define WAN_CONNECTION_COULD_CP_ENABLE  (1)
	uword8 could_cp_en;

	/* 基于WAN接口上下行限速，数值为n, 表示限速为n*512kbps, 0表示不限速，默认值为0*/
	uword32 speed_limit_up;
	uword32 speed_limit_down;

	/* AddressingType为DHCP时的认证账号 */
#define WAN_CONNECTION_DHCP_USERNAME_LEN (64)
	word8 dhcp_username[WAN_CONNECTION_DHCP_USERNAME_LEN];
#define WAN_CONNECTION_DHCP_PASSWORD_LEN (64)
	word8 dhcp_password[WAN_CONNECTION_DHCP_PASSWORD_LEN];

#define WAN_PIN_RESPONSE_WHITE_LIST_LEN (1024)
	word8 ping_response_white_list[WAN_PIN_RESPONSE_WHITE_LIST_LEN];
#define WAN_PIN_RESPONSE_DISABLE (0)
#define WAN_PIN_RESPONSE_ENABLE (1)
	uword8 ping_response_enable;

#define WAN_CONNECTION_IPV6_NAT_DISABLE (0)
#define WAN_CONNECTION_IPV6_NAT_ENABLE (0)
	uword8 ipv6_nat_enable;/*NATv6功能是否启用，默认值false*/

	uword8 pad[CM_TWO_PADS];

#define WAN_CONNECTION_ATTR_MASK_BIT0_VLAN_ENABLE (0x01)
#define WAN_CONNECTION_ATTR_MASK_BIT1_VLAN_MODE (0x02)
#define WAN_CONNECTION_ATTR_MASK_BIT2_8021P_MARK (0x04)
#define WAN_CONNECTION_ATTR_MASK_BIT3_DSCP_ENABLE (0x08)
#define WAN_CONNECTION_ATTR_MASK_BIT4_DSCP_VALUE (0x10)
#define WAN_CONNECTION_ATTR_MASK_BIT5_VLANID_MARK (0x20)
#define WAN_CONNECTION_ATTR_MASK_BIT6_ENABLE (0x40)
#define WAN_CONNECTION_ATTR_MASK_BIT7_CONNECTION_TYPE (0x80)
#define WAN_CONNECTION_ATTR_MASK_BIT8_ADDRESSING_TYPE (0x100)
#define WAN_CONNECTION_ATTR_MASK_BIT9_NAT_ENABLE (0x200)
#define WAN_CONNECTION_ATTR_MASK_BIT10_SERVERLIST (0x400)
#define WAN_CONNECTION_ATTR_MASK_BIT11_WAN_NAME (0x800)
#define WAN_CONNECTION_ATTR_MASK_BIT12_WANIF_NAME (0x1000)
#define WAN_CONNECTION_ATTR_MASK_BIT13_BIND_PORT (0x2000)
#define WAN_CONNECTION_ATTR_MASK_BIT14_AUTO_DISCONNECT_TIME (0x4000)
#define WAN_CONNECTION_ATTR_MASK_BIT15_IDLE_DISCONNECT_TIME (0x8000)
#define WAN_CONNECTION_ATTR_MASK_BIT16_WARN_DISCONNECT_DELAY (0x10000)
#define WAN_CONNECTION_ATTR_MASK_BIT17_USERNAME (0x20000)
#define WAN_CONNECTION_ATTR_MASK_BIT18_PASSWORD (0x40000)
#define WAN_CONNECTION_ATTR_MASK_BIT19_PPPOE_AC_NAME (0x80000)
#define WAN_CONNECTION_ATTR_MASK_BIT20_PPPOE_SERVICE_NAME (0x100000)
#define WAN_CONNECTION_ATTR_MASK_BIT21_PPPOE_AUTH_PROTOCOL (0x200000)
#define WAN_CONNECTION_ATTR_MASK_BIT22_DIAL_DEMAND (0x400000)
#define WAN_CONNECTION_ATTR_MASK_BIT23_SUBNET_MASK (0x800000)
#define WAN_CONNECTION_ATTR_MASK_BIT24_DEFAULT_GATEWAY (0x1000000)
#define WAN_CONNECTION_ATTR_MASK_BIT25_EXTERNAL_IP_ADDR (0x2000000)
#define WAN_CONNECTION_ATTR_MASK_BIT26_MAX_MTU (0x4000000)
#define WAN_CONNECTION_ATTR_MASK_BIT27_DNS_ENABLED (0x8000000)
#define WAN_CONNECTION_ATTR_MASK_BIT28_DNS_OVERRIDE_ALLOWED (0x10000000)
#define WAN_CONNECTION_ATTR_MASK_BIT29_MAC_ADDR_OVERRIDE (0x20000000)
#define WAN_CONNECTION_ATTR_MASK_BIT30_DNS_SERVERS (0x40000000)
#define WAN_CONNECTION_ATTR_MASK_BIT31_MAC_ADDRESS (0x80000000)
#define WAN_CONNECTION_ATTR_MASK_ALL (0xffffffff)
	uword32 ulBitmap;

#define WAN_CONNECTION_ATTR_MASK1_BIT0_CONNECTION_TRIGGER (0x01)
#define WAN_CONNECTION_ATTR_MASK1_BIT1_ROUTE_PROTOCOL_RX (0x02)
#define WAN_CONNECTION_ATTR_MASK1_BIT2_SHAPING_RATE (0x04)
#define WAN_CONNECTION_ATTR_MASK1_BIT3_SHAPING_BURST_RATE (0x08)
#define WAN_CONNECTION_ATTR_MASK1_BIT4_IPFORWARD_LIST (0x10)
#define WAN_CONNECTION_ATTR_MASK1_BIT5_LAN_INTERFACE_DHCP_ENABLE (0x20)
#define WAN_CONNECTION_ATTR_MASK1_BIT6_IP_MODE (0x40)
#define WAN_CONNECTION_ATTR_MASK1_BIT7_PROXY_ENABLE (0x80)
#define WAN_CONNECTION_ATTR_MASK1_BIT8_MAX_USER (0x100)
#define WAN_CONNECTION_ATTR_MASK1_BIT9_MULTICAST_VLAN (0x200)
#define WAN_CONNECTION_ATTR_MASK1_BIT10_IPV6_ENABLE (0x400)
#define WAN_CONNECTION_ATTR_MASK1_BIT11_GATEWAY_ACHIEVE (0x800)
#define WAN_CONNECTION_ATTR_MASK1_BIT12_DNS_ACHIEVE (0x1000)
#define WAN_CONNECTION_ATTR_MASK1_BIT13_IPV6_ADDR_ORIGIN (0x2000)
#define WAN_CONNECTION_ATTR_MASK1_BIT14_IPV6_PREFIX_DELEGATION_ENABLED (0x4000)
#define WAN_CONNECTION_ATTR_MASK1_BIT15_DSLITE_ENABLE (0x8000)
#define WAN_CONNECTION_ATTR_MASK1_BIT16_AFTR_MODE (0x10000)
#define WAN_CONNECTION_ATTR_MASK1_BIT17_IPV6_ADDRESS (0x20000)
#define WAN_CONNECTION_ATTR_MASK1_BIT18_IPV6_ADDRESS_ALIAS (0x40000)
#define WAN_CONNECTION_ATTR_MASK1_BIT19_IPV6_DNS_SERVERS (0x80000)
#define WAN_CONNECTION_ATTR_MASK1_BIT20_IPV6_PREFIX_ORIGIN (0x100000)
#define WAN_CONNECTION_ATTR_MASK1_BIT21_IPV6_PREFIX (0x200000)
#define WAN_CONNECTION_ATTR_MASK1_BIT22_DEFAULT_IPV6_GATEWAY  (0x400000)
#define WAN_CONNECTION_ATTR_MASK1_BIT23_IPV6_DOMAIN_NAME      (0x800000)
#define WAN_CONNECTION_ATTR_MASK1_BIT24_AFTR                 (0x1000000)
#define WAN_CONNECTION_ATTR_MASK1_BIT25_IPV6_PREFIX_ALIAS    (0x2000000)
#define WAN_CONNECTION_ATTR_MASK1_BIT26_IPV6_PREFIX_PLTIME   (0x4000000)
#define WAN_CONNECTION_ATTR_MASK1_BIT27_IPV6_PREFIX_VLTIME   (0x8000000)
#define WAN_CONNECTION_ATTR_MASK1_BIT28_MAX_MRU_SIZE        (0x10000000)
#define WAN_CONNECTION_ATTR_MASK1_BIT29_8021P_MARK_ENABLE   (0x20000000)
#define WAN_CONNECTION_ATTR_MASK1_BIT30_CONDEVINDEX         (0x40000000)
#define WAN_CONNECTION_ATTR_MASK1_BIT31_XPCONINDEX          (0x80000000)
#define WAN_CONNECTION_ATTR_MASK1_ALL (0xffffffff)
	uword32 ulBitmap1;

#define WAN_CONNECTION_ATTR_MASK2_BIT0_NPTV6_ENABLE  (0x01)
#define WAN_CONNECTION_ATTR_MASK2_BIT1_SERVICEALIAS (0x02)
#define WAN_CONNECTION_ATTR_MASK2_PPP_MIX_ENABLE (0x04)
#define WAN_CONNECTION_ATTR_MASK2_MLD_PROXY_ENABLE (0x08)
#define WAN_CONNECTION_ATTR_MASK2_IPTV_USRNAME  (0x10)
#define WAN_CONNECTION_ATTR_MASK2_IPTV_PASSWORD (0x20)
#define WAN_CONNECTION_ATTR_MASK2_SPEEDLIMIT_UP (0x40)
#define WAN_CONNECTION_ATTR_MASK2_SPEEDLIMIT_DOWN (0x80)
#define WAN_CONNECTION_ATTR_MASK2_COULD_CP_ENABLE (0x100)
#define WAN_CONNECTION_ATTR_MASK2_DHCP_USERNAME (0x200)
#define WAN_CONNECTION_ATTR_MASK2_DHCP_PASSWORD (0x400)
#define WAN_CONNECTION_ATTR_MASK2_PING_RESPONSE_WHITE_LIST (0x800)
#define WAN_CONNECTION_ATTR_MASK2_PING_RESPONSE_ENABLE (0x1000)
#define WAN_CONNECTION_ATTR_MASK2_DOMAIN_FORWARD_LIST (0x2000)
#define WAN_CONNECTION_ATTR_MASK2_IPV6_NAT_ENABLE (0x4000)
#define WAN_CONNECTION_ATTR_MASK2_ALL (0x3fff)
	uword32 ulBitmap2;

} __PACK__ IgdWanConnectionAttrConfTab;


/***************************Wan连接*********************************/

/***************************Wan连接状态信息*********************************/
/*表相关宏*/




typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 aucPad[CM_THREE_PADS];

	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN];/*支持索引+名称的查询方式*/

#define WAN_CONNECTION_CURRENT_USE_IPV4 (0)
#define WAN_CONNECTION_CURRENT_USE_IPV6 (1)
#define WAN_CONNECTION_CURRENT_USE_IPV4_OR_IPV6 (2)
	uword8 ucIPMode;/*web用来区分ipv4 或者 ipv6*/
#define WAN_CONNECTION_STATUS_UNCONFIGURED (0)
#define WAN_CONNECTION_STATUS_CONNECTING (1)
#define WAN_CONNECTION_STATUS_AUTHENTICATING (2)
#define WAN_CONNECTION_STATUS_CONNECTED (3)
#define WAN_CONNECTION_STATUS_PENDING_DISCONNECT (4)
#define WAN_CONNECTION_STATUS_DISCONNECTING (5)
#define WAN_CONNECTION_STATUS_DISCONNECTED (6)
	uword8 ucConnectionStatus; /*当前IPv4连接状态*/
#define WAN__POSSIBLE_CONNECTION_STATUS_UNCONFIGURED (0)
#define WAN__POSSIBLE_CONNECTION_STATUS_IP_ROUTED (1)
#define WAN__POSSIBLE_CONNECTION_STATUS_IP_BRIDGED (2)
#define WAN__POSSIBLE_CONNECTION_STATUS_DHCP_SPOOFED (3)
#define WAN__POSSIBLE_CONNECTION_STATUS_PPPOE_BRIDGED (4)
#define WAN__POSSIBLE_CONNECTION_STATUS_PPPOE_RELAY (5)
#define WAN__POSSIBLE_CONNECTION_STATUS_PPTP_RELAY (6)
#define WAN__POSSIBLE_CONNECTION_STATUS_L2TP_RELAY (7)
	uword8 ucPossibleConnectionTypes; /*可能的链接类型*/
	uword8 optEnable;

	uword32 ulUptime; /*连接已UP时间*/
#define WAN_LAST_CONNECTION_ERROR_NONE (0)
#define WAN_LAST_CONNECTION_ERROR_ISP_TIME_OUT (1)
#define WAN_LAST_CONNECTION_ERROR_COMMAND_ABORTED (2)
#define WAN_LAST_CONNECTION_ERROR_NOT_ENABLED_FOR_INTERNET (3)
#define WAN_LAST_CONNECTION_ERROR_BAD_PHONE_NUMBER (4)
#define WAN_LAST_CONNECTION_ERROR_USER_DISCONNECT (5)
#define WAN_LAST_CONNECTION_ERROR_ISP_DISCONNECT (6)
#define WAN_LAST_CONNECTION_ERROR_IDLE_DISCONNECT (7)
#define WAN_LAST_CONNECTION_ERROR_FORCED_DISCONNECT (8)
#define WAN_LAST_CONNECTION_ERROR_SERVER_OUT_OF_RESOURCES (9)
#define WAN_LAST_CONNECTION_ERROR_RESTRICTED_LOGON_HOURS (10)
#define WAN_LAST_CONNECTION_ERROR_ACCOUNT_DISABLED (11)
#define WAN_LAST_CONNECTION_ERROR_ACCOUNT_EXPIRED (12)
#define WAN_LAST_CONNECTION_ERROR_PASSWORD_EXPIRED (13)
#define WAN_LAST_CONNECTION_ERROR_AUTHENTICATION_FAILURE (14)
#define WAN_LAST_CONNECTION_ERROR_NO_DIALTONE (15)
#define WAN_LAST_CONNECTION_ERROR_NO_CARRIER (16)
#define WAN_LAST_CONNECTION_ERROR_NO_ANSWER (17)
#define WAN_LAST_CONNECTION_ERROR_LINE_BUSY (18)
#define WAN_LAST_CONNECTION_ERROR_UNSUPPORTED_BITSPERSECOND (19)
#define WAN_LAST_CONNECTION_ERROR_TOO_MANY_LINE_ERRORS (20)
#define WAN_LAST_CONNECTION_ERROR_IP_CONFIGURATION (21)
#define WAN_LAST_CONNECTION_ERROR_UNKNOWN (22)
	uword8 ucLastConnectionError; /*最后一次建连接产生的错误*/
#define WAN_CONNECTION_RSIP_NOT_AVAILABLE (0)
#define WAN_CONNECTION_RSIP_AVAILABLE (1)
	uword8 ucRSIPAvailable; /*是否支持RSIP*/

	//PPP-Connection only.
#define WAN_CONNECTION_PPP_ENCRYPTION_PROTOCOL_NONE (0)
#define WAN_CONNECTION_PPP_ENCRYPTION_PROTOCOL_MPPE (1)
	uword8 ucPPPEncryptionProtocol; /*PPP封装协议*/
#define WAN_CONNECTION_PPP_COMPRESSSION_PROTOCOL_NONE (0)
#define WAN_CONNECTION_PPP_COMPRESSSION_PROTOCOL_VAN_JACOBSEN (1)
#define WAN_CONNECTION_PPP_COMPRESSSION_PROTOCOL_STAC_LZS (2)
	uword8 ucPPPCompressionProtocol; /*PPP压缩协议*/
#define WAN_CONNECTION_PPP_AUTHENTICATION_PROTOCOL_AUTO (0)
#define WAN_CONNECTION_PPP_AUTHENTICATION_PROTOCOL_PAP (1)
#define WAN_CONNECTION_PPP_AUTHENTICATION_PROTOCOL_CHAP (2)
#define WAN_CONNECTION_PPP_AUTHENTICATION_PROTOCOL_MS_CHAP (3)
	uword8 ucPPPAuthenticationProtocol; /*PPP认证协议*/
	uword8 aucPad1[CM_THREE_PADS];

	uword8 aucExternalIPAddress[CM_IP_ADDR_LEN]; /*NAT使用的WAN连接外部地址*/
	uword8 aucRemoteIPAddress[CM_IP_ADDR_LEN]; /*该连接的远端IP地址*/
	uword32 ulCurrentMRUSize; /*当前连接使用的MRU*/
#define WAN_CONNECTION_TRANSPORT_TYPE_PPPOA (0)
#define WAN_CONNECTION_TRANSPORT_TYPE_PPPOE (1)
#define WAN_CONNECTION_TRANSPORT_TYPE_L2TP (2)
#define WAN_CONNECTION_TRANSPORT_TYPE_PPTP (3)
	uword8 ucTransportType; /*PPP传输类型*/
	uword8 ucPPPLCPEcho; /*PPP LCP 回显周期*/
	uword8 ucPPPLCPEchoRetry; /*一个PPP LCP回显周期内的重试次数*/
	uword8 ucPad2;
	uword32 ucPPPLCPReqEchoInterval; /* lcp心跳包时延 单位微秒*/
	//PPP-Connection only.

	/*IPv4*/
	word8 aucIPv4Address[CM_IP_ADDR_STRING_LEN];
	word8 aucIPv4Mask[CM_IP_ADDR_STRING_LEN];
	word8 aucIPv4Gateway[CM_IP_ADDR_STRING_LEN];
	word8 aucIPv4DnsPrimary[CM_IP_ADDR_STRING_LEN];
	word8 aucIPv4DnsBackup[CM_IP_ADDR_STRING_LEN];
	word8 aucIPv4DnsThird[CM_IP_ADDR_STRING_LEN];
	/*IPv4*/

	/*IPv6*/
#define WAN_CONNECTION_IPV6_STATUS_UNCONFIGURED (0)
#define WAN_CONNECTION_IPV6_STATUS_CONNECTING (1)
#define WAN_CONNECTION_IPV6_STATUS_AUTHENTICATING (2)
#define WAN_CONNECTION_IPV6_STATUS_CONNECTED (3)
#define WAN_CONNECTION_IPV6_STATUS_PENDING_DISCONNECT (4)
#define WAN_CONNECTION_IPV6_STATUS_DISCONNECTING (5)
#define WAN_CONNECTION_IPV6_STATUS_DISCONNECTED (6)
	uword8 ucIPv6ConnectionStatus; /*当前IPv6连接状态*/
	uword8 aucPad3[CM_TWO_PADS];

	word8 aucIpv6Address[CM_IPV6_ADDR_LEN_MAX];
	word8 aucLinkIpv6Address[CM_IPV6_ADDR_LEN_MAX];
	uword32 ulIpv6Mask;
	word8 aucIpv6Gateway[CM_IPV6_ADDR_LEN_MAX];
	word8 aucIpv6Prefix[CM_IPV6_ADDR_LEN_MAX];
	uword32 ulIpv6PrefixMask;
	word8 aucIpv6DnsPrimary[CM_IPV6_ADDR_LEN_MAX];
	word8 aucIpv6DnsBackup[CM_IPV6_ADDR_LEN_MAX];
	word8 aucIpv6DnsThird[CM_IPV6_ADDR_LEN_MAX];
	/*IPv6*/

#define WAN_CONNECTION_IS_DEFCON_FALSE (0)
#define WAN_CONNECTION_IS_DEFCON_TRUE  (1)
	uword8 ucIsDefconWan;
	uword8 ucLastConnectionErrorRdClr;
	uword8 aucPad4[2];
#define WAN_CONNECTION_STATE_MASK_BIT0_CONNECTION_STATUS (0x01)
#define WAN_CONNECTION_STATE_MASK_BIT1_POSSIBLE_CONNECTION_STATUS (0x02)
#define WAN_CONNECTION_STATE_MASK_BIT2_UPTIME (0x04)
#define WAN_CONNECTION_STATE_MASK_BIT3_LASTCONNECTION_ERROR (0x08)
#define WAN_CONNECTION_STATE_MASK_BIT4_RSIP_AVAILABLE (0x10)
#define WAN_CONNECTION_STATE_MASK_BIT5_PPP_ENCRY_PROTOCOL (0x20)
#define WAN_CONNECTION_STATE_MASK_BIT6_PPP_COMPRESS_PROTOCOL (0x40)
#define WAN_CONNECTION_STATE_MASK_BIT7_PPP_AUTHENTICATION_PROTOCOL (0x80)
#define WAN_CONNECTION_STATE_MASK_BIT8_EXTERNAL_IP_ADDR (0x100)
#define WAN_CONNECTION_STATE_MASK_BIT9_REMOTE_IP_ADDR (0x200)
#define WAN_CONNECTION_STATE_MASK_BIT10_CURRENT_MRU_SIZE (0x400)
#define WAN_CONNECTION_STATE_MASK_BIT11_TRANSPORT_TYPE (0x800)
#define WAN_CONNECTION_STATE_MASK_BIT12_PPP_LCP_ECHO (0x1000)
#define WAN_CONNECTION_STATE_MASK_BIT13_PPP_LCP_ECHO_RETRY (0x2000)
#define WAN_CONNECTION_STATE_MASK_BIT14_IPV6_CONNECTION_STATUS (0x4000)
#define WAN_CONNECTION_STATE_MASK_BIT15_IPV4_ADDRESS (0x8000)
#define WAN_CONNECTION_STATE_MASK_BIT16_IPV4_MASK (0x10000)
#define WAN_CONNECTION_STATE_MASK_BIT17_IPV4_GATEWAY (0x20000)
#define WAN_CONNECTION_STATE_MASK_BIT18_IPV4_DNS_PRIMARY (0x40000)
#define WAN_CONNECTION_STATE_MASK_BIT19_IPV4_DNS_BACKUP (0x80000)
#define WAN_CONNECTION_STATE_MASK_BIT20_IPV6_CONNECTION_STATUS (0x100000)
#define WAN_CONNECTION_STATE_MASK_BIT21_IPV6_ADDRESS (0x200000)
#define WAN_CONNECTION_STATE_MASK_BIT22_IPV6_MASK (0x400000)
#define WAN_CONNECTION_STATE_MASK_BIT23_IPV6_GATEWAY (0x800000)
#define WAN_CONNECTION_STATE_MASK_BIT24_IPV6_PREFIX (0x1000000)
#define WAN_CONNECTION_STATE_MASK_BIT25_IPV6_PREFIX_MASK (0x2000000)
#define WAN_CONNECTION_STATE_MASK_BIT26_IPV6_DNS_PRIMARY (0x4000000)
#define WAN_CONNECTION_STATE_MASK_BIT27_IPV6_DNS_BACKUP (0x8000000)
#define WAN_CONNECTION_STATE_MASK_BIT28_IS_DEFCON (0x10000000)
#define WAN_CONNECTION_STATE_MASK_BIT29_LASTCONNECTION_ERROR_RD_CLR (0x20000000)
#define WAN_CONNECTION_STATE_MASK_ALL (0x3fffffff)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionStateInfoTab;

/***************************端口映射*********************************/
#define IGD_WAN_CONNECTION_PORTMAP_RECORD_NUM_PER_CONNECTION (4)
#define IGD_WAN_CONNECTION_PORTMAPIN_NUM (IGD_WAN_CONNECTION_PORTMAP_RECORD_NUM_PER_CONNECTION * IGD_WAN_CONNECTION_RECORD_NUM)
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 ucMappingIndex;

#define WAN_CONNECTION_PORTMAPING_DISABLE (0)
#define WAN_CONNECTION_PORTMAPING_ENABLE (1)
	uword8 ucPortMappingEnabled;/*默认关闭*/
#define WAN_CONNECTION_PORTMAPING_PROTOCOL_TCP (0x01)
#define WAN_CONNECTION_PORTMAPING_PROTOCOL_UDP (0x02)
#define WAN_CONNECTION_PORTMAPING_PROTOCOL_BOTH (0x03) /** TCP & UDP */
#define WAN_CONNECTION_PORTMAPING_PROTOCOL_ICMP (0x04)
#define WAN_CONNECTION_PORTMAPING_PROTOCOL_RTP (0x08)
	uword8 ucPortMappingProtocol; /*支持的协议*/
	uword8 aucPad[CM_TWO_PADS];

	word8 aucVirtualServerHostWan[CM_WAN_CONNECTION_NAME_LEN]; /*WAN连接的名字*/

	uword32 ulPortMappingLeaseDuration; /*端口映射租约持续时间，单位：秒，0 表示静态端口映射（永不失效）*/
	uword32 ulExternalPort; /*允许访问的外部端口*/
	uword32 ulExternalPortEnd; /*允许访问的外部范围结束端口*/
	uword32 ulInternalPort; /*需转发到的内部端口*/
#define WAN_CONNECTION_PORTMAPING_REMOTE_HOST_LEN (16)
	word8 aucRemoteHost[WAN_CONNECTION_PORTMAPING_REMOTE_HOST_LEN]; /*远端主机IP地址*/
	word8 aucRemoteHostEnd[WAN_CONNECTION_PORTMAPING_REMOTE_HOST_LEN];
#define WAN_CONNECTION_PORTMAPING_INTERNAL_CLIENT_LEN (16)
	word8 aucInternalClient[WAN_CONNECTION_PORTMAPING_INTERNAL_CLIENT_LEN]; /*内部提供服务的客户端，LAN侧的IP地址或DNS主机名*/
#define WAN_CONNECTION_PORTMAPING_DESCRIPTION_LEN (64)
	word8 aucPortMappingDescription[WAN_CONNECTION_PORTMAPING_DESCRIPTION_LEN]; /*该端口映射的可读性描述*/

#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_BIT0_ENABLE (0x01)
#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_BIT1_PROTOCOL (0x02)
#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_BIT2_VIRTUAL_SERVER_HOST_WAN (0x04)
#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_BIT3_LEASE_DURATION (0x08)
#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_BIT4_EXTERNAL_PORT (0x10)
#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_BIT5_INTERNAL_PORT (0x20)
#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_BIT6_REMOTE_HOST (0x40)
#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_BIT7_DESCRIPTION (0x80)
#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_BIT8_INTERNAL_CLIENT (0x100)
#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_BIT9_REMOTE_HOST_END (0x200)
#define WAN_CONNECTION_PORTMAPING_ATTR_MASK_ALL (0x3ff)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionPortMappingAttrConfTab;


/***************************DDNS*********************************/
#define IGD_WAN_CONNECTION_DDNS_INSTANCE_NUM_MAX (8)

typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 ucDDNSIndex;
#define WAN_CONNECTION_DDNS_CONFIG_DISABLE (0)
#define WAN_CONNECTION_DDNS_CONFIG_ENABLE (1)
	uword8 ucDDNSCfgEnabled;/*启动DDNS功能开关量，缺省关闭*/
	uword8 ucPad;

#define WAN_CONNECTION_DDNS_PROVIDER_LEN (64)
	word8 aucDDNSProvider[WAN_CONNECTION_DDNS_PROVIDER_LEN]; /*花生壳*/
	word8 aucDDNSUsername[CM_USERNAME_LEN];
	word8 aucDDNSPassword[CM_PASSWORD_LEN];
	uword32 ulServicePort;
	word8 aucDDNSDomainName[CM_DOMAIN_NAME_LEN];
#define WAN_CONNECTION_DDNS_HOST_NAME_LEN (64)
	word8 aucDDNSHostName[WAN_CONNECTION_DDNS_HOST_NAME_LEN];
	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN];

#define WAN_CONNECTION_DDNS_ATTR_MASK_BIT0_ENABLE (0x01)
#define WAN_CONNECTION_DDNS_ATTR_MASK_BIT1_PROVIDER (0x02)
#define WAN_CONNECTION_DDNS_ATTR_MASK_BIT2_USERNAME (0x04)
#define WAN_CONNECTION_DDNS_ATTR_MASK_BIT3_PASSWORD (0x08)
#define WAN_CONNECTION_DDNS_ATTR_MASK_BIT4_SERVICE_PORT (0x10)
#define WAN_CONNECTION_DDNS_ATTR_MASK_BIT5_DOMAIN_NAME (0x20)
#define WAN_CONNECTION_DDNS_ATTR_MASK_BIT6_HOST_WAN (0x40)
#define WAN_CONNECTION_DDNS_ATTR_MASK_BIT7_SELECT_WAN_NAME (0x80)
#define WAN_CONNECTION_DDNS_ATTR_MASK_ALL (0xff)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionDDNSAttrConfTab;

/***************************OPTION60*********************************/
#define IGD_WAN_CONNECTION_OPTION60_RECORD_NUM_PER_CONNECTION (4)

typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 ucOption60Index;
#define WAN_CONNECTION_OPTION60_LOAD_DISABLE (0)
#define WAN_CONNECTION_OPTION60_LOAD_ENABLE (1)
	uword8 ucEnable;
#define WAN_CONNECTION_OPTION60_VLAN_MODE_ITMS (0)
#define WAN_CONNECTION_OPTION60_VLAN_MODE_TYPE (1)
#define WAN_CONNECTION_OPTION60_VLAN_MODE_F40 (2)
	word8 ucVlanMode;

#define WAN_CONNECTION_OPTION60_TYPE_MANUFACTURE (1)
#define WAN_CONNECTION_OPTION60_TYPE_CATEGORY (2)
#define WAN_CONNECTION_OPTION60_TYPE_PRODUCTCLASS (3)
#define WAN_CONNECTION_OPTION60_TYPE_VERSION (4)
#define WAN_CONNECTION_OPTION60_TYPE_PORT_MAPPING (5)
#define WAN_CONNECTION_OPTION60_TYPE_OTT (31)
#define WAN_CONNECTION_OPTION60_TYPE_VOIP (34)
#define WAN_CONNECTION_OPTION60_TYPE_CLOUD_VR (35)
#define WAN_CONNECTION_OPTION60_TYPE_WAN_CON_MODE_TYPE (36)
	word32 lType;
#define WAN_CONNECTION_OPTION60_VLAN_VALUE_LEN (256)
	word8 aucValue[WAN_CONNECTION_OPTION60_VLAN_VALUE_LEN];

#define WAN_CONNECTION_OPTION60_ATTR_MASK_BIT0_ENABLE (0x01)
#define WAN_CONNECTION_OPTION60_ATTR_MASK_BIT1_VLAN_MODE (0x02)
#define WAN_CONNECTION_OPTION60_ATTR_MASK_BIT2_TYPE (0x04)
#define WAN_CONNECTION_OPTION60_ATTR_MASK_BIT3_VLAN_VALUE (0x08)
#define WAN_CONNECTION_OPTION60_ATTR_MASK_ALL (0x0f)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionOption60AttrConfTab;

/***************************OPTION125*********************************/
/*表相关宏*/

#define IGD_WAN_CONNECTION_OPTION125_RECORD_NUM_PER_CONNECTION (2)


typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 ucOption125Index;
#define WAN_CONNECTION_OPTION125_DETECT_DISABLE (0)
#define WAN_CONNECTION_OPTION125_DETECT_ENABLE (1)
	uword8 ucEnable;
#define WAN_CONNECTION_OPTION125_VLAN_MODE_ITMS (0)
#define WAN_CONNECTION_OPTION125_VLAN_MODE_TYPE (1)
#define WAN_CONNECTION_OPTION125_VLAN_MODE_VALUE (2)
#define WAN_CONNECTION_OPTION125_VLAN_MODE_SHARED_KEY (3)
	uword8 ucType;

#define WAN_CONNECTION_OPTION125_SubOptCode_MANUFACURE (1)
#define WAN_CONNECTION_OPTION125_SubOptCode_PRODUCTKIND (2)
#define WAN_CONNECTION_OPTION125_SubOptCode_PRODUCTCLASS (3)
#define WAN_CONNECTION_OPTION125_SubOptCode_WANINTERNET (10)
#define WAN_CONNECTION_OPTION125_SubOptCode_WANIPTV (11)
#define WAN_CONNECTION_OPTION125_SubOptCode_WANITMS (12)
#define WAN_CONNECTION_OPTION125_SubOptCode_WANVOIP (13)
	word32 lSubOptCode;
#define WAN_CONNECTION_OPTION125_VERIFY_STRING_LEN  (64)
	word8 aucValue[WAN_CONNECTION_OPTION125_VERIFY_STRING_LEN];
	word8 aucSubOptionData[WAN_CONNECTION_OPTION125_VERIFY_STRING_LEN];
	word8 aucServerID[WAN_CONNECTION_OPTION125_VERIFY_STRING_LEN];
	word8 aucSharedKey[WAN_CONNECTION_OPTION125_VERIFY_STRING_LEN];

#define WAN_CONNECTION_OPTION125_ATTR_MASK_BIT0_ENABLE (0x01)
#define WAN_CONNECTION_OPTION125_ATTR_MASK_BIT1_TYPE (0x02)
#define WAN_CONNECTION_OPTION125_ATTR_MASK_BIT2_SUB_OPT_CODE (0x04)
#define WAN_CONNECTION_OPTION125_ATTR_MASK_BIT3_SUB_OPTION_DATA (0x08)
#define WAN_CONNECTION_OPTION125_ATTR_MASK_BIT4_VALUE (0x10)
#define WAN_CONNECTION_OPTION125_ATTR_MASK_BIT5_SERVER_ID (0x20)
#define WAN_CONNECTION_OPTION125_ATTR_MASK_BIT6_SHARED_KEY (0x40)
#define WAN_CONNECTION_OPTION125_ATTR_MASK_ALL (0x7f)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionOption125AttrConfTab;

/***************************OPTION16*********************************/
#define IGD_WAN_CONNECTION_OPTION16_RECORD_NUM_PER_CONNECTION (4)


typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 ucOption16Index;
#define WAN_CONNECTION_OPTION16_DETECT_DISABLE (0)
#define WAN_CONNECTION_OPTION16_DETECT_ENABLE (1)
	uword8 ucEnable;/*是否加载，缺省值：false*/
#define WAN_CONNECTION_OPTION16_VLAN_MODE_ITMS (0)
#define WAN_CONNECTION_OPTION16_VLAN_MODE_TYPE (1)
#define WAN_CONNECTION_OPTION16_VLAN_MODE_F40 (2)
	uword8 ucVlanMode;

#define WAN_CONNECTION_OPTION16_TYPE_MANUFACTURE (1)
#define WAN_CONNECTION_OPTION16_TYPE_CATEGORY (2)
#define WAN_CONNECTION_OPTION16_TYPE_PRODUCTCLASS (3)
#define WAN_CONNECTION_OPTION16_TYPE_VERSION (4)
#define WAN_CONNECTION_OPTION16_TYPE_PORT_MAPPING (5)
#define WAN_CONNECTION_OPTION16_TYPE_OTT (31)
#define WAN_CONNECTION_OPTION60_TYPE_IGMP (32)
#define WAN_CONNECTION_OPTION16_TYPE_VOIP (34)
#define WAN_CONNECTION_OPTION16_TYPE_CLOUD_VR (35)
#define WAN_CONNECTION_OPTION16_TYPE_WAN_CON_MODE_TYPE (36)
	word32 ucType;
#define WAN_CONNECTION_OPTION16_VLAN_VALUE_LEN (256)
	word8 aucValue[WAN_CONNECTION_OPTION16_VLAN_VALUE_LEN];

#define WAN_CONNECTION_OPTION16_ATTR_MASK_BIT0_ENABLE (0x01)
#define WAN_CONNECTION_OPTION16_ATTR_MASK_BIT1_VLAN_MODE (0x02)
#define WAN_CONNECTION_OPTION16_ATTR_MASK_BIT2_TYPE (0x04)
#define WAN_CONNECTION_OPTION16_ATTR_MASK_BIT3_VLAN_VALUE (0x08)
#define WAN_CONNECTION_OPTION16_ATTR_MASK_ALL (0x0f)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionOption16AttrConfTab;

/***************************OPTION17*********************************/
#define IGD_WAN_CONNECTION_OPTION17_RECORD_NUM_PER_CONNECTION (2)


typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 ucOption17Index;
#define WAN_CONNECTION_OPTION17_DETECT_DISABLE (0)
#define WAN_CONNECTION_OPTION17_DETECT_ENABLE (1)
	uword8 ucEnable;
#define WAN_CONNECTION_OPTION17_VLAN_MODE_ITMS (0)
#define WAN_CONNECTION_OPTION17_VLAN_MODE_TYPE (1)
#define WAN_CONNECTION_OPTION17_VLAN_MODE_VALUE (2)
#define WAN_CONNECTION_OPTION17_VLAN_MODE_SHARED_KEY (3)
	uword8 ucType;

#define WAN_CONNECTION_OPTION17_SubOptCode_MANUFACURE (1)
#define WAN_CONNECTION_OPTION17_SubOptCode_PRODUCTKIND (2)
#define WAN_CONNECTION_OPTION17_SubOptCode_PRODUCTCLASS (3)
#define WAN_CONNECTION_OPTION17_SubOptCode_WANINTERNET (10)
#define WAN_CONNECTION_OPTION17_SubOptCode_WANIPTV (11)
#define WAN_CONNECTION_OPTION17_SubOptCode_WANITMS (12)
#define WAN_CONNECTION_OPTION17_SubOptCode_WANVOIP (13)
	word32 lSubOptCode;
#define WAN_CONNECTION_OPTION17_VERIFY_STRING_LEN (64)
	word8 aucValue[WAN_CONNECTION_OPTION17_VERIFY_STRING_LEN];
	word8 aucSubOptionData[WAN_CONNECTION_OPTION17_VERIFY_STRING_LEN];
	word8 aucServerID[WAN_CONNECTION_OPTION17_VERIFY_STRING_LEN];
	word8 aucSharedKey[WAN_CONNECTION_OPTION17_VERIFY_STRING_LEN];

#define WAN_CONNECTION_OPTION17_ATTR_MASK_BIT0_ENABLE (0x01)
#define WAN_CONNECTION_OPTION17_ATTR_MASK_BIT1_TYPE (0x02)
#define WAN_CONNECTION_OPTION17_ATTR_MASK_BIT2_SUB_OPT_CODE (0x04)
#define WAN_CONNECTION_OPTION17_ATTR_MASK_BIT3_SUB_OPTION_DATA (0x08)
#define WAN_CONNECTION_OPTION17_ATTR_MASK_BIT4_VALUE (0x10)
#define WAN_CONNECTION_OPTION17_ATTR_MASK_BIT5_SERVER_ID (0x20)
#define WAN_CONNECTION_OPTION17_ATTR_MASK_BIT6_SHARED_KEY (0x40)
#define WAN_CONNECTION_OPTION17_ATTR_MASK_ALL (0x5f)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionOption17AttrConfTab;


/***************************Wan连接统计*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 aucPad[CM_THREE_PADS];

	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN];/*支持索引 | 名称两种查询方式*/
	ulword64 ulEthernetBytesSent;
	ulword64 ulEthernetBytesReceived;
	ulword64 ulEthernetPacketsSent;
	ulword64 ulEthernetPacketsReceived;

	uword32 ulTxRate;  /* WAN 连接发送速率 kbps (上行) */
	uword32 ulRxRate;  /* WAN 连接接收速率 kbps (下行) */

#define WAN_CONNECTION_STATISTIC_INFO_ATTR_MASK_BIT0_BYTES_SNET (0x01)
#define WAN_CONNECTION_STATISTIC_INFO_ATTR_MASK_BIT1_BYTES_RECIEVED (0x02)
#define WAN_CONNECTION_STATISTIC_INFO_ATTR_MASK_BIT2_PACKETS_SNET (0x04)
#define WAN_CONNECTION_STATISTIC_INFO_ATTR_MASK_BIT3_PACKETS_RECIEVED (0x08)
#define WAN_CONNECTION_STATISTIC_INFO_ATTR_MASK_BIT4_TXRATE (0x10)
#define WAN_CONNECTION_STATISTIC_INFO_ATTR_MASK_BIT5_RXRATE (0x20)
#define WAN_CONNECTION_STATISTIC_INFO_ATTR_MASK_ALL (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionStatisticInfoTab;


/***************************绑定*********************************/
#define IGD_WAN_CONNECTION_BIND_TAB_RECORD_NUM (20)
#define IGD_WAN_CONNECTION_VLAN_BIND_NUM_PER_PORT_MAX (20)

typedef struct {
	uword32 ulStateAndIndex;

#define BIND_PORT_LAN1 (0x01)
#define BIND_PORT_LAN2 (0x02)
#define BIND_PORT_LAN3 (0x04)
#define BIND_PORT_LAN4 (0x08)
#define BIND_PORT_LAN5 (0x10)
#define BIND_PORT_LAN6 (0x20)
#define BIND_PORT_LAN7 (0x40)
#define BIND_PORT_LAN8 (0x80)
#define BIND_PORT_SSID1 (0x100)
#define BIND_PORT_SSID2 (0x200)
#define BIND_PORT_SSID3 (0x400)
#define BIND_PORT_SSID4 (0x800)
#define BIND_PORT_SSID5 (0x1000)
#define BIND_PORT_SSID6 (0x2000)
#define BIND_PORT_SSID7 (0x4000)
#define BIND_PORT_SSID8 (0x8000)
#define BIND_PORT_SSID9 (0x10000)
#define BIND_PORT_SSID10 (0x20000)
#define BIND_PORT_SSID11 (0x40000)
#define BIND_PORT_SSID12 (0x80000)
#define BIND_PORT_SSID13 (0x100000)
#define BIND_PORT_SSID14 (0x200000)
#define BIND_PORT_SSID15 (0x400000)
#define BIND_PORT_SSID16 (0x800000)
	uword32 ucPortNum;/*端口号*/
#define BIND_MODE_PORT_BIND (0)
#define BIND_MODE_VLAN_BIND (1)
	uword8 ucBindMode;/*绑定模式*/
	uword8 aucPad[CM_THREE_PADS];

#define VLAN_BINDING_STRING_LEN (256)
	uword8 aucVlanBinding[VLAN_BINDING_STRING_LEN];/*每个端口下最多支持16对vlan绑定对*/

#define BIND_ATTR_MASK_BIT0_BIND_MODE (0x01)
#define BIND_ATTR_MASK_BIT1_VLAN_BIND_LIST (0x02)
#define BIND_ATTR_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionBindAttrConfTab;


/***************************Wan连接索引*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 ucConDevIndex; /*WANConnectionDevice 索引*/
	uword8 ucXPConIndex; /*IP或PPP Connection 索引*/

#define WAN_CONNECTION_INDEX_IP_MODE_V4 (1)
#define WAN_CONNECTION_INDEX_IP_MODE_V6 (2)
#define WAN_CONNECTION_INDEX_IP_MODE_V4_AND_V6 (3)
	uword8 ucIPMode;
#define WAN_CONNECTION_INDEX_TYPE_UNCONFIGURED (0)
#define WAN_CONNECTION_INDEX_TYPE_IP_ROUTED (1)
#define WAN_CONNECTION_INDEX_TYPE_IP_BRIDGED (2)
#define WAN_CONNECTION_INDEX_TYPE_DHCP_SPOOFED (3)
#define WAN_CONNECTION_INDEX_TYPE_PPPOE_BRIDGED (4)
#define WAN_CONNECTION_INDEX_TYPE_PPPOE_RELAY (5)
#define WAN_CONNECTION_INDEX_TYPE_PPTP_RELAY (6)
#define WAN_CONNECTION_INDEX_TYPE_L2TP_RELAY (7)
	uword8 ucConnectionType; /* 连接模式 路由、桥；路由不做区分，都选IP_ROUTED,桥作区分，IP桥选IP_BRIDGED，PPP桥选择PPPOE_BRIDGED*/
#define WAN_CONNECTION_INDEX_ADDRESSING_TYPE_STATIC (1)
#define WAN_CONNECTION_INDEX_ADDRESSING_TYPE_DHCP (2)
#define WAN_CONNECTION_INDEX_ADDRESSING_TYPE_PPPOE (3)
	uword8 ucAddressingType; /* static/dhcp/pppoe，pppoe是为了区分路由的PPP连接，须注意*/
#define WAN_CONNECTION_INDEX_SERVICE_TR069 (0x01)
#define WAN_CONNECTION_INDEX_SERVICE_VOIP (0x02)
#define WAN_CONNECTION_INDEX_SERVICE_INTERNET (0x04)
#define WAN_CONNECTION_INDEX_SERVICE_OTHER (0x08)
#define WAN_CONNECTION_INDEX_SERVICE_SPECIAL_WAN1 (0x10)
#define WAN_CONNECTION_INDEX_SERVICE_SPECIAL_WAN2 (0x20)
#define WAN_CONNECTION_INDEX_SERVICE_SPECIAL_WAN3 (0x40)
#define WAN_CONNECTION_INDEX_SERVICE_SPECIAL_WAN4 (0x80)
	uword8 ucServiceList;

#define WAN_CONNECTION_INDEX_MULTICAST_VLAN_MIN (0)
#define WAN_CONNECTION_INDEX_MULTICAST_VLAN_MAX (4094)
	word32 ulMulticastVlan;

	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN]; /*Wan连接名称*/
#define WAN_CONNECTION_INF_NAME_LEN (64)
	word8 aucWanIfName[WAN_CONNECTION_INF_NAME_LEN]; /*Wan物理接口名*/

	uword32 ulBitmap;
} __PACK__ IgdWanConnectionIndexAttrTab;


/***************************端口映射索引*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucGlobalIndex;
	uword8 ucMappingIndex;
	uword8 aucPad[CM_TWO_PADS];

	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN];
	uword32 ulBitmap;

} __PACK__ IgdWanConnectionPortMappingIndexAttrTab;

/*************************** EponLink / GponLink *********************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucConDevIndex; /*WANConnectionDevice 索引*/
	uword8 ucVlanEnable;
	uword8 ucVlanMode; /*是否启用Vlan标志*/
	uword8 uc8021pMark; /*接口的802.1q，取值范围0-7*/

	uword8 ucDscpEnable; /*DSCP使能*/
	uword8 ucDscpValue; /*DSCP取值*/
	uword8 aucPad[CM_TWO_PADS];

	uword32 ulVlanIdMark; /* VlanID*/

#define WAN_CONNECTION_GEPON_LINK_ATTR_MASK_BIT0_VLAN_ENABLE (0x01)
#define WAN_CONNECTION_GEPON_LINK_ATTR_MASK_BIT1_VLAN_MODE (0x02)
#define WAN_CONNECTION_GEPON_LINK_ATTR_MASK_BIT2_8021P_MARK (0x04)
#define WAN_CONNECTION_GEPON_LINK_ATTR_MASK_BIT3_DSCP_ENABLE (0x08)
#define WAN_CONNECTION_GEPON_LINK_ATTR_MASK_BIT4_DSCP_VALUE (0x10)
#define WAN_CONNECTION_GEPON_LINK_ATTR_MASK_BIT5_VLANID_MARK (0x20)
#define WAN_CONNECTION_GEPON_LINK_ATTR_MASK_ALL (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionGeponLinkAttrTab;
/*************************** EponLink / GponLink *********************************/

/***************************DNS_TUNNEL*********************************/
#define IGD_WAN_DNSTUNNEL_RECORD_NUM (8)

typedef struct {
	uword32 ulStateAndIndex;

	uword8 uctunnelIndex;
	uword8 aucPad[CM_THREE_PADS];
#define CM_SERVERIP_ADDR_STRING_LEN (48)
	word8 aucServerIP[CM_SERVERIP_ADDR_STRING_LEN];
#define WAN_CONNECTION_DNS_TUNNEL_DOMAINNAME_MAX (1024)
	word8 aucDomainName[WAN_CONNECTION_DNS_TUNNEL_DOMAINNAME_MAX];

#define WAN_CONNECTION_DNS_TUNNEL_MASK_BIT0_SERVERIP (0x01)
#define WAN_CONNECTION_DNS_TUNNEL_MASK_BIT1_DOMAINNAME (0x02)
#define WAN_CONNECTION_DNS_TUNNEL_MASK_ALL (0x3)
	uword32 ulBitmap;

} __PACK__ IgdWanConnectionDNSTunnelAttrTab;

#define DNS_TUNNEL_FILE_PATH "/config/work/"
#define IGD_DNSTUNNEL_MAX_NUM (16)
typedef struct {
	uint32_t state_and_index;
	uint8_t index;
#define DNSTUNNEL_DISABLE (0)
#define DNSTUNNEL_ENABLE (1)
	uint8_t enable;
	uint8_t pad[CM_TWO_PADS];
#define DOMIAN_FILE_LEN (48)
	char domain_file[DOMIAN_FILE_LEN];
#define DNS_SERVER_LEN (48)
	char dns_server[DNS_SERVER_LEN];
#define DNS_WAN_NAME_LEN (64)
	char dns_wanname[DNS_WAN_NAME_LEN];
#define DNSTUNNEL_MASK_ENABLE (1 << 0)
#define DNSTUNNEL_MASK_DOMAIN (1 << 1)
#define DNSTUNNEL_MASK_DNS_SERVER (1 << 2)
#define DNSTUNNEL_MASK_WAN_NAME (1 << 3)
#define DNSTUNNEL_MASK_ALL ((1 << 4) - 1)
	uint32_t bit_map;
} __PACK__ igd_dns_tunnel_tab;

/***************************DNS_STATUS*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define WAN_CONNECTION_FLUSHDNS_DISABLE (0)
#define WAN_CONNECTION_FLUSHDNS_ENABLE (1)
	uword8 ucFlushDNS;
#define WAN_CONNECTION_DDNSSTATUS_NORMAL (0)
#define WAN_CONNECTION_DDNSSTATUS_NOTUSED (1)
#define WAN_CONNECTION_DDNSSTATUS_CONFIGINFO_INCOMPLETE (2)
#define WAN_CONNECTION_DDNSSTATUS_CONNECT_SERVER_FAILED (3)
#define WAN_CONNECTION_DDNSSTATUS_AUTH_SERVER_FAILED (4)
#define WAN_CONNECTION_DDNSSTATUS_OTHER_FAILED (5)
	uword8 ucDDNSStatus;
	uword8 aucPad[CM_TWO_PADS];
#define WAN_CONNECTION_DDNS_IP (32)
	word8 aucIp[WAN_CONNECTION_DDNS_IP];
#define WAN_CONNECTION_DNS_STATUS_MASK_BIT0_FLUSHDNS (0x1)
#define WAN_CONNECTION_DNS_STATUS_MASK_BIT1_DDNS_STATUS (0x2)
#define WAN_CONNECTION_DNS_STATUS_MASK_BIT2_DDNS_IP (0x4)
#define WAN_CONNECTION_DNS_STATUS_MASK_ALL (0x7)
	uword32 ulBitmap;

} __PACK__ IgdWanConnectionDNSStatusTab;


typedef struct {
	uword32 ulStateAndIndex;
#define WAN_CONNECTION_IPTV_DISABLE (0)
#define WAN_CONNECTION_IPTV_ENABLE (1)
	uword8 aucUpstreamWan;
	word8 aucWanName[CM_WAN_CONNECTION_NAME_LEN];

#define WAN_CONNECTION_IPTVDEF_ATTR_MASK_BIT0_WAN_NAME (0x01)
#define WAN_CONNECTION_IPTVDEF_ATTR_MASK_BIT1_UPSTREAM_WAN (0x02)
#define WAN_CONNECTION_IPTVDEF_ATTR_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdWanConnectionIptvDefTab;

word32 igdCmWanConnectionIptvDefSet(uword8 *pucInfo, uword32 len);
word32 igdCmWanConnectionIptvDefGet(uword8 *pucInfo, uword32 len);

/******************************dns服务********************************/
typedef struct {
	uword32 ulStateAndIndex;
	word8 aucDnsPrimary[CM_IP_ADDR_STRING_LEN];
	word8 aucDnsBackup[CM_IP_ADDR_STRING_LEN];
	word8 aucDnsPrimaryV6[CM_IPV6_ADDR_LEN_MAX];
	word8 aucDnsBackupV6[CM_IPV6_ADDR_LEN_MAX];
#define DBUS_DNS_SERVICE_ATTR_MASK_BIT0_DNS_PRIMARY (0x01)
#define DBUS_DNS_SERVICE_ATTR_MASK_BIT1_DNS_BACKUP (0x02)
#define DBUS_DNS_SERVICE_ATTR_MASK_BIT3_DNS_PRIMARY_V6 (0x04)
#define DBUS_DNS_SERVICE_ATTR_MASK_BIT4_DNS_BACKUP_V6 (0x08)
#define DBUS_DNS_SERVICE_ATTR_MASK_ALL	(0x1f)
	uword32 ulBitmap;
} __PACK__ IgdDbusDnsServiceAttrTab;

/* 	IGD_WAN_PORT_TRIGGER_ATTR_CFG_TAB */
#define  IGD_WAN_PORT_TRIGGER_MAXNUM                                     (32)

typedef struct {
	uint32_t state_and_index;
	uint32_t instance_index;
	uint8_t  port_trigger_enabled;
	uint8_t  pad[CM_THREE_PADS];
	uint32_t trigger_port;
#define WAN_PORT_TRIGGER_TRIGGER_PROTOCOL_STRING_LEN                     (16)
	uint8_t  trigger_protocol[WAN_PORT_TRIGGER_TRIGGER_PROTOCOL_STRING_LEN];
	uint32_t internal_port;
#define WAN_PORT_TRIGGER_PORT_TRIGGER_DESCRIPTION_STRING_LEN             (256)
	uint8_t  port_trigger_description[WAN_PORT_TRIGGER_PORT_TRIGGER_DESCRIPTION_STRING_LEN];
#define WAN_PORT_TRIGGER_ATTR_MASK_PORT_TRIGGER_ENABLED                  (1 << 0)
#define WAN_PORT_TRIGGER_ATTR_MASK_TRIGGER_PORT                          (1 << 1)
#define WAN_PORT_TRIGGER_ATTR_MASK_TRIGGER_PROTOCOL                      (1 << 2)
#define WAN_PORT_TRIGGER_ATTR_MASK_INTERNAL_PORT                         (1 << 3)
#define WAN_PORT_TRIGGER_ATTR_MASK_PORT_TRIGGER_DESCRIPTION              (1 << 4)
#define WAN_PORT_TRIGGER_ATTR_MASK_ALL                                  ((1 << 5) - 1)
	uint32_t bit_map;
} __PACK__ igd_wan_port_trigger_attr_conf_tab_t;

#endif
