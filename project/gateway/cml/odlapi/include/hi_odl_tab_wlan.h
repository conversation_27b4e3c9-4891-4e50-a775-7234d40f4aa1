/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab wlan obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_WLAN_H
#define HI_ODL_TAB_WLAN_H
#include "hi_odl_basic_type.h"


#define HI_CM_BAND_SSID_MAX  8 /*每个频段支持的SSID个数，给CM之上的模块引用 HI_BAND_SSID_MAX */
#define WLAN_INIT_CFG_STATEANDINDEX		(0xFFFFFFFF)
#define WLAN_AP_STA_MACADDR_STR_LEN     (24)
/***************************无线通用功能属性表************************************/
#define IGD_WLAN_GLOBAL_CFG_ATTR_NUM (2)

typedef struct {
	uword32 ulStateAndIndex;
#define WLAN_APMODULE_DEACTIVATE         (0)
#define WLAN_APMODULE_ACTIVATE           (1)
	uword8  ucAPModuleEnable; /*IN&OUT  (true,false), default: true */
#define WLAN_RFBAND_2_4G                 (0)
#define WLAN_RFBAND_5G                   (1)
#define WLAN_RFBAND_5_8G                 (2)
	uword8  ucRFBand; /*IN&OUT  band: 0(2.4GHz)/1(5GHz), default: 0 */
#define WLAN_CHANNEL_SEL_AUTO            (0)
#define WLAN_CHANNEL_2_4G_DEFAULT        (1)
#define WLAN_CHANNEL_5G_DEFAULT          (36)
#define WLAN_CHANNEL_2_4G_MAX            (13)
#define WLAN_CHANNEL_5G_MAX              (254)
	uword8  ucChannel; /*IN&OUT  channel 2.4G: 0 auto, 1~13, 5G: 0 auto, 1~255*/
#define WLAN_CHANNELWIDTH_20M            (0)
#define WLAN_CHANNELWIDTH_40M            (1)
#define WLAN_CHANNELWIDTH_20_40M         (2)
#define WLAN_CHANNELWIDTH_80M            (3)
#define WLAN_CHANNELWIDTH_20_40_80M      (4)
#define WLAN_CHANNELWIDTH_20_40_80_160M  (5)
#define WLAN_CHANNELWIDTH_160M           (6) /** 160M */
#define WLAN_CHANNELWIDTH_160_80PLUS80M  (7) /** 160M / 80+80M */
	uword8  ucChannelWidth; /*IN&OUT 2.4GHz default: 20MHz, 5.8GHz default: 20/40/80/160M self adapt*/
#define WLAN_RF_WORK_MODE_A              (1)
#define WLAN_RF_WORK_MODE_B              (2)
#define WLAN_RF_WORK_MODE_G              (3)
#define WLAN_RF_WORK_MODE_N              (4)
#define WLAN_RF_WORK_MODE_BGN            (5)
#define WLAN_RF_WORK_MODE_AC             (6)
#define WLAN_RF_WORK_MODE_A_AC           (7)
#define WLAN_RF_WORK_MODE_A_AC_N         (8)
#define WLAN_RF_WORK_MODE_BG             (9)
#define WLAN_RF_WORK_MODE_GN             (10)
#define WLAN_RF_WORK_MODE_A_N            (11)
#define WLAN_RF_WORK_MODE_BGNAX          (12)
#define WLAN_RF_WORK_MODE_ANACAX         (13)
#define WLAN_RF_WORK_MODE_BGNAXBE        (14)
#define WLAN_RF_WORK_MODE_ANACAXBE       (15)
#define WLAN_RF_WORK_MODE_N_AX           (16)
#define WLAN_RF_WORK_MODE_AC_AX          (17)
#define WLAN_RF_WORK_MODE_AX             (18)
#define WLAN_RF_WORK_MODE_BE             (19)
	uword8  ucRFMode; /*IN&OUT WiFi mode*/
#define WLAN_POWER_LEVEL_100_PERCENT     (1)
#define WLAN_POWER_LEVEL_80_PERCENT      (2)
#define WLAN_POWER_LEVEL_60_PERCENT      (3)
#define WLAN_POWER_LEVEL_40_PERCENT      (4)
#define WLAN_POWER_LEVEL_20_PERCENT      (5)
#define WLAN_POWER_LEVEL_200_PERCENT     (6)
	uword8  ucPowerlevel; /*IN&OUT  RF power level 20%/40%/60%/80%/100% */
#define WLAN_RF_REGION_CN                (0) /**< China */
#define WLAN_RF_REGION_US                (1) /**< The United States of America */
#define WLAN_RF_REGION_AU                (2) /**< Australia */
#define WLAN_RF_REGION_CA                (3) /**< Canada */
#define WLAN_RF_REGION_IN                (4) /**< India */
#define WLAN_RF_REGION_MA                (5) /**< Morocco */
#define WLAN_RF_REGION_BD                (6) /**< Bangladesh */
#define WLAN_RF_REGION_BO                (7) /**< Bolivia */
#define WLAN_RF_REGION_CU                (8) /**< Cuba */
#define WLAN_RF_REGION_DZ                (9) /**< Algeria */
#define WLAN_RF_REGION_ID                (10) /**< Indonesia */
#define WLAN_RF_REGION_JO                (11) /**< Indonesia */
#define WLAN_RF_REGION_KE                (12) /**< Kenya */
#define WLAN_RF_REGION_LC                (13) /**< Kenya */
#define WLAN_RF_REGION_KP                (14) /**< Korea */
#define WLAN_RF_REGION_MY                (15) /**< Malaysia */
#define WLAN_RF_REGION_RU                (16) /**< Russian Federation */
#define WLAN_RF_REGION_VN                (17) /**< Russian Federation */
#define WLAN_RF_REGION_TH                (18) /**< Thailand */
#define WLAN_RF_REGION_BR                (19) /**< Brazil */
	uword8  ucRegion; /*default: 0(China) */
#define WLAN_GUARD_INTERVAL_0        (0)
#define WLAN_GUARD_INTERVAL_1        (1)
#define WLAN_GUARD_INTERVAL_2        (2)
#define WLAN_GUARD_INTERVAL_3        (3)
    uword8 ucGuardInterval; /* Indicates the guard interval of the corresponding wireless module.
                               For the 5 GHz frequency band, the guard interval can be set to 400 ns or 800 ns.
			       The value 0 indicates 400 ns and the value 1 indicates 800 ns.The default value is 0.
			       Devices that support Wi-Fi 6 should support three guard intervals: 800 ns, 1600 ns, and 3200 ns.
			       The value 1 indicates 800 ns, the value 2 indicates 1600 ns, and the value 3 indicates 3200 ns.
			       The default value is 1. */
    uword32 ulRetryTimeout; /*IN&OUT  Timeout of the wireless module. For the 5 GHz frequency band, data retransmission
                               control based on the maximum delay time is supported. If a data packet fails to be sent
                               after the time threshold is exceeded, the data packet is discarded. The unit is
                               millisecond. default: 20 ms.*/
                            /*follow from hs sal*/
#define WLAN_SSID_MBR_AUTO                           (0)
	uword32 ulMaxBitRate; /*IN&OUT  Maximum upload and download rates for the link*/
#define WLAN_DTIMCYCLE_MIN                           (1)
#define WLAN_DTIMCYCLE_MAX                           (255)
	uword32 ulDTIMCycle; /*IN&OUT  DTIM period, 1 ~ 255, default: 1*/
#define WLAN_BEACONCYCLE_MIN                         (20)
#define WLAN_BEACONCYCLE_MAX                         (1000)
	uword32 ulBeaconCycle; /*IN&OUT  Beacon period, 20 ~ 1000ms, default value: 100*/
#define WLAN_RTSTHLD_MIN                             (1)
#define WLAN_RTSTHLD_MAX                             (2346)
	uword32 ulRTSThld; /*IN&OUT  Sending request, 1 ~ 2346 bytes, default: 2346*/
#define WLAN_FRAGTHLD_MIN                            (256)
#define WLAN_FRAGTHLD_MAX                            (2346)
    uword32 ulFragThld; /*IN&OUT  Fragmentation threshold, 256-2346 bytes, default: 2346*/
#define WLAN_SSID_AUTO_RATE_FALLBACK_DEACTIVATE      (0)
#define WLAN_SSID_AUTO_RATE_FALLBACK_ACTIVATE        (1)
    uword8 ucAutoRateFallBackEnabled; /*IN&OUT  Indicates whether to automatically reduce the transmission rate when the
                                         environment is poor. Value range: true, false. Default: false.*/
    uword8  aucPad[3];
#define WLAN_SSID_BASIC_DTR_STR_LEN                  (64)//(256)
    word8 aucBasicDataTransmitRates[WLAN_SSID_BASIC_DTR_STR_LEN]; /*IN&OUT  Indicates the maximum unicast, multicast,
                                                                     and broadcast data transmission rates of an AP. The
                                                                     values are separated by commas (,). For example,
                                                                     "1, 2" indicates that unicast, multicast, and
                                                                     broadcast frames can be transmitted at 1 Mbit/s and
                                                                     2 Mbit/s.*/
#define WLAN_SSID_OPER_DTR_STR_LEN                   (64)//(256)
    word8 aucOperationalDataTransmitRates[WLAN_SSID_OPER_DTR_STR_LEN]; /*IN&OUT  The AP's maximum unicast, multicast,
                                                                          and broadcast data transfer rates (extended
                                                                          set) are comma-separated.*/
#define WLAN_SSID_REGULATORY_DOMAIN_STR_LEN          (32)//(4)
	word8  aucRegulatoryDomain[WLAN_SSID_REGULATORY_DOMAIN_STR_LEN];      /*IN&OUT  802.11d Management Zone*/
#define WLAN_SSID_LOCATION_DESCRIPTION_STR_LEN       (32)//(4096)
    word8 aucLocationDescription[WLAN_SSID_LOCATION_DESCRIPTION_STR_LEN]; /*IN&OUT  Indicates the name and location of
                                                                             an AP in XML format.*/
#define WLAN_SSID_POSSIBLE_CHANNELS_STR_LEN          (1024)//(1024)
    word8 aucPossibleChannels[WLAN_SSID_POSSIBLE_CHANNELS_STR_LEN]; /*OUT A comma-separated string of possible
                                                                       channels supported by the region and standard,
                                                                       for example, 1 - 11 for 802.11b channels
                                                                       supported in North America.*/
#define WLAN_SSID_POSSIBLE_DTR_STR_LEN               (64)//(256)
	word8 aucPossibleDataTransmitRates[WLAN_SSID_POSSIBLE_DTR_STR_LEN]; /*OUT Rates at which STAs are allowed to
									      transmit unicast frames,
									      separated by commas(,).*/
	uword32 ulWiFiModeSupported; /*OUT     All modes supported by the wireless module*/
#define WLAN_RATEPRIORITY_MODE_DEACTIVATE            (0)
#define WLAN_RATEPRIORITY_MODE_ACTIVATE              (1)
	uword8 ucRatePriority;   /*IN&OUT 2.4G Wi-Fi rate priority mode. The options are true/false. default:false */
	uword8 ucRatePriority5G; /*IN&OUT Indicates whether to enable the 5G Wi-Fi rate priority mode. The options are
					true and false. The default value is false.*/
#define WLAN_SSID_SAMESSID_DISABLED                  (0)
#define WLAN_SSID_SAMESSID_ENABLED                   (1)
	uword8 ucSameSSIDStatus;    /* IN&OUT     SameSSID configuration status */
	uword8 ucSameSSIDIndex;     /* IN&OUT Save the index of the SameSSID pair. */
	uword32 ulWLANForGuestSSID; /* Guest Wi-Fi SSID. One Wi-Fi SSID can be set for each frequency band.
					In dual-band mode, one Wi-Fi SSID can be set for
					the 2.4 GHz and 5 GHz frequency bands respectively. */
#define WLAN_POWER_CHANGE_DISABLE                    (0)
#define WLAN_POWER_CHANGE_ENABLE                     (1)
	uword8  ucPowerChangeEnable;
	uword8  ucPowerBeforeChange;
#define WLAN_THROUGH_WALLS_DISABLE                   (0)
#define WLAN_THROUGH_WALLS_ENABLE                    (1)
	uword8  ucPowerThroughWalls;
	uword8 ssidNum;                /* Number of SSIDs in the current frequency band */
#define WLAN_FLOOR_NOISE_DEFAULT_VALUE               (32767)
	word32 lNoise;                 /* Noise floor of the current channel (dBm) */
	uword32 ulInterferencePercent; /* Interference signal duty ratio of the current channel (%) */
	uword32 ulBeaconFail;          /* Number of Beacon Frame Failures */
	uint32_t channel_scan_status;
	uint32_t ucChannelCollectionRequest;
	uint32_t enWifiQualityCollection; /* Specifies the Wi-Fi performance parameters to be collected, 32 bits totally */
#define WLAN_CHANNEL_SCAN_FINISH_TIME          (32)
	word8 aucChannelScanFinishTime[WLAN_CHANNEL_SCAN_FINISH_TIME];
#define WLAN_CHANNEL_COLLECTION          (512)
	word8 aucChannelCollection[WLAN_CHANNEL_COLLECTION];
	uint32_t ucChannelSwitch;

#define WLAN_GLOBAL_CFG_ATTR_MASK_APMODULEENABLE       (1<<0)
#define WLAN_GLOBAL_CFG_ATTR_MASK_RFBAND               (1<<1)
#define WLAN_GLOBAL_CFG_ATTR_MASK_CHANNEL              (1<<2)
#define WLAN_GLOBAL_CFG_ATTR_MASK_CHANNELWIDTH         (1<<3)
#define WLAN_GLOBAL_CFG_ATTR_MASK_RFMODE               (1<<4)
#define WLAN_GLOBAL_CFG_ATTR_MASK_POWERLEVEL           (1<<5)
#define WLAN_GLOBAL_CFG_ATTR_MASK_REGION               (1<<6)
#define WLAN_GLOBAL_CFG_ATTR_MASK_GUARDINTERVAL        (1<<7)
#define WLAN_GLOBAL_CFG_ATTR_MASK_RETRYTIMEOUT         (1<<8)
#define WLAN_GLOBAL_CFG_ATTR_MASK_MAXBITRATE           (1<<9)
#define WLAN_GLOBAL_CFG_ATTR_MASK_ARFENABLED           (1<<10)
#define WLAN_GLOBAL_CFG_ATTR_MASK_BASICDTR             (1<<11)
#define WLAN_GLOBAL_CFG_ATTR_MASK_OPERDTR              (1<<12)
#define WLAN_GLOBAL_CFG_ATTR_MASK_REGULATORYDOMAIN     (1<<13)
#define WLAN_GLOBAL_CFG_ATTR_MASK_LOCATIONDESC         (1<<14)
#define WLAN_GLOBAL_CFG_ATTR_MASK_DTIMCYCLE            (1<<15)
#define WLAN_GLOBAL_CFG_ATTR_MASK_BEACONCYCLE          (1<<16)
#define WLAN_GLOBAL_CFG_ATTR_MASK_RTSTHLD              (1<<17)
#define WLAN_GLOBAL_CFG_ATTR_MASK_FRAGTHLD             (1<<18)
#define WLAN_GLOBAL_CFG_ATTR_MASK_RATEPRIORITY         (1<<19)
#define WLAN_GLOBAL_CFG_ATTR_MASK_RATEPRIORITY5G       (1<<20)
#define WLAN_GLOBAL_CFG_ATTR_MASK_SAMESSIDSTATUS       (1<<21)
#define WLAN_GLOBAL_CFG_ATTR_MASK_WLANGUESTSSID        (1<<22)
#define WLAN_GLOBAL_CFG_ATTR_MASK_APSTATUS             (1<<23)
#define WLAN_GLOBAL_CFG_ATTR_MASK_POWER_CHANGE_ENABLE  (1<<24)
#define WLAN_GLOBAL_CFG_ATTR_MASK_POWER_THROUGH_WALLS  (1<<25)
#define WLAN_GLOBAL_CFG_ATTR_MASK_NOISE                (1<<28)
#define WLAN_GLOBAL_CFG_ATTR_MASK_INTERFERENCE_PERCENT (1<<29)
#define WLAN_GLOBAL_CFG_ATTR_MASK_BEACON_FAIL          (1<<30)
#define WLAN_GLOBAL_CFG_ATTR_MASK_CHANNEL_SCAN_STA     (1<<31)
	uword32 ulBitmap;

#define WLAN_GLOBAL_CFG_ATTR_MASK2_WIFI_QUALITY_COLLECTION     (1<<0)
#define WLAN_GLOBAL_CFG_ATTR_MASK_CHANNELCOLLREQ               (1<<1)
#define WLAN_GLOBAL_CFG_ATTR_MASK_CHANNELSWITCH                (1<<2)
	uword32 ulBitmap1;
} __PACK__ IgdWLANGlobalAttrCfgTab;

/***************************无线网络状态信息属性表************************************/
typedef struct {
	uword32 ulStateAndIndex;
#define WLAN_CONN_DISABLED            (0)
#define WLAN_CONN_ENABLED             (1)
	uword8  ucConnStatus;      /*OUT  无线网络连接状态*/
	uword8  ucChannelInUse;    /*OUT  实际工作信道*/
	uword32  ucPowerValue;      /*OUT  无线模块发射功率最大值 单位：0.1dBm*/
	uword8  ucRFBand;          /*IN&OUT  频段,0：表示2.4GHz，1：表示5GHz，缺省为0*/

#define WLAN_CUR_BW_CAP_20M            (0)
#define WLAN_CUR_BW_CAP_40M            (1)
#define WLAN_CUR_BW_CAP_80M            (2)
#define WLAN_CUR_BW_CAP_160M           (3)
#define WLAN_CUR_BW_CAP_80PLUS80M      (4)
	uword8  ucCurChannelWidth; /*OUT  实际频宽：0:HT20/40 1:HT20 2:HT40 3:HT80 4:HT160 5:HT80+80 */
	uword8  ucIdlePercent;        /*空闲占空比，百分比值*/
	uword8  ssidNum;              /* 频段内的SSID个数 */
	uword8  ucWiFiInfoCollect;    /* 触发信息收集的动作 IN\OUT */
	uword32 ulRecvPackets;    /*接收到的报文数*/
	uword32 ulSendPackets;    /*发送的报文数*/
	uword32 ulRecvErrorPackets; /*接收到错误的包数*/
	uword32 ulBeaconFails;      /*beacon 发送失败的次数，默认为0，暂不支持获取*/
#define WLAN_RADIO_INFO_LEN            (8)
	word8  cNoiseValue[WLAN_RADIO_INFO_LEN];       /*OUT 底噪，单位：dBm*/
	word8  uInterferentPercent[WLAN_RADIO_INFO_LEN]; /*干扰占空比,百分比*/
#define WLAN_PROCESS_LIST (128)
	/*WIFI核心线程和进程格式: name,pid,isProcess;用";"隔开不同实例isProcess: 0:进程flase: 内核线程*/
	word8  aucWiFiProceccList[WLAN_PROCESS_LIST];
} __PACK__ IgdWLANGlobalInfoAttrTab;

/***************************无线网络包统计属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;              /*IN&OUT  SSID实例索引*/
	uword32 ulSSIDIndex;              /*IN&OUT  SSID索引*/
	uint64_t ulTotalBytesReceived;     /*OUT  接收字节数*/
	uword32 ulTotalPacketsReceived;   /*OUT  接收帧数*/
	uint64_t ulTotalBytesSent;         /*OUT  发送字节数*/
	uword32 ulTotalPacketsSent;       /*OUT  发送帧数*/
	uword32 ulErrorPacketsReceived;   /*OUT  接收错误帧数*/
	uword32 ulDroppedPacketsReceived; /*OUT  接收丢弃帧数*/
	uword32 ulErrorPacketsSent;       /*OUT  发送错误帧数*/
	uword32 ulDroppedPacketsSent;     /*OUT  发送丢弃帧数*/
} __PACK__ IgdWLANSsidPktStatTab;

/***************************无线SSID属性表*********************************/
#define IGD_WLAN_SSID_CFG_ATTR_TAB_MAX (HI_CM_BAND_SSID_MAX * IGD_WLAN_GLOBAL_CFG_ATTR_NUM)
#define IGD_WLAN_SSID_CFG_ATTR_NUM HI_CM_BAND_SSID_MAX
#define CONFIG_NC_WPS
#define CONFIG_NC_SSID_BINDING
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;                                   /*IN&OUT  实例索引*/
	uword32 ulSSIDIndex;                                   /*IN&OUT  SSID索引*/
#define WLAN_SSID_NAME_STR_LEN                             (36)
	word8  aucSSIDName[WLAN_SSID_NAME_STR_LEN];           /*IN&OUT  SSID名称，无线网络名称（如ChinaNet-xxxx）*/
#define WLAN_SSID_DEACTIVATE                               (0)
#define WLAN_SSID_ACTIVATE                                 (1)
	uword8  ucSSIDEnable;                                  /*IN&OUT  SSID启用或关闭对应,缺省值：false*/
#define WLAN_SSID_HIDE_DEACTIVATE                          (0)
#define WLAN_SSID_HIDE_ACTIVATE                            (1)
	uword8  ucSSIDHide;                                    /*IN&OUT  隐藏无线网络名称,缺省值：false*/
	uword8  ulMLDIndex;                                   /* 新增MLD ID，从0开始的数字 */
	uword8  ucMLDEnable;                                 /* 新增MLD开关 */
	/*ucAuthType=OPEN,ucEncryptType=NONE对应页面“安全配置”为“未配置”，其他为“已配置”*/
#define WLAN_SSID_NC_AUTH_TYPE_OPEN                   (0)  /*加密模式可以选NONE，WEP*/
#define WLAN_SSID_NC_AUTH_TYPE_SHARED                 (1)  /*加密模式只能选WEP*/
#define WLAN_SSID_NC_AUTH_TYPE_WPAPSK                 (2)
#define WLAN_SSID_NC_AUTH_TYPE_WPA2PSK                (3)
#define WLAN_SSID_NC_AUTH_TYPE_WPA_WPA2_PSK           (4)
#define WLAN_SSID_NC_AUTH_TYPE_WPA_ENTERPRISE         (5)
#define WLAN_SSID_NC_AUTH_TYPE_WPA2_ENTERPRISE        (6)
#define WLAN_SSID_NC_AUTH_TYPE_WPA_WPA2_ENTERPRISE    (7)
#define WLAN_SSID_NC_AUTH_TYPE_WPS                    (8)
#define WLAN_SSID_NC_AUTH_TYPE_WPA3SAE                    (9)
#define WLAN_SSID_NC_AUTH_TYPE_WPA2_WPA3                 (10)
	uword8  ucAuthType;                             /*IN&OUT  WiFi认证模式，参考HW页面扩展*/
#define WLAN_SSID_NC_ENC_MODE_NONE                    (0)
#define WLAN_SSID_NC_ENC_MODE_WEP                     (1)
#define WLAN_SSID_NC_ENC_MODE_TKIP                    (2)
#define WLAN_SSID_NC_ENC_MODE_AES                     (3)
#define WLAN_SSID_NC_ENC_MODE_TKIP_AES                (4)
#define WLAN_SSID_NC_ENC_MODE_CCMP                    (5)
#define WLAN_SSID_NC_ENC_MODE_TKIP_CCMP               (6)

	uword8  ucEncryptType;                          /*IN&OUT  WiFi加密模式，参考HW页面扩展*/
	uword8  aucPad2[2];

#define WLAN_SSID_NC_WPS_TYPE_OFF                     (0)  /*OFF*/
#define WLAN_SSID_NC_WPS_TYPE_PBC                     (1)  /*PBC*/
#define WLAN_SSID_NC_WPS_TYPE_PIN                     (3)  /*Client PIN*/
#define WLAN_SSID_NC_WPS_TYPE_AP_PIN                  (2)  /*AP PIN*/
	uword8  ucWPSType;                              /*IN&OUT  WiFi加密模式，参考HW页面扩展*/
	uword8  aucPad3[3];
#define WLAN_SSID_NC_PIN_LEN                          (16)
	word8  ucPINAP[WLAN_SSID_NC_PIN_LEN];     /*IN&OUT  本地PIN码  参考HW页面输入8位整数*/
	word8  ucPINSTA[WLAN_SSID_NC_PIN_LEN];    /*IN&OUT  客户端PIN码 参考HW页面输入8位整数*/

#define WLAN_SSID_NC_RADIUS_IPDNS_STR_LEN             (64)
	word8  aucRadiusServerIP[WLAN_SSID_NC_RADIUS_IPDNS_STR_LEN];  /*IN&OUT  EAP radius server addr*/
	uword32 ulRadiusServerPort;                     /*IN&OUT  EAP radius server port*/
#define WLAN_SSID_NC_RADIUS_SHAREDKEY_STR_LEN         (64)
	word8  aucRadiusSharedKey[WLAN_SSID_NC_RADIUS_SHAREDKEY_STR_LEN]; /*IN&OUT  EAP radius shared key*/

#define WLAN_SSID_BEACON_TYPE_NONE                         (0)  /*None（不加密，对应的BasicAuthenticationMode为OpenSystem）*/
#define WLAN_SSID_BEACON_TYPE_WEP                          (1)  /*Basic（对应WEP加密，对应的BasicAuthenticationMode默认值为Both）*/
#define WLAN_SSID_BEACON_TYPE_WPA                          (2)  /*WPA*/
#define WLAN_SSID_BEACON_TYPE_WPA2                         (3)  /*11i（WPA2）*/
#define WLAN_SSID_BEACON_TYPE_WPA_WPA2                     (4)  /*WPA/11i*/
#define WLAN_SSID_BEACON_TYPE_WPA3                        (5)  /*WPA3*/
#define WLAN_SSID_BEACON_TYPE_WPA2_WPA3                        (6)  /*WPA3*/
	uword8  ucBeaconType;                                  /*IN&OUT  加密安全模式*/
	uword8  aucPad5[3];
#define WLAN_SSID_BASIC_AUTH_MODE_OPEN                     (0)
#define WLAN_SSID_BASIC_AUTH_MODE_SHARE                    (1)
#define WLAN_SSID_BASIC_AUTH_MODE_BOTH                     (2)
	uword8  ucBasicAuthMode;                               /*IN&OUT  基本认证模式,当BeaconType取值为WEP(basic)时，该参数有效*/
#define WLAN_SSID_BASIC_ENC_MODE_NONE                      (0)
#define WLAN_SSID_BASIC_ENC_MODE_WEP                       (1)
	uword8  ucBasicEncryptMode;                            /*IN&OUT  基本加密模式,当BeaconType取值为WEP(basic)时，该参数有效*/
#define WLAN_SSID_WPA_AUTH_MODE_PSK                        (1)
#define WLAN_SSID_WPA_AUTH_MODE_SAE                        (2)
#define WLAN_SSID_WPA_AUTH_MODE_PSK_SAE                    (3)
	uword8  ucWPAAuthMode;                                 /*IN&OUT  WPA认证模式,1：PSK。当BeaconType取值为WPA、WPA2/11i时，该参数有效。*/
#define WLAN_SSID_WPA_ENC_MODE_AES                         (1)
#define WLAN_SSID_WPA_ENC_MODE_TKIP                        (2)
#define WLAN_SSID_WPA_ENC_MODE_TKIP_AES                    (3)
#define WLAN_SSID_WPA_ENC_MODE_CCMP                        (4)
	uword8  ucWPAEncryptMode;                              /*IN&OUT  WPA加密模式,默认值为 3,当BeaconType取值为WPA、WPA/11i时，该参数有效。*/

#define WLAN_SSID_WEP_ENC_LEVEL_DISABLED                   (0)
#define WLAN_SSID_WEP_ENC_LEVEL_40BIT                      (1)  /*64bits*/
#define WLAN_SSID_WEP_ENC_LEVEL_104BIT                     (2)  /*128bits*/
	uword8  ucWEPEncryptionLevel;                          /*IN&OUT  WEP密钥长度,当BeaconType取值为basic时，该参数有效。*/
#define WLAN_SSID_WPA_ENC_LEVEL_64BIT                      (1)
#define WLAN_SSID_WPA_ENC_LEVEL_128BIT                     (2)
	uword8  ucWPAEncryptionLevel;                          /*IN&OUT  WPA密钥长度,128位无线网络密钥需输入13个ASCII字符或26个十六进制数。 --hs no use*/
	uword8  aucPad6[2];
#define WLAN_SSID_WPA_UPDATE_TIME_MIN                      (600)
#define WLAN_SSID_WPA_UPDATE_TIME_MAX                      (86400)
	uword32 ulWPAUpdateTime;                               /*IN&OUT  WPA密钥更新时间, 600~86400秒(参考HW页面)*/

	uword8  ucWEPKeyIndex;                                 /*IN&OUT  默认WEP密钥的索引*/
	uword8  aucPad7[3];
#define WLAN_SSID_WEP_KEY_INDEX_MAX                        (4)
#define WLAN_SSID_WEP_KEY_STR_LEN                          (128)
	word8  aucWEPKey[WLAN_SSID_WEP_KEY_INDEX_MAX][WLAN_SSID_WEP_KEY_STR_LEN]; /*IN&OUT  WEP密钥表 十六进制字符串值，可以通过KeyPassphrase生成，不能与KeyPassphrase同时设置。*/
#define WLAN_SSID_KEY_PASSPHRASE_INDEX_MAX                 (10)
#define WLAN_SSID_KEY_PASSPHRASE_STR_LEN                   (64)
	word8  aucKeyPassphrase[WLAN_SSID_KEY_PASSPHRASE_INDEX_MAX][WLAN_SSID_KEY_PASSPHRASE_STR_LEN];   /*IN&OUT  WPA的预共享密钥表*/
#define WLAN_SSID_PSK_INDEX_MAX                            (10)
#define WLAN_SSID_PSK_STR_LEN                              (64)
	word8  aucPreSharedKey[WLAN_SSID_PSK_INDEX_MAX][WLAN_SSID_PSK_STR_LEN];  /*IN&OUT  WEP或WPA的密码,WEP方式使用KeyPassphrase[0]-[3]，WPA方式使用KeyPassphrase[0]-[9]*/
#define WLAN_SSID_PSK_STA_MAC_INDEX_MAX                    (10)
	word8  aucAssociatedDeviceMACAddress[WLAN_SSID_PSK_STA_MAC_INDEX_MAX][WLAN_AP_STA_MACADDR_STR_LEN]; /*IN&OUT  关联到到WPA预共享密钥表的客户端设备MAC地址 --hs no use*/

#define WLAN_SSID_MACADDRCTRL_DEACTIVATE                   (0)
#define WLAN_SSID_MACADDRCTRL_ACTIVATE                     (1)
	uword8  ucMACAddrCtrl;                                 /*IN&OUT  MAC地址过滤,取值范围(true,false)，缺省值：false。(调用安全模块的API) --hs no use*/
#define WLAN_SSID_WMM_DEACTIVATE                           (0)
#define WLAN_SSID_WMM_ACTIVATE                             (1)
	uword8  ucWMMEnable;                                                   /*IN&OUT  开启WMM,无线多媒体,QoS 取值范围(true,false)，缺省值：false。*/
#define WLAN_SSID_AIRTIMEFAIR_DISABLE                      0
#define WLAN_SSID_AIRTIMEFAIR_ENABLE                       1
#define WLAN_SSID_AIRTIMEFAIR_NONE                         2
	uword8 ucAirtimeFairEnable;
	uword8  ucPad8;
#define WLAN_SSID_INSECURE_OBB_ACCESS_DEACTIVATE           (0)
#define WLAN_SSID_INSECURE_OBB_ACCESS_ACTIVATE             (1)
	uword8  ucInsecureOOBAccessEnabled;                    /*IN&OUT  不安全的OOB访问 取值范围(true,false)，缺省值：false。 --hs no use*/
#define WLAN_SSID_BEACON_AD_DEACTIVATE                     (0)
#define WLAN_SSID_BEACON_AD_ACTIVATE                       (1)
	uword8  ucBeaconAdvertisementEnabled;	               /*IN&OUT  Beacon帧通告 取值范围(true,false)，缺省值：false。*/
#define WLAN_SSID_RADIO_DEACTIVATE                         (0)
#define WLAN_SSID_RADIO_ACTIVATE                           (1)
	uword8  ucRadioEnabled;		                           /*IN&OUT  AP广播 取值范围(true,false)，缺省值：false。*/
#define WLAN_SSID_ASSOCIATED_DEV_NUM_MAX                   (128)
	uword8  ucAssociatedDevNum;                            /*IN&OUT  连接设备数目*/
	word8  aucPeerBSSID[WLAN_AP_STA_MACADDR_STR_LEN];     /*IN&OUT  Repeater或者Bridge模式下的MAC地址对 --hs no use*/

#define WLAN_SSID_BIND_MODE_PORT                           (0)
#define WLAN_SSID_BIND_MODE_VLAN                           (1)
	uword8  ucSSIDBindMode;                                /*IN&OUT  绑定模式,0：通过端口和WAN连接绑定；1：通过VLAN和WAN连接绑定,缺省为:0 */
	uword8  aucPad9[3];
#define WLAN_SSID_VLAN_BINDING_STR_LEN                     (256)
	word8  aucVlanBinding[WLAN_SSID_VLAN_BINDING_STR_LEN];/*IN&OUT  “m1/n1,m2/n2”方式设置，其中”m”为接收到的VLAN，“n”为映射到的目标VLAN*/

#define WLAN_SSID_CONN_DISABLED                            (0)
#define WLAN_SSID_CONN_ENABLED                             (1)
	uword8  ucSSIDStatus;                                  /*OUT     SSID连接状态*/
	uword8  aucPad10[3];
	word8  aucSSIDMACAddr[WLAN_AP_STA_MACADDR_STR_LEN];   /*OUT     SSID对应的MAC地址*/
	uword32 ulTotalPSKFailures;                            /*OUT     PSK认证失败次数,WPA或11i有效 */
	uword32 ulTotalIntegrityFailures;                      /*OUT     MICHAEL完整性检查失败次数,WPA或11i有效*/

#define WLAN_SSID_FILTER_DISABLED				   (0)
#define WLAN_SSID_FILTER_BLACKLIST				   (1)
#define WLAN_SSID_FILTER_WHITELIST				   (2)
	uword32 ulFilterMode;
#define WLAN_SSID_WPS_DISABLED                             (0)
#define WLAN_SSID_WPS_ENABLED                              (1)
	uword8  ucWPSEnable;                                   /*IN&OUT  WPS功能开关*/
#define WLAN_SSID_WPS_STATUS_PROCESS                       (0)       /*表示已进入WPS配置状态，但设备尚未与网关进行WPS交互*/
#define WLAN_SSID_WPS_STATUS_SETUP_FAILED                  (1)       /*表示WPS启动失败*/
#define WLAN_SSID_WPS_STATUS_CONNECTED                     (2)       /*表示已有设备通过WPS接入网络*/
#define WLAN_SSID_WPS_STATUS_TIMEOUT                       (3)       /*表示WPS超时关闭，未有设备连接*/
	uword8  ucWPSStatus;                                   /*OUT     WPS启动、配置状态*/
	uword8  aucPad11[2];
#define WLAN_SSID_WPS_HOLDSECONDS_DEF                      (120)      /*wps持续时间默认值(秒)*/
#define WLAN_SSID_WPS_HOLDSECONDS_MIN                      (60)       /*wps持续时间最小值(秒)*/
#define WLAN_SSID_WPS_HOLDSECONDS_MAX                      (300)      /*wps持续时间最大值(秒)*/
	uword32 ulWPSHoldSeconds;                              /*IN      WPS开启持续时间，参数绑定SetWPS*/
#define WLAN_WPS_CONNECTED_DEVINFO_STR_LEN                 (48)
	word8  aucWPSConnDevInfo[WLAN_WPS_CONNECTED_DEVINFO_STR_LEN];   /*OUT     表示本次通过WPS接入网络的设备信息*/
#define WLAN_SSID_KICKOUTDEVICE_DEACTIVATE                 (0)
#define WLAN_SSID_KICKOUTDEVICE_ACTIVATE                   (1)
	uword8  ucKickOutDevice;                               /*IN      触发KickOutDevice动作开关，开true/关false，默认false*/
	uword8  aucPad12[3];
	word8  aucKickoutDeviceMACAddr[CM_MAC_CHAR_LEN];      /*IN     KickOutDevice动作对象的MAC地址*/
#define WLAN_SSID_PORT_ISO_DEACTIVATE                      (0)
#define WLAN_SSID_PORT_ISO_ACTIVATE                        (1)
	uword8  ucPortIsolation;                               /*IN&OUT  与其他SSID和LAN是否隔离 取值范围(true,false)，缺省值：true*/
#define WLAN_SSID_CLIENTS_RELAY                            (0)
#define WLAN_SSID_CLIENTS_BLOCK_RELAY_DROP                 (1)
#define WLAN_SSID_CLIENTS_BLOCK_RELAY_TO_BRIDGE            (2)
	uword8  ucBlockRelay;                                  /*IN&OUT  下挂STA间隔离模式*/
#define WLAN_SSID_TYPE_NOMAL                               (0)
#define WLAN_SSID_TYPE_GUESTLY                             (1)
#define WLAN_SSID_TYPE_SHARED                              (2)
#define WLAN_SSID_TYPE_QUICK_LINK                          (3)
#define WLAN_SSID_TYPE_ANDLINK_ONLY                        (4)
	uword8  ucSSIDtype;                                     /*IN&OUT  SSID的类型:普通,访客，共享，andlink设备快连*/
	uword8  ucPad13;
	uword32 ulDuration;                                    /*IN&OUT  SSID开放时长(单位分钟，0表示无限时长)*/
	uword32 ulRemainDuration;                              /*IN&OUT  SSID剩余开放时长*/
#define WLAN_SSID_ALIAS_LEN                                (32)
	word8  aucAliasName[WLAN_SSID_ALIAS_LEN];          /*OUT alias name*/
#define WLAN_SSID_SERVICE_LEN                              (32)
	word8  aucSsidService[WLAN_SSID_SERVICE_LEN];         /*IN&OUT ssid service*/
#define WLAN_SSID_ACCESSRULE_DEFAULT                       (0) /* wan连接缺省权限*/
#define WLAN_SSID_ACCESSRULE_ONLY_INTERNET                 (1) /* 只能通过internet wan连接公网，隔离本地网络 */
#define WLAN_SSID_ACCESSRULE_ALLOWEDIPPORT                 (2) /* 只能访问固定IP地址的固定端口 */
	uword32 ulAccessRule;                                  /*IN&OUT access rule*/
#define WLAN_SSID_ALLOWED_IPPORT_LEN                       (256)
	word8   aucAllowdIPPORT[WLAN_SSID_ALLOWED_IPPORT_LEN]; /*IN&OUT access rule ip&port */
#define WLAN_SSID_OWNER_LEN                                 8
	word8   aucSSIDOwner[WLAN_SSID_OWNER_LEN];              /*IN&OUT ssid owner */
#define WLAN_SSID_SLEEP_SET_OFF                        (0)
#define WLAN_SSID_SLEEP_SET_ON                         (1)
	uword32 ulSleepSetState;                                    /*是否是休眠配置wifi的enable*/
#define WLAN_MULTI_AP_DISABLED      (0)
#define WLAN_MULTI_AP_BACKHAUL_BSS  (1)
#define WLAN_MULTI_AP_FRONTHAUL_BSS (2)
	uword32 ulMultiAp;                                          /* multi-ap功能,0:disabled 1:bBSS 2:fBSS 3:both bBSS and fBSS */
	word8   aucBackhaulSSID[WLAN_SSID_NAME_STR_LEN];            /*IN&OUT  Backhaul SSID名称，回程无线网络名称（如backhaul-xxxx）*/
	word8   aucBackhaulPwd[WLAN_SSID_PSK_STR_LEN];              /*IN&OUT  回程BSS密码*/
#define WLAN_PMF_DISABLED                             (0)
#define WLAN_PMF_ENABLED                              (1)
	uword32  ulPmfEnable;
	uword32 ulDismissTime;
	uword32 ul_usbandwidth;
	uword32 ul_dsbandwidth;
#define WLAN_FT_ROAMING_DISABLED (0)
#define WLAN_FT_ROAMING_ENABLED  (1)
    uword8 ft_en;             /**< FT快速漫游使能*/
#define WLAN_FT_ROAMING_OVER_DS  (0)
    uword8 ft_over_ds;        /**< FT over the ds支持，默认0*/
#define WLAN_FT_ROAMING_REASSOC_DEADLINE  (1000)
    uword32 reassoc_deadline; /**< 重关联门限值，默认1000*/
    word8 ft_md[8];           /**< MDID配置值*/
    word8 nas_id[52];         /**< PMK-R0KH标识符*/
#define WLAN_SSID_CFG_ATTR_MASK_SSIDNAME                   (1<<0)
#define WLAN_SSID_CFG_ATTR_MASK_SSIDENABLE                 (1<<1)
#define WLAN_SSID_CFG_ATTR_MASK_SSIDHIDE                   (1<<2)
#define WLAN_SSID_CFG_ATTR_MASK_NCAUTHTYPE                 (1<<3)
#define WLAN_SSID_CFG_ATTR_MASK_NCENCRYPTTYPE              (1<<4)
#define WLAN_SSID_CFG_ATTR_MASK_NCWPSTYPE                  (1<<5)
#define WLAN_SSID_CFG_ATTR_MASK_NCPINAP                    (1<<6)
#define WLAN_SSID_CFG_ATTR_MASK_NCPINSTA                   (1<<7)
#define WLAN_SSID_CFG_ATTR_MASK_BEACONTYPE                 (1<<8)
#define WLAN_SSID_CFG_ATTR_MASK_BASICAUTHMODE              (1<<9)
#define WLAN_SSID_CFG_ATTR_MASK_BASICENCRYPTMODE           (1<<10)
#define WLAN_SSID_CFG_ATTR_MASK_WPAAUTHMODE                (1<<11)
#define WLAN_SSID_CFG_ATTR_MASK_WPAENCRYPTMODE             (1<<12)
#define WLAN_SSID_CFG_ATTR_MASK_WEPENCRYPTIONLEVEL         (1<<13)
#define WLAN_SSID_CFG_ATTR_MASK_WPAENCRYPTIONLEVEL         (1<<14)
#define WLAN_SSID_CFG_ATTR_MASK_WPAUPDATETIME              (1<<15)
#define WLAN_SSID_CFG_ATTR_MASK_WEPKEYINDEX                (1<<16)
#define WLAN_SSID_CFG_ATTR_MASK_WEPKEY                     (1<<17)
#define WLAN_SSID_CFG_ATTR_MASK_KEYPASSPHRASE              (1<<18)
#define WLAN_SSID_CFG_ATTR_MASK_PRESHAREDKEY               (1<<19)
#define WLAN_SSID_CFG_ATTR_MASK_STAMAC                     (1<<20)
#define WLAN_SSID_CFG_ATTR_MASK_MACADDRCTRL                (1<<21)
#define WLAN_SSID_CFG_ATTR_MASK_WMMENABLE                  (1<<22)
#define WLAN_SSID_CFG_ATTR_MASK_OOBACCESSENABLED           (1<<23)
#define WLAN_SSID_CFG_ATTR_MASK_ADENABLED                  (1<<24)
#define WLAN_SSID_CFG_ATTR_MASK_RADIOENABLED               (1<<25)
#define WLAN_SSID_CFG_ATTR_MASK_ASSOCIATEDDEVNUM           (1<<26)
#define WLAN_SSID_CFG_ATTR_MASK_SSIDBINDMODE               (1<<27)
#define WLAN_SSID_CFG_ATTR_MASK_VLANBINDING                (1<<28)
#define WLAN_SSID_CFG_ATTR_MASK_NCRADIUSSERVERIP           (1<<29)
#define WLAN_SSID_CFG_ATTR_MASK_NCRADIUSSERVERPORT         (1<<30)
#define WLAN_SSID_CFG_ATTR_MASK_FILTER_MODE       		   ((uword32)1<<31)

	uword32 ulBitmap;

#define WLAN_SSID_CFG_ATTR_MASK_WPSHOLDSECOND              (1<<0)
#define WLAN_SSID_CFG_ATTR_MASK_KICKOUTDEVICE              (1<<1)
#define WLAN_SSID_CFG_ATTR_MASK_KICKOUT_DEVICEMAC          (1<<2)
#define WLAN_SSID_CFG_ATTR_MASK_PORTISOLATION              (1<<3)
#define WLAN_SSID_CFG_ATTR_MASK_BLOCKRELAY                 (1<<4)
#define WLAN_SSID_CFG_ATTR_MASK_SSIDTYPE                   (1<<5)
#define WLAN_SSID_CFG_ATTR_MASK_DURATION                   (1<<6)
#define WLAN_SSID_CFG_ATTR_MASK_WPSENABLE                  (1<<7)

#define WLAN_SSID_CFG_ATTR_MASK_SSIDSTATUS                 (1<<8)
#define WLAN_SSID_CFG_ATTR_MASK_SERVICE                    (1<<9)
#define WLAN_SSID_CFG_ATTR_MASK_ACCESSRULE                 (1<<10)
#define WLAN_SSID_CFG_ATTR_MASK_ALLOWEDIPPORT              (1<<11)
#define WLAN_SSID_CFG_ATTR_MASK_OWNER                      (1<<12)
#define WLAN_SSID_CFG_ATTR_MASK_SLEEPSET                   (1<<13)

#define WLAN_SSID_CFG_ATTR_MASK_MULTIAP                    (1<<14)
#define WLAN_SSID_CFG_ATTR_MASK_MULTIAP_B_SSID             (1<<15)
#define WLAN_SSID_CFG_ATTR_MASK_MULTIAP_B_PWD              (1<<16)
#define WLAN_SSID_CFG_ATTR_MASK_PMFENABLE                  (1<<17)
#define WLAN_SSID_CFG_ATTR_MASK_DISMISSTIME                (1<<18)
#define WLAN_SSID_CFG_ATTR_MASK_AIRTIMEFAIR_ENABLE         (1<<19)
#define WLAN_SSID_CFG_ATTR_MASK_AIRTIMEFAIR_MLDINDEX       (1<<20)
#define WLAN_SSID_CFG_ATTR_MASK_AIRTIMEFAIR_MLDENABLE      (1<<21)
#define WLAN_SSID_CFG_ATTR_MASK_US_BANDWIDTH               (1<<22)
#define WLAN_SSID_CFG_ATTR_MASK_DS_BANDWIDTH               (1<<23)

#define WLAN_SSID_CFG_ATTR_MASK_FT                         (1<<24)
	uword32 ulBitmap1;
} __PACK__ IgdWLANSsidAttrCfgTab;

/* IGD_WLAN_SSID_ENTRY_TAB */
typedef struct {
	uword32 ulStateAndIndex;
#define WLAN_SSID_CFG_SSIDINDEX_MAX        (16)
	uword32 ulSSIDIndex;                                   /*IN&OUT  SSID索引*/
#define WLAN_SSID_CFG_ENTRY_MASK_SSID1     (1<<0)
#define WLAN_SSID_CFG_ENTRY_MASK_SSID2     (1<<1)
#define WLAN_SSID_CFG_ENTRY_MASK_SSID3     (1<<2)
#define WLAN_SSID_CFG_ENTRY_MASK_SSID4     (1<<3)
#define WLAN_SSID_CFG_ENTRY_MASK_SSID5     (1<<4)
#define WLAN_SSID_CFG_ENTRY_MASK_SSID6     (1<<5)
#define WLAN_SSID_CFG_ENTRY_MASK_SSID7     (1<<6)
#define WLAN_SSID_CFG_ENTRY_MASK_SSID8     (1<<7)
#define WLAN_SSID_CFG_ENTRY_MASK_SSID9     (1<<8)
#define WLAN_SSID_CFG_ENTRY_MASK_SSID10    (1<<9)
	uword32 ulSsidBitmap;                                  /*OUT     SSID预置表*/
	uword8 deviceBitmap[WLAN_SSID_CFG_SSIDINDEX_MAX];
} __PACK__ IgdWLANSsidEntryCfgTab;

#define WLAN_SSID_CFG_ENTRY_INIT_BITMAP0 0xe04ba0ff
#define WLAN_SSID_CFG_ENTRY_INIT_BITMAP1 0x20078
/* IGD_WLAN_MLD_ATTR_TAB */
typedef struct {
	uword32 ulStateAndIndex;
	uword8  ulMLDIndex;		// MLD ID
#define MLD_DEACTIVATE		(0)
#define MLD_ACTIVATE		(1)
	uword8  ucMLDEnable; 		// MLD开关
	uword8  ulSSIDIndex_2g;		// 2.4G MLD link VAP ID
	uword8  ulSSIDIndex_5g;		// 5G MLD link VAP ID
#define WLAN_MLD_ATTR_MASK_MLDINDEX                        (1<<0)
#define WLAN_MLD_ATTR_MASK_MLDENABLE                       (1<<1)
#define WLAN_MLD_ATTR_MASK_SSID2                           (1<<2)
#define WLAN_MLD_ATTR_MASK_SSID5                           (1<<3)
	uword32 ulBitmap;
} __PACK__ IgdWLANMldAttrTab;


typedef struct {
#define ROAMING_OPTIMIZATION_DEACTIVATE		(0)
#define ROAMING_OPTIMIZATION_ACTIVATE		(1)
	uword8  roaming_optimization_enable; 		// 漫游优化开关
	uword8  roaming_optimization_2g_ssidindex;		// 漫游优化 2.4G SSID
	uword8  roaming_optimization_5g_ssidindex;		// 漫游优化 5G SSID
#define WLAN_ATTR_MASK_ROAMING_OPTIMIZATION_ENABLE            (1<<0)
	uword32 bit_map;
} __PACK__ igd_wlan_roaming_optimization_attr_tab;

/***************************无线AP关联的当前设备的信息属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;                                                    /* IN&OUT 索引*/
	uword32 ulSSIDIndex;
	uword32 ulAssocStatus;                                              /*设备关联状态*/
	uword32 ulChannelInUse;                                             /*OUT     SSID工作信道*/
	word8  aucAssociatedDeviceMACAddress[WLAN_AP_STA_MACADDR_STR_LEN]; /*关联设备的MAC地址*/
#define WLAN_AP_STA_IPDNS_STR_LEN       (64)
	word8  aucAssociatedDeviceIPAddress[WLAN_AP_STA_IPDNS_STR_LEN];	/*关联设置的IP地址或DNS名*/
#define WLAN_AP_STA_AUTH_DISABLED       (0)
#define WLAN_AP_STA_AUTH_ENABLED        (1)
	uword8  AssociatedDeviceAuthenticationState;                        /*关联设备的认证状态*/
	uword8 maxMode; /*最高支持的802.11工作模式*/
	uword8 maxBandWidth; /*最高支持的频宽*/
	uword8  aucPad;
#define WLAN_AP_STA_EXT_INFO_LEN        (16)
	word8  aucLastRequestedUnicastCipher[WLAN_AP_STA_EXT_INFO_LEN];    /*指定MAC地址的STA最近使用的unicast cipher*/
	word8  aucLastRequestedMulticastCipher[WLAN_AP_STA_EXT_INFO_LEN];  /*指定MAC地址的STA最近使用的multicast cipher*/
	word8  aucLastPMKid[WLAN_AP_STA_EXT_INFO_LEN];                     /*指定MAC地址的STA最近使用的PMK*/
	uword32	ulPktTx;													/*指定MAC地址的STA的发送包数*/
	uword32 ulPktRx; 													/*指定MAC地址的STA的接收包数*/
	uword64	ullBytesTx;													/*指定MAC地址的STA的发送字节数*/
	uword64 ullBytesRx; 													/*指定MAC地址的STA的接收字节数*/
	word8	aucRateTx[WLAN_AP_STA_EXT_INFO_LEN];						/*指定MAC地址的STA的协商(实时)发送速率(MCS表示)*/
	word8  aucRateRx[WLAN_AP_STA_EXT_INFO_LEN]; 						/*指定MAC地址的STA的协商(实时)接收速率(MCS表示)*/
	uword32	ulExpireTime;												/*指定MAC地址的STA的过期时间*/
	word32	lRssi;												        /*指定MAC地址的STA的连接信号强度，单位dBm*/
	uword32 ulAssocTime;                                                /*指定MAC地址的STA的关联时间*/
#define WLAN_AP_STA_STANDARD_MAXLEN (32)
	word8 aucOperatingStandard[WLAN_AP_STA_STANDARD_MAXLEN];            /*指定MAC地址的STA的工作模式*/
	uword8 ucBandWidth;
	uword32 ulBSSTrasitionSupport;                                      /**OUT    指定MAC地址的STA是否支持BSS Transition*/
	uword32 ulBeaconReportSupport;                                      /**OUT    指定MAC地址的STA是否支持802.11K定义的Beacon report*/
} __PACK__ IgdWLANApStaInfoAttrTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulSSIDIndex;                                                /*IN&OUT  SSID索引*/
	uword32 ulEntryNum;                                                 /*OUT     Entry Num*/
#define WLAN_AP_STA_INFO_ENTRY_MAX     (32)
	IgdWLANApStaInfoAttrTab stStaInfo[WLAN_AP_STA_INFO_ENTRY_MAX];      /*OUT     Entry attr*/

#define WLAN_AP_STA_INFO_MASK_UNASSOC      (1<<0)
#define WLAN_AP_STA_INFO_MASK_GETENTRYNUM  (1<<1)
	uword32 ulBitmap;
	uword32 unAssocQueryFlag;
} __PACK__ IgdWLANApStaInfoEntryAttrTab;

/*******************************无线AP邻居AP的信息属性表*************************************/
typedef struct {
#define WLAN_SSID_NAME_STR_LEN                             (36)
	word8  aucSSIDName[WLAN_SSID_NAME_STR_LEN];            /*IN&OUT Neighbor SSID名称，无线网络名称（如ChinaNet-xxxx）*/
	word8  aucSSIDMACAddr[WLAN_AP_STA_MACADDR_STR_LEN];    /*OUT    Neighbor SSID对应的MAC地址*/
#define WLAN_AP_NETWORKTYPE_AP                             "AP"
#define WLAN_AP_NETWORKTYPE_AD_HOC                         "Ad-Hoc"
#define WLAN_AP_NETWORKTYPE_STR_LEN                        (16)
	word8  aucNetworkType[WLAN_AP_NETWORKTYPE_STR_LEN];     /*OUT    Neighbor 网络类型，取值范围("AP"、"Ad-Hoc")，缺省值: "AP"*/
	uword8  ucChannelInUse;                                 /*OUT    实际工作信道*/
#define WLAN_AP_RSSI_STR_LEN                               (8)
	word8  aucRSSI[WLAN_AP_RSSI_STR_LEN];                   /*OUT    无线模块发射功率最大值 单位：dBm*/
	uword8  aucPad[2];
#define WLAN_AP_RFMODE_STR_LEN                        (16)
	word8  aucRFMode[WLAN_AP_RFMODE_STR_LEN];               /*IN&OUT  无线模式 a、b、g、n、b/g/n（802.11b/g/n混和模式）*/
	uword8 ucBandWidth;
#define WLAN_AP_AUTH_MODE_STR_LEN                           (32)
	word8  aucAuthMode[WLAN_AP_AUTH_MODE_STR_LEN];          /*OUT     OPEN/WEP/WPAPSK/WPAPSK2/MIXED-WPAPSK2 */
} __PACK__ IgdWLANApNeighborInfoAttrTab;

typedef struct {
	uword32 ulStateAndIndex;
#define WLAN_RFBAND_2_4G                 (0)
#define WLAN_RFBAND_5G                   (1)
#define WLAN_RFBAND_5_8G                 (2)
	uword8  ucRFBand;                                     /*IN&OUT  频段,0：表示2.4GHz，1：表示5GHz，缺省为0*/
	uword8  aucPad[3];
	uword32 ulEntryNum;                                                           /*OUT     Entry Num*/
#define WLAN_AP_NEIGHBOR_INFO_ENTRY_MAX  (32)
	IgdWLANApNeighborInfoAttrTab stNeighborInfo[WLAN_AP_NEIGHBOR_INFO_ENTRY_MAX]; /*OUT     Entry attr*/
} __PACK__ IgdWLANApNeighborInfoEntryAttrTab;


/***************************无线WLAN共享功能属性表************************************/

#define WLAN_SSID_SHARE_INSTANCE_MAX                (2)
#define WLAN_SSID_SHARE_SSID_INDEX                  (2)
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulWlanShareInstance;                                        /*IN&OUT  WLAN共享实例索引*/
	uword32 ulSSIDIndex;                                                /*IN&OUT  SSID索引*/
#define WLAN_SSID_SHARE_PORT_ISO_DEACTIVATE         (0)
#define WLAN_SSID_SHARE_PORT_ISO_ACTIVATE           (1)
	uword8  ucPortIsolation;                                            /*IN&OUT  与其他SSID和LAN是否隔离 取值范围(true,false)，缺省值：true*/
#define WLAN_SSID_SHARE_STA_ISO_DEACTIVATE          (0)
#define WLAN_SSID_SHARE_STA_ISO_ACTIVATE            (1)
	uword8  ucStaIsolation;                                             /*IN&OUT  下挂STA间是否隔离 取值范围(true,false)，缺省值：true*/
#define WLAN_SSID_SHARE_USERID_DEACTIVATE           (0)
#define WLAN_SSID_SHARE_USERID_ACTIVATE             (1)
	uword8  ucEnableUserId;                                             /*IN&OUT  是否启用共享用户标识 取值范围(true,false)，缺省值：true*/
	uword8  ucPad;
#define WLAN_SSID_SHARE_USERID_STR_LEN              (64)
	word8  aucUserId[WLAN_SSID_SHARE_USERID_STR_LEN];                  /*IN&OUT  共享用户标识信息（用户Option82）*/

#define WLAN_GLOBAL_CFG_ATTR_MASK_SSIDINDEX         (1<<0)
#define WLAN_GLOBAL_CFG_ATTR_MASK_PORTISOLATION     (1<<1)
#define WLAN_GLOBAL_CFG_ATTR_MASK_STAISOLATION      (1<<2)
#define WLAN_GLOBAL_CFG_ATTR_MASK_ENABLEUSERID      (1<<3)
#define WLAN_GLOBAL_CFG_ATTR_MASK_USERID            (1<<4)

	uword32 ulBitmap;
} __PACK__ IgdWLANSsidShareAttrCfgTab;

#define CM_EM_MESH_TYPE_MASTER_MESH 0
#define CM_EM_MESH_TYPE_CONFIG_BSSID_MESH 1
#define CM_EM_MESH_TYPE_SLAVE_MESH 2
typedef struct {
	uword32 ulStateAndIndex;
	uword8  ulAgentEnable;
	uword8  ulControllerEnable;
	uword8  ulEmSelfAdapt;
	uword8  ucConStatus;
	uword8  MeshBand; /* 0:2.4G 1:5G 2:MLD */
	uword8 WpsType;

#define WLAN_EASYMESH_ATTR_CFG_MAP_AGENT_ON    (1)
#define WLAN_EASYMESH_ATTR_CFG_MAP_AGENT_OFF   (0)
#define WLAN_EASYMESH_ATTR_CFG_MAP_AGENT_AUTO  (2)
	uword8  map_agent;
	uword8  pad[2];
	uword32 ulBitmap;

#define EASYMESH_ATTR_CFG_MASK_AGENT_ENABLE        (0x1)
#define EASYMESH_ATTR_CFG_MASK_CONTROLLER_ENABLE   (0x2)
#define EASYMESH_ATTR_CFG_MASK_EM_SELF_ADAPT       (0x4)
#define EASYMESH_ATTR_CFG_MASK_CON_STATUS          (0x8)
#define EASYMESH_ATTR_CFG_MASK_MESH_BAND           (0x10)
#define EASYMESH_ATTR_CFG_MASK_MAP_AGENT           (0x20)
#define EASYMESH_ATTR_CFG_MASK_MAP_ROAMING_DISABLE (0x40)
#define EASYMESH_ATTR_CFG_MASK_ALL                 (0xFF)
} __PACK__ IgdWLANEasymeshAttrCfgTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulRFBand;/* 0:2.4G 1:5G */
} __PACK__ IgdWlanStaWpsParam;

/* IGD_WLAN_ASSOCIATE_SSID_CONF_TAB */
#define IGD_WLAN_ASSOCIATE_SSID_CONF_MAX 32
typedef struct {
	uword32 ulStateAndIndex; /* need define called by web or andlink */

#define ASSOCIATE_SSID_RF_BAND_24G          (0)
#define ASSOCIATE_SSID_RF_BAND_5G           (1)
#define ASSOCIATE_SSID_RF_BAND_MLD          (2)
	uword32 uiRfBand;
	word8 aucAssociateSSIDName[WLAN_SSID_NAME_STR_LEN];
	word8 aucAssociateSSIDPasswd[WLAN_SSID_PSK_STR_LEN];
#define ASSOCIATE_SSID_BSSID_STR_LEN        (32)
	word8 aucAssociateBSSID[ASSOCIATE_SSID_BSSID_STR_LEN];
	uword32 uiChannelInUse;
#define ASSOCIATE_SSID_ENCRYPT_OPEN          (0)
#define ASSOCIATE_SSID_ENCRYPT_WEP           (1)
#define ASSOCIATE_SSID_ENCRYPT_WPAPSK        (2)
#define ASSOCIATE_SSID_ENCRYPT_WPAPSK2       (3)
#define ASSOCIATE_SSID_ENCRYPT_MIXED_WPAPSK2 (4)
#define ASSOCIATE_SSID_ENCRYPT_WPA3_SAE       (5)
#define ASSOCIATE_SSID_ENCRYPT_WPA2_PSK_MIX_WPA3_SAE (6)
	uword32 ulAssociateSSIDEncrypt;
#define ASSOCIATE_SSID_CFG_INIT         (0)
#define ASSOCIATE_SSID_CFG_WEB          (1)
#define ASSOCIATE_SSID_CFG_EASYMESH     (2)
#define ASSOCIATE_SSID_CFG_OTHER        (3)
	uword32 ulAssociateSSIDCfgFrom;
#define ASSOCIATE_SSID_WIFI_INFO_LEN    (8)
	uword8 aucNoise[ASSOCIATE_SSID_WIFI_INFO_LEN];
	uword8 aucSNR[ASSOCIATE_SSID_WIFI_INFO_LEN];
	uword8 aucRSSI[ASSOCIATE_SSID_WIFI_INFO_LEN];
	uword32 uiNegoRxRate;
	uword32 uiNegoTxRate;

#define WLAN_ASSOCIATE_SSID_MASK_RFBAND     (0x01)
#define WLAN_ASSOCIATE_SSID_MASK_SSID       (0x02)
#define WLAN_ASSOCIATE_SSID_MASK_PASSWORD   (0x04)
#define WLAN_ASSOCIATE_SSID_MASK_BSSID      (0x08)
#define WLAN_ASSOCIATE_SSID_MASK_CHANNEL    (0x10)
#define WLAN_ASSOCIATE_SSID_MASK_AUTH_MODE  (0x20)
#define WLAN_ASSOCIATE_SSID_MASK_ALL        (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdWlanAssociateSSIDAttrTab;


typedef struct {
#define ASSOCIATE_SSID_BAND_2_4G (0)
#define ASSOCIATE_SSID_BAND_5G (1)
	uword32 ulRFBand;
	uword8 aucSSIDName[WLAN_SSID_NAME_STR_LEN];
	uword8 aucBSSID[CM_MAC_CHAR_LEN];
#define CONNECT_AP_HAS_WAN_CABLE (1)
#define CONNECT_AP_Not_HAS_WAN_CABLE (0)
	word32 lHasWanCable;
#define CONNECT_AP_RESULT_INIT  (0xFFFF)
#define CONNECT_AP_RESULT_SUCC  (0)
#define CONNECT_AP_RESULT_DISASSOC (1)
#define CONNECT_AP_RESULT_ASSOC_FAIL (2)
#define CONNECT_AP_RESULT_ASSOC_BUTT (3)
#define CONNECT_AP_RESULT_TIMEOUT_FAIL (4)
	word32 lConnResult;
} __PACK__ IgdWlanConnectApResultTab;

typedef struct {
	uword32 ulStateAndIndex;
#define WLAN_REPEATER_MODE_DISABLE (0)
#define WLAN_REPEATER_MODE_ENABLE  (1)
	uword32  uiRepeaterEnable;
	uword32 ulRepeaterModeType;
	uword32 ulBitmap;
} __PACK__ IgdWlanRepeaterModeTab;


/***************************无线SSID过滤MAC列表************************************/
#define IGD_WLAN_SSID_FILTER_LIST_NUM 32
typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulIndex;                                   	   /*索引*/
#define WLAN_SSID_FILTER_ALL               			(32)	/*SSID 配置为该值时表示所有SSID生效*/
	uword32	ulSSIDIndex;                                  /*IN&OUT  SSID索引*/
#define WLAN_SSID_FILTER_NAME_STR_LEN               (64)
	word8   aucStaName[WLAN_SSID_FILTER_NAME_STR_LEN];     /* 规则名称STA的别名 */
	uword8	aucMACAddr[CM_MAC_ADDR_LEN];				   /*MAC地址*/

#define WLAN_SSID_FILTER_LIST_DISABLE     			(0)
#define WLAN_SSID_FILTER_LIST_ENABLE     			(1)
	uword8	ucEnable;
	uword8  ucPad;
	uword8	ucFilterMode;
	uword32 ulBlockedTimes;                                /* 拦截该MAC的次数 */

#define WLAN_SSID_FILTER_LIST_MASK_SSID_INDEX       (1<<0)
#define WLAN_SSID_FILTER_LIST_MASK_STANAME          (1<<1)
#define WLAN_SSID_FILTER_LIST_MASK_MAC              (1<<2)
#define WLAN_SSID_FILTER_LIST_MASK_ENABLE           (1<<3)
#define WLAN_SSID_FILTER_LIST_MASK_FILTERMODE       (1<<4)
	uword32 ulBitmap;
} __PACK__ IgdWLANSsidFilterListTab;

/***************************无线SSID黑名单过滤MAC列表************************************/
#define IGD_WLAN_SSID_BLACK_FILTER_LIST_NUM 32
typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulIndex;                                   	   /*索引*/
#define WLAN_SSID_BLACK_FILTER_ALL               			(32)	/*SSID 配置为该值时表示所有SSID生效*/
	uword32	ulSSIDIndex;                                  /*IN&OUT  SSID索引*/
#define WLAN_SSID_BLACK_FILTER_NAME_STR_LEN               (64)
	word8   aucStaName[WLAN_SSID_BLACK_FILTER_NAME_STR_LEN];     /* 规则名称STA的别名 */
	uword8	aucMACAddr[CM_MAC_ADDR_LEN];				   /*MAC地址*/

#define WLAN_SSID_BLACK_FILTER_LIST_DISABLE     			(0)
#define WLAN_SSID_BLACK_FILTER_LIST_ENABLE     			(1)
	uword8	ucEnable;
	uword8  ucPad;
	uword8	ucFilterMode;
	uword32 ulBlockedTimes;                                /* 拦截该MAC的次数 */

#define WLAN_SSID_BLACK_FILTER_LIST_MASK_SSID_INDEX       (1<<0)
#define WLAN_SSID_BLACK_FILTER_LIST_MASK_STANAME          (1<<1)
#define WLAN_SSID_BLACK_FILTER_LIST_MASK_MAC              (1<<2)
#define WLAN_SSID_BLACK_FILTER_LIST_MASK_ENABLE           (1<<3)
#define WLAN_SSID_BLACK_FILTER_LIST_MASK_FILTERMODE       (1<<4)
	uword32 ulBitmap;
} __PACK__ IgdWLANSsidBlackFilterListTab;

/********************无线频率自动转换属性表*****************************/
typedef struct {
#define WLAN_BANDSTEERING_ATTR_INIT_STATEANDINDEX   (0xFFFFFFFF)
	uword32 ulStateAndIndex;

#define WLAN_BANDSTEERING_DEACTIVATE                     (0)
#define WLAN_BANDSTEERING_ACTIVATE                       (1)
	uword8  ucEnable;                                    /*IN&OUT  功能开启*/
#define WLAN_BANDSTEERING_UNSUPPORT                      (0)
#define WLAN_BANDSTEERING_SUPPORT                        (1)
	uword8  ucCapability;                                /*OUT  是否支持*/
	uword8  aucPad[2];

	uword32 ulRSSIThreshold;                             /*IN&OUT 接收信号强度阈值，范围:-100~0，默认-40*/
	uword32 ulRSSIThreshold5G;                           /*IN&OUT 5G接收信号强度阈值，范围:-100~0，默认-80*/
	uword32 ulChannelUtilization5G;                      /*OUT  5G信号利用率*/
	uword32 ulChannelUtilizationThreshold5G;             /*IN&OUT  5G信号利用率参考阈值，范围:-100~0，默认80*/
	uword32 ulSTALoadThreshold2G;                        /*IN&OUT  2G下挂双频STA终端的流量参考阈值，默认75*/
	uword32 ulSteeringDetectInternal;                    /*IN&OUT  steering状态持续时间，默认30*/

#define WLAN_BANDSTEERING_ATTR_CFG_MASK_0_ENABLE                            0x1
#define WLAN_BANDSTEERING_ATTR_CFG_MASK_1_RSSITHRESHOLD                     0x2
#define WLAN_BANDSTEERING_ATTR_CFG_MASK_2_RSSITHRESHOLD5G                   0x4
#define WLAN_BANDSTEERING_ATTR_CFG_MASK_3_CHANNELUTILIZATIONTHRESHOLD5G     0x8
#define WLAN_BANDSTEERING_ATTR_CFG_MASK_4_STALOADTHRESHOLD2G                0x10
#define WLAN_BANDSTEERING_ATTR_CFG_MASK_5_STEERINGDETECTINTERNAL            0x20

#define WLAN_BANDSTEERING_ATTR_CFG_MASK_ALL                   (0x3F)
	uword32 ulBitmap;

} __PACK__ IgdWLANBandSteeringAttrTab;


/********************无线SSID Beacon帧扩展VSIE服务管理表*****************************/
typedef struct {
#define WLAN_BTVSIE_MNG_INIT_STATEANDINDEX   (0xFFFFFFFF)
	uword32 ulStateAndIndex;

#define WLAN_BTVSIE_DEACTIVATE                        (0)
#define WLAN_BTVSIE_ACTIVATE                          (1)
	uword8  ucEnable;                                    /*IN&OUT  功能开启*/
	uword8  aucPad[3];

#define WLAN_BTVSIE_MAXNUM_DEF                        (8)
	uword32 ulMaxEntryNum;                               /*IN&OUT  最大实例条目，默认值为8*/
	uword32 ulCurrentEntryNum;                           /*   OUT  当前实例条目*//*IN&OUT*/
	uword32 ulMaxConcurrentTask;                         /*IN&OUT  最大的并行发送任务数，不得低于3。*/

#define WLAN_BTVSIE_MNG_MASK_0_ENABLE              (1<<0)
#define WLAN_BTVSIE_MNG_MASK_1_ENTRY_MAXNUM        (1<<1)

#define WLAN_BTVSIE_MNG_MASK_ALL                   (0xFFFFFFFF)
	uword32 ulBitmap;

} __PACK__ IgdWLANBeaconTxVSIEMngTab;

/********************无线SSID Beacon帧扩展VSIE服务属性表*****************************/
#define IGD_WLAN_BEACONTXVSIE_MAXNUM   (32)

typedef struct {
#define WLAN_BTVSIE_ATTR_INIT_STATEANDINDEX      (0xFFFFFFFF)
	uword32 ulStateAndIndex;

	uword32 ulIndex;                                     /*IN&OUT  实例索引*/

#define WLAN_BTVSIE_SERVICENAME_LEN              (64)
	word8  aucServiceName[WLAN_BTVSIE_SERVICENAME_LEN]; /*IN&OUT  服务名称*/
#define WLAN_BTVSIE_STATUS_SUCC                  (0)
#define WLAN_BTVSIE_STATUS_FAIL                  (1)
#define WLAN_BTVSIE_STATUS_BUSY                  (2)
#define WLAN_BTVSIE_STATUS_STOP                  (3)
	uword8  ucStatus;                                    /*   OUT  0表示成功，1表示失败, 2表示忙，3表示被停止*/
	uword8  ucSSIDIndex;                                 /*IN&OUT  对应的SSID Index*/
	uword8  aucPad[2];
	uword32 ulDuration;                                  /*IN&OUT  发送时间，默认值为0，表示没有发送。取值范围0-300*/
#define WLAN_BTVSIE_IEDATA_LEN                   (256)
	word8  aucIEData[WLAN_BTVSIE_IEDATA_LEN];           /*IN&OUT  发送的Vendor-specific Information Element内容*/
	uword32 ulLenofIEData;                               /*IN&OUT  IEData的长度*/
	uword8  ucStart;                                     /*IN&OUT  开始发送*/
	uword8  ucStop;                                      /*IN&OUT  停止发送*/
	uword8  aucPad1[2];

#define WLAN_BTVSIE_ATTR_MASK_0_SERVICENAME      (1<<0)
#define WLAN_BTVSIE_ATTR_MASK_1_STATUS           (1<<1)
#define WLAN_BTVSIE_ATTR_MASK_2_SSIDINDEX        (1<<2)
#define WLAN_BTVSIE_ATTR_MASK_3_DURATION         (1<<3)
#define WLAN_BTVSIE_ATTR_MASK_4_IEDATA           (1<<4)
#define WLAN_BTVSIE_ATTR_MASK_5_IEDATALEN        (1<<5)
#define WLAN_BTVSIE_ATTR_MASK_6_START            (1<<6)
#define WLAN_BTVSIE_ATTR_MASK_7_STOP             (1<<7)

#define WLAN_BTVSIE_ATTR_MASK_ALL                (0xFFFFFFFF)
	uword32 ulBitmap;

} __PACK__ IgdWLANBeaconTxVSIEAttrTab;

/********************无线SSID Probe帧扩展VSIE服务管理表*****************************/
typedef struct {
#define WLAN_PRVSIE_MNG_INIT_STATEANDINDEX   (0xFFFFFFFF)
	uword32 ulStateAndIndex;

#define WLAN_PRVSIE_DEACTIVATE                        (0)
#define WLAN_PRVSIE_ACTIVATE                          (1)
	uword8  ucEnable;                                    /*IN&OUT  服务是否开启*/
	uword8  aucPad[3];

#define WLAN_PRVSIE_MAXNUM_DEF                        (3)
	uword32 ulMaxEntryNum;                               /*IN&OUT  支持最大的条目数，要求不得小于3条。*//*OUT*/
	uword32 ulCurrentEntryNum;                           /*   OUT  当前实例条目*/

#define WLAN_PRVSIE_MNG_MASK_0_ENABLE              (1<<0)

#define WLAN_PRVSIE_MNG_MASK_ALL                   (0xFFFFFFFF)
	uword32 ulBitmap;

} __PACK__ IgdWLANProbeRxVSIEMngTab;

/********************无线SSID Probe帧扩展VSIE服务属性表*****************************/
#define IGD_WLAN_PROBERXVSIE_MAXNUM   (32)

typedef struct {
#define WLAN_PRVSIE_ATTR_INIT_STATEANDINDEX      (0xFFFFFFFF)
	uword32 ulStateAndIndex;

	uword32 ulIndex;                                     /*IN&OUT  实例索引*/

#define WLAN_PRVSIE_SERVICENAME_LEN                (64)
	word8  aucServiceName[WLAN_PRVSIE_SERVICENAME_LEN]; /*IN&OUT  服务名称*/
	uword8  ucProbeBand;                                 /*IN&OUT  0表示2.4G，1表示5G，2表示2.4G和5G*/
	uword8  aucOUI[3];                                   /*IN&OUT  OUI，二进制格式，数组大小是3*/

#define WLAN_PRVSIE_ATTR_MASK_0_SERVICENAME      (1<<0)
#define WLAN_PRVSIE_ATTR_MASK_1_PROBEBAND        (1<<1)
#define WLAN_PRVSIE_ATTR_MASK_2_OUI              (1<<2)

#define WLAN_PRVSIE_ATTR_MASK_ALL                (0xFFFFFFFF)
	uword32 ulBitmap;

} __PACK__ IgdWLANProbeRxVSIEAttrTab;

/********************无线SSID Probe帧扩展VSIE记录管理表*****************************/
typedef struct {
	uword32 ulStateAndIndex;

	uword32 ulProbeIndex;                                /*probe实例索引*/

	uword32 ulRecordNum;                                 /*   record数目*/
#define WLAN_PRVSIE_FLUSH_DEACTIVATE                     (0)
#define WLAN_PRVSIE_FLUSH_ACTIVATE                       (1)
	uword32 ulFlushRecord;                               /*   清空当前实例下record记录*/

#define WLAN_PRVSIE_REC_MNG_MASK_0_RECORDNUM        (0x01)
#define WLAN_PRVSIE_REC_MNG_MASK_1_FLUSHRECORD      (0x02)
	uword32 ulBitmap;

} __PACK__ IgdWLANProbeRxVSIERecMngTab;

/********************无线SSID Probe帧扩展VSIE记录属性表*****************************/
#define IGD_WLAN_PROBERXVSIE_REC_MAXNUM   (256)

typedef struct {
	uword32 ulStateAndIndex;

	uword32 ulProbeIndex;                                /* probe实例索引*/
	uword32 ulRecordAttrIndex;                           /* Record实例索引*/
#define WLAN_PRVSIE_VSIE_LEN                    (256)
	word8  aucVSIE[WLAN_PRVSIE_VSIE_LEN];               /*   OUT  Vendor specific Information Element内容*/
	uword8  aucMAC[CM_MAC_ADDR_LEN];                     /*   OUT  STA的MAC*/
	uword8  ucBAND;                                      /*   OUT  无线频段，0表示2.4g, 1表示5G。*/
	uword8  ucPad;
	uword32 ulVSIELength;                                /* length of Vendor specific Information Element */

	uword32 ulBitmap;

} __PACK__ IgdWLANProbeRxVSIERecAttrTab;

typedef struct {
	uword32 ulStateAndIndex;

	uword32 ulProbeIndex;                                /* probe实例索引*/
	uword32 ulRecordAttrIndex;                           /* Record实例索引*/

} __PACK__ IgdWLANProbeRxVSIERecIndexAttrTab;


/*******************************WIFI漫游功能信息属性表*************************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;                                                    /*IN&OUT  实例索引*/
	uword32 ulSSIDIndex;                                                /**IN&OUT SSID索引*/
#define WLAN_ROAMING_DEACTIVATE         (0)
#define WLAN_ROAMING_ACTIVATE           (1)
	uword32 ulRoamingSwitch;                                            /**IN&OUT 漫游开关*/
	uword32 ulStartTime;                                                /**IN&OUT 当某个STA的关联时间大于ulStartTime，开启判断逻辑*/
	uword32 ulThresholdLowRSSI;                                         /**IN&OUT 当STA到AP的RSSI低于该门限，上告RSSI低告警*/
	uword32 ulThresholdHighRSSI;                                        /**IN&OUT 当STA到AP的RSSI高于该门限，上报RSSI低告警清除*/
	uword32 ulAPRSSIQuery;                                              /**IN     设置为1，表示启动STA到指定AP的RSSI扫描*/
#define WLAN_ROAMING_MAC_LEN            (24)
	word8  aucAPRSSIStaMACAddr[WLAN_ROAMING_MAC_LEN];                  /**IN     STA到指定AP的RSSI扫描的STA的MAC地址 */
	word8  aucAPRSSIBSSID[WLAN_ROAMING_MAC_LEN];                       /**IN     扫描的AP设备的BSSID，即AP的MAC地址*/
	uword32 ulAPRSSIChannel;                                            /**IN     扫描的信道*/
	uword32 ulBSSTransitionRequest;                                     /**IN     启动BSS切换请求*/
	word8  aucBSSTransitionStaMACAddr[WLAN_ROAMING_MAC_LEN];           /**IN     启动BSS切换请求的STA的MAC地址 */
	uword32 ulBSSTransitionChannel;                                     /**IN     BSS切换的信道*/
	word8  aucBSSTrasitionBSSID[WLAN_ROAMING_MAC_LEN];                 /**IN     BSS切换的目的BSSID*/
	uword32 ulGetStaRSSIQuery;                                          /**IN     设置为1，表示启动获取非关联STA的RSSI值*/
	word8  aucGetStaRSSIStaMACAddr[WLAN_ROAMING_MAC_LEN];              /**IN     启动获取非关联STA的RSSI值的STA的MAC地址 */
	uword32 ulGetStaRSSIChannel;                                        /**IN     非关联STA扫描信道*/
	uword32 ulPacketLoss;
	uword32 ulRetryRatio;
	uword32 ulLowRSSIPeriod;
	uword32 ulDetectPeriod;
	uword32 ulSeqId;

#define WLAN_ROAMING_ATTR_CFG_MASK_SSIDINDEX                            (1<<0)
#define WLAN_ROAMING_ATTR_CFG_MASK_ROAMINGSWITCH                        (1<<1)
#define WLAN_ROAMING_ATTR_CFG_MASK_STARTTIME                            (1<<2)
#define WLAN_ROAMING_ATTR_CFG_MASK_THRESHOLDLOWRSSI                     (1<<3)
#define WLAN_ROAMING_ATTR_CFG_MASK_THRESHOLDHIGHRSSI                    (1<<4)
#define WLAN_ROAMING_ATTR_CFG_MASK_APRSSIQUERY                          (1<<5)
#define WLAN_ROAMING_ATTR_CFG_MASK_APRSSISTAMACADDR                     (1<<6)
#define WLAN_ROAMING_ATTR_CFG_MASK_APRSSIBSSID                          (1<<7)
#define WLAN_ROAMING_ATTR_CFG_MASK_APRSSICHANNEL                        (1<<8)
#define WLAN_ROAMING_ATTR_CFG_MASK_BSSTRANSITIONREQUEST                 (1<<9)
#define WLAN_ROAMING_ATTR_CFG_MASK_BSSTRANSITIONSTAMACADDR              (1<<10)
#define WLAN_ROAMING_ATTR_CFG_MASK_BSSTRANSITIONCHANNEL                 (1<<11)
#define WLAN_ROAMING_ATTR_CFG_MASK_BSSTRANSITIONBSSID                   (1<<12)
#define WLAN_ROAMING_ATTR_CFG_MASK_GETSTARSSIQUERY                      (1<<13)
#define WLAN_ROAMING_ATTR_CFG_MASK_GETSTARSSISTAMACADDR                 (1<<14)
#define WLAN_ROAMING_ATTR_CFG_MASK_GETSTARSSICHANNEL                    (1<<15)
#define WLAN_ROAMING_ATTR_CFG_MASK_PACKETLOSS                           (1<<16)
#define WLAN_ROAMING_ATTR_CFG_MASK_RETRYRATIO                           (1<<17)
#define WLAN_ROAMING_ATTR_CFG_MASK_LOWRSSIPERIOD                        (1<<18)
#define WLAN_ROAMING_ATTR_CFG_MASK_DETECTPERIOD                         (1<<19)
#define WLAN_ROAMING_ATTR_CFG_MASK_SEQID                                (1<<20)
	uword32 ulBitmap;
} __PACK__ IgdWLANRoamingAttrCfgTab;

/********************无线SSID ProbeRsp帧扩展VSIE服务属性表*****************************/

typedef struct {
#define WLAN_PRSVSIE_ATTR_INIT_STATEANDINDEX      (0xFFFFFFFF)
	uword32 ulStateAndIndex;
	uword32 ulIndex;                                     /*IN&OUT  实例索引*/
#define WLAN_PRSVSIE_SERVICENAME_LEN              (64)
	word8  aucServiceName[WLAN_PRSVSIE_SERVICENAME_LEN]; /*IN&OUT  服务名称*/
#define WLAN_PRSVSIE_STATUS_SUCC                  (0)
#define WLAN_PRSVSIE_STATUS_FAIL                  (1)
#define WLAN_PRSVSIE_STATUS_BUSY                  (2)
#define WLAN_PRSVSIE_STATUS_STOP                  (3)
	uword8  ucStatus;                                    /*   OUT  0表示成功，1表示失败, 2表示忙，3表示被停止*/
	uword8  ucSSIDIndex;                                 /*IN&OUT  对应的SSID Index*/
	uword8  aucPad[2];
	uword32 ulDuration;                                  /*IN&OUT  发送时间，默认值为0，表示没有发送。取值范围0-300*/
#define WLAN_PRSVSIE_IEDATA_LEN                   (256)
	word8  aucIEData[WLAN_PRSVSIE_IEDATA_LEN];           /*IN&OUT  发送的Vendor-specific Information Element内容*/
	uword32 ulLenofIEData;                               /*IN&OUT  IEData的长度*/
	uword8  aucMac[CM_MAC_ADDR_LEN];
	uword8  ucStart;                                     /*IN&OUT  开始发送*/
	uword8  ucStop;                                      /*IN&OUT  停止发送*/
#define WLAN_PRSVSIE_ATTR_MASK_0_SERVICENAME      (1<<0)
#define WLAN_PRSVSIE_ATTR_MASK_1_STATUS           (1<<1)
#define WLAN_PRSVSIE_ATTR_MASK_2_SSIDINDEX        (1<<2)
#define WLAN_PRSVSIE_ATTR_MASK_3_DURATION         (1<<3)
#define WLAN_PRSVSIE_ATTR_MASK_4_IEDATA           (1<<4)
#define WLAN_PRSVSIE_ATTR_MASK_5_IEDATALEN        (1<<5)
#define WLAN_PRSVSIE_ATTR_MASK_6_START            (1<<6)
#define WLAN_PRSVSIE_ATTR_MASK_7_STOP             (1<<7)
#define WLAN_PRSVSIE_ATTR_MASK_8_MAC              (1<<8)

#define WLAN_PRSVSIE_ATTR_MASK_ALL                (0xFFFFFFFF)
	uword32 ulBitmap;

} __PACK__ IgdWLANProbeRspVSIEAttrTab;

/* IGD_WLANCFG_WPS_ATTR_CFG_TAB */
#define IGD_WLANCFG_WPS_ATTR_CFG_MAX 1
typedef struct {
	uint32_t state_and_index;
	uint8_t  enable;
	uint8_t  setup_lock;
	uint8_t  registrar_established;
	uint8_t  pad;
#define WLANCFG_WPS_DEVICE_NAME_STRING_LEN                       (32)
	uint8_t  device_name[WLANCFG_WPS_DEVICE_NAME_STRING_LEN];
	uint32_t device_password;
#define WLANCFG_WPS_UUID_STRING_LEN                              (36)
	uint8_t  uuid[WLANCFG_WPS_UUID_STRING_LEN];
	uint32_t version;
#define WLANCFG_WPS_CONFIG_METHODS_SUPPORTED_STRING_LEN          (32)
	uint8_t  config_methods_supported[WLANCFG_WPS_CONFIG_METHODS_SUPPORTED_STRING_LEN];
#define WLANCFG_WPS_CONFIG_METHODS_ENABLED_STRING_LEN            (32)
	uint8_t  config_methods_enabled[WLANCFG_WPS_CONFIG_METHODS_ENABLED_STRING_LEN];
#define WLANCFG_WPS_SETUP_LOCKED_STATE_STRING_LEN                (32)
	uint8_t  setup_locked_state[WLANCFG_WPS_SETUP_LOCKED_STATE_STRING_LEN];
#define WLANCFG_WPS_CONFIGURATION_STATE_STRING_LEN               (32)
	uint8_t  configuration_state[WLANCFG_WPS_CONFIGURATION_STATE_STRING_LEN];
#define WLANCFG_WPS_LAST_CONFIGURATION_ERROR_STRING_LEN          (32)
	uint8_t  last_configuration_error[WLANCFG_WPS_LAST_CONFIGURATION_ERROR_STRING_LEN];
#define WLANCFG_WPS_ATTR_MASK_ENABLE                             (1 << 0)
#define WLANCFG_WPS_ATTR_MASK_SETUP_LOCK                         (1 << 1)
#define WLANCFG_WPS_ATTR_MASK_DEVICE_PASSWORD                    (1 << 2)
#define WLANCFG_WPS_ATTR_MASK_CONFIG_METHODS_ENABLED             (1 << 3)
#define WLANCFG_WPS_ATTR_MASK_ALL                               ((1 << 4) - 1)
	uint32_t bit_map;
} __PACK__ igd_wlancfg_wps_attr_conf_tab_t;

/* IGD_WLANCFG_WPS_REGISTRAR_ATTR_CFG_TAB */
#define  IGD_WLANCFG_WPS_REGISTRAR_MAXNUM                        (32)
typedef struct
{
	uint32_t state_and_index;
	uint32_t instance_index;
	uint8_t  enable;
	uint8_t  pad[CM_THREE_PADS];
#define WLANCFG_WPS_REGISTRAR_UUID_STRING_LEN                    (36)
	uint8_t  uuid[WLANCFG_WPS_REGISTRAR_UUID_STRING_LEN];
#define WLANCFG_WPS_REGISTRAR_DEVICENAME_STRING_LEN              (32)
	uint8_t  devicename[WLANCFG_WPS_REGISTRAR_DEVICENAME_STRING_LEN];
#define WLANCFG_WPS_REGISTRAR_ATTR_MASK_ENABLE                   (1 << 0)
#define WLANCFG_WPS_REGISTRAR_ATTR_MASK_ALL                     ((1 << 1) - 1)
	uint32_t bit_map;
} __PACK__ igd_wlancfg_wps_registrar_attr_conf_tab_t;

/* IGD_WLANCFG_AP_WMM_PARAMETER_ATTR_CFG_TAB */
#define  IGD_WLANCFG_AP_WMM_PARAMETER_MAXNUM                     (32)

typedef struct {
	uint32_t state_and_index;
	uint32_t instance_index;
	uint32_t aifsn;
	uint32_t ecw_min;
	uint32_t ecw_max;
	uint32_t txop;
	uint32_t ack_policy;
#define WLANCFG_AP_WMM_PARAMETER_ATTR_MASK_AIFSN                 (1 << 0)
#define WLANCFG_AP_WMM_PARAMETER_ATTR_MASK_ECW_MIN               (1 << 1)
#define WLANCFG_AP_WMM_PARAMETER_ATTR_MASK_ECW_MAX               (1 << 2)
#define WLANCFG_AP_WMM_PARAMETER_ATTR_MASK_TXOP                  (1 << 3)
#define WLANCFG_AP_WMM_PARAMETER_ATTR_MASK_ACK_POLICY            (1 << 4)
#define WLANCFG_AP_WMM_PARAMETER_ATTR_MASK_ALL                  ((1 << 5) - 1)
	uint32_t bit_map;
} __PACK__ igd_wlancfg_ap_wmm_parameter_attr_conf_tab_t;

#define  IGD_WLANCFG_STA_WMM_PARAMETER_MAXNUM                    (32)
typedef struct {
	uint32_t state_and_index;
	uint32_t instance_index;
	uint32_t aifsn;
	uint32_t ecw_min;
	uint32_t ecw_max;
	uint32_t txop;
	uint32_t ack_policy;
#define WLANCFG_STA_WMM_PARAMETER_ATTR_MASK_AIFSN                (1 << 0)
#define WLANCFG_STA_WMM_PARAMETER_ATTR_MASK_ECW_MIN              (1 << 1)
#define WLANCFG_STA_WMM_PARAMETER_ATTR_MASK_ECW_MAX              (1 << 2)
#define WLANCFG_STA_WMM_PARAMETER_ATTR_MASK_TXOP                 (1 << 3)
#define WLANCFG_STA_WMM_PARAMETER_ATTR_MASK_ACK_POLICY           (1 << 4)
#define WLANCFG_STA_WMM_PARAMETER_ATTR_MASK_ALL                 ((1 << 5) - 1)
	uint32_t bit_map;
} __PACK__ igd_wlancfg_sta_wmm_parameter_attr_conf_tab_t;

#define  IGD_WMM_CONFIGURATION_MAXNUM                                    (8)
typedef struct
{
	uint32_t state_and_index;
	uint32_t instance_index;
	uint8_t wmm_enable;
#define WMM_CONFIGURATION_WMM_SUPPORTED_SSID_STRING_LEN                  (32)
	char wmm_supported_ssid[WMM_CONFIGURATION_WMM_SUPPORTED_SSID_STRING_LEN];
	uint8_t uapsd_supported;
#define WMM_CONFIGURATION_ATTR_MASK_WMM_ENABLE                           (1 << 0)
#define WMM_CONFIGURATION_ATTR_MASK_WMM_SUPPORTED_SSID                   (1 << 1)
#define WMM_CONFIGURATION_ATTR_MASK_UAPSD_SUPPORTED                      (1 << 2)
#define WMM_CONFIGURATION_ATTR_MASK_ALL                                  ((1 << 3) - 1)
	uint32_t bit_map;
} __PACK__ igd_wmm_configuration_attr_conf_tab_t;

#define  IGD_WMM_SERVICE_IPSET_MAXNUM                                    (32)
typedef struct
{
	uint32_t state_and_index;
	uint32_t instance_index;
	uint32_t wmm_conf_index;
	uint32_t wmm_ipset_index;
#define WMM_SERVICE_IPSET_SERVICE_NAME_STRING_LEN                        (32)
	char service_name[WMM_SERVICE_IPSET_SERVICE_NAME_STRING_LEN];
#define WMM_SERVICE_IPSET_AC_LEVEL_STRING_LEN                            (32)
	char ac_level[WMM_SERVICE_IPSET_AC_LEVEL_STRING_LEN];
#define WMM_SERVICE_IPSET_TARGET_IP_STRING_LEN                           (64)
	char target_ip[WMM_SERVICE_IPSET_TARGET_IP_STRING_LEN];
#define WMM_SERVICE_IPSET_TARGET_PORT_STRING_LEN                         (64)
	char target_port[WMM_SERVICE_IPSET_TARGET_PORT_STRING_LEN];
#define WMM_SERVICE_IPSET_SOURCE_IP_STRING_LEN                           (64)
	char source_ip[WMM_SERVICE_IPSET_SOURCE_IP_STRING_LEN];
#define WMM_SERVICE_IPSET_SOURCE_PORT_STRING_LEN                         (64)
	char source_port[WMM_SERVICE_IPSET_SOURCE_PORT_STRING_LEN];
#define WMM_SERVICE_IPSET_PROTOCOL_STRING_LEN                            (8)
	char protocol[WMM_SERVICE_IPSET_PROTOCOL_STRING_LEN];
#define WMM_SERVICE_IPSET_ATTR_MASK_WMMCONF_INDEX                        (1 << 0)
#define WMM_SERVICE_IPSET_ATTR_MASK_WMMIPSET_INDEX                       (1 << 1)
#define WMM_SERVICE_IPSET_ATTR_MASK_SERVICE_NAME                         (1 << 2)
#define WMM_SERVICE_IPSET_ATTR_MASK_AC_LEVEL                             (1 << 3)
#define WMM_SERVICE_IPSET_ATTR_MASK_TARGET_IP                            (1 << 4)
#define WMM_SERVICE_IPSET_ATTR_MASK_TARGET_PORT                          (1 << 5)
#define WMM_SERVICE_IPSET_ATTR_MASK_SOURCE_IP                            (1 << 6)
#define WMM_SERVICE_IPSET_ATTR_MASK_SOURCE_PORT                          (1 << 7)
#define WMM_SERVICE_IPSET_ATTR_MASK_PROTOCOL                             (1 << 8)
#define WMM_SERVICE_IPSET_ATTR_MASK_ALL                                  ((1 << 9) - 1)
	uint32_t bit_map;
} __PACK__ igd_wmm_service_ipset_attr_conf_tab_t;

typedef struct {
	uword32 ulStateAndIndex;
	uword8  aucMac[CM_MAC_ADDR_LEN];
	uword32 ulLimitTime;
} __PACK__ IgdWLAN5GPrefer;

typedef struct igd_cm_wlan_channel_info {
	uint8_t region;
	uint8_t band;
	uint8_t channel;
} igd_cm_wlan_channel_info_s;

#endif
