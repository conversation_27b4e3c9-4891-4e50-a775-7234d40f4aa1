/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm netmonitor obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_NETMONITOR_H
#define HI_ODL_TAB_NETMONITOR_H
#include "hi_odl_basic_type.h"

/* IGD_PACKETCAPTURE_ATTR_TAB */
#define IGD_PACKETCAPTURE_ATTR_MAX 1
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulState;
#define DIAGNOSTIC_STATE_NONE  0
#define DIAGNOSTIC_STATE_REQUESTED 1
#define DIAGNOSTIC_STATE_COMPLETE 2
#define DIAGNOSTIC_STATE_ERROR_OUT_OF_RESOURCES 3
#define DIAGNOSTIC_STATE_ERROR_NO_PACKET 4
#define DIAGNOSTIC_STATE_ERROR_OTHER 5
#define DIAGNOSTIC_STATE_ERROR_UPLOAD_CMD_TIMEOUT 6
#define DIAGNOSTIC_STATE_ERROR_UPLOADFAIL 7
	uword32 ulMode;
#define EXECUTE_DEFALUT_STATE 0
#define EXECUTE_IMMEDIATELY 1
#define EXECUTE_AFTER_REBOOT 2

#define DURATION_MAX 60
	uword32 ulDuration; /* unit: second */
#define INTERFACE_LEN 64
	uword8 aucInterface[INTERFACE_LEN];
#define IP_LEN 64
	uword8 aucIP[IP_LEN];
#define PORT_INVALID_VALUE 65535
	uword32 ulPort;
#define PROTOCOL_LEN 64
	uword8 aucProtocol[PROTOCOL_LEN];
#define CAPTURE_NAME_LEN 128
	uword8 aucCaptureName[CAPTURE_NAME_LEN];
	uword32 ulCaptureSize;  /* unit: byte */
	uword32 ulUploadState;
#define PACKET_CAPTURE_CONF_MASK_BIT0_STATE        (0x01)
#define PACKET_CAPTURE_CONF_MASK_BIT1_MODE         (0x02)
#define PACKET_CAPTURE_CONF_MASK_BIT2_DURATION     (0x04)
#define PACKET_CAPTURE_CONF_MASK_BIT3_INTERFACE    (0x08)
#define PACKET_CAPTURE_CONF_MASK_BIT4_IP           (0x10)
#define PACKET_CAPTURE_CONF_MASK_BIT5_PORT         (0x20)
#define PACKET_CAPTURE_CONF_MASK_BIT6_PROTOCOL     (0x40)
#define PACKET_CAPTURE_CONF_MASK_BIT7_CAPTURE_NAME (0x80)
#define PACKET_CAPTURE_CONF_MASK_BIT8_CAPTURE_SIZE (0x100)
#define PACKET_CAPTURE_CONF_MASK_BIT9_UPLOAD_STATE (0x200)
#define PACKET_CAPTURE_CONF_MASK_ALL               (0x1ff)
	uword32 ulBitmap;
} __PACK__ IgdCmPacketCaptureConfTab;

#endif
