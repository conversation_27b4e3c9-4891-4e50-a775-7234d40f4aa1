/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab mac filter obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_MAC_FILTER_H
#define HI_ODL_TAB_MAC_FILTER_H
#include "hi_odl_basic_type.h"

#define SECUR_INIT_CFG_STATEANDINDEX		(0xFFFFFFFF)
typedef struct {
	uword32 ulStateAndIndex;

#define MAC_FILTER_DISABLE (0)
#define MAC_FILTER_ENABLE (1)
	uword8 ucMacFilterEnable;
#define MAC_FILTER_MODE_BLACK_USEFUL (0)
#define MAC_FILTER_MODE_WHITE_USEFUL (1)
	uword8 ucFilterMode;
	uword8 aucPad[CM_TWO_PADS];

#define MAC_FILTER_ATTR_MASK_BIT0_FILETER_ENABLE (0x01)
#define MAC_FILTER_ATTR_MASK_BIT1_FILTER_MODE (0x02)
#define MAC_FILTER_ATTR_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdSecurMacFilterAttrConfTab;


/***************************Mac过滤名单*********************************/

#define IGD_SECUR_MAC_FILTER_RECORD_NUM (100)

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulEnable;

	uword32 ulIndex;
	uword8 aucFilterMAC[CM_MAC_ADDR_LEN];
	uword8 aucFilterDestMAC[CM_MAC_ADDR_LEN];

#define SECUR_MAC_FILTER_PORT_LAN1 (0x1)
#define SECUR_MAC_FILTER_PORT_LAN2 (0x2)
#define SECUR_MAC_FILTER_PORT_LAN3 (0x4)
#define SECUR_MAC_FILTER_PORT_LAN4 (0x8)
#define SECUR_MAC_FILTER_PORT_LAN_ALL (0xF)
	uword32 ulMACPortBind;
	uword32 ulDestMACPortBind;

#define SECUR_MAC_FILTER_SELECT_SMAC (0)
#define SECUR_MAC_FILTER_SELECT_DMAC (1)
	uword32 ulMacSelet;

#define MAC_FILTER_LIST_ATTR_MASK_BIT0_FILETER_SRC_MAC (0x01)
#define MAC_FILTER_LIST_ATTR_MASK_BIT1_FILETER_DEST_MAC (0x02)
#define MAC_FILTER_LIST_ATTR_MASK_BIT1_FILETER_SRC_MAC_PORT (0x04)
#define MAC_FILTER_LIST_ATTR_MASK_BIT1_FILETER_DEST_MAC_PORT (0x08)
#define MAC_FILTER_LIST_ATTR_MASK_BIT2_FILETER_SELECT       (0x10)
#define MAC_FILTER_LIST_ATTR_MASK_BIT3_FILETER_ENABLE       (0x20)
#define MAC_FILTER_LIST_ATTR_MASK_ALL (0x3f)

	uword32 ulBitmap;
} __PACK__ IgdSecurMacFilterListAttrConfTab;

#endif
