/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab user limit obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_USER_LIMIT_H
#define HI_ODL_TAB_USER_LIMIT_H
#include "hi_odl_basic_type.h"

/***************************接入用户限制*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define ACCESS_USER_LIMIT_DISABLE (0)
#define ACCESS_USER_LIMIT_MODE_TOTAL (1)
#define ACCESS_USER_LIMIT_MODE_SEPARATE (2)
	uword8 ucMode; /* 0：不启用 1：模式一 2：模式二，缺省值：1*/
	uword8 ucTotalTerminalNumber;/*缺省值：4*/
#define STB_RESTRICT_DISABLE (0)
#define STB_RESTRICT_ENABLE (1)
	uword8 ucStbRestrictEnable;/*缺省值：0*/
	uword8 ucStbNumber;

#define CAMERA_RESTRICT_DISABLE (0)
#define CAMERA_RESTRICT_ENABLE (1)
	uword8 ucCameraRestrictEnable;
	uword8 ucCameraNumber;
#define COMPUTER_RESTRICT_DISABLE (0)
#define COMPUTER_RESTRICT_ENABLE (1)
	uword8 ucComputerRestrictEnable;
	uword8 ucComputerNumber;/*默认值：1*/

#define PHONE_RESTRICT_DISABLE (0)
#define PHONE_RESTRICT_ENABLE (1)
	uword8 ucPhoneRestrictEnable;
	uword8 ucPhoneNumber;

#define TERMINAL_TYPE_SRCIP (0)
#define TERMINAL_TYPE_SRMAC (1)
	uword8 ucTerminalType;	/*限制接入公网终端数方式*/
	uword8 ucPad;

#define USER_LIMIT_ATTR_MASK_BIT0_MODE (0x01)
#define USER_LIMIT_ATTR_MASK_BIT1_TOTAL_NUM (0x02)
#define USER_LIMIT_ATTR_MASK_BIT2_STB_ENABLE (0x04)
#define USER_LIMIT_ATTR_MASK_BIT3_STB_NUM (0x08)
#define USER_LIMIT_ATTR_MASK_BIT4_CAMERA_ENABLE (0x10)
#define USER_LIMIT_ATTR_MASK_BIT5_CAMERA_NUM (0x20)
#define USER_LIMIT_ATTR_MASK_BIT6_COMPUTER_ENABLE (0x40)
#define USER_LIMIT_ATTR_MASK_BIT7_COMPUTER_NUM (0x80)
#define USER_LIMIT_ATTR_MASK_BIT8_PHONE_ENABLE (0x100)
#define USER_LIMIT_ATTR_MASK_BIT9_PHONE_NUM (0x200)
#define USER_LIMIT_ATTR_MASK_BIT10_TERMINAL_TYPE (0x400)
#define USER_LIMIT_ATTR_MASK_ALL (0x7ff)
	uword32 ulBitmap;
} __PACK__ IgdSecurUserLimitAttrConfTab;

#endif
