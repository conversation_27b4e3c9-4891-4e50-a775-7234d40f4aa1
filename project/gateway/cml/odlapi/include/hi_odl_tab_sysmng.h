/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab user limit obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_SYSMNG_H
#define HI_ODL_TAB_SYSMNG_H
#include "hi_odl_basic_type.h"

#define IGD_SYSMNG_ACCOUNT_PASSWORD_VERLEN (8)

typedef struct {
	uword32 ulStateAndIndex;

#define TELE_ACCOUNT_DISABLE (0)
#define TELE_ACCOUNT_ENABLE (1)
	uword8 ucTeleAccountEnable;/* 是否启用管理帐户，缺省启用*/
	uword8 ucUserAccountChangeEnable;
#define WEB_ENABLE (0)
#define WEB_DISABLE (1)
	uint8_t web_disable;
#define USER_WEB_DISABLE (0)
#define USER_WEB_ENABLE (1)
	uint8_t user_web_enable;

	word8 aucTeleAccountName[CM_USERNAME_LEN];
	word8 aucTeleAccountPassword[CM_PASSWORD_LEN];
	word8 aucUserAccountName[CM_USERNAME_LEN];
	word8 aucUserAccountPassword[CM_PASSWORD_LEN];
#define USER_WEB_IP_LEN (32)
	uint8_t user_web_ip[USER_WEB_IP_LEN];
#define ADMIN_WEB_IP_LEN (32)
	uint8_t admin_web_ip[ADMIN_WEB_IP_LEN];
	uint32_t admin_account_change;

#define ACCOUNT_ATTR_MASK_BIT0_TELE_ACCOUNT_ENABLE (0x01)
#define ACCOUNT_ATTR_MASK_BIT1_TELE_ACCOUNT_NAME (0x02)
#define ACCOUNT_ATTR_MASK_BIT2_TELE_ACCOUNT_PASSWORD (0x04)
#define ACCOUNT_ATTR_MASK_BIT3_USER_ACCOUNT_NAME (0x08)
#define ACCOUNT_ATTR_MASK_BIT4_USER_ACCOUNT_PASSWORD (0x10)
#define ACCOUNT_ATTR_MASK_WEB_ENABLE (0x20)
#define ACCOUNT_ATTR_MASK_USER_WEB_ENABLE (0x40)
#define ACCOUNT_ATTR_MASK_ADMIN_ACCOUNT_CHANGE (0x80)
#define ACCOUNT_ATTR_MASK_ALL (0xff)
	uword32 ulBitmap;
} __PACK__ IgdSysmngAccountAttrConfTab;

/***************************日志属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define SYSLOG_LOG_FILE_DISABLE (0)
#define SYSLOG_LOG_FILE_ENABLE (1)
	uword8 ucLogfileEnable;/*默认启用日志文件*/
#define SYSLOG_LOG_LEVEL_EMERGENCY (0)
#define SYSLOG_LOG_LEVEL_ALERT (1)
#define SYSLOG_LOG_LEVEL_CRITICAL (2)
#define SYSLOG_LOG_LEVEL_ERROR (3)
#define SYSLOG_LOG_LEVEL_WARNING (4)
#define SYSLOG_LOG_LEVEL_NOTICE (5)
#define SYSLOG_LOG_LEVEL_INFORMITIONAL (6)
#define SYSLOG_LOG_LEVEL_DEBUG (7)
	uword8 ucLogLevel;/*默认等级：Error*/
	uword8 ucDisplayLogLevel; /*默认等级：Error*/
	uword8 ucPad;

#define SYSLOG_LOG_FILE_NAME_STRING_LEN (64)
	word8 ucLogfileName[SYSLOG_LOG_FILE_NAME_STRING_LEN];
	uword32 ulSeqId;
#define SYSLOG_LOG_UPLOAD_DISABLE (0)
#define SYSLOG_LOG_UPLOAD_ENABLE (1)
	uint8_t log_upload_enable;
	uint8_t pads1[CM_THREE_PADS];
	uint32_t log_upload_interval;
#define SYSLOG_ALARM_DISABLE (0)
#define SYSLOG_ALARM_ENABLE (1)
	uint8_t alarm_enable;
	uint8_t pads2[CM_THREE_PADS];
#define SYSLOG_ALARM_LEVEL_IGNORE (0)
#define SYSLOG_ALARM_LEVEL_IMPORTANT (1)
#define SYSLOG_ALARM_LEVEL_MAIN (2)
#define SYSLOG_ALARM_LEVEL_MINOR (3)
	uint32_t alarm_level;
#define SYSLOG_UPLOAD_SERVER_LEN (256)
	char upload_server [SYSLOG_UPLOAD_SERVER_LEN];
	char username[CM_USERNAME_LEN];
	char password[CM_PASSWORD_LEN];
	uint32_t number_of_alarm_info;
#define SYSLOG_ATTR_MASK_BIT0_LOGFILE_ENABLE (0x01)
#define SYSLOG_ATTR_MASK_BIT1_LOG_LEVEL (0x02)
#define SYSLOG_ATTR_MASK_BIT2_DISPLAY_LOG_LEVEL (0x04)
#define SYSLOG_ATTR_MASK_BIT3_LOGFILE_NAME (0x08)
#define SYSLOG_ATTR_MASK_BIT4_SEQ_ID (0x10)
#define SYSLOG_ATTR_MASK_LOG_UPLOAD_ENABLE (0x20)
#define SYSLOG_ATTR_MASK_LOG_UPLOAD_INTERVAL (0x40)
#define SYSLOG_ATTR_MASK_ALARM_ENABLE (0x80)
#define SYSLOG_ATTR_MASK_ALARM_LEVEL (0x100)
#define SYSLOG_ATTR_MASK_UPLOAD_SERVER (0x200)
#define SYSLOG_ATTR_MASK_USERNAME (0x400)
#define SYSLOG_ATTR_MASK_PASSWORD (0x800)
#define SYSLOG_ATTR_MASK_NUMBER_OF_ALARM_INFO (0x1000)
#define SYSLOG_ATTR_MASK_ALL (0x1fff)
	uword32 ulBitmap;
} __PACK__ IgdSysmngSyslogAttrConfTab;


typedef struct {
	uword8 ucLogLevel;
	uword16 ucmode;
	uword8 uc_type;

#define SYSLOG_LOG_DATA_STRING_LEN (512)
	word8 uclogData[SYSLOG_LOG_DATA_STRING_LEN];

#define SYSLOG_DATA_MASK_BIT0_LOG_LEVEL (0x01)
#define SYSLOG_DATA_MASK_BIT1_LOG_MODE (0x02)
#define SYSLOG_DATA_MASK_BIT2_LOG_DATA (0x04)
#define SYSLOG_DATA_MASK_BIT3_LOG_TYPE (0x08)
#define SYSLOG_DATA_MASK_ALL (0x0f)
	uword32 ulBitmap;
} __PACK__ IgdSysmngSyslogDataTab;


/***************************告警基本属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define ALARM_DISABLE (0)
#define ALARM_ENABLE (1)
	uword8 ucEnable;/*默认不启用告警监控*/
	uword8 aucPad[CM_THREE_PADS];


#define ALARM_ATTR_MASK_BIT0_ENABLE (0x01)
#define ALARM_ATTR_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdSysmngAlarmAttrConfTab;

/***************************告警参数属性表*********************************/
#define IGD_SYSMNG_ALARM_CONFIG_TAB_RECORD_NUM (16)


typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucAlarmConfigIndex;
#define ALARM_VALUE_GET_BY_ACCUMULATION_VALUE (1)
#define ALARM_VALUE_GET_BY_AVERAGE_VALUE (2)
#define ALARM_VALUE_GET_BY_MOMENT_VALUE (3)
	uword8 ucMode;
	uword8 aucPad[CM_TWO_PADS];

#define ALARM_CONFIG_PARA_LEN (128)
	uword8 aucParaList[ALARM_CONFIG_PARA_LEN];
	uword32 ulAlarmCode;/*告警编码*/
	uword32 ulLimitMax;
	uword32 ulLimitMin;
	uword32 ulTimeList;
#define ALARM_CONFIG_ATTR_MASK_BIT0_MODE (0x01)
#define ALARM_CONFIG_ATTR_MASK_BIT1_PARALIST (0x02)
#define ALARM_CONFIG_ATTR_MASK_BIT2_ALARM_CODE (0x04)
#define ALARM_CONFIG_ATTR_MASK_BIT3_LIMIT_MAX (0x08)
#define ALARM_CONFIG_ATTR_MASK_BIT4_LIMIT_MIN (0x10)
#define ALARM_CONFIG_ATTR_MASK_BIT5_TIMELIST (0x20)
#define ALARM_CONFIG_ATTR_MASK_ALL (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdSysmngAlarmConfigAttrConfTab;

/***************************参数监控基本属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define MONITOR_DISABLE (0)
#define MONITOR_ENABLE (1)
	uword8 ucEnable;/*默认不启用监控*/
	uword8 aucPad[CM_THREE_PADS];

#define MONITOR_ATTR_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdSysmngMonitorAttrConfTab;

/***************************监控参数属性表*********************************/
#define IGD_SYSMNG_MONITOR_CONFIG_TAB_RECORD_NUM (16)


typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucMonitorConfigIndex;
	uword8 aucPad[CM_THREE_PADS];

#define MONITOR_CONFIG_PARA_LEN (256)
	word8 aucParaList[MONITOR_CONFIG_PARA_LEN];
	uword32 ulTimeList;
#define MONITOR_CONFIG_ATTR_MASK_BIT0_PARA_LIST (0x01)
#define MONITOR_CONFIG_ATTR_MASK_BIT1_TIME_LIST (0x02)
#define MONITOR_CONFIG_ATTR_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdSysmngMonitorConfigAttrConfTab;

/***************************Ping基本属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define PING_DISABLE (0)
#define PING_ENABLE (1)
	uword8 ucEnable;/*默认不启用监控*/
	uword8 aucPad[CM_THREE_PADS];

#define PING_ATTR_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdSysmngPingAttrConfTab;

/***************************Ping参数属性表*********************************/
#define IGD_SYSMNG_PING_CONFIG_TAB_RECORD_NUM (8)
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucPingConfigIndex;
#define PING_STOP_DISABLE (0)
#define PING_STOP_ENABLE (1)
	uword8 ucPingStopEnable;/* 终止PING操作，1：终止，0：不干预，缺省为：0*/
#define PING_DIAGNOSITC_STATE_NONE (0)
#define PING_DIAGNOSITC_STATE_REQUESTED (1)
#define PING_DIAGNOSITC_STATE_COMPLETE (2)
#define PING_DIAGNOSITC_STATE_RESOLVE_HOST_NAME_ERROR (3)
#define PING_DIAGNOSITC_STATE_INTERNER_ERROR (4)
#define PING_DIAGNOSITC_STATE_OTHER_ERROR (5)
	uword8 ucDiagnosticState;/*操作状态*/
	uword8 ucPad;

#define PING_INTERFACE_NAME_LEN (128)
	word8 aucPingInterface[PING_INTERFACE_NAME_LEN];/*操作路径*/
#define PING_HOST_IP_DOMAIN_LEN (64)
	word8 aucHostName[PING_HOST_IP_DOMAIN_LEN];/*目标IP或域名*/
#define PING_REPEAT_NUM_INFINITE (0)
	uword32 ulPingRepetitionsNum;/*Ping次数，0表示持续ping*/
	uword32 ulPingTimeoutValue;/*超时时间*/
	uword32 ulPingDataBlockSize;/*数据包大小*/
	uword32 ucPingDscpValue;/*DSCP值*/
	uword32 ucPingInterval;/*Ping时间间隔*/
#define PING_LAST_DIAGNOSITC_RESULT_DEFAULT (0)
#define PING_LAST_DIAGNOSITC_RESULT_TERMINATED_OF_POWER_OFF (1)
#define PING_LAST_DIAGNOSITC_RESULT_TERMINATE_NORMALLY (2)
	uword32 ucPingLastResult;/* 默认值(尚未执行Ping操作)、断电结束、正常结束 */

#define PING_CONFIG_ATTR_MASK_BIT0_STOP_ENABLE (0x01)
#define PING_CONFIG_ATTR_MASK_BIT1_DIAGNOSTIC_STATE (0x02)
#define PING_CONFIG_ATTR_MASK_BIT2_INTERFACE (0x04)
#define PING_CONFIG_ATTR_MASK_BIT3_HOST_NAME (0x08)
#define PING_CONFIG_ATTR_MASK_BIT4_REPEAT_NUM (0x10)
#define PING_CONFIG_ATTR_MASK_BIT5_TIMEOUT_VALUE (0x20)
#define PING_CONFIG_ATTR_MASK_BIT6_DATA_BLOCK_SIZE (0x40)
#define PING_CONFIG_ATTR_MASK_BIT7_DSCP_VALUE (0x80)
#define PING_CONFIG_ATTR_MASK_BIT8_INTERVAL (0x100)
#define PING_CONFIG_ATTR_MASK_BIT9_LAST_RESULT (0x200)
#define PING_CONFIG_ATTR_MASK_ALL (0x3ff)
	uword32 ulBitmap;
} __PACK__ IgdSysmngPingConfigAttrConfTab;

/***************************环路检测全局属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define LOOPBACK_DETECT_DISABLE (0)
#define LOOPBACK_DETECT_ENABLE (1)
	uword8 ucLoopbackEnable;/*默认开启*/
	/*gaozm add 2016-04-15*/
	uword32 ucPkgPerSec;	/*每秒最多发送检测报文数，默认为5*/
	/*gaozm add 2016-04-15*/
	uword8 aucPad[CM_TWO_PADS];

	uword32 ucLoopExistPeriod;/* 有无环路检测周期，单位：秒。缺省：5*/
	uword32 ucLoopCancelPeriod;/*环路是否解除检测周期，单位：秒. 缺省：300*/
#define LOOPBACK_VLAN_LEN (48)
	word8 aucVlanTag[LOOPBACK_VLAN_LEN];/*多个VLAN逗号间隔。缺省："untagged"*/
#define LOOPBACK_ETHERNET_TYPE_LEN (8)
	uword8 aucLoopEthType[LOOPBACK_ETHERNET_TYPE_LEN];/*报文以太网类型。缺省："0xFFFA"*/

#define LOOPBACK_ATTR_MASK_BIT0_ENABLE (0x01)
#define LOOPBACK_ATTR_MASK_BIT1_LOOP_EXIST_PERIOD (0x02)
#define LOOPBACK_ATTR_MASK_BIT2_LOOP_CANCEL_PERIOD (0x04)
#define LOOPBACK_ATTR_MASK_BIT3_VLAN_TAG (0x08)
#define LOOPBACK_ATTR_MASK_BIT4_EHTERNET_TYPE (0x10)
#define LOOPBACK_ATTR_MASK_BIT5_PKGPERSEC (0x20)
#define LOOPBACK_ATTR_MASK_ALL (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdSysmngLoopbackAttrConfTab;

typedef struct {
	uword32 ulStateAndIndex;

#define LAN_ETH_PORT_LAN1 (1)
#define LAN_ETH_PORT_LAN2 (2)
#define LAN_ETH_PORT_LAN3 (3)
#define LAN_ETH_PORT_LAN4 (4)
	uword8 ucPortId;/*端口号*/
#define LOOPBACK_STATE_NO_LOOP (0)
#define LOOPBACK_STATE_LOOP_WITH_PORT_DOWN (1)
#define LOOPBACK_STATE_LOOP_WITH_PORT_UP (2)
	uword8 ucLoopStatus;/* 0无环路，1有环路已自动关闭，2有环路无法关闭*/
	uword8 aucPad[CM_TWO_PADS];

#define LOOPBACK_STATE_INFO_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdSysmngLoopbackStateInfoTab;


typedef struct {
	uword32 ulStateAndIndex;

#define IPPING_DIAGNOSTIC_STATE_NONE (0)
#define IPPING_DIAGNOSTIC_STATE_REQUESTED (1)
#define IPPING_DIAGNOSTIC_STATE_COMPLETE (2)
#define IPPING_DIAGNOSTIC_STATE_RESOLVE_HOST_NAME_ERROR (3)
#define IPPING_DIAGNOSTIC_STATE_ERROR (4)
	uword8 ucDiagnosticsState;
	uword8 aucPad[CM_THREE_PADS];

#define IPPING_DIAGNOSTIC_INTERFACE_NAME_LEN (128)
	uword8 aucInterface[IPPING_DIAGNOSTIC_INTERFACE_NAME_LEN];/*操作路径*/
#define IPPING_DIAGNOSTIC_HOST_IP_NAME_LEN (128)
	uword8 aucHost[IPPING_DIAGNOSTIC_HOST_IP_NAME_LEN];/*目标IP或域名*/
#define IPPING_DIAGNOSTIC_RETRY_NUM_MIN (1)
	uword32 ulNumberOfTries;
#define IPPING_DIAGNOSTIC_TIMEOUT_MIN (1)
	uword32 ulTimeout;
#define IPPING_DIAGNOSTIC_DATABLOCK_MIN (1)
#define IPPING_DIAGNOSTIC_DATABLOCK_MAX (65535)
	uword32 ulDataBlockSize;
#define IPPING_DIAGNOSTIC_DSCP_MIN (0)
#define IPPING_DIAGNOSTIC_DSCP_MAX (63)
	uword32 ulDscp;

#define IPPING_DIAGNOSTIC_CONFIG_ATTR_MASK_BIT0_DIAGNOSTIC_STATE  (0x01)
#define IPPING_DIAGNOSTIC_CONFIG_ATTR_MASK_BIT1_INTERFACE (0x02)
#define IPPING_DIAGNOSTIC_CONFIG_ATTR_MASK_BIT2_HOST_NAME (0x04)
#define IPPING_DIAGNOSTIC_CONFIG_ATTR_MASK_BIT3_REPEAT_NUM (0x08)
#define IPPING_DIAGNOSTIC_CONFIG_ATTR_MASK_BIT4_TIMEOUT_VALUE (0x10)
#define IPPING_DIAGNOSTIC_CONFIG_ATTR_MASK_BIT5_DATA_BLOCK_SIZE (0x20)
#define IPPING_DIAGNOSTIC_CONFIG_ATTR_MASK_BIT6_DSCP_VALUE (0x40)
#define IPPING_DIAGNOSTIC_CONFIG_ATTR_MASK_ALL (0x7f)
	uword32 ulBitmap;
} __PACK__ IgdSysmngIPPingDiagAttrConfTab;


typedef struct {
	uword32 ulStateAndIndex;

	uword32 ulOffset; /*偏移量*/
	uword64 ulFirmwareSize; /*文件长度*/
#define SYSMNG_UPGRADE_FIRMWARE_NAME_LEN (128)
	word8 aucFirmwareName[SYSMNG_UPGRADE_FIRMWARE_NAME_LEN]; /*升级文件全路径*/
	uword8 ucMethod; //0-推荐更新，1-强制更新
	uword8 ucReboot;  //0-立即重启，1-等待重启
	uword32 ulBitmap;
} __PACK__ IgdSysmngFirmwareAttrTab;

typedef struct {
	uword32 ulStateAndIndex;

	uword32 ulUpgradeStatus;
	uword32 ulErrCode;
} __PACK__ IgdSysmngUpgradeStatusTab;

typedef struct {
	uword32 ulStateAndIndex;

#define UPGRADE_SOFTWARE_VERSION_LEN (128)
	char acSoftwareVersion[UPGRADE_SOFTWARE_VERSION_LEN];

#define SYSMNG_UPGRADE_INFO_MASK_BIT0_SOFTWARE_VERSION  (0x01)
#define SYSMNG_UPGRADE_INFO_MASK_ALL (0x1)
	uword32 ulBitmap;
} __PACK__ IgdSysmngUpgradeInfoTab;


/***************************固件升级*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define SYSMNG_UPGRADE_LOCAL (0)	//web/tr069升级网关固件
#define SYSMNG_UPGRADE_PLAT (1)		//业务管理平台升级
	uword8 ucLocal;   	//内部参数

#define SYSMNG_UPGRADE_ISREBOOT_YES (0)	//立即重启生效，缺省值0
#define SYSMNG_UPGRADE_ISREBOOT_NO (1)	//等待重启生效
	uword8	 ucIsReboot;

#define SYSMNG_UPGRADE_METHOD_RECOMMEND (0) 		//推荐更新
#define SYSMNG_UPGRADE_METHOD_FORCE (1)	//强制更新
	uword8 ucMethod;

#define SYSMNG_UPGRADE_MODE_GATEWAY_FIRMWARE (0)
#define SYSMNG_UPGRADE_MODE_OSGI_FRAMEWORK (1)
#define SYSMNG_UPGRADE_MODE_JAVA_MACHINE (2)
#define SYSMNG_UPGRADE_MODE_NOS (3)
#define SYSMNG_UPGRADE_MODE_ANDROID (4)
#define SYSMNG_UPGRADE_MODE_ANDRO (5)
#define SYSMNG_UPGRADE_MODE_CTWRT_FRAMEWORK (6)
#define SYSMNG_UPGRADE_MODE_CTWRT_CLOUDCLIENT (7)
	uword8 ucMode;

#define SYSMNG_UPGRADE_MODE_STATUS_NOJOB (0) //无升级任务
#define SYSMNG_UPGRADE_MODE_STATUS_WRONG_VERSION (1) //下发到网关的版本不正确
#define SYSMNG_UPGRADE_MODE_STATUS_DOWNLOAD_FAILED (2) //下载失败
#define SYSMNG_UPGRADE_MODE_STATUS_SPACE_UNAVAILABLE (3) //网关空间不够
#define SYSMNG_UPGRADE_MODE_STATUS_UPGRADEED (4) //当前已经是目标升级版本
#define SYSMNG_UPGRADE_MODE_STATUS_OTHER_ERROR (5) //其他错误
#define SYSMNG_UPGRADE_MODE_STATUS_UPGRADEING (6) //正在升级中
#define SYSMNG_UPGRADE_MODE_STATUS_SUCESS (7) //升级成功
	uword8 ucUpgradeStatus;

#define SYSMNG_UPGRADE_MODE_UPGRADE (0) //升级
#define SYSMNG_UPGRADE_MODE_NOT_UPGRADE (1) //不升级
#define SYSMNG_UPGRADE_MODE_NOT_UPGRADE_TEMPORARILY (2) //暂不升级
	uword8 ucUpgradeOp;
	uword8 aucPad[CM_TWO_PADS];

	uword32 ulStartTime;		//选择暂不升级选项起经过的时间s。

#define SYSMNG_UPGRADE_URL_LEN (256)
	word8 aucUrl[SYSMNG_UPGRADE_URL_LEN];
	uword32 ulOffset; /*偏移量*/
	uword64 ulFirmwareSize; /*文件长度*/

#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_BIT0_LOCAL (0x01)
#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_BIT1_ISREBOOT (0x02)
#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_BIT2_METHOD (0x04)
#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_BIT3_MODE (0x08)
#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_BIT4_STATUS (0x10)
#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_BIT5_OP (0x20)
#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_BIT6_STARTTIME (0x40)
#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_BIT7_URL (0x80)
#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_BIT8_OFFSET (0x100)
#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_BIT9_FIRMWARESIZE (0x200)
#define SYSMNG_UPGRADE_CONFIG_ATTR_MASK_ALL (0x3ff)
	uword32 ulBitmap;
} __PACK__ IgdSysmngDbusUpgradeTab;


/***************************中间件升级*********************************/
typedef struct {
	uword32 ulStateAndIndex;
#define SYSMNG_UPGRADE_FIRMWORK_URL_LEN (128)
	uword8  aucDownUrl[SYSMNG_UPGRADE_FIRMWORK_URL_LEN];
#define SYSMNG_UPGRADE_FIRMWORK_METHOD_NOMINATE 0
#define SYSMNG_UPGRADE_FIRMWORK_METHOD_FORCE 1
#define SYSMNG_UPGRADE_FIRMWORK_METHOD_CHANGE 2
	uword8  ucMethod; //0-nominate,1-force,2-change backup

	uword32 ulBitmap;
} __PACK__ IgdSysmngFirmworkAttrTab;

/* IGD_SYSMNG_UPGRADE_FIRMWARE_DATA_TAB */
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulSeqId;
	uword64 ullDownloadTime;
	uword64 ullUpgradeTime;
#define SYS_UPGRADESOURCE_WEB    0U
#define SYS_UPGRADESOURCE_UBUS   1U
#define SYS_UPGRADESOURCE_TR069  2U
#define SYS_UPGRADESOURCE_OTHER  3U
	uword32 ulUpgradeSource;
#define SYS_UPGRADE_DATA_NO_REBOOT  (0x0)
#define SYS_UPGRADE_DATA_REBOOT     (0x1)
	uword32 ulReboot; /*0:not reboot, 1:reboot*/
	uword8 aucDownloadUrl[256];
	uword8 aucChksum[36];
	uword8 aucPad[3];
	uword8 ucUpgradeStatus;
	uword32 ulFileSize;
	uword8 aucFailReason[128];
	uword8 aucExectime[20];
#define SYSMNG_UPGRADE_DATA_MASK_BIT0_SEQID             (0x01)
#define SYSMNG_UPGRADE_DATA_MASK_BIT1_DOWNLOADTIME      (0x02)
#define SYSMNG_UPGRADE_DATA_MASK_BIT2_UPGRADETIME       (0x04)
#define SYSMNG_UPGRADE_DATA_MASK_BIT3_UPGRADESOURCE     (0x08)
#define SYSMNG_UPGRADE_DATA_MASK_BIT4_ISREBOOT          (0x10)
#define SYSMNG_UPGRADE_DATA_MASK_BIT5_DOWNLOADURL       (0x20)
#define SYSMNG_UPGRADE_DATA_MASK_BIT6_CHKSUM            (0x40)
#define SYSMNG_UPGRADE_DATA_MASK_ALL                    (0x7f)
	uword32 ulBitmap;
} __PACK__ IgdUpgradeDataTab;


typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulMemAlarm;
	uword32 ulMemLimit;
#define MEM_STATUS_RESUME_NORMAL (0)
#define MEM_STATUS_ALARM (1)
#define MEM_STATUS_LIMIT (2)
	uword32 ulMemStatus;

#define MEM_ALARM_ATTR_MASK_BIT0_ALARM (0x01)
#define MEM_ALARM_ATTR_MASK_BIT1_LIMIT (0x02)
	uword32 ulBitmap;
} __PACK__ IgdSysmngMemAlarmAttrConfTab;


/***************************Memory 告警基本属性表*********************************/
#define OMD_PROCESS_NAME_LEN (32)

typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucCPUCurTemp; /*CPU当前温度*/
	uword8 ucCPUTempThreshold;
	uword8 ucPONTempThreshold;
	uword8 ucSysFlashThreshold;
	uword32 ulConntrackNumber;
	uword32 ulConnNumThreshold;

#define OMDIAG_ATTR_MASK_BIT0_CPUTEMP_CUR (0x01)
#define OMDIAG_ATTR_MASK_BIT1_CPUTEMP_THRESHOLD (0x02)
#define OMDIAG_ATTR_MASK_BIT2_PONTEMP_THRESHOLD (0x04)
#define OMDIAG_ATTR_MASK_BIT3_SYSFLASH_THRESHOLD (0x08)
#define OMDIAG_ATTR_MASK_BIT4_CONNTRACKNUM_CUR (0x10)
#define OMDIAG_ATTR_MASK_BIT5_CONNTRACKNUM_THRESHOLD (0x20)

	uword32 ulBitmap;
} __PACK__ IgdSysmngOMDiagAttrConfTab;

#define SYSMNG_OMDIAG_RESOURCE_NUM_MAX (16)

typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucResIndex;
	uword8 ucCPUPercentage;
	uword8 ucMEMPercentage;
	uword8 ucPad;
	word8 aucResName[OMD_PROCESS_NAME_LEN];


#define OMDIAG_RESOURCE_MASK_BIT0_CPU_PERCENTAGE (0x01)
#define OMDIAG_RESOURCE_MASK_BIT1_MEM_PERCENTAGE (0x02)
#define OMDIAG_RESOURCE_MASK_BIT2_RESOURCE_NAME (0x04)

	uword32 ulBitmap;
} __PACK__ IgdSysmngOMDiagResourceAttrConfTab;

#define SYSMNG_OMDIAG_PROCESS_NUM_MAX (16)
#define SYSMNG_OMDIAG_PROCESS_APP_NUM (4)

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulProcIndex;
	uword32 ulProcNum;
	word8 aucAppName[OMD_PROCESS_NAME_LEN];
	word8 aucProcName[SYSMNG_OMDIAG_PROCESS_APP_NUM][OMD_PROCESS_NAME_LEN];

#define OMDIAG_PROCESS_MASK_BIT0_APPNAME (0x01)
	uword32 ulBitmap;
} __PACK__ IgdSysmngOMDiagProcessAttrConfTab;

typedef struct {
	uword32 ulStateAndIndex;
#define CM_TEST_MODE_DDR_UP_LV3     (14)
#define CM_TEST_MODE_DDR_UP_LV2     (13)
#define CM_TEST_MODE_DDR_UP_LV1     (12)
#define CM_TEST_MODE_DDR_UP_LV0     (11)
#define CM_TEST_MODE_CPU_UP_LV2     (10)
#define CM_TEST_MODE_CPU_UP_LV1     (9)
#define CM_TEST_MODE_CPU_UP_LV0     (8)
#define CM_TEST_MODE_RESET          (7)
#define CM_TEST_MODE_KILL           (6)
#define CM_TEST_MODE_ANTI           (5)
#define CM_TEST_MODE_CLEAR_SET      (4)
#define CM_TEST_MODE_AIR_PERF       (3)
#define CM_TEST_MODE_CABLE_LATENCY  (2)
#define CM_TEST_MODE_CABLE_PREF     (1)
	uword32 ulTestMode;
	unsigned int ulTestModeShow;
} __PACK__ IgdTestModeTab;

#define IGD_SYSMNG_ALARM_INFO_NUM_MAX (32)

typedef struct
{
	uint32_t state_and_index;
	uint32_t instance_index;
	uint32_t alarm_id;
	uint32_t alarm_code;
#define ALARM_ALARM_STATUS_RECOVER_ALLOWED (1)
#define ALARM_ALARM_STATUS_RECOVER_NOT_ALLOWED (2)
#define ALARM_ALARM_STATUS_RECOVER (3)
	uint32_t alarm_status;
#define ALARM_PERCEIVED_SEVERITY_SERIOUS (1)
#define ALARM_PERCEIVED_SEVERITY_MAIN (2)
#define ALARM_PERCEIVED_SEVERITY_MINOR (3)
	uint32_t perceived_serverity;
#define SYSMNG_ALARM_INFO_ALARM_RAISED_TIME_STRING_LEN                   (32)
	uint8_t  alarm_raised_time[SYSMNG_ALARM_INFO_ALARM_RAISED_TIME_STRING_LEN];
#define SYSMNG_ALARM_INFO_ALARM_CLEARED_TIME_STRING_LEN                  (32)
	uint8_t  alarm_cleared_time[SYSMNG_ALARM_INFO_ALARM_CLEARED_TIME_STRING_LEN];
#define SYSMNG_ALARM_INFO_ALARM_DETAIL_STRING_LEN                        (256)
	uint8_t alarm_detail[SYSMNG_ALARM_INFO_ALARM_DETAIL_STRING_LEN];
#define SYSMNG_ALARM_INFO_ATTR_MASK_ALL                                 ((1 << 0) - 1)
	uint32_t bit_map;
} __PACK__ igd_sysmng_alarm_info_attr_conf_tab_t;

#endif
