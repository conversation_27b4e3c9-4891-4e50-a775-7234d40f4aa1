/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm easymesh obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_EASYMESH_H
#define HI_ODL_TAB_EASYMESH_H

#include "hi_odl_basic_type.h"
#include "em_consts.h"

/* controller基本设置 */
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucEnable; //是否开启Multi-AP Controller自动配置功能,1开启，0禁用
	uword8 ucPad[CM_THREE_PADS];

	uword8 ucSsid2G[EM_SSID_LEN];
	uword8 ucAuthMode2G;
	uword8 ucPad1[CM_THREE_PADS];

	uword8 ucKey2G[EM_KEY_LEN];
	uword8 ucBackFront2G;
	uword8 ucTearDown2G;
	uword8 ucHideAttr2G;
	uword8 ucPad2;
	uword8 ucSsid5G[EM_SSID_LEN];
	uword8 ucAuthMode5G;
	uword8 ucPad3[CM_THREE_PADS];
	uword8 ucKey5G[EM_KEY_LEN];
	uword8 ucBackFront5G;
	uword8 ucTearDown5G;
	uword8 ucHideAttr5G;
	uword8 ucEnablebh;// 0表示M2中不下发backhaul配置; 1,表示2.4G, 5G均下发backhaul配置 2表示仅下2.4G backhaul配置, 3表示仅下5G backhaul配置, 4表示设置2.4G 5G 回传网络为mld
	uword8 ucAuthModebh;
	uword8 ucPad4[CM_THREE_PADS];
	uword8 ucSsidbh[EM_SSID_LEN];
	uword8 ucKeybh[EM_KEY_LEN];
	uword8 ucTearDownbh;
	uword8 ucHideAttrbh;
	uword8 ucPad5[CM_TWO_PADS];
	uword32 ulBitmap;
} __PACK__ igdEmCtrConfigTab;

/* 漫游策略 */
typedef struct  {
	uword32 ulStateAndIndex;
	uword8 ucLowRSSI2G;
	uword8 ucLowRSSI5G;
	uword8 ucChannelThreshold;
	uword8 ucRoamingMode;
	uword32 ulBitmap;
} __PACK__ igdEmCtrlSteeringPolicy;

/* 强制漫游配置 */
typedef struct  {
	uword32 ulStateAndIndex;
	uword8 ucMandateRoamSta[EM_MAC_LEN];
	uword8 ucMandateRoamBss[EM_MAC_LEN];
} __PACK__ igdEmCtrlMandateSteering;

/* TOPO列表查询 */
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulDevNum;
	uword8 ucAlMac[EM_DEVNUM_MAX * EM_MAC_LEN];
	uword32 ulBitmap;
} __PACK__ igdEmTopoDevList;

/* TOPO信息查询 */
typedef struct {
	uword8 ucIntfMacAddr[EM_MAC_LEN];

	uword8 ucIntfName[EM_IFNAME_LEN];
	uword16 usMediaType;
	uword8 ucPad[CM_TWO_PADS];
} __PACK__ igdEmApInterFace;

typedef struct {
	uword32 ulRxPacket;
	uword32 ulRxPacketError;
	uword32 ulTxPacket;
	uword32 ulTxPacketError;
	uword16 usPhyRate;
	uword8 ucPad[CM_TWO_PADS];
	uword32 ulRssi;
} __PACK__ igdEmApNbLinkMetric;

typedef struct {
	uword8 ucNbAlMacAddr[EM_MAC_LEN];
	uword8 ucLocalIntfMacAddr[EM_MAC_LEN];
	uword8 ucLinkNum;
	uword8 ucpad[CM_THREE_PADS];
	igdEmApNbLinkMetric stApNbLinkTbl[EM_TOPO_LINK_NUM_MAX];
} __PACK__ igdEmAp1905NbInfo;

typedef struct {
	uword8 ucLocalIntfMacAddr[EM_MAC_LEN];
	uword8 ucNbIntfMacAddr[EM_MAC_LEN];
} __PACK__ igdEmApNon1905NbInfo;

typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucAlMacAddr[EM_MAC_LEN];
	uword8 ucDevRole;
	uword8 ucOperState;
	uword32 ulActiveTime;

	uword8 ucIntfNum;
	uword8 ucPad1[CM_THREE_PADS];
	igdEmApInterFace stIntfTbl[EM_DEVICE_INTF_LIST_SIZE];

	uword8 ucI1905NbNum;
	uword8 ucPad2[CM_THREE_PADS];
	igdEmAp1905NbInfo stAp1905NbTbl[EM_DISPLAY_TOPO_1905NB_LIST_SIZE];

	uword8 ucNon1905NbNum;
	uword8 ucPad3[CM_THREE_PADS];
	igdEmApNon1905NbInfo stApNon1905NbTbl[EM_DISPLAY_TOPO_NON1905NB_LIST_SIZE];
	uword32 ulUplinkRate;
	uword32 ulDownlinkRate;
	uword8 ucBhType;
	uword8 ucBhAlMac[EM_MAC_LEN];
	uword32 ulBitmap;
} __PACK__ igdEmApTopology;


/* 网页TOPO结构 */
typedef struct EmApWebTopology {
	igdEmApTopology *stCurNode;
	uword32 ulChildNum;
	uword32 ulJumpStep;
	struct EmApWebTopology *stChildArray[EM_DEVNUM_MAX];
	uword16 uplinkMediaType;
} __PACK__ igdEmApWebTopology;

/* client信息查询 */
typedef struct {
	uword8 ucClientMac[EM_MAC_LEN];
	uword8 ucBssId[EM_MAC_LEN];
	uword8 ucRadioIndex;
	uword32 ulAssociationTime;
	uword32 ulUplinkRate;
	uword32 ulDownlinkRate;
	word32 slUplinkRssi;
} __PACK__ igdEmApStaInfo;

typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucAlMac[EM_MAC_LEN];
	uword16  usClientNum;
	igdEmApStaInfo  stClientTbl[EM_CLIENT_STA_OF_BSS_LIST_SIZE];
	uword32 ulBitmap;
} __PACK__ igdEmApClientInfo;


/* 信道选择 */
typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucChannel2G;
	uword8 ucPowerLimit2G;
	uword8 ucChannel5G;
	uword8 ucPowerLimit5G;
	uword32 ulBitmap;
} __PACK__ igdEmChannelSelectCfg;

typedef struct  {
	uword32 ulStateAndIndex;
	uword8 ucAlMac[EM_MAC_LEN];
	uword8 pad[CM_TWO_PADS];
	uword32 ulBitmap;
} __PACK__ igdEmCtrlMacFilterSync;

typedef struct  {
	uword32 ulStateAndIndex;
	uword8 ucBssid[EM_MAC_LEN];
	uword8 pad[CM_TWO_PADS];
	uword32 ulBitmap;
} __PACK__ igdEmWpsEventParam;

typedef struct  {
	uword32 ulStateAndIndex;
	uword8 ucAlMac[EM_MAC_LEN];
	uword32 ulBitmap;
} __PACK__ igdEmCtrlUnbindAgent;

typedef struct {
	uword8 alMac[EM_MAC_LEN];//全F表示要同步到所有Agent,非全F只同步到指定的Agent
	uword8 enable;
	uword8 isWhite;
	uword8 num;
	uword8 macList[0][EM_MAC_LEN];
} __PACK__ igdEmMacFilterInfo;

typedef struct {
	uword8 alMac[EM_MAC_LEN];
} __PACK__ igdEmUnbindAgentInfo;

typedef struct {
	uword8 type;
	uword16 length;
} __PACK__ igdEmTlvHeader;

typedef struct {
	igdEmTlvHeader tlvHeader;
	uword8 mode;  //0x00:close 0x01:while 0x02:black
	uword8 num;  //max = 0x32
	uword8 macAddr[0]; //formate aabbccddeeff,aabbccddeeff
} __PACK__ igdEmTlvMacFilterModeInfo;

typedef struct {
	igdEmTlvHeader tlvHeader;
	uword8 macAddr[EM_MAC_LEN];
} __PACK__ igdEmTlvUnbindAgentInfo;

typedef struct {
	uint32_t table_id;
    uint8_t vendorOui[EM_OUI_LEN];
    uint16_t dataLen; //data长度
    uint8_t data[0]; //一个或多个tlv,总长度不要超过1k
} __attribute__((__packed__)) igd_em_vendor_spec_msg_info_t;

typedef struct {
	uint32_t table_id;
    uint8_t almac[EM_MAC_LEN];
    uint32_t onlinetime;
    uint8_t status;
    uint8_t controller_mac[EM_MAC_LEN];
} __attribute__((__packed__)) igd_ctc_mesh_info_t;

#endif
