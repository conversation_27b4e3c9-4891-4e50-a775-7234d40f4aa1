/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab remote manager obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_REMOTE_MGT_H
#define HI_ODL_TAB_REMOTE_MGT_H
#include "hi_odl_basic_type.h"

typedef struct {
	uword32 ulStateAndIndex;
#define TR069_ATTR_WEB_MODE_DISABLE (0)
#define TR069_ATTR_WEB_MODE_ENABLE (1)
	uword8 ucWebModEnable; /*web是否可修改*/
#define TR069_ATTR_RAND_INFORM_DISABLE (0)
#define TR069_ATTR_RAND_INFORM_ENABLE (1)
	uword8 ucRandInformEnable; /*默认：false*/
#define TR069_ATTR_PERIOD_INFORM_DISABLE (0)
#define TR069_ATTR_PERIOD_INFORM_ENABLE (1)
	uword8 ucPeriodicInformEnable;
#define TR069_ATTR_UPGRADES_MANAGED_NO (0)
#define TR069_ATTR_UPGRADES_MANAGED_YES (1)
	uword8 ucUpgradesManaged;

#define TR069_ATTR_STUN_DISABLE (0)
#define TR069_ATTR_STUN_ENABLE (1)
	uword8 ucStunEnable;/*默认：false*/
#define TR069_ATTR_NAT_DETECT_DISABLE (0)
#define TR069_ATTR_NAT_DETECT_ENABLE (1)
	uword8 ucNatDetected;/*默认：false*/
	uword8 aucPad[CM_TWO_PADS];

	word8 aucAcsUrl[CM_URL_LEN];
	uword32 ulPortNum;
	word8 aucUsername[CM_USERNAME_LEN];
	word8 aucPassword[CM_USERNAME_LEN];
	word8 aucConnReqUsername[CM_USERNAME_LEN];
	word8 aucConnReqPassword[CM_USERNAME_LEN];
#define TR069_ATTR_PERIOD_INFORM_INTERVAL_MIN (1)
#define TR069_ATTR_PERIOD_INFORM_INTERVAL_MAX (864000)
	uword32 ulPeriodicInformInterval;
	uword64 ulPeriodicInformTime;
	uword32 ulConnReqAddrNotiLimit;
	word8 aucStunServerAddr[CM_URL_LEN];
#define TR069_ATTR_STUN_SERVER_PORT_MIN (0)
#define TR069_ATTR_STUN_SERVER_PORT_MAX (65535)
	uword32 ulStunServerPort;
	word8 aucStunUsername[CM_USERNAME_LEN];
	word8 aucStunPassword[CM_USERNAME_LEN];
#define TR069_ATTR_STUN_KEEP_ALIVE_PERIOD_MIN (-1)
	word32 lStunMaxKeepAlivePeriod;
	uword32 ulStunMinKeepAlivePeriod;

#define TR069_PARAMETER_KEY_LEN (36) //string(32)
	word8 aucParameterKey[TR069_PARAMETER_KEY_LEN];
	word8 aucAcsOldUrl[CM_URL_LEN];
	word8 aucLanConfPassword[CM_PASSWORD_LEN];
#define TR069_ATTR_DHCP_SERVER_CONF_DISABLE (0)
#define TR069_ATTR_DHCP_SERVER_CONF_ENABLE (1)
	uword8 ucDHCPServerConf;
#define TR069_ATTR_LAN_IF_CONF_DISABLE (0)
#define TR069_ATTR_LAN_IF_ENABLE (1)
	uword8 ucLanIpIfEnable;
#define TR069_ATTR_ETH_IF_DISABLE (0)
#define TR069_ATTR_ETH_IF_ENABLE (1)
	uword8 ucLanEthIfEnable;

	uword8 ucFlag;
	uword8 ucFlag2;
	uword8 ucFirstInform;
	uword8 ucPad2;

#define TR069_ATTR_KEY_LEN (36) //string(32)
	uword8 aucDownCommandKey[TR069_ATTR_KEY_LEN];
	uword32 ulDownStartTime;
	uword32 ulDownCompleteTime;
	uword32 ulDownFaultCode;
	uword32 ulInformEventCode;
	word8 aucRBCommandKey[TR069_ATTR_KEY_LEN];
	word8 aucCertPassword[CM_PASSWORD_LEN];
	word8 aucSICommandKey[TR069_ATTR_KEY_LEN];

#define TR069_ATTR_PERSISTENT_DATA_LEN (256)
	word8 aucPersistentData[TR069_ATTR_PERSISTENT_DATA_LEN];
	word8 aucConnReqPath[TR069_ATTR_KEY_LEN];

#define TR069_ATTR_URL_LEN (128)
	word8 aucUdpConnReqAddr[TR069_ATTR_URL_LEN];
	word8 aucKickUrl[TR069_ATTR_URL_LEN];
	word8 aucDownProgressUrl[TR069_ATTR_URL_LEN];

#define TR069_INTERVAL_LEN_MIN 1
#define TR069_MULTIPLIER_LEN_MIN 2000
#define TR069_MULTIPLIER_DIV 1000
	uword32 ulMinimumWaitInterval;
	uword32 ulIntervalMultiplier;
	uint32_t uludpconnectionrequestaddressnotificationlimit;

#define TR069_ATTR_MASK_BIT0_WEB_MOD_ENABLE (0x01)
#define TR069_ATTR_MASK_BIT1_RAND_INFORM_ENABLE (0x02)
#define TR069_ATTR_MASK_BIT2_PEROID_INFORM_ENABLE (0x04)
#define TR069_ATTR_MASK_BIT3_UPGRADES_MANAGED (0x08)
#define TR069_ATTR_MASK_BIT4_STUN_ENABLE (0x10)
#define TR069_ATTR_MASK_BIT5_NAT_DETECTED (0x20)
#define TR069_ATTR_MASK_BIT6_ACS_URL (0x40)
#define TR069_ATTR_MASK_BIT7_PORTNUM (0x80)
#define TR069_ATTR_MASK_BIT8_ACS_USERNAME (0x10)
#define TR069_ATTR_MASK_BIT9_ACS_PASSWORD (0x200)
#define TR069_ATTR_MASK_BIT10_CONNREQ_USERNAME (0x400)
#define TR069_ATTR_MASK_BIT11_CONNREQ_PASSWORD (0x800)
#define TR069_ATTR_MASK_BIT12_INFORM_INTERNAL (0x1000)
#define TR069_ATTR_MASK_BIT13_INFORM_TIME (0x2000)
#define TR069_ATTR_MASK_BIT14_CONN_REQ_ADDR_NOTI_LIMIT (0x4000)
#define TR069_ATTR_MASK_BIT15_STUN_SERVER_ADDR (0x8000)
#define TR069_ATTR_MASK_BIT16_STUN_SERVER_PORT (0x10000)
#define TR069_ATTR_MASK_BIT17_STUN_USERNAME (0x20000)
#define TR069_ATTR_MASK_BIT18_STUN_PASSWORD (0x40000)
#define TR069_ATTR_MASK_BIT19_STUN_MAX_KEEPALIVE_PERIOD (0x80000)
#define TR069_ATTR_MASK_BIT20_STUN_MIN_KEETPALIVE_PERIOD (0x100000)
#define TR069_ATTR_MASK_BIT21_PARAMETER_KEY (0x200000)
#define TR069_ATTR_MASK_BIT22_ACS_OLD_URL (0x400000)
#define TR069_ATTR_MASK_BIT23_LAN_CONF_PASSWORD (0x800000)
#define TR069_ATTR_MASK_BIT24_FLAG1 (0x1000000)
#define TR069_ATTR_MASK_BIT25_FLAG2 (0x2000000)
#define TR069_ATTR_MASK_BIT26_DOWN_COMMAND_KEY (0x4000000)
#define TR069_ATTR_MASK_BIT27_DOWN_START_TIME (0x8000000)
#define TR069_ATTR_MASK_BIT28_DOWN_COMPLETE_TIME (0x10000000)
#define TR069_ATTR_MASK_BIT29_DOWN_FAULT_CLDE (0x20000000)
#define TR069_ATTR_MASK_BIT30_INFORM_EVENT_CODE (0x40000000)
#define TR069_ATTR_MASK_BIT31_RB_COMMAND_KEY (0x80000000)
#define TR069_ATTR_MASK_ALL (0xffffffff)
	uword32 ulBitmap;

#define TR069_ATTR_MASK1_BIT1_CERT_PASSWORD (0x01)
#define TR069_ATTR_MASK1_BIT2_SI_COMMAND_KEY (0x02)
#define TR069_ATTR_MASK1_BIT3_PERSISTENT_DATA (0x04)
#define TR069_ATTR_MASK1_BIT4_CONNREQ_PATH (0x08)
#define TR069_ATTR_MASK1_BIT5_UDP_CONREQ_URL (0x10)
#define TR069_ATTR_MASK1_BIT6_KICK_URL (0x20)
#define TR069_ATTR_MASK1_BIT7_DOWN_PROGRESS_URL (0x40)
#define TR069_ATTR_MASK1_BIT8_FIRST_INFORM (0x80)
#define TR069_ATTR_MASK1_BIT9_MINIMUM_WAIT_INTERVAL (0x100)
#define TR069_ATTR_MASK1_BI20_INTERVAL_MULTIPLIER (0x200)
#define TR069_ATTR_MASK1_BIT11_UDP_CONNECTION_REQUEST_ADDRESS_NOTIFICATION_LIMIT       (1 << 10)
#define TR069_ATTR_MASK1_ALL       ((1 << 11) - 1)
	uword32 ulBitmap1;
} __PACK__ IgdRmtMgtTr069AttrConfTab;

/***************************Tr069属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define TR069_INFO_URL_LEN (128)
	uword8 aucConnReqURL[TR069_INFO_URL_LEN];/*acs查询设备的URL*/

#define TR069_STATE_INFO_ATTR_MASK_BIT0_CONREQ_URL (0x01)
#define TR069_STATE_INFO_ATTR_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtTr069InfoConfTab;

/**********************单播ARP/NP探测节点配置表**************************/
#define IGD_NEIGHBOR_DETECTION_CFG_STATEANDINDEX     (0xFFFFFFFF)
#define IGD_NEIGHBOR_DETECTION_CONFIG_TAB_RECORD_NUM (8)
#define IGD_NEIGHBOR_DETECTION_INTERVAL_DEFAULT (60)
#define IGD_NEIGHBOR_DETECTION_REPETITION_DEFAULT (3)
#define IGD_NEIGHBOR_DETECTION_IPV4_FAIL_MASK (1 << 0)
#define IGD_NEIGHBOR_DETECTION_IPV6_FAIL_MASK (1 << 1)
typedef struct {
	uword32 state_and_index;

	uword8 neighbor_detection_config_index;

#define NEIGHBOR_DETECTION_CONFIG_DISABLE (0)
#define NEIGHBOR_DETECTION_CONFIG_ENABLE (1)
	uword8 config_enable;
#define NEIGHBOR_DETECTION_CONFIG_NUM_REPET_MIN (1)
#define NEIGHBOR_DETECTION_CONFIG_NUM_REPET_MAX (255)
	uword8 number_of_repetitions;
	uword8 pad;
#define NEIGHBOR_DETECTION_CONFIG_INTERVAL_MIN (1)
#define NEIGHBOR_DETECTION_CONFIG_INTERVAL_MAX (3600)
	uword32 interval;
	uword32 neighbor_wan_global_index;
	word8 neighbor_wan_name[CM_WAN_CONNECTION_NAME_LEN];

#define NEIGHBOR_DETECTION_CONFIG_MASK_BIT0_ENABLE (0x01)
#define NEIGHBOR_DETECTION_CONFIG_MASK_BIT1_NUM_OF_REPT (0x02)
#define NEIGHBOR_DETECTION_CONFIG_MASK_BIT2_INTERVAL (0x04)
#define NEIGHBOR_DETECTION_CONFIG_MASK_BIT3_WAN_GLOBAL_INDEX (0x08)
#define NEIGHBOR_DETECTION_CONFIG_MASK_BIT4_WAN_NAME (0x10)
#define NEIGHBOR_DETECTION_CONFIG_MASK_ALL (0x1F)
	uword32 bit_map;
} __PACK__ idg_neighbor_detection_config_tab;

/***************************中间键属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define MIDDWARE_ONLY_ENABLE (0)
#define TR069_ONLY_ENABLE (1)
#define MIDDWARE_AND_TR069_ENABLE (2)
	uword8 ucTr069Enable; /*默认启用tr069而关闭中间件*/
	uword8 aucPad[CM_THREE_PADS];

#define MIDDWARE_URL_LEN (128)
	word8 aucMiddlewareURL[MIDDWARE_URL_LEN];
	uword32 ulMiddwarePort;
#define MIDDWARE_MGT_ATTR_MASK_BIT0_TR069_ENABLE (0x01)
#define MIDDWARE_MGT_ATTR_MASK_BIT1_MIDDWARE_URL (0x02)
#define MIDDWARE_MGT_ATTR_MASK_BIT2_MIDDWARE_PORT (0x04)
#define MIDDWARE_MGT_ATTR_MASK_ALL (0x07)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtMiddwareAttrConfTab;


/***************************LOID注册信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define LOID_PASSWORD_CONFIG_DISABLE (0)
#define LOID_PASSWORD_CONFIG_ENABLE (1)
	uword8 ucPasswdConfigEnable; /* web use only*/

#define DEVICE_REG_STATE_INIT (0)
#define DEVICE_REG_STATE_SUCC_ONCE (1)
	uword8 device_reg_state;
	uword8 aucPad1[CM_TWO_PADS];

#define LOID_NAME_LEN (24+1)
	word8 aucLoidName[LOID_NAME_LEN];/*LOID*/
#define LOID_PASSWORD_LEN (12+1)
	word8 aucLoidPasswd[LOID_PASSWORD_LEN];
	uword8 aucPad[CM_TWO_PADS];

#define ITMS_AUTH_STATUS_SUCCESS (0)
#define ITMS_AUTH_STATUS_USER_AUTH_CODE_NOT_EXIST (1)
#define ITMS_AUTH_STATUS_USER_LOID_NOT_EXIST (2)
#define ITMS_AUTH_STATUS_LOID_MATCH_FAILED (3)
#define ITMS_AUTH_STATUS_TIMEOUT (4)
#define ITMS_AUTH_STATUS_NO_NEW_JOB_TO_EXECURE (5)
#define ITMS_AUTH_STATUS_NO_AUTH_RESULT (99)
	uword8 ucStatus; /*缺省值：99，表示无认证结果信息*/
	uword8 ucLimit;/* 缺省值：10 */
	uword8 ucTimes;/*终端认证重试次数*/
#define ITMS_WORKING_ORDER_ISSUE_START (0)
#define ITMS_WORKING_ORDER_ISSUE_SUCCESS (1)
#define ITMS_WORKING_ORDER_ISSUE_FAILED (2)
#define ITMS_WORKING_ORDER_ISSUE_IDLE (99)
	uword8 ucResult; /*缺省值为99，表示无下发结果信息*/

#define ITMS_SERVICE_NUM_MAX (8)
	uword8 ucSeviceNum;
	/*逻辑ID注册时将要下发配置的业务数量*/
#define ITMS_SERVICE_NAME_IPTV (0x01)
#define ITMS_SERVICE_NAME_INTERNET (0x02)
#define ITMS_SERVICE_NAME_VOIP (0x04)
#define ITMS_SERVICE_NAME_OTHER (0x08)
#define ITMS_SERVICE_NAME_OTT (0x10)
	uword8 ucSeviceName;/*当前下发业务名称*/
#define ITMS_AUTH_TYPE_NONE (0)
#define ITMS_AUTH_TYPE_LOID (1)
#define ITMS_AUTH_TYPE_LOID_PWD (2)
	uword8 aucAuthType;/*认证类型*/

#define PUSH_AUTH_WEB   (1)
#define NO_PUSH_AUTH_WEB (2)
	uword8 ucAuthFailProcMode;     /*认证失败后是否强推模式: 1,强制推送， 2:不推送(缺省值)*/
#define   ITMS_SERVICE_TOTAL_NAME (64)
	uword8 aucServiceTotalName[ITMS_SERVICE_TOTAL_NAME];
#define LOID_REG_ATTR_MASK_BIT0_PASSWD_CONF_ENABLE (0x01)
#define LOID_REG_ATTR_MASK_BIT1_LOID (0x02)
#define LOID_REG_ATTR_MASK_BIT2_LOID_PASSWD (0x04)
#define LOID_REG_ATTR_MASK_BIT3_STATUS (0x08)
#define LOID_REG_ATTR_MASK_BIT4_LIMITS (0x10)
#define LOID_REG_ATTR_MASK_BIT5_TIMES (0x20)
#define LOID_REG_ATTR_MASK_BIT6_RESULT (0x40)
#define LOID_REG_ATTR_MASK_BIT7_SERVICE_NUM (0x80)
#define LOID_REG_ATTR_MASK_BIT8_SERVICE_NAME (0x100)
#define LOID_REG_ATTR_MASK_BIT9_SERVICE_TOTAL_NAME (0x200)
#define LOID_REG_ATTR_MASK_ALL (0x3ff)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtLoidRegAttrConfTab;


/***************************ITMS注册状态*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define ITMS_INFORM_STATUS_SUCCESS (0)
#define ITMS_INFORM_STATUS_NO_REPORT (1)
#define ITMS_INFORM_STATUS_NO_RESPONSE (2)
#define ITMS_INFORM_STATUS_ABORT (3)
#define ITMS_INFORM_STATUS_NO_REPORT_RMT_MNG_WAN (4)
#define ITMS_INFORM_STATUS_NO_REPORT_INVALID_WAN (5)
#define ITMS_INFORM_STATUS_NO_REPORT_INVALID_ACS (6)
#define ITMS_INFORM_STATUS_NO_REPORT_FAIL_TO_GET_ACS_ADDR (7)
	uword8 ucInformStatus; /*Inform上报状态*/
#define ITMS_CONN_STATUS_CONNECTON_ACCEPTED (0)
#define ITMS_CONN_STATUS_SUCCESS (1)
#define ITMS_CONN_STATUS_CONNECTION_UNRECEIVED (2)
#define ITMS_CONN_STATUS_CONNECTION_ABORT (3)
#define ITMS_CONN_STATUS_UNKNOWN (4)
	uword8 ucITMSConnectStatus; /*ITMS连接CPE设备的状态*/
	uword8 aucPad[CM_TWO_PADS];

#define ITMS_REG_INFO_MASK_BIT0_INFORM_STATUS (0x01)
#define ITMS_REG_INFO_MASK_BIT1_CONNECTION_STATUS (0x02)
#define ITMS_REG_INFO_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtITMSRegStateTab;


/***************************PASSWORD 认证信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
#define SN_NAME_LEN (12+1)
	word8 aucSN[SN_NAME_LEN];
	// uword8 aucPad1[CM_TWO_PADS];

#define PASSWORD_LEN (10+1)
	word8 aucPasswd[PASSWORD_LEN];
	// uword8 aucPad[CM_TWO_PADS];

#define ITMS_AUTH_STATUS_SUCCESS (0)
#define ITMS_AUTH_STATUS_USER_AUTH_CODE_NOT_EXIST (1)
#define ITMS_AUTH_STATUS_UNDETERMINED2 (2)
#define ITMS_AUTH_STATUS_UNDETERMINED3 (3)
#define ITMS_AUTH_STATUS_TIMEOUT (4)
#define ITMS_AUTH_STATUS_NO_NEW_JOB_TO_EXECURE (5)
#define ITMS_AUTH_STATUS_NO_AUTH_RESULT (99)
	uword8 ucStatus; /*缺省值：99，表示无认证结果信息*/
	uword8 ucLimit;/* 缺省值：10 */
	uword8 ucTimes;/*终端认证重试次数*/
#define ITMS_WORKING_ORDER_ISSUE_START (0)
#define ITMS_WORKING_ORDER_ISSUE_SUCCESS (1)
#define ITMS_WORKING_ORDER_ISSUE_FAILED (2)
#define ITMS_WORKING_ORDER_ISSUE_IDLE (99)
	uword8 ucResult; /*缺省值为99，表示无下发结果信息*/

#define ITMS_SERVICE_NUM_MAX (8)
	uword8 ucSeviceNum;
	/*逻辑ID注册时将要下发配置的业务数量*/
#define ITMS_SERVICE_NAME_IPTV (0x01)
#define ITMS_SERVICE_NAME_INTERNET (0x02)
#define ITMS_SERVICE_NAME_VOIP (0x04)
#define ITMS_SERVICE_NAME_OTHER (0x08)
#define ITMS_SERVICE_NAME_OTT (0x10)
	uword8 ucSeviceName;/*当前下发业务名称*/
#define ITMS_AUTH_TYPE_NONE (0)
#define ITMS_AUTH_TYPE_PASSWORD (1)
#define ITMS_AUTH_TYPE_LOID_SN  (2)
	uword8 aucAuthType;/*认证类型*/
#define PUSH_AUTH_WEB   (1)
#define NO_PUSH_AUTH_WEB (2)
	uword8 ucAuthFailProcMode;     /*认证失败后是否强推模式: 1,强制推送， 2:不推送(缺省值)*/

#define PASSWORD_REG_ATTR_MASK_BIT0_PASSWD (0x01)
#define PASSWORD_REG_ATTR_MASK_BIT1_STATUS (0x02)
#define PASSWORD_REG_ATTR_MASK_BIT2_LIMITS (0x04)
#define PASSWORD_REG_ATTR_MASK_BIT3_TIMES (0x08)
#define PASSWORD_REG_ATTR_MASK_BIT4_RESULT (0x10)
#define PASSWORD_REG_ATTR_MASK_BIT5_SERVICE_NUM (0x20)
#define PASSWORD_REG_ATTR_MASK_BIT6_SERVICE_NAME (0x40)
#define PASSWORD_REG_ATTR_MASK_BIT7_SN (0x80)
#define PASSWORD_REG_ATTR_MASK_ALL (0xff)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtPasswordRegAttrConfTab;

/*************************** 远程访问web *********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulEnable;								/* 是否使能 */
	uword32 ulPort;									/* 远程访问web 端口 */
	word8	aucIpAddr[CM_IP_ADDR_STRING_LEN];		/* 远程访问web IP地址 */
#define MGTWEB_REG_ATTR_MASK_BIT0_ENABLE (0x01)
#define MGTWEB_REG_ATTR_MASK_BIT1_PORT (0x02)
#define MGTWEB_REG_ATTR_MASK_BIT2_IPADDR (0x04)
#define MGTWEB_REG_ATTR_MASK_ALL 		(0xff)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtWebAttrConfTab;

/*业务逻辑处理函数声明*/
word32 igdCmRmtMgtWebAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtWebAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtWebAttrInit(void);

/*************************** 用户和性能数据上传*********************************/

/*表相关宏*/


typedef struct {
	uword32 ulStateAndIndex;

	uword32	ulEnable;
	word8	acServerUrl[128];
	uword32	ulUploadInterval;
	uword32	ulCountInterval;

	uword8	ucReportSnEn;
	uword8	ucCpuRateEn;
	uword8	ucMemRateEn;
	uword8	ucSerEn;
	uword8	ucErrCodeEn;
	uword8	ucPlrEn;
	uword8	ucPacketLostEn;
	uword8	ucTempEn;
	uword8	ucUpdataEn;
	uword8	ucDowndataEn;
	uword8	ucAllDeviceNumEn;
	uword8	ucLanDeviceMacEn;
	uword8	ucWlanDeviceMacEn;
	uword8	ucOpticalInPowerEn;
	uword8	ucOpticalOutPowerEn;
	uword8	ucMulticastNumEn;
	uword8	ucRouteModeEn;
	uword8	ucDialingNumEn;
	uword8	ucDialingErrEn;
	uword8	ucRegisterNumEn;
	uword8	ucRegisterSucNumEn;

	uword8	ucAllWirelessChannelEn;
	uword8	ucBestWirelessChannelEn;
	uword8	ucWirelessChannelNumEn;
	uword8	ucWirelessBandWidthEn;
	uword8	ucWirelessPowerEn;

	uword8	ucQosTypeEn;
	uword8	ucWirelessTypeEn;

#define DATA_UPLOAD_ATTR_MASK_BIT0_ENABLE 						(0x01)
#define DATA_UPLOAD_ATTR_MASK_BIT1_SERVER_URL 					(0x02)
#define DATA_UPLOAD_ATTR_MASK_BIT2_UPLOAD_INTERVAL 				(0x04)
#define DATA_UPLOAD_ATTR_MASK_BIT3_COUNT_INTERVAL				(0x08)
#define DATA_UPLOAD_ATTR_MASK_BIT4_REPORT_SN_EN 				(0x10)
#define DATA_UPLOAD_ATTR_MASK_BIT5_CPU_RATE_EN 					(0x20)
#define DATA_UPLOAD_ATTR_MASK_BIT6_MEM_RATE_EN 					(0x40)
#define DATA_UPLOAD_ATTR_MASK_BIT7_SER_EN 						(0x80)
#define DATA_UPLOAD_ATTR_MASK_BIT8_ERR_CODE_EN 					(0x100)
#define DATA_UPLOAD_ATTR_MASK_BIT9_PLR_EN 						(0x200)
#define DATA_UPLOAD_ATTR_MASK_BIT10_PACKET_LOST_EN 				(0x400)
#define DATA_UPLOAD_ATTR_MASK_BIT11_TEMP_EN 					(0x800)

#define DATA_UPLOAD_ATTR_MASK_BIT12_UP_DATA_EN 					(0x1000)
#define DATA_UPLOAD_ATTR_MASK_BIT13_DOWN_DATA_EN 				(0x2000)
#define DATA_UPLOAD_ATTR_MASK_BIT14_ALL_DEV_NUM_EN 				(0x4000)
#define DATA_UPLOAD_ATTR_MASK_BIT15_LAN_DEV_MAC_EN				(0x8000)
#define DATA_UPLOAD_ATTR_MASK_BIT16_WLAN_DEV_MAC_EN 			(0x10000)
#define DATA_UPLOAD_ATTR_MASK_BIT17_OPTICAL_IN_POWER_EN 		(0x20000)
#define DATA_UPLOAD_ATTR_MASK_BIT18_OPTICAL_OUT_POWER_EN		(0x40000)
#define DATA_UPLOAD_ATTR_MASK_BIT19_MC_NUM_EN 					(0x80000)
#define DATA_UPLOAD_ATTR_MASK_BIT20_ROUTE_MODE_EN 				(0x100000)
#define DATA_UPLOAD_ATTR_MASK_BIT21_DIAL_NUM_EN 				(0x200000)
#define DATA_UPLOAD_ATTR_MASK_BIT22_DIAL_ERR_EN 				(0x400000)
#define DATA_UPLOAD_ATTR_MASK_BIT23_REGISTER_NUM_EN 			(0x800000)
#define DATA_UPLOAD_ATTR_MASK_BIT24_REGISTER_SUC_NUM_EN 		(0x1000000)
#define DATA_UPLOAD_ATTR_MASK_BIT25_ALL_WIRELESS_CHANNEL_EN 	(0x2000000)
#define DATA_UPLOAD_ATTR_MASK_BIT26_BEST_WIRELESS_CHANNEL_EN	(0x4000000)
#define DATA_UPLOAD_ATTR_MASK_BIT27_WIRELESS_CHANNEL_NUM_EN 	(0x8000000)
#define DATA_UPLOAD_ATTR_MASK_BIT28_WIRELESS_BANDWIDTH_EN 		(0x10000000)
#define DATA_UPLOAD_ATTR_MASK_BIT29_WIRELESS_POWER_EN 			(0x20000000)
#define DATA_UPLOAD_ATTR_MASK_BIT30_QOS_TYPE_EN 				(0x80000000)
#define DATA_UPLOAD_ATTR_MASK_BIT31_WIRELESS_TYPE_EN 			(0x40000000)

#define DATA_UPLOAD_ATTR_MASK_ALL (0xffffffff)
	uword32 ulBitmap;
} __PACK__ IgdRmtMgtDataUploadAttrConfTab;

/*业务逻辑处理函数声明*/
word32 igdCmRmtMgtDataUploadAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtMgtDataUploadAttrGet(uword8 *pucInfo, uword32 len);

word32 igdCmRmtMgtDataUploadAttrInit(void);



typedef struct {
	uword32 ulStateAndIndex;
#define REMOTEMGT_RESTORE_STATUS_DEFAULT (0)
#define REMOTEMGT_RESTORE_STATUS_LONG (1)
#define REMOTEMGT_RESTORE_STATUS_SHORT (2)
#define REMOTEMGT_RESTORE_STATUS_LOCAL (3)
#define REMOTEMGT_RESTORE_STATUS_ITMS (4)
#define REMOTEMGT_RESTORE_STATUS_PLATFORM (5)
	uword8   ucRestoreStatus;

#define  REMOTEMGT_TAG_TRIGGER_OP (0)
#define  REMOTEMGT_TAG_UNTRIGGER_OP (1)
	uword8 ucOPTag;
	uword8 aucPad[CM_TWO_PADS];

#define REMOTEMGT_RESTORE_MODE_SAVE_KEYCONFIG (0)
#define REMOTEMGT_RESTORE_MODE_NOTSAVE_KEYCONFIG (1)
	word32 mode;

#define REMOTEMGT_RESTORE_ATTR_MASK_RESTORE_STATUS              (0x01)
#define REMOTEMGT_RESTORE_ATTR_MASK_MODE                        (0x02)
#define REMOTEMGT_RESTORE_ATTR_MASK_ALL                         (0x03)
	uword32 ulBitmap;
} __PACK__ IgdRmtRestoreTab;

/*òμ?????-′|àíoˉêyéù?÷*/

word32 igdCmRmtRestoreAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmRmtRestoreAttrGet(uword8 *pucInfo, uword32 len);

/*表相关宏*/


typedef struct {
	word8	acReportSn[32];
	word8	acCpuRate[32];
	word8	acMemRate[32];
	word8	acSer[32];
	word8	acErr[32];
	word8	acPlr[32];
	word8	acPacketLost[32];
	word8	acTemp[32];
	word8	acUpdata[128];
	word8	acDowndata[128];
	word8	acAllClientNum[32];
	word8	acLanClient[128];
	word8	acWlanClient[128];
	word8	acOpticalInPower[32];
	word8	acOpticalOutPower[32];
	word8	acMulticastNum[32];
	word8	acRoutingMode[32];
	word8	acDialingNum[32];
	word8	acDialingErr[32];
	word8	acRegisterNum[32];
	word8	acRegisterSucNum[32];

	word8	acAllWlanChannel[128];
	word8	acBestWlanChannel[128];
	word8	acWlanChannel[32];
	word8	acWlanBw[32];
	word8	acWlanPower[32];

	word8	acQosType[32];
	word8	acWlanMode[32];
} __PACK__ IgdRmtMgtDataUploadResultConfTab;

#endif
