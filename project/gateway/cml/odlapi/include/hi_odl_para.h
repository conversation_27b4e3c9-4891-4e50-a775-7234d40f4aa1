/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: cm tab attribute large value function
 * Author: HSAN
 * Create: 2024-06-17
 */
#ifndef HI_ODL_PARA_H
#define HI_ODL_PARA_H
#include <stddef.h>
#include <string.h>
#include "hi_odl_tab_cfg_def.h"

#define HI_ODL_LARGE_V_H   "FILE@"
#define HI_ODL_TMP_EXT     "_tmp"
#define HI_ODL_DFILE_PATH  "/tmp/.mib"

inline static char *fpath_from_tag(const char *tag)
{
	char *fpath = strstr(tag, HI_ODL_LARGE_V_H);
	return (fpath != NULL) ? (fpath + strlen(HI_ODL_LARGE_V_H)) : (fpath);
}

int32_t hi_odl_get_lstring(const char *tag, char *buf, uint32_t size);

int32_t hi_odl_set_lstring(const char *tag, const char *buf, uint32_t size);

int32_t hi_odl_lstring_tag(int32_t tabid, int32_t inst, const char *para_name, char *tag, uint32_t size);

int32_t hi_odl_lstring_size(const char *tag);

void lstring_get_fpath(const char *tag, char *fpath, uint32_t size);

void lstring_tmp_fpath(const char *tag, char *fpath, uint32_t size);

char *hi_odl_lstring_dup(const char *tag);

char *hi_odl_lstring_dup_tmp(const char *tag);

#endif
