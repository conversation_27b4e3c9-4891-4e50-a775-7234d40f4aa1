/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm awifi obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_AFIFI_H
#define HI_ODL_TAB_AFIFI_H
#include "hi_odl_basic_type.h"

#define IGD_AWIFI_SERVER_URL_LENGTH   64
#define IGD_AWIFI_UPGRADE_KEY_LENGTH  32
#define IGD_AWIFI_CITYCODE_LENGTH     16
#define IGD_AWIFI_VERSION_LENGTH	32
#define IGD_AWIFI_VENDOR_LENGTH	 	32
#define IGD_AWIFI_GW_ID_LENGTH		128
#define IGD_AWIFI_PORTAL_SWITH_OFF   0
#define IGD_AWIFI_PORTAL_SWITH_ON   1

typedef struct {
	uword32 ulStateAndIndex;
#define IGD_AWIFI_PPPOE_DOMAIN_LENGTH   32
	uword8	aucDomain[IGD_AWIFI_PPPOE_DOMAIN_LENGTH + 4];
#define IGD_AWIFI_PPPOE_COMMAND_OFF		0
#define IGD_AWIFI_PPPOE_COMMAND_ON		1
	uword32 ulCommand; /*0:OFF 1:ON*/
#define IGD_AWIFI_PPPOE_DOMAIN_ATTR_MASK_BIT0_DOMAIN	0x1
#define IGD_AWIFI_PPPOE_DOMAIN_ATTR_MASK_BIT1_COMMAND	0x2
	uword32 ulBitmap;
} __PACK__ IgdAwifiPPPoEDomainAttrConfTab;

typedef struct {
	uword32 ulStateAndIndex;

	uword8	aucRegisterServer[IGD_AWIFI_SERVER_URL_LENGTH + 4];
	uword32 ulRegisterPort;
	uword8	aucAuthServer[IGD_AWIFI_SERVER_URL_LENGTH + 4];
	uword32 ulAuthPort;
	uword8	aucPortalServer[IGD_AWIFI_SERVER_URL_LENGTH + 4];
	uword32 ulPortalPort;
	uword8	aucUtilsServer[IGD_AWIFI_SERVER_URL_LENGTH + 4];
	uword32 ulUtilsPort;
#define IGD_AWIFI_DEFAULT_SERVER_MASK_BIT0_REGISTER_SERVER	0x1
#define IGD_AWIFI_DEFAULT_SERVER_MASK_BIT1_REGISTER_PORT	0x2
#define IGD_AWIFI_DEFAULT_SERVER_MASK_BIT2_AUTH_SERVER		0x4
#define IGD_AWIFI_DEFAULT_SERVER_MASK_BIT3_AUTH_PORT		0x8
#define IGD_AWIFI_DEFAULT_SERVER_MASK_BIT4_PORTAL_SERVER	0x10
#define IGD_AWIFI_DEFAULT_SERVER_MASK_BIT5_PORTAL_PORT		0x20
#define IGD_AWIFI_DEFAULT_SERVER_MASK_BIT6_UTILS_SERVER		0x40
#define IGD_AWIFI_DEFAULT_SERVER_MASK_BIT7_UTILS_PORT		0x80
#define IGD_AWIFI_DEFAULT_SERVER_MASK_ALL 					0xff
	uword32 ulBitmap;
} __PACK__ IgdAwifiDefaultServerAttrConfTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword8	aucUpgradeServer[IGD_AWIFI_SERVER_URL_LENGTH + 4];
	uword8	aucKey[IGD_AWIFI_UPGRADE_KEY_LENGTH + 4];
	uword8	aucReportServer[IGD_AWIFI_SERVER_URL_LENGTH + 4];
	uword8	aucCityCode[IGD_AWIFI_CITYCODE_LENGTH + 4];
#define IGD_AWIFI_AUTO_UPGRADE_MASK_BIT0_UPGRADE_SERVER		0x1
#define IGD_AWIFI_UPGRADE_MASK_MASK_BIT1_KEY				0x2
#define IGD_AWIFI_UPGRADE_MASK_MASK_BIT2_REPORT_SERVER		0x4
#define IGD_AWIFI_UPGRADE_MASK_MASK_BIT3_CITYCODE			0x8
#define IGD_AWIFI_UPGRADE_MASK_MASK_ALL 					0xf
	uword32 ulBitmap;
} __PACK__ IgdAwifiAutoUpgradeAttrConfTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword8	aucAwifiVersion[IGD_AWIFI_VERSION_LENGTH];	/*awifi软件版本。从awifi组件配置文件获取*/
	uword8	aucSupplier[IGD_AWIFI_VENDOR_LENGTH];	/*供应商。VendorID目前使用的SUNN，是否要用厂商拼音ZhaoGe*/
#define CM_MAC_ADDR_STRING_LEN 18
	uword8	aucGwMacAddress[CM_MAC_ADDR_STRING_LEN];	/*网关MAC地址--网管导入的MAC--确认是哪个mac。先用PON MAC*/
	uword8	aucGwID[IGD_AWIFI_GW_ID_LENGTH];	/*厂家ID*/
	uword32 ulPort;								/*awifi绑定端口。默认2060。如果配置文件配置Gatewayport，则以配置文件为准*/
	uword8	aucGwIP[CM_IP_ADDR_STRING_LEN];			/*网关IP地址*/
#define IGD_AWIFI_FIRMWARE_INFO_MASK_BIT0_GW_ID  0x1
} __PACK__ IgdAwifiFirmwareInfoTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword32	ulGlobalLanSwitch;					/*全局LAN Switch开关*/
	uword32	ulLanPortSwitch;					/*LAN&WLAN开关。bit 0、1、2、3 LAN1、LAN2、LAN3、LAN4，bit5、6、7、8 SSID1、SSID2、SSID3、SSID4*/
	uword8 aucIPv4Addr[CM_IP_ADDR_LEN];
	uword8 aucSubnetMask[CM_IP_ADDR_LEN];
	uword8 aucMinAddr[CM_IP_ADDR_LEN]; /*起始地址*/
	uword8 aucMaxAddr[CM_IP_ADDR_LEN]; /*结束地址*/
	word32 lDhcpLeaseTime;/*延续时间：-1表示无限*/
#define IGD_AWIFI_GLOBAL_LANSWITH_MASK_BIT0_LANSWITCH   0x1
#define IGD_AWIFI_GLOBAL_LANSWITH_MASK_BIT1_PORTSWITCH  0x2
#define IGD_AWIFI_GLOBAL_LANSWITH_MASK_BIT2_IP			0x4
#define IGD_AWIFI_GLOBAL_LANSWITH_MASK_BIT3_NETMASK		0x8
#define IGD_AWIFI_GLOBAL_LANSWITH_MASK_BIT4_DHCP_MIN_IP	0x10
#define IGD_AWIFI_GLOBAL_LANSWITH_MASK_BIT5_DHCP_MAX_IP	0x20
#define IGD_AWIFI_GLOBAL_LANSWITH_MASK_BIT6_DHCP_LEASE	0x40
	uword32 ulBitmap;
} __PACK__ IgdAwifiLanAuthConfTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword8  aucRegisterServer[IGD_AWIFI_SERVER_URL_LENGTH + 4];
	uword32 ulRegisterPort;
	uword8  aucRegisterUrl[IGD_AWIFI_SERVER_URL_LENGTH + 4];
	uword8  aucAuthServer[IGD_AWIFI_SERVER_URL_LENGTH + 4];
	uword32 ulAuthPort;
	uword8  aucAuthUrl[IGD_AWIFI_SERVER_URL_LENGTH + 4];
	uword32 ulBitmap;
#define IGD_AWIFI_CUSTOM_SITE_SERVER_MASK_BIT0_REGISTER_SERVER	0x1
#define IGD_AWIFI_CUSTOM_SITE_SERVER_MASK_BIT1_REGISTER_PORT	0x2
#define IGD_AWIFI_CUSTOM_SITE_SERVER_MASK_BIT2_REGISTER_URL		0x4
#define IGD_AWIFI_CUSTOM_SITE_SERVER_MASK_BIT3_AUTH_SERVER		0x8
#define IGD_AWIFI_CUSTOM_SITE_SERVER_MASK_BIT4_AUTH_PORT		0x10
#define IGD_AWIFI_CUSTOM_SITE_SERVER_MASK_BIT5_AUTH_URL			0x20
} __PACK__ IgdAwifiCustomSiteServerAttrTab;

typedef struct awifi_firmware_info_entry {
    unsigned int  ulStateAndIndex;
    char aucSupplier[IGD_AWIFI_VENDOR_LENGTH];
    char aucGwID[IGD_AWIFI_GW_ID_LENGTH];
    unsigned int  ulPort;
    unsigned int  ulBitmap;
} igd_awifi_firmware_info_tab;

#endif
