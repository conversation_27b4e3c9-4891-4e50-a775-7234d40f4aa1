/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab url filter obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_URL_FILTER_H
#define HI_ODL_TAB_URL_FILTER_H
#include "hi_odl_basic_type.h"

/***************************URL过滤基本属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define URL_FILTER_DISABLE (0)
#define URL_FILTER_ENABLE (1)
	uword8 ucUrlFilterEnable;
#define URL_FILTER_MODE_BLACK_USEFUL (0)
#define URL_FILTER_MODE_WHITE_USEFUL (1)
	uword8 ucFilterMode;
	uword8 aucPad[CM_TWO_PADS];

#define URL_FILTER_ATTR_MASK_BIT0_FILETER_ENABLE (0x01)
#define URL_FILTER_ATTR_MASK_BIT1_FILTER_MODE (0x02)
#define URL_FILTER_ATTR_MASK_ALL (0x03)
	uword32 ulBitmap;
} __PACK__ IgdSecurUrlFilterAttrConfTab;

/***************************URL过滤名单*********************************/
#define IGD_SECUR_URL_FILTER_LIST_RECORD_NUM (100)


typedef struct {
	uword32 ulStateAndIndex;

	uword32 ulIndex;
#define URL_FILTER_LIST_DISABLE 0
#define URL_FILTER_LIST_ENABLE 1
	uint32_t enable;

#define URL_FILTER_LIST_PROTO_IPV4 0x1
#define URL_FILTER_LIST_PROTO_IPV6 0x2
#define URL_FILTER_LIST_PROTO_IPV4_OR_IPV6 0x3
	uint32_t protocol_mask;
	word8 aucFilterUrl[CM_URL_LEN];
#define URL_FILTER_LIST_ATTR_ENABLE_MASK    (1 << 0)
#define URL_FILTER_LIST_ATTR_PROTOCOL_MASK  (1 << 1)
#define URL_FILTER_LIST_ATTR_FILTERURL_MASK (1 << 2)
#define URL_FILTER_LIST_ATTR_MASK_ALL       ((1 << 3) - 1)
	uword32 ulBitmap;
} __PACK__ IgdSecurUrlFilterListAttrConfTab;


/***************************Dbus URL过滤名单*********************************/
#define IGD_SECUR_URL_FILTER_DBUS_LIST_RECORD_NUM (100)

#define IGD_SECUR_URL_FILTER_URLLIST_FILE_LEN (64)
#define IGD_SECUR_URL_FILTER_TEMP_FILE "/tmp/urlfilter_urllist_"
#define IGD_SECUR_URL_FILTER_WEEKDAY_LEN (32)
#define IGD_SECUR_URL_FILTER_TIME_LEN (64)


typedef struct {
	uword32 ulStateAndIndex;

	uword32 ulIndex;
#define URL_FILTER_DBUS_DISABLE (0)
#define URL_FILTER_DBUS_ENABLE (1)
	uword8 ucUrlFilterDbusEnable;
	uword8 ucMode;
	uword8 aucPad[CM_TWO_PADS];
	word8 aucFilterUrl[CM_URL_LEN];
	word8 aucFilterMac[CM_URL_FILTER_MAC_LEN];
#define URL_FILTER_DBUS_NAME_LEN64 (64)
	word8 aucFilterName[URL_FILTER_DBUS_NAME_LEN64];
	uword8 aucURLListFile[IGD_SECUR_URL_FILTER_URLLIST_FILE_LEN];
	uword8 aucWeekDays[IGD_SECUR_URL_FILTER_WEEKDAY_LEN];
	uword8 aucTime[IGD_SECUR_URL_FILTER_TIME_LEN];

	/*下面参数不存mib*/
	uword32 ulBlockedTimes;

#define URL_FILTER_DBUS_LIST_ATTR_BIT0_ENABLE (0x1)
#define URL_FILTER_DBUS_LIST_ATTR_BIT1_URL (0x2)
#define URL_FILTER_DBUS_LIST_ATTR_BIT2_NAME (0x4)
#define URL_FILTER_DBUS_LIST_ATTR_BIT3_MAC (0x8)
#define URL_FILTER_DBUS_LIST_ATTR_BIT4_MODE (0x10)
#define URL_FILTER_DBUS_LIST_ATTR_BIT5_URLLIST (0x20)
#define URL_FILTER_DBUS_LIST_ATTR_BIT6_WEEKDAYS (0x40)
#define URL_FILTER_DBUS_LIST_ATTR_BIT7_TIME (0x80)

#define URL_FILTER_DBUS_LIST_ATTR_MASK_ALL (0xff)
	uword32 ulBitmap;
} __PACK__ IgdSecurUrlFilterDbusListAttrConfTab;

#endif
