/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm client download obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_CLIENT_DOWNLOAD_H
#define HI_ODL_TAB_CLIENT_DOWNLOAD_H
#include "hi_odl_basic_type.h"

/***************************FTP Client 远程下载*********************************/
#define IGD_PM_FTP_CLIENT_DOWNLOAD_TAB_RECORD_NUM (16)
typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucFtpClinetIndex;
#define FTP_CLIENT_DOWNLOAD_FTP_DISABLE (0)
#define FTP_CLIENT_DOWNLOAD_FTP_ENABLE (1)
	uword8 ucClientEnable;			/*启动FTP客户端服务*/
#define  FTP_CLIENT_DOWNLOAD_PROTOCOL_HTTP (0)
#define  FTP_CLIENT_DOWNLOAD_PROTOCOL_FTP (1)
	uword8 ucprotocol;			/*HTTP/FTP*/
#define FTP_CLIENT_DOWNLOAD_LOGIN_DISABLE (0)
#define FTP_CLIENT_DOWNLOAD_LOGIN_ENABLE (1)
	uword8 ucAnonymousLoginEnable;	/*匿名登录*/

	uword8 aucFtpServerURL[CM_URL_LEN];	/*serverip或域名*/
	word32 lFtpServerPort;				/*server 端口号*/
	uword8 aucUserName[CM_USERNAME_LEN];	/*登录用户名*/
	uword8 aucPassWord[CM_PASSWORD_LEN];	/*登录密码*/
	uword8 aucDownLoadPath[CM_URL_LEN];	/*下载路径*/
	uword8 aucSavePath[CM_URL_LEN];			/*保存路径*/

#define FTP_CLIENT_STATUS_NODOWNLOAD (0)
#define FTP_CLIENT_STATUS_DOWNLOADING (1)
#define FTP_CLIENT_STATUS_DOWNLOADED (2)
#define FTP_CLIENT_STATUS_DOWNLOAD_FAILE (3)

	uword8 ucDownLoadStatus;				/*下载状态*/
#define FTP_CLIENT_CONNECTION_STATUS_FAILURE (0)
#define FTP_CLIENT_CONNECTION_STATUS_SUCCESS (1)
	uword8 ucConnectStatus;					/*链接状态*/
	uword8 aucPad[CM_TWO_PADS];

	uword8 aucUsbPart[CM_URL_LEN];


	uword32 ulHttpResultByte;                /*返回下载结果字节数*/
#define HTTP_DOWNLOAD_TEST_DISABLE (0)
#define HTTP_DOWNLOAD_TEST_ENABLE  (1)
	uword8  ucHttpDownLoadTest;
	uword8  aucPad1[CM_THREE_PADS];


#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT0_CLIENTENABLE (0x01)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT1_PROTOCOL (0x02)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT2_ANONYMOUSLOGINENABLE (0x04)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT3_FTPSERVERURL (0x08)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT4_FTPSERVERPORT (0x10)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT5_USERNAME (0x20)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT6_PASSWORD (0x40)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT7_DOWNLOADPATH (0x80)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT8_SAVEPATH (0x100)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT9_CONNECTIONSTATUS (0x200)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT10_USBPART (0x400)

#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT11_RESULTBYTE (0x800)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_BIT12_DOWNLOADTEST (0x1000)
#define FTP_CLIENT_DOWNLOAD_CFG_ATTR_MASK_ALL (0x1fff)

	uword32 ulBitmap;
} __PACK__ IgdPMFtpClientDownloadCfgAttrConfTab;


#endif
