/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef HI_ODL_TAB_HARD_ACC_CANCEL_MGR_H
#define HI_ODL_TAB_HARD_ACC_CANCEL_MGR_H
#include "hi_odl_basic_type.h"

/* FIXME: domain_list\dest_ip_list\mac_list 如何存储 */
typedef struct {
	uword32 state_and_index;
	uword8 enable;
	uword8 pad1[3];
	uword32 ha_cancel_list_number;

#define IGD_ATTR_MASK_HARDACCCANCELMANAGER_ENABLE (0x01)
#define IGD_ATTR_MASK_HARDACCCANCELMANAGER_NUMBER (0x02)
#define IGD_ATTR_MASK_HARDACCCANCELMANAGER_ALL (0xffff)

	uword32 bitmap;
} __PACK__ IgdHardAccCancelManagerAttrConfTab;

#endif
