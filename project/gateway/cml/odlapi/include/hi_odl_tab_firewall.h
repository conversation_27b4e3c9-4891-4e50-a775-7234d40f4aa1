/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab firewall obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_FIREWALL_H
#define HI_ODL_TAB_FIREWALL_H
#include "hi_odl_basic_type.h"

/***************************防火墙基本属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
#define FIREWALL_DISABLE (0)
#define FIREWALL_ENABLE (1)
	uword8 ucFirlewallEnable;/*默认启用*/

#define DOS_DISABLE (0)
#define DOS_ENABLE (1)
#define  DOS_SYNFLOOD       (0x1)
#define  DOS_ICMPECHO       (0x2)
#define  DOS_ICMPREDIRECT   (0x4)
#define  DOS_LAND           (0x8)
#define  DOS_SMURF          (0x10)
#define  DOS_WINNUKE        (0x20)
#define  DOS_PINGSWEEP      (0x40)
	uword8 ucForbidDosEnable; /*默认启用*/
#define FOBID_PORT_SCAN_DISABLE (0)
#define FOBID_PORT_SCAN_ENABLE (1)
	uword8 ucForbidScanPort; /*默认启用防端口扫描*/
#define FORBID_BAD_PKG_DISABLE (0)
#define FORBID_BAD_PKG_ENABLE (1)
	uword8 ucForbidBadPkg; /*默认启用防端口扫描*/

#define FIREWALL_LEVEL_LOW (0)
#define FIREWALL_LEVEL_MEDIUM (1)
#define FIREWALL_LEVEL_HIGH (2)
#define FIREWALL_LEVEL_CUSTOM (3)
	uword8 ucFirewallLevel;/*默认值 中*/

#define IPV6_SESSION_SECURITY_DISABLE (0)
#define IPV6_SESSION_SECURITY_ENABLE (1)
	uword8 ucIPv6SessionSecurityEnable;
	uword8 aucPad[CM_TWO_PADS];

#define FIREWALL_ATTR_MASK_BIT0_ENALBE (0x01)
#define FIREWALL_ATTR_MASK_BIT1_FORBID_DOS (0x02)
#define FIREWALL_ATTR_MASK_BIT2_FORBID_PORT_SCAN (0x04)
#define FIREWALL_ATTR_MASK_BIT3_FORBID_BAD_PKG (0x08)
#define FIREWALL_ATTR_MASK_BIT4_FIREWALLLEVEL (0x10)
#define FIREWALL_ATTR_MASK_BIT5_IPV6_SESSION_SECURITY_ENABLE (0x20)

#define FIREWALL_ATTR_MASK_ALL (0x1f)
	uword32 ulBitmap;
} __PACK__ IgdSecurFirewallAttrConfTab;


/***************************IPv6 Session Whitelist*********************************/

#define IGD_SECUR_IPV6_SESSION_WHITELIST_NUM (100)

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;
#define IPV6_SESSION_FILTER_DISABLE (0)
#define IPV6_SESSION_FILTER_ENABLE (1)
	uword32 ulEnable; /* 默认使能 */
#define IPV6_SESSION_FILTER_PROTOCOL_ALL (0)
#define IPV6_SESSION_FILTER_TCP_UDP (1)
#define IPV6_SESSION_FILTER_TCP (2)
#define IPV6_SESSION_FILTER_UDP (3)
#define IPV6_SESSION_FILTER_ICMP (4)
	uword32 ulProtocol;
#define IPV6_SESSION_FILTER_PORT_MIN (0)
#define IPV6_SESSION_FILTER_PORT_MAX (65535)
	uword32 ulDestPort;
#define IPV6_SESSION_RULE_NAME_LEN (256)
	word8 aucRuleName[IPV6_SESSION_RULE_NAME_LEN];
#define IPV6_ADDRESS_LEN (64)
	word8 aucRemoteIPv6Address[IPV6_ADDRESS_LEN];

#define IPV6_SESSION_WHITELIST_MASK_BIT0_NAME (0x01)
#define IPV6_SESSION_WHITELIST_MASK_BIT1_ENABLE (0x02)
#define IPV6_SESSION_WHITELIST_MASK_BIT2_ADDR (0x04)
#define IPV6_SESSION_WHITELIST_MASK_BIT3_PORT (0x08)
#define IPV6_SESSION_WHITELIST_MASK_BIT4_PROTO (0x10)
#define IPV6_SESSION_WHITELIST_MASK_ALL (0x1f)
	uword32 ulBitmap;
} __PACK__ IgdSecureIPv6SessionWhiteListTab;

/***************************远端控制基本属性表*********************************/
#define IGD_SECUR_REMOTE_CONTROL_LIST_NUM 32

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;

#define SECUR_REMOTE_CONTROL_PROTOCOL_TELNET (1)
#define SECUR_REMOTE_CONTROL_PROTOCOL_FTP (2)
#define SECUR_REMOTE_CONTROL_PROTOCOL_TFTP (3)
#define SECUR_REMOTE_CONTROL_PROTOCOL_HTTP (4)
#define SECUR_REMOTE_CONTROL_PROTOCOL_PING (5)
	uword32 ulProtocol;

#define SECUR_REMOTE_CONTROL_INTERFACE_NONE (0)
#define SECUR_REMOTE_CONTROL_INTERFACE_LAN (1)
#define SECUR_REMOTE_CONTROL_INTERFACE_WAN (2)
#define SECUR_REMOTE_CONTROL_INTERFACE_ALL (3)
	uword32 ulInterface; /* 启用的接口*/
	uword32 ulLanPort; /* LAN侧端口*/
	uword32 ulWanPort; /* LAN侧端口*/

#define SECURE_REMOTE_CONTROL_ATTR_MASK_INDEX (1 << 0)
#define SECURE_REMOTE_CONTROL_ATTR_MASK_PROTOCOL (1 << 1)
#define SECURE_REMOTE_CONTROL_ATTR_MASK_INTERFACE (1 << 2)
#define SECURE_REMOTE_CONTROL_ATTR_MASK_LAN_PORT (1 << 3)
#define SECURE_REMOTE_CONTROL_ATTR_MASK_WAN_PORT (1 << 4)

	uword32 ulBitmap;
} __PACK__ IgdSecurRemoteControlListTab;

#define  IGD_FIREWALL_TRAFFIC_CONTROL_MAXNUM                             (8)

typedef struct
{
	uint32_t state_and_index;
	uint32_t instance_index;
#define FIREWALL_TRAFFIC_CONTROL_NAME_STRING_LEN                         (256)
	uint8_t  name[FIREWALL_TRAFFIC_CONTROL_NAME_STRING_LEN];
	uint8_t  enable;
	uint8_t  target;
	uint8_t  operate;
	uint8_t  priority;
#define FIREWALL_TRAFFIC_CONTROL_IP_SET_INDEX_MAX                        (8)
#define FIREWALL_TRAFFIC_CONTROL_SET_NAME_STRING_LEN                     (64)
	uint8_t  set_name[FIREWALL_TRAFFIC_CONTROL_IP_SET_INDEX_MAX * FIREWALL_TRAFFIC_CONTROL_SET_NAME_STRING_LEN];
#define FIREWALL_TRAFFIC_CONTROL_ATTR_MASK_NAME                          (1 << 0)
#define FIREWALL_TRAFFIC_CONTROL_ATTR_MASK_ENABLE                        (1 << 1)
#define FIREWALL_TRAFFIC_CONTROL_ATTR_MASK_TARGET                        (1 << 2)
#define FIREWALL_TRAFFIC_CONTROL_ATTR_MASK_OPERATE                       (1 << 3)
#define FIREWALL_TRAFFIC_CONTROL_ATTR_MASK_PRIORITY                      (1 << 4)
#define FIREWALL_TRAFFIC_CONTROL_ATTR_MASK_SET_NAME                      (1 << 5)
#define FIREWALL_TRAFFIC_CONTROL_ATTR_MASK_ALL                          ((1 << 6) - 1)
	uint32_t bit_map;
} __PACK__ igd_firewall_traffic_control_attr_conf_tab_t;

#define  IGD_FIREWALL_NETWORK_ACCELERATION_MAXNUM                        (8)

typedef struct
{
	uint32_t state_and_index;
	uint32_t instance_index;
#define FIREWALL_NETWORK_ACCELERATION_NAME_STRING_LEN                    (256)
	uint8_t  name[FIREWALL_NETWORK_ACCELERATION_NAME_STRING_LEN];
	uint8_t  enable;
	uint8_t  operate;
	uint8_t  pad[CM_TWO_PADS];
#define FIREWALL_NETWORK_ACCELERATION_IP_SET_INDEX_MAX                   (8)
#define FIREWALL_NETWORK_ACCELERATION_SET_NAME_STRING_LEN                (64)
	uint8_t  set_name[FIREWALL_NETWORK_ACCELERATION_IP_SET_INDEX_MAX * FIREWALL_NETWORK_ACCELERATION_SET_NAME_STRING_LEN];
	uint8_t  wan_name[FIREWALL_NETWORK_ACCELERATION_NAME_STRING_LEN];
#define FIREWALL_NETWORK_ACCELERATION_ATTR_MASK_NAME                     (1 << 0)
#define FIREWALL_NETWORK_ACCELERATION_ATTR_MASK_ENABLE                   (1 << 1)
#define FIREWALL_NETWORK_ACCELERATION_ATTR_MASK_OPERATE                  (1 << 2)
#define FIREWALL_NETWORK_ACCELERATION_ATTR_MASK_SET_NAME                 (1 << 3)
#define FIREWALL_NETWORK_ACCELERATION_ATTR_MASK_WAN_NAME                 (1 << 4)
#define FIREWALL_NETWORK_ACCELERATION_ATTR_MASK_ALL                     ((1 << 5) - 1)
	uint32_t bit_map;
} __PACK__ igd_firewall_network_acceleration_attr_conf_tab_t;

#define  IGD_FIREWALL_IPSET_CONFIG_MAXNUM                                         (500)

typedef struct
{
	uint32_t state_and_index;
	uint32_t instance_index;
#define FIREWALL_IPSET_CONFIG_SETNAME_STRING_LEN                                  (64)
	char  set_name[FIREWALL_IPSET_CONFIG_SETNAME_STRING_LEN];
	uint32_t protocol;
	uint32_t direction;
#define FIREWALL_IPSET_CONFIG_SETTYPE_STRING_LEN                                  (32)
	char  set_type[FIREWALL_IPSET_CONFIG_SETTYPE_STRING_LEN];
	uint32_t entry_num;
#define FIREWALL_IPSET_CONFIG_ATTR_MASK_SETNAME                                  (1 << 0)
#define FIREWALL_IPSET_CONFIG_ATTR_MASK_PROTOCOL                                  (1 << 1)
#define FIREWALL_IPSET_CONFIG_ATTR_MASK_DIRECTION                                 (1 << 2)
#define FIREWALL_IPSET_CONFIG_ATTR_MASK_SETTYPE                                  (1 << 3)
#define FIREWALL_IPSET_CONFIG_ATTR_MASK_ENTRYNUM                                  (1 << 4)
#define FIREWALL_IPSET_CONFIG_ATTR_MASK_ALL                                      ((1 << 5) - 1)
	uint32_t bit_map;
} __PACK__ igd_firewall_ipset_config_attr_conf_tab_t;

typedef struct
{
	char type[32];
	uint32_t match_count;
} __PACK__ igd_firewall_ipset_type_match_t;

#define  IGD_FIREWALL_IPSET_ENTRY_MAXNUM                                     (8000)

typedef struct
{
	uint32_t state_and_index;
	uint32_t instance_index;
	uint32_t ipset_index;
#define FIREWALL_IPSET_ENTRY_ENTRY_STRING_LEN                                (64)
	char  entry[FIREWALL_IPSET_ENTRY_ENTRY_STRING_LEN];
#define FIREWALL_IPSET_ENTRY_ATTR_MASK_IPSET_INDEX                           (1 << 0)
#define FIREWALL_IPSET_ENTRY_ATTR_MASK_ENTRY                                 (1 << 1)
#define FIREWALL_IPSET_ENTRY_ATTR_MASK_ALL                                  ((1 << 2) - 1)
	uint32_t bit_map;
} __PACK__ igd_firewall_ipset_entry_attr_conf_tab_t;

#endif
