/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab cmcc smart env obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_OSGI_ENV_H
#define HI_ODL_TAB_OSGI_ENV_H
#include "hi_odl_basic_type.h"

/***********************JSON服务器地址环境表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	word8  aucJSONAddr[CM_URL_LEN];  /*平台JSON模块的IP地址，需要支持域名 */
#define SWM_GLOBLE_PORT_MAX  (0xffff)
	uword32 ulPort;                  /* 省平台JSON模块的端口号 */
#define JSON_MODE_SN_PWD 1
#define JSON_MODE_MAC_RAWSN 2
#define JSON_MODE_MAC_PWD 3
	uword32 ulMode;
	uword32 ulIntervalTime;          /* 默认心跳保活间隔 */
	uword32 ulIntervalTimeDynFlag;   /* 是否同步平台心跳保活间隔 */
	uword32 ulPluginSizeCheckFlag;   /* 是否校验下载插件的文件大小 */
	uword32 ulWakeUp;

#define SWM_GLOBLE_MASK_JSON_SERVER_ADDR		(1 << 0)
#define SWM_GLOBLE_MASK_JSON_SERVER_PORT		(1 << 1)
#define SWM_GLOBLE_MASK_JSON_INTERVAL_TIME		(1 << 2)
#define SWM_GLOBLE_MASK_JSON_INTERVAL_TIME_DYN_FLAG	(1 << 3)
#define SWM_GLOBLE_MASK_PLUGIN_SIZE_CHECK_FLAG		(1 << 4)
#define SWM_GLOBLE_MASK_JSON_MODE			(1 << 5)
#define SWM_GLOBLE_MASK_WAKE_UP				(1 << 6)
#define SWM_GLOBLE_MASK_ALL				(0x7f)
	uword32 ulBitmap;
} __PACK__ IgdSwmGlbAttrCfgTab;


/***********************插件运行环境配置表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulExecEvnIndex;     /* 插件运行环境索引 */
	uword8  ucEnable;           /* 运行环境开关设置 */
#define  EXEC_EVN_STATUS_UP         (0)
#define  EXEC_EVN_STATUS_ERROR      (1)
#define  EXEC_EVN_STATUS_DISABLED   (2)
	uword8  ucStatus;           /* 运行环境状态 */
	uword8  ucReset;            /* 复位运行环境。此节点不保存，读取时始终为False */
	uword8  ucParentExecEnv;     /* 父运行环境Index */
#define EXEC_ENV_NAME_LEN           (128)
	word8  aucName[EXEC_ENV_NAME_LEN];
#define EXEC_ENV_VENDOR_NAME_LEN     (128)
	word8  aucVendors[EXEC_ENV_VENDOR_NAME_LEN];
#define EXEC_ENV_VERSION_LEN           (32)
	word8  aucVersion[EXEC_ENV_VERSION_LEN];
#define EXEC_ENV_TYPE_LEN           (32)
	word8  aucType[EXEC_ENV_TYPE_LEN];
#define SWM_EXEC_ENV_MASK_ENABLE           						(0x1)
#define SWM_EXEC_ENV_MASK_STATUS           						(0x2)
#define SWM_EXEC_ENV_MASK_RESET            						(0x4)
#define SWM_EXEC_ENV_MASK_PARENTENV       						(0x8)
#define SWM_EXEC_ENV_MASK_NAME            						(0x10)
#define SWM_EXEC_ENV_MASK_VENDORS          						(0x20)
#define SWM_EXEC_ENV_MASK_VERSION          						(0x40)
#define SWM_EXEC_ENV_MASK_TYPE          						(0x80)
#define SWM_EXEC_ENV_MASK_ALL                                	(0xFF)
	uword32 ulBitmap;
} __PACK__ IgdSwmExecEnvAttrCfgTab;

/***********************发布单元表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulInstId;
#define IGD_SWM_DEPLOYMENTUNIT_UUID_LEN (36)
	word8  aucUUID[IGD_SWM_DEPLOYMENTUNIT_UUID_LEN];
#define IGD_SWM_DEPLOYMENTUNIT_DUID_LEN (64)
	word8  aucDUID[IGD_SWM_DEPLOYMENTUNIT_DUID_LEN];

#define IGD_SWM_DEPLOYMENTUNIT_NAME_LEN (128)
	word8  aucName[IGD_SWM_DEPLOYMENTUNIT_NAME_LEN];          /* 插件名称 */

#define IGD_SWM_DEPLOYMENTUNIT_STATUS_INSTALLING (0)
#define IGD_SWM_DEPLOYMENTUNIT_STATUS_INSTALLED (1)
#define IGD_SWM_DEPLOYMENTUNIT_STATUS_UPDATING (2)
#define IGD_SWM_DEPLOYMENTUNIT_STATUS_UNINSTALLING (3)
#define IGD_SWM_DEPLOYMENTUNIT_STATUS_UNINSTALLED (4)
	uword8  ucStatus;           /* 插件状态 */
	uword8  ucResolved;         /* 插件是否解析成功 */
	uword8  ucExecEnvRef;       /* 对应的运行环境 */
	uword8  ucPad;

#define IGD_SWM_DEPLOYMENTUNIT_URL_LEN           (1024)
	word8  aucURL[IGD_SWM_DEPLOYMENTUNIT_URL_LEN];  /* 加载插件的URL */

#define IGD_SWM_DEPLOYMENTUNIT_DESCRIPTION_LEN     (256)
	word8  aucDescription[IGD_SWM_DEPLOYMENTUNIT_DESCRIPTION_LEN];

#define IGD_SWM_DEPLOYMENTUNIT_VENDOR_LEN           (128)
	word8  aucVendor[IGD_SWM_DEPLOYMENTUNIT_VENDOR_LEN];

#define IGD_SWM_DEPLOYMENTUNIT_VERSION_LEN           (32)
	word8  aucVersion[IGD_SWM_DEPLOYMENTUNIT_VERSION_LEN];

#define IGD_SWM_DEPLOYMENTUNIT_EULIST_LEN           (32)
	word8  aucExecutionUnitList[IGD_SWM_DEPLOYMENTUNIT_EULIST_LEN];

#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_UUID             (1<<0)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_DUID             (1<<1)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_NAME             (1<<2)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_STATUS           (1<<3)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_RESOLVED         (1<<4)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_EXECENVREF       (1<<5)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_URL              (1<<6)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_DESCRIPTION      (1<<7)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_VENDOR           (1<<8)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_VERSION          (1<<9)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_EULIST           (1<<10)
#define IGD_SWM_DEPLOYMENTUNIT_INFO_MASK_ALL              (0x7FF)
	uword32 ulBitmap;
} __PACK__ IgdSwmDeploymentUnitInfoTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulInstId;
#define IGD_SWM_EXECUTIONUNIT_EUID_LEN (64)
	word8  aucEUID[IGD_SWM_EXECUTIONUNIT_EUID_LEN];
#define IGD_SWM_EXECUTIONUNIT_NAME_LEN (64)
	word8  aucName[IGD_SWM_EXECUTIONUNIT_NAME_LEN];

#define IGD_SWM_EXECUTIONUNIT_STATUS_IDLE (0)
#define IGD_SWM_EXECUTIONUNIT_STATUS_STARTING (1)
#define IGD_SWM_EXECUTIONUNIT_STATUS_ACTIVE (2)
#define IGD_SWM_EXECUTIONUNIT_STATUS_STOPPING (3)
	uword8  ucStatus;           /* 插件运行状态 */

#define IGD_SWM_EXECUTIONUNIT_STATE_IDLE   (0)
#define IGD_SWM_EXECUTIONUNIT_STATE_ACTIVE        (1)
	uword8  ucRequestedState;     /* 插件变更状态 */
	uword8  ucAutoStart;          /* 是否自动启动 */

#define IGD_SWM_EXECUTIONUNIT_FAULTCODE_NOFAULT         (0)
#define IGD_SWM_EXECUTIONUNIT_FAULTCODE_FAILONSTART     (1)
#define IGD_SWM_EXECUTIONUNIT_FAULTCODE_FAILONAUTOSTART (2)
#define IGD_SWM_EXECUTIONUNIT_FAULTCODE_FAILONSTOP      (3)
#define IGD_SWM_EXECUTIONUNIT_FAULTCODE_FAILWHILEACTIVE (4)
#define IGD_SWM_EXECUTIONUNIT_FAULTCODE_DEPENDENCYFAIL  (5)
#define IGD_SWM_EXECUTIONUNIT_FAULTCODE_UNSTARTABLE     (6)
	uword8  ucExecutionFaultCode;

#define IGD_SWM_EXECUTIONUNIT_FAULT_MSG_LEN             (256)
	word8  aucExecutionFaultMessage[IGD_SWM_EXECUTIONUNIT_FAULT_MSG_LEN];

#define IGD_SWM_EXECUTIONUNIT_VENDOR_LEN           (128)
	word8  aucVendor[IGD_SWM_EXECUTIONUNIT_VENDOR_LEN];

#define IGD_SWM_EXECUTIONUNIT_VERSION_LEN           (32)
	word8  aucVersion[IGD_SWM_EXECUTIONUNIT_VERSION_LEN];

#define IGD_SWM_EXECUTIONUNIT_DESCRIPTION_LEN     (256)
	word8  aucDescription[IGD_SWM_EXECUTIONUNIT_DESCRIPTION_LEN];

	uword32 ulExecutionEnvRef;                          /* 对应的运行环境 */
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_EUID             (1<<0)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_NAME             (1<<1)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_STATUS           (1<<2)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_REQSTATE         (1<<3)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_AUTOSTART        (1<<4)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_FAULTCODE        (1<<5)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_FAULTMSG         (1<<6)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_VENDOR           (1<<7)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_VERSION          (1<<8)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_DESCRIPTION      (1<<9)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_EXECENVREF       (1<<10)
#define IGD_SWM_EXECUTIONUNIT_INFO_MASK_ALL              (0x7FF)
	uword32 ulBitmap;
} __PACK__ IgdSwmExecutionUnitInfoTab;

/***********************插件权限表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulDuIndex;     /* 插件索引 */
#define  IGD_SWM_DUPERMISSION_DUNAME_LEN    (128)
	word8  aucDUName[IGD_SWM_DUPERMISSION_DUNAME_LEN];
#define  IGD_SWM_DUPERMISSION_DU_MAX    (128)
	uword8  aucAppIndexs[IGD_SWM_DUPERMISSION_DU_MAX];

#define IGD_SWM_DUPERMISSION_MASK_DUNAME             (1<<0)
#define IGD_SWM_DUPERMISSION_MASK_APPINDEXS          (1<<1)
	uword32 ulBitmap;
} __PACK__ IgdSwmDuPermissionAttrTab;


/***********************插件权限能力集表*********************************/
typedef struct {
	uword32  ulStateAndIndex;
	uword32  ulApiIndex;         /* API索引 */
	uword32  ulExecEvnIndex;     /* 插件运行环境索引 */

#define  IGD_SWM_APICAPABILITES_APINAME_LEN    (128)
	word8  aucApiName[IGD_SWM_APICAPABILITES_APINAME_LEN];

	uword32 ulBitmap;
} __PACK__ IgdSwmApiCapabilitesAttrTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword32  ulDuIndex;         /* 插件索引 */
	uword32  ulApiInstId;       /* 插件API InstID */

#define  IGD_SWM_DUPERMISSION_API_NAME_LEN    (128)
	word8  aucApiName[IGD_SWM_DUPERMISSION_API_NAME_LEN];

	uword32 ulBitmap;
} __PACK__ IgdSwmDuPermissionApiTab;

#endif
