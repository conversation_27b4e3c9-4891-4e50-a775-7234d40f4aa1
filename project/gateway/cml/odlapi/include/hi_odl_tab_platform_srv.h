/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm packet capture obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_PLATFORM_SRV_H
#define HI_ODL_TAB_PLATFORM_SRV_H
#include "hi_odl_basic_type.h"

/* 属性表相关宏 */
#define IGD_PLATFORMSRV_UA_PWD_LEN          (64)
#define IGD_PLATFORMSRV_SSID_NAME_LEN       (36)
#define IGD_PLATFORMSRV_SSID_PWD_LEN        (64)
#define IGD_PLATFORMSRV_DIST_ADDR_LEN       (64)
#define IGD_PLATFORMSRV_VERSION_LEN         (64)
#define IGD_PLATFORMSRV_SSN_LEN             (64)
#define IGD_PLATFORMSRV_DIST_ERR_MSG_LEN    (64)
#define IGD_PLATFORMSRV_OPER_ADDR_LEN       (64)
#define IGD_PLATFORMSRV_OPER_ERROR_MSG_LEN  (64)
#define IGD_PLATFORMSRV_PLUGIN_ADDR_LEN     (64)
#define IGD_PLATFORMSRV_PLUGIN_ERROR_MSG_LEN (64)
#define IGD_PLATFORMSRV_BSS_ADDR_LEN     (64)
#define IGD_PLATFORMSRV_WAN_INTERFACE_LEN   (256)

/* 属性表结构体定义 */
typedef struct {
	uword32 ulStateAndIndex;

#define IGD_PLATFORMSRV_APP_MODEL_OPEN  (1)
#define IGD_PLATFORMSRV_APP_MODEL_CLOSE (0)
	uword8 ucAppmodel;                                  /* 云客户端模块开关 */

	uword8 aucPad[CM_THREE_PADS];

	word8 aucInitUAPwd[IGD_PLATFORMSRV_UA_PWD_LEN];    /* Useradmin的初始密码 */
	word8 aucInitSSID[IGD_PLATFORMSRV_SSID_NAME_LEN];  /* SSID1的初始名称 */
	word8 aucInitSSIDPwd[IGD_PLATFORMSRV_SSID_PWD_LEN];/* SSID1的初始密码 */
	word8 aucDistAddr[IGD_PLATFORMSRV_DIST_ADDR_LEN];  /* 分发平台地址 */
	uword32 ulPort;                                     /* 分发平台端口 */
	uword32 ulHeartbeat;                                /* 终端和运营平台心跳 */

#define IGD_PLATFORMSRV_ABILITY_OPEN  (0)
#define IGD_PLATFORMSRV_ABILITY_CLOSE (1)
	uword32 ulAbility;                                  /* 网关封装接口能力是否向本地开放 */

	uword32 ulLocalPort;                                /* 网关内部为运维客户端本地访问的TCP端口 缺省17998 */
	uint32_t local_port1;                               /* 网关内部为用户客户端本地访问的TCP端口 缺省17999 */
	word8 aucVersion[IGD_PLATFORMSRV_VERSION_LEN];     /* 网关通信接口版本号 */
	word8 aucSSN[IGD_PLATFORMSRV_SSN_LEN];             /* 和平台交互验证码 */

#define IGD_PLATFORMSRV_DIST_STATUS_UNCONNECT        (1)
#define IGD_PLATFORMSRV_DIST_STATUS_CONNECT_TRYING   (2)
#define IGD_PLATFORMSRV_DIST_STATUS_CONNECTED        (3)
#define IGD_PLATFORMSRV_DIST_STATUS_CONNECT_END      (4)
#define IGD_PLATFORMSRV_DIST_STATUS_CONNECT_FAIL     (5)
	uword8 ucDistStatus;                                /* 分发平台连接情况 */

	uword8 aucPad1[CM_THREE_PADS];

	word8 aucDistErrorMsg[IGD_PLATFORMSRV_DIST_ERR_MSG_LEN];   /* 当DistStatus=5时,用于描述失败原因 */
	word8 aucOperAddr[IGD_PLATFORMSRV_OPER_ADDR_LEN];  /* 运营平台地址 */

#define IGD_PLATFORMSRV_OPER_STATUS_UNCONNECT           (1)
#define IGD_PLATFORMSRV_OPER_STATUS_CONNECT_TRYING      (2)
#define IGD_PLATFORMSRV_OPER_STATUS_REGISTERING         (3)
#define IGD_PLATFORMSRV_OPER_STATUS_HEARTBEAT_KEEPING   (4)
#define IGD_PLATFORMSRV_OPER_STATUS_HEARTBEAT_WAITING   (5)
#define IGD_PLATFORMSRV_OPER_STATUS_CONNECT_FAIL        (6)
	uword8 ucOperStatus;                                /* 运营平台连接情况 */

	uword8 aucPad2[CM_THREE_PADS];

	word8 aucOperErrorMsg[IGD_PLATFORMSRV_OPER_ERROR_MSG_LEN];/* 当OperStatus=6时，用于描述错误的原因 */
	word8 aucPluginAddr[IGD_PLATFORMSRV_PLUGIN_ADDR_LEN];/* 插件中心地址 */

#define IGD_PLATFORMSRV_PLUGIN_STATUS_UNCONNECT           (1)
#define IGD_PLATFORMSRV_PLUGIN_STATUS_CONNECT_TRYING      (2)
#define IGD_PLATFORMSRV_PLUGIN_STATUS_REGISTERING         (3)
#define IGD_PLATFORMSRV_PLUGIN_STATUS_HEARTBEAT_KEEPING   (4)
#define IGD_PLATFORMSRV_PLUGIN_STATUS_HEARTBEAT_WAITING   (5)
#define IGD_PLATFORMSRV_PLUGIN_STATUS_CONNECT_FAIL        (6)
	uword8 ucPluginStatus;                              /* 插件中心连接情况 */
	uword8 aucPad3[CM_THREE_PADS];

	word8 aucPluginErrorMsg[IGD_PLATFORMSRV_PLUGIN_ERROR_MSG_LEN]; /* 当PluginStatus = 6是，用于描述错误的原因 */

	word8 aucBSSAddr[IGD_PLATFORMSRV_BSS_ADDR_LEN];/* BSS分发平台地址 */
	uint8_t wan_interface[IGD_PLATFORMSRV_WAN_INTERFACE_LEN];
#define PLATFORMSRV_ATTR_MASK_BIT0_APP_MODEL        (0x01)
#define PLATFORMSRV_ATTR_MASK_BIT1_INIT_UAPWD       (0x02)
#define PLATFORMSRV_ATTR_MASK_BIT2_INIT_SSID        (0x04)
#define PLATFORMSRV_ATTR_MASK_BIT3_INIT_SSID_PWD    (0x08)
#define PLATFORMSRV_ATTR_MASK_BIT4_DIST_ADDR        (0x10)
#define PLATFORMSRV_ATTR_MASK_BIT5_PORT             (0x20)
#define PLATFORMSRV_ATTR_MASK_BIT6_HEARTBEAT        (0x40)
#define PLATFORMSRV_ATTR_MASK_BIT7_ABILITY          (0x80)
#define PLATFORMSRV_ATTR_MASK_BIT8_LOCAL_PORT       (0x100)
#define PLATFORMSRV_ATTR_MASK_BIT9_VERSION          (0x200)
#define PLATFORMSRV_ATTR_MASK_BIT10_SSN             (0x400)
#define PLATFORMSRV_ATTR_MASK_BIT11_DIST_STATUS     (0x800)
#define PLATFORMSRV_ATTR_MASK_BIT12_DIST_ERROR_MSG  (0x1000)
#define PLATFORMSRV_ATTR_MASK_BIT13_OPER_ADDR       (0x2000)
#define PLATFORMSRV_ATTR_MASK_BIT14_OPER_STATUS     (0x4000)
#define PLATFORMSRV_ATTR_MASK_BIT15_OPER_ERROR_MSG  (0x8000)
#define PLATFORMSRV_ATTR_MASK_BIT16_PLUGIN_ADDR     (0x10000)
#define PLATFORMSRV_ATTR_MASK_BIT17_PLUGIN_STATUS   (0x20000)
#define PLATFORMSRV_ATTR_MASK_BIT18_PLUGIN_ERROR_MSG (0x40000)
#define PLATFORMSRV_ATTR_MASK_BIT19_BSS_ADDR  (0x80000)
#define PLATFORMSRV_ATTR_MASK_LOCAL_PORT1     (0x100000)
#define PLATFORMSRV_ATTR_MASK_WAN_INTERFACE   (0x200000)
#define PLATFORMSRV_ATTR_MASK_ALL (0x3FFFFF)
	uword32 ulBitmap;

} __PACK__ IgdPlatformSrvAttrConfTab;

#endif
