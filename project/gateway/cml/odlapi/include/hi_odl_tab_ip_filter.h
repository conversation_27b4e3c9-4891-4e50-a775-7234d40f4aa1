/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab ip filter obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_IP_FILTER_H
#define HI_ODL_TAB_IP_FILTER_H
#include "hi_odl_basic_type.h"

/***************************IP过滤基本属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define IP_FILTER_DISABLE (0)
#define IP_FILTER_ENABLE (1)
	uword8 ucIPFilterEnable;	/*LAN到WAN*/
#define IP_FILTER_MODE_BLACK_USEFUL (0)
#define IP_FILTER_MODE_WHITE_USEFUL (1)
	uword8 ucIPFilterMode;	/*上行WAN到LAN*/
	uword8 ucIPFilterDownMode;	/*下行LAN到WAN*/

	/*gaozm add 20160527*/
#define WAN_IP_FILTER_DISABLE (0)
#define WAN_IP_FILTER_ENABLE (1)
	uword8 ucWanIPFilterInEnable;		/*wan到lanIPv4报文过滤*/
#define WAN_IP_FILTER_MODE_BLACK_POLICY (0)
#define WAN_IP_FILTER_MODE_WHITE_POLICY (1)
	uword8 ucIPFilterInPolicy;		/*wan 到LAN 过滤模式*/

	uword8 aucPad[CM_THREE_PADS];
#define IP_FILTER_ATTR_MASK_BIT0_FILETER_ENABLE (0x01)
#define IP_FILTER_ATTR_MASK_BIT1_FILTER_MODE (0x02)
#define IP_FILTER_ATTR_MASK_BIT2_FILTER_DOWN_MODE 	(0x04)
#define IP_FILTER_ATTR_MASK_BIT3_FILETER_ENABLE_WAN (0x08)
#define IP_FILTER_ATTR_MASK_BIT4_FILTER_POLICY (0x10)
#define IP_FILTER_ATTR_MASK_ALL (0x1f)

	uword32 ulBitmap;
} __PACK__ IgdSecurIPFilterAttrConfTab;

/***************************IP过滤表*********************************/
#define IGD_SECUR_IP_FILTER_RECORD_NUM (100)
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucIndex;
#define IP_FILTER_DIR_DOWN		(0)
#define IP_FILTER_DIR_UP		(1)
	uword8 ucIPFilterDir;
#define IP_FILTER_PROTOCOL_ALL (0)
#define IP_FILTER_PROTOCOL_TCP_UDP (1)
#define IP_FILTER_PROTOCOL_TCP (2)
#define IP_FILTER_PROTOCOL_UDP (3)
#define IP_FILTER_PROTOCOL_ICMP (4)
	uword8 ucIPFilterProtocol;/*协议类型*/
	uword8 aucPad;

#define IP_FILTER_IP_ADDRESS_LEN (40)
	word8 aucLanIPStart[IP_FILTER_IP_ADDRESS_LEN];
	word8 aucLanIPEnd[IP_FILTER_IP_ADDRESS_LEN];
	word8 aucWanIPStart[IP_FILTER_IP_ADDRESS_LEN];
	word8 aucWanIPEnd[IP_FILTER_IP_ADDRESS_LEN];

#define IP_FILTER_PROTOCOL_PORT_MIN (0)
#define IP_FILTER_PROTOCOL_PORT_MAX (65535)
	uword32 ulLanPortStart;
	uword32 ulLanPortEnd;
	uword32 ulWanPortStart;
	uword32 ulWanPortEnd;

#define IP_FILTER_LIST_ATTR_MASK_BIT0_FILETER_PROTOCOL (0x01)
#define IP_FILTER_LIST_ATTR_MASK_BIT1_FILTER_LAN_IP_START (0x02)
#define IP_FILTER_LIST_ATTR_MASK_BIT2_FILTER_LAN_IP_END (0x04)
#define IP_FILTER_LIST_ATTR_MASK_BIT3_FILTER_WAN_IP_START (0x08)
#define IP_FILTER_LIST_ATTR_MASK_BIT4_FILTER_WAN_IP_END (0x10)
#define IP_FILTER_LIST_ATTR_MASK_BIT5_FILTER_LAN_PORT_START (0x20)
#define IP_FILTER_LIST_ATTR_MASK_BIT6_FILTER_LAN_PORT_END (0x40)
#define IP_FILTER_LIST_ATTR_MASK_BIT7_FILTER_WAN_PORT_START (0x80)
#define IP_FILTER_LIST_ATTR_MASK_BIT8_FILTER_WAN_PORT_END (0x100)
#define IP_FILTER_LIST_ATTR_MASK_BIT9_FILTER_DIR 			(0x200)
#define IP_FILTER_LIST_ATTR_MASK_ALL (0x3ff)
	uword32 ulBitmap;
} __PACK__ IgdSecurIPFilterListAttrConfTab;


/***************************LAN IP过滤基本属性表*********************************/
#define IGD_SECUR_LAN_IP_FILTER_RECORD_NUM (100)

typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucLANIndex;
#define LAN_IP_FILTER_PROTOCOL_ALL (0)
#define LAN_IP_FILTER_PROTOCOL_TCP_UDP (1)
#define LAN_IP_FILTER_PROTOCOL_TCP (2)
#define LAN_IP_FILTER_PROTOCOL_UDP (3)
#define LAN_IP_FILTER_PROTOCOL_ICMP (4)
	uword8 ucLANIPFilterProtocol;/*协议类型*/
#define LAN_IP_FILTER_DISABLE (0)
#define LAN_IP_FILTER_ENABLE (1)
	uword8 ucEnable;
	uword8 ucPad;

#define SECUR_LAN_IP_FILTER_NAME_LEN (256)
	word8 aucName[SECUR_LAN_IP_FILTER_NAME_LEN];
#define LAN_IP_FILTER_IP_ADDRESS_LEN (40)
	word8 aucSrcIPStart[LAN_IP_FILTER_IP_ADDRESS_LEN];
	word8 aucSrcIPEnd[LAN_IP_FILTER_IP_ADDRESS_LEN];
	word8 aucDestIPStart[LAN_IP_FILTER_IP_ADDRESS_LEN];
	word8 aucDestIPEnd[LAN_IP_FILTER_IP_ADDRESS_LEN];

#define LAN_IP_FILTER_PROTOCOL_PORT_MIN (0)
#define LAN_IP_FILTER_PROTOCOL_PORT_MAX (65535)
	uword32 ulSrcPortStart;
	uword32 ulSrcPortEnd;
	uword32 ulDestPortStart;
	uword32 ulDestPortEnd;

#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT0_FILETER_PROTOCOL (0x01)
#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT1_FILETER_NAME (0x02)
#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT2_FILTER_SRCIP_START (0x04)
#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT3_FILTER_SRCIP_END (0x08)
#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT4_FILTER_DESTIP_START (0x10)
#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT5_FILTER_DESTIP_END (0x20)
#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT6_FILTER_SRCPORT_START (0x40)
#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT7_FILTER_SRCPORT_END (0x80)
#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT8_FILTER_DESTPORT_START (0x100)
#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT9_FILTER_DESTPORT_END (0x200)
#define LAN_IP_FILTER_LIST_ATTR_MASK_BIT10_FILTER_ENABLE (0x400)
#define LAN_IP_FILTER_LIST_ATTR_MASK_ALL (0x7ff)
	uword32 ulBitmap;
} __PACK__ IgdSecurLANIPFilterListAttrConfTab;


/***************************wan IP过滤表*********************************/

#define IGD_SECUR_WAN_IP_FILTER_RECORD_NUM (100)
typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucIndex;
#define WAN_IP_FILTER_PROTOCOL_ALL (0)
#define WAN_IP_FILTER_PROTOCOL_TCP_UDP (1)
#define WAN_IP_FILTER_PROTOCOL_TCP (2)
#define WAN_IP_FILTER_PROTOCOL_UDP (3)
#define WAN_IP_FILTER_PROTOCOL_ICMP (4)
	uword8 ucIPFilterProtocol;/*协议类型*/
#define WAN_IP_FILTER_DISABLE (0)
#define WAN_IP_FILTER_ENABLE (1)
	uword8 ucEnable;
	uword8 ucPad;

#define SECUR_WAN_IP_FILTER_NAME_LEN (256)
	uword8 aucName[SECUR_WAN_IP_FILTER_NAME_LEN];
#define WAN_IP_FILTER_IP_ADDRESS_LEN (40)
	uword8 aucSrcIPStart[WAN_IP_FILTER_IP_ADDRESS_LEN];
	uword8 aucSrcIPEnd[WAN_IP_FILTER_IP_ADDRESS_LEN];
	uword8 aucWanIPStart[WAN_IP_FILTER_IP_ADDRESS_LEN];		/*Dest*/
	uword8 aucWanIPEnd[WAN_IP_FILTER_IP_ADDRESS_LEN];		/*Dest*/

#define WAN_IP_FILTER_PROTOCOL_PORT_MIN (0)
#define WAN_IP_FILTER_PROTOCOL_PORT_MAX (65535)
	uword32 ulSrcPortStart;
	uword32 ulSrcPortEnd;
	uword32 ulWanPortStart;							/*Dest*/
	uword32 ulWanPortEnd;							/*Dest*/

#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT0_FILETER_PROTOCOL (0x01)
#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT1_FILTER_WAN_SrcIP_START (0x02)
#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT2_FILTER_WAN_SrcIP_END (0x04)
#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT3_FILTER_WAN_IP_START (0x08)
#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT4_FILTER_WAN_IP_END (0x10)
#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT5_FILTER_WAN_SrcPORT_START (0x20)
#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT6_FILTER_WAN_SrcPORT_END (0x40)
#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT7_FILTER_WAN_PORT_START (0x80)
#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT8_FILTER_WAN_PORT_END (0x100)
#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT9_FILTER_WAN_NAME (0x200)
#define WAN_IP_FILTER_LIST_ATTR_MASK_BIT10_FILTER_ENABLE (0x400)
#define WAN_IP_FILTER_LIST_ATTR_MASK_ALL (0x7ff)
	uword32 ulBitmap;
} __PACK__ IgdSecurWANIPFilterListAttrConfTab;


/* IGD_USER_MANAGEMENT_ATTR_CFG_TAB */
#define  IGD_USER_MANAGEMENT_MAXNUM                                      (30)
typedef struct {
	uint32_t state_and_index;
	uint8_t  instance_index;
	uint8_t  enable;
	uint8_t  pad1[CM_TWO_PADS];
#define USER_MANAGEMENT_NAME_STRING_LEN                                  (64)
	uint8_t  name[USER_MANAGEMENT_NAME_STRING_LEN];
#define USER_MANAGEMENT_DESCRIPTION_STRING_LEN                           (64)
	uint8_t  description[USER_MANAGEMENT_DESCRIPTION_STRING_LEN];
	uint8_t  numberofsubuser;
	uint8_t  macfilternumberofentries;
	uint8_t  ipfilternumberofentries;
	uint8_t  whitelistnumberofentries;
	uint8_t  blacklistnumberofentries;
	uint8_t  urllistfiltermode;
	uint8_t  time_enable;
	uint8_t  pad2;
#define USER_MANAGEMENT_TIME_RULES_STR_LEN                               (256)
	uint8_t  time_rules[USER_MANAGEMENT_TIME_RULES_STR_LEN];
#define USER_MANAGEMENT_ATTR_MASK_ENABLE                                 (1 << 0)
#define USER_MANAGEMENT_ATTR_MASK_NAME                                   (1 << 1)
#define USER_MANAGEMENT_ATTR_MASK_DESCRIPTION                            (1 << 2)
#define USER_MANAGEMENT_ATTR_MASK_NUMBEROFSUBUSER                        (1 << 3)
#define USER_MANAGEMENT_ATTR_MASK_URLLISTFILTERMODE                      (1 << 4)
#define USER_MANAGEMENT_ATTR_MASK_TIME_ENABLE                            (1 << 5)
#define USER_MANAGEMENT_ATTR_MASK_TIME_RULES                             (1 << 6)
#define USER_MANAGEMENT_ATTR_MASK_ALL                                   ((1 << 7) - 1)
	uint32_t bit_map;
} __PACK__ igd_user_management_attr_conf_tab_t;

#endif
