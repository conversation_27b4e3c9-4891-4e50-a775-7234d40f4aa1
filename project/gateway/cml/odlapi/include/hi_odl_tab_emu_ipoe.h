/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm emu ipoe obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_EMU_IPOE_H
#define HI_ODL_TAB_EMU_IPOE_H

typedef struct {
	uword32     ulStateAndIndex;
#define IPOE_EMULATOR_DIAGNOSTIC_STATE_NONE (0)
#define IPOE_EMULATOR_DIAGNOSTIC_STATE_START (1)
#define IPOE_EMULATOR_DIAGNOSTIC_STATE_STOP (2)
#define IPOE_EMULATOR_DIAGNOSTIC_STATE_COMPLETE (3)
#define IPOE_EMULATOR_DIAGNOSTIC_STATE_RUNNING (4)
	uword8      ucDiagnosticsState;
	word8       acWanInterface[256];
	word8       acUserMac[32];
	word8       acVendorClassID[64];
	word8       acPingDestIPAddress[256];
	uword32     ulRepNum;
	uword32     	ulTimeout;
#define IPOE_EMULATOR_ATTR_MASK_BIT0_STATE              (0x01)
#define IPOE_EMULATOR_ATTR_MASK_BIT1_WANIF              (0x02)
#define IPOE_EMULATOR_ATTR_MASK_BIT2_USERMAC            (0x04)
#define IPOE_EMULATOR_ATTR_MASK_BIT3_VENDORCLASSSID     (0x08)
#define IPOE_EMULATOR_ATTR_MASK_BIT4_PINGDESTADDR       (0x10)
#define IPOE_EMULATOR_ATTR_MASK_BIT5_PINGREPNUM         (0x20)
#define IPOE_EMULATOR_ATTR_MASK_BIT6_TIMEOUT            (0x40)
#define IPOE_EMULATOR_ATTR_MASK_BIT7_LOCALADDR          (0x80)
#define IPOE_EMULATOR_ATTR_MASK_BIT8_GATEWAY            (0x100)
#define IPOE_EMULATOR_ATTR_MASK_ALL                     (0x1ff)
	uword32 ulBitmap;
} __PACK__ IgdIPOEEmulatorAttrConfTab;


typedef struct {
	uword32      	ulDiagnosticsState;
#define 	IPOE_EMULATOR_RESULT_SUCC					(0)
#define 	IPOE_EMULATOR_RESULT_SENDDHCP_ERR			(1)
#define		IPOE_EMULATOR_RESULT_SERV_NOTFOUND			(2)
#define		IPOE_EMULATOR_RESULT_SERV_DENY				(3)
#define		IPOE_EMULATOR_RESULT_GETADDR_TIMEOUT			(4)
#define		IPOE_EMULATOR_RESULT_All_PING_FAIL				(5)
#define		IPOE_EMULATOR_RESULT_SOME_PING_FAIL				(6)
#define		IPOE_EMULATOR_RESULT_OTHER_E					(7)
	uword32      	ulResult;
	uword32      	ulSuccCnt;
	uword32      	ulFailCnt;
	uword32      	ulAvrTime;
	uword32      	ulMinTime;
	uword32      	ulMaxTime;
	word8       	acLocalIPAddress[16];
	word8       	acGateWay[16];
} IgdIPOEEmulatorStatic;

#endif
