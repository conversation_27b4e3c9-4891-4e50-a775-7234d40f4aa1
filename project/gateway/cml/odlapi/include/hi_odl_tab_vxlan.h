/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab vxlan obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_VXLAN_H
#define HI_ODL_TAB_VXLAN_H
#include "hi_odl_basic_type.h"

#define IGD_VXLAN_VNI_ATTR_TAB_RECORD_NUM (4)
typedef struct {
	/* 通用配置 */
	uword32 ulStateAndIndex;
	uword32	ulIndex;
#define VXLAN_VNIID_MIN 0
#define VXLAN_VNIID_MAX 16777215
	uword32 ulVniID;
	uword8  ucSwitch;
#define VXLAN_VNI_FWD_MODE_L2      (1)
#define VXLAN_VNI_FWD_MODE_L3      (2)
	uword8  ucWorkMode;
#define VXLAN_CONFIG_WEB      (1)
#define VXLAN_CONFIG_CWMP      (0)
	uword8	ucConfigMode;
	uword8	ucRsv;
	uword32 ulMtu;
	uword8	ucVlanSwitch;
	uword8	ucRsv_0[3];
	uword32 usVlanID;
	uword32 ulBindLan;
	word8  aucBindWan[64];

	/* 外层配置 */
#define VXLAN_VNI_ADDR_IPV4      (1)
#define VXLAN_VNI_ADDR_IPV6      (2)
#define VXLAN_VNI_ADDR_DUAL      (3)
	uword32 ucOuterIpMode;
	word8  aucTunnelRemoteIp[CM_IP_ADDR_STRING_LEN];
	word8  aucTunnelRemoteIpv6[CM_IPV6_ADDR_LEN_MAX];
	/* 内层配置 */
	uword32 ucInnerIpMode;
	/* IPv4 */
#define VXLAN_VNI_ADDR_CFG_DHCP      (0)
#define VXLAN_VNI_ADDR_CFG_STATIC    (1)
	uword8	ucAddrMode;
	uword8	ucNat;
	uword8	ucRsv_1[2];
	word8  aucVniIp[CM_IP_ADDR_STRING_LEN];
	word8  aucVniIpMask[CM_IP_ADDR_STRING_LEN];
	word8  aucDNSMaster[CM_IP_ADDR_STRING_LEN];
	word8  aucDNSSlave[CM_IP_ADDR_STRING_LEN];
	word8  aucGateWay[CM_IP_ADDR_STRING_LEN];

	/* Ipv6 */
#define VXLAN_VNI_IPV6_ADDR_CFG_DHCPV6      (0)
#define VXLAN_VNI_IPV6_ADDR_CFG_STATIC      (1)
	uword8	ucIPv6AddrMode;
	word8  ucVniIpv6PrefixLen;
	uword8	ucRsv_2[2];
	word8  aucVniIpv6[CM_IPV6_ADDR_LEN_MAX];
	word8  aucIpv6DNSMaster[CM_IPV6_ADDR_LEN_MAX];
	word8  aucIpv6DNSSlave[CM_IPV6_ADDR_LEN_MAX];
	word8  aucIpv6GateWay[CM_IPV6_ADDR_LEN_MAX];

	/* 内层配置 */

#define VXLAN_VNI_ATTR_MASK_BIT0_VNI_ID               (0x01)
#define VXLAN_VNI_ATTR_MASK_BIT1_SWITCH               (0x02)
#define VXLAN_VNI_ATTR_MASK_BIT2_WORK_MODE            (0x04)
#define VXLAN_VNI_ATTR_MASK_BIT3_REMOTE_IP            (0x08)
#define VXLAN_VNI_ATTR_MASK_BIT4_MTU                  (0x10)
#define VXLAN_VNI_ATTR_MASK_BIT5_VNI_IP               (0x20)
#define VXLAN_VNI_ATTR_MASK_BIT6_VNI_IP_MASK          (0x40)
#define VXLAN_VNI_ATTR_MASK_BIT7_ADDR_MODE            (0x80)
#define VXLAN_VNI_ATTR_MASK_BIT8_NAT                  (0x100)
#define VXLAN_VNI_ATTR_MASK_BIT9_DNS_MASTER           (0x200)
#define VXLAN_VNI_ATTR_MASK_BIT10_DNS_SLAVE           (0x400)
#define VXLAN_VNI_ATTR_MASK_BIT11_GATEWAY             (0x800)
#define VXLAN_VNI_ATTR_MASK_BIT12_VLAN_SWITCH         (0x1000)
#define VXLAN_VNI_ATTR_MASK_BIT13_VLANID              (0x2000)
#define VXLAN_VNI_ATTR_MASK_BIT14_BIND_LAN            (0x4000)
#define VXLAN_VNI_ATTR_MASK_BIT15_BIND_WAN            (0x8000)
#define VXLAN_VNI_ATTR_MASK_BIT16_OUTER_IPMODE        (0x10000)
#define VXLAN_VNI_ATTR_MASK_BIT17_INNER_IPMODE        (0x20000)

#define VXLAN_VNI_ATTR_MASK_BIT18_REMOTE_IPV6         (0x40000)
#define VXLAN_VNI_ATTR_MASK_BIT19_VNI_IPV6            (0x80000)
#define VXLAN_VNI_ATTR_MASK_BIT20_VNI_IPV6_PREFIX     (0x100000)
#define VXLAN_VNI_ATTR_MASK_BIT21_IPV6_ADDR_MODE      (0x200000)
#define VXLAN_VNI_ATTR_MASK_BIT22_DNS_MASTER_IPV6     (0x400000)
#define VXLAN_VNI_ATTR_MASK_BIT23_DNS_SLAVE_IPV6      (0x800000)
#define VXLAN_VNI_ATTR_MASK_BIT24_GATEWAY_IPV6        (0x1000000)

#define VXLAN_VNI_ATTR_MASK_ALL                       (0x1FFFFFF)
	uword32 ulBitmap;
} __PACK__ IgdVxlanVniAttrTab;

#endif
