/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm global obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_EMU_PING_H
#define HI_ODL_TAB_EMU_PING_H
#include "hi_odl_basic_type.h"

#define IGD_DIAG_PING_MAX           (5)        /* 最大支持同时运行的TraceRoute个数 */

#define IGD_DIAG_PING_INDEX_1       (0)
#define IGD_DIAG_PING_INDEX_2       (1)
#define IGD_DIAG_PING_INDEX_3       (2)
#define IGD_DIAG_PING_INDEX_4       (3)
#define IGD_DIAG_PING_INDEX_5       (4)
#define IGD_DIAG_CTPING_IDX(inst)      ((inst) - 1)
#define IGD_DIAG_IPPING_IDX(inst)      IGD_DIAG_PING_INDEX_4

enum {
	EMU_PING_STATE_NONE_E,
	EMU_PING_STATE_REQ_E,
	EMU_PING_STATE_COMPLETE_E,
	EMU_PING_STATE_NOTRESOLV_E,
	EMU_PING_STATE_ERR_INTERNAL_E,
	EMU_PING_STATE_ERR_OTHER_E,
	EMU_PING_STATE_NUM_E,
} ;

typedef struct {
	uword32						ulStateAndIndex;
	uword32						ulInstId;
	uword32						ulState;
	uword32						ulStop;
	word8						acWanIf[256];
	word8						acHost[256];
	uword32						ulIPver;
	uword32						ulPingCnt;
	uword32						ulBlkSize;
	uword32						ulTimeout;
	uword32						ulInterval;
	uword32						ulDscp;
	uword32						ulLastResult;
#define EMU_PING_ATTR_MASK_BIT0_INSTID					(0x01)
#define EMU_PING_ATTR_MASK_BIT1_WANIF					(0x02)
#define EMU_PING_ATTR_MASK_BIT2_HOST					(0x04)
#define EMU_PING_ATTR_MASK_BIT3_IPVER					(0x08)
#define EMU_PING_ATTR_MASK_BIT4_PINGCNT					(0x10)
#define EMU_PING_ATTR_MASK_BIT5_BLKSIZE					(0x20)
#define EMU_PING_ATTR_MASK_BIT6_TIMEOUT					(0x40)
#define EMU_PING_ATTR_MASK_BIT7_INTVAL					(0x80)
#define EMU_PING_ATTR_MASK_BIT8_DSCP					(0x100)
#define EMU_PING_ATTR_MASK_BIT9_STOP					(0x200)
#define EMU_PING_ATTR_MASK_BIT10_LASTRESULT				(0x400)
#define EMU_PING_ATTR_MASK_ALL							(0x7ff)
	uword32						ulBitmap;
}  IgdCmEmuPingConfTab;

typedef struct {
	uword32						ulStateAndIndex;
	uword32						ulInstId;
	uword32						ulState;
	uword32						ulSuccCnt;
	uword32						ulFailCnt;
	uword32						ulAvrTime;
	uword32						ulMinTime;
	uword32						ulMaxTime;
} IgdCmEmuPingStaticTab;

/*TCP ping */

#define IGD_DIAG_TCP_PING_IP_LEN 64
#define IGD_DIAG_TCP_PING_RTT_LEN 8
#define IGD_DIAG_TCP_PING_LOST_LEN 8
typedef struct {
	uword32						ulStateAndIndex;
	uword32 wanIndex;
#define IGD_TCP_PING_TPYE_ICMP (0)
#define IGD_TCP_PING_TPYE_TCP (1)
	uword32						ulType; /*Ping 类型0-标准Ping ; 1-TCP ping*/
	word8						acDstIP[IGD_DIAG_TCP_PING_IP_LEN]; /*目的IP*/
	uword32						ulPort; /*目的端口*/
	uword32						ulState; /*ping是否执行完成,1: 完成，0: 未完成; 2 测试结果失败*/
	word8                                       acRtt[IGD_DIAG_TCP_PING_RTT_LEN] ; /*延时: 单位ms*/
	word8                                    aclost[IGD_DIAG_TCP_PING_LOST_LEN]; /*丢包率ulLost%*/
	word8
	acResultDstIP[IGD_DIAG_TCP_PING_IP_LEN]; /*猜测可能目的地址是域名，这里获取域名对应的IP*/
#define EMU_TCP_PING_MASK_BIT0_TYPE				       	(0x01)
#define EMU_TCP_PING_MASK_BIT1_DSTIP 					(0x02)
#define EMU_TCP_PING_MASK_BIT2_PORT  					(0x04)
#define EMU_TCP_PING_MASK_BIT3_ACTION					(0x08)
#define EMU_TCP_PING_MASK_BIT4_WANINDEX					(0x10)
	uword32						ulBitmap;
}  IgdCmTCPPingDetectTab;

#endif
