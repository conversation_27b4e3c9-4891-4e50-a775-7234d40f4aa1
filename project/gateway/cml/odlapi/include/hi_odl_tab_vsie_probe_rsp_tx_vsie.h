/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author: HSAN
 * Create: 2023-12-18
 */
#ifndef HI_ODL_TAB_PROBE_RSP_TX_VSIE_H
#define HI_ODL_TAB_PROBE_RSP_TX_VSIE_H
#include "hi_odl_basic_type.h"

#define IGD_PROBE_RSP_TX_VSIE_MAC_LEN (18)
#define IGD_PROBE_RSP_TX_VSIE_IEDATA_LEN (256)
#define IGD_PROBE_RSP_TX_VSIE_IF_NAME_LEN (32)
#define IGD_PROBE_RSP_TX_VSIE_DOMAINLIST_LEN (512)
#define IGD_PROBE_RSP_TX_VSIE_DESTIPLIST_LEN (512)
#define IGD_PROBE_RSP_TX_VSIE_MACLIST_LEN (512)
#define IGD_PROBE_RSP_TX_VSIE_DURATION_DEFAULT (100)

typedef enum {
	IGD_PROBE_RSP_TX_VSIE_BAND_24G = 0,
	IGD_PROBE_RSP_TX_VSIE_BAND_5G = 1,
	IGD_PROBE_RSP_TX_VSIE_BAND_BOTTOM
} IGD_PROBE_RSP_TX_VSIE_BAND;

/* FIXME:  iedata 如何存储 */
typedef struct {
	uword32 state_and_index;
	uword32 entry_index;
	char mac[IGD_PROBE_RSP_TX_VSIE_MAC_LEN];
	uword8 band;
	uword8 pad[1];
	uword8 iedata[IGD_PROBE_RSP_TX_VSIE_IEDATA_LEN];
	uword32 len_of_iedata;

#define IGD_ATTR_MASK_PROBE_RSP_TX_VSIE_MAC (0x01)
#define IGD_ATTR_MASK_PROBE_RSP_TX_VSIE_BAND (0x02)
#define IGD_ATTR_MASK_PROBE_RSP_TX_VSIE_IEDATA (0x04)
#define IGD_ATTR_MASK_PROBE_RSP_TX_VSIE_LEN_OF_IEDATA (0x08)
#define IGD_ATTR_MASK_PROBE_RSP_TX_VSIE_ALL (0xffff)

	uword32 bitmap;
} __PACK__ IgdVSIEProbeRspTxVSIEAttrConfTab;

#endif
