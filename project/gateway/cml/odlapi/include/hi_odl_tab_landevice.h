/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm lan device obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_LANDEVICE_H
#define HI_ODL_TAB_LANDEVICE_H
#include "hi_odl_basic_type.h"

/***************************LAN口IP地址信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
#define IP_INTERERFACE_ENTRY_DISABLE (0)
#define IP_INTERERFACE_ENTRY_ENABLE (1)
	uword8 ucEnable;/*默认：false*/
#define LAN_IP_ADDRESSING_TYPE_DHCP (1)
#define LAN_IP_ADDRESSING_TYPE_STATIC (2)
#define LAN_IP_ADDRESSING_TYPE_AUTOIP (3)
	uword8 ucAddressingType;
	uword8 aucPad[CM_TWO_PADS];

	uword8 aucIPv4Addr[CM_IP_ADDR_LEN];
	uword8 aucSubnetMask[CM_IP_ADDR_LEN];

#define LAN_IP_ADDR_ATTR_MASK_BIT0_ENABLE (0x01)
#define LAN_IP_ADDR_ATTR_MASK_BIT1_ADDRESSING_TYPE (0x02)
#define LAN_IP_ADDR_ATTR_MASK_BIT2_IP_ADDRESS (0x04)
#define LAN_IP_ADDR_ATTR_MASK_BIT3_SUBNET_MASK (0x08)
#define LAN_IP_ADDR_ATTR_MASK_ALL (0x0f)
	uword32 ulBitmap;
} __PACK__ IgdLanIPAddrAttrConfTab;

/***************************LAN侧IPv4属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

	/*DHCP Server*/
#define LAN_DHCP_SERVER_CONFIG_DISABLE (0)
#define LAN_DHCP_SERVER_CONFIG_ENABLE (1)
	uword8 ucDhcpServerConfigurable;/*默认值：1，可配置*/
#define LAN_DHCP_RELAY_NO (0)
#define LAN_DHCP_RELAY_YES (1)
	uword8 ucDhcpRelay; /*默认值：0，DHCP Server*/
#define LAN_DHCP_SERVER_DISABLE (0)
#define LAN_DHCP_SERVER_ENABLE (1)
	uword8 ucDhcpServerEnable; /*使能DHCP Server*/
#define LAN_IP_POOL_DISABLE (0)
#define LAN_IP_POOL_ENABLE (1)
	uword8 ucIpPoolEnable;/*设备地址区间设置*/

#define LAN_STB_DISABLE (0)
#define LAN_STB_ENABLE (1)
	uword8 ucStbEnable;
#define LAN_PHONE_DISABLE (0)
#define LAN_PHONE_ENABLE (1)
	uword8 ucPhoneEnable;
#define LAN_CAMERA_DISABLE (0)
#define LAN_CAMERA_ENABLE (1)
	uword8 ucCameraEnable;
#define LAN_COMPUTER_DISABLE (0)
#define LAN_COMPUTER_ENABLE (1)
	uword8 ucComputerEnable;

#define DHCP_LEASE_TIME_INFINITE (-1)
	word32 lDhcpLeaseTime;/*延续时间：-1表示无限*/
	uword8 aucMinAddr[CM_IP_ADDR_LEN]; /*起始地址*/
	uword8 aucMaxAddr[CM_IP_ADDR_LEN]; /*结束地址*/
#define DHCP_RESERVE_IPADDR_LEN (64)
	word8 aucResvAddr[DHCP_RESERVE_IPADDR_LEN];
	uword8 aucSubMask[CM_IP_ADDR_LEN];/*子网掩码*/
	uword8 aucStbMinAddr[CM_IP_ADDR_LEN];
	uword8 aucStbMaxAddr[CM_IP_ADDR_LEN];
	uword8 aucPhoneMinAddr[CM_IP_ADDR_LEN];
	uword8 aucPhoneMaxAddr[CM_IP_ADDR_LEN];
	uword8 aucCameraMinAddr[CM_IP_ADDR_LEN];
	uword8 aucCameraMaxAddr[CM_IP_ADDR_LEN];
	uword8 aucComputerMinAddr[CM_IP_ADDR_LEN];
	uword8 aucComputerMaxAddr[CM_IP_ADDR_LEN];

	/*DNS Server*/
#define LAN_DNS_SERVERS_LEN (256)
	word8 aucDnsServers[LAN_DNS_SERVERS_LEN]; /*同LAN侧IP地址一致*/
	word8 aucDomainName[CM_DOMAIN_NAME_LEN];
#define LAN_IP_ROUTERS_LEN (256)
	word8 aucIpRouters[LAN_IP_ROUTERS_LEN];/*跟随LAN侧IP变化，反之，不允许*/
#define LAN_USE_ALLOC_WAN_NORMAL (1)
#define LAN_USE_ALLOC_WAN_USE_ALLOCATED_SUBNET (2)
#define LAN_USE_ALLOC_WAN_PASS_THROUGH (3)
	uword8 ucUseAllocWan;
	/*gaozm add 20160527*/
#define LAN_OPTION60_DISABLE (0)
#define LAN_OPTION60_ENABLE (1)
	uword8 ucLANOPTION60Enable;
#define LAN_OPTION125_DISABLE (0)
#define LAN_OPTION125_ENABLE (1)
	uword8 ucLANOPTION125Enable;
	uword8 aucPad[1];

	word8 aucAssocConn[CM_WAN_CONNECTION_NAME_LEN];
	uword32 ulPassthroughLease;
#define LAN_PASSTHROUGH_MAC_ADDR_LEN (64)
	uword8 aucPassthroughMACAddr[LAN_PASSTHROUGH_MAC_ADDR_LEN];
#define LAN_ALLOWED_MAC_ADDR_LEN (64)
	uword8 aucAllowedMACAddr[LAN_ALLOWED_MAC_ADDR_LEN];
#define LAN_DNS_SOURCE_PROXY (1) /* 1 家庭网关代理 */
#define LAN_DNS_SOURCE_WAN (2) /* 2 网络连接 */
#define LAN_DNS_SOURCE_STATIC (3) /* 3 静态配置 */
	uword8 ucDnsSource;
	uword8 aucPad2[3];
	word8 aucDnsWanName[CM_WAN_CONNECTION_NAME_LEN];/*当DNS来源为2时，选择的wan连接*/

#define LAN_IPV4_ATTR_MASK_BIT0_DHCP_SERVER_CONF (0x01)
#define LAN_IPV4_ATTR_MASK_BIT2_DHCP_RELAY (0x02)
#define LAN_IPV4_ATTR_MASK_BIT3_DHCP_SERVER_ENABLE (0x04)
#define LAN_IPV4_ATTR_MASK_BIT4_IPPOOL_ENABLE (0x08)
#define LAN_IPV4_ATTR_MASK_BIT5_STB_ENABLE (0x10)
#define LAN_IPV4_ATTR_MASK_BIT6_PHONE_ENABLE (0x20)
#define LAN_IPV4_ATTR_MASK_BIT7_CAMERA_ENABLE (0x40)
#define LAN_IPV4_ATTR_MASK_BIT8_COMPUTER_ENABLE (0x80)
#define LAN_IPV4_ATTR_MASK_BIT9_DHCP_LEASE_TIME (0x100)
#define LAN_IPV4_ATTR_MASK_BIT10_MIN_ADDR (0x200)
#define LAN_IPV4_ATTR_MASK_BIT11_MAX_ADDR (0x400)
#define LAN_IPV4_ATTR_MASK_BIT12_RESV_ADDR (0x800)
#define LAN_IPV4_ATTR_MASK_BIT13_SUB_MASK (0x1000)
#define LAN_IPV4_ATTR_MASK_BIT14_STB_MIN (0x2000)
#define LAN_IPV4_ATTR_MASK_BIT15_STB_MAX (0x4000)
#define LAN_IPV4_ATTR_MASK_BIT16_PHONE_MIN (0x8000)
#define LAN_IPV4_ATTR_MASK_BIT17_PHONE_MAX (0x10000)
#define LAN_IPV4_ATTR_MASK_BIT18_CAMERA_MIN (0x20000)
#define LAN_IPV4_ATTR_MASK_BIT19_CAMERA_MAX (0x40000)
#define LAN_IPV4_ATTR_MASK_BIT20_COMPUTER_MIN (0x80000)
#define LAN_IPV4_ATTR_MASK_BIT21_COMPUTER_MAX (0x100000)
#define LAN_IPV4_ATTR_MASK_BIT22_DNS_SERVERS (0x200000)
#define LAN_IPV4_ATTR_MASK_BIT23_DOMAIN_NAME (0x400000)
#define LAN_IPV4_ATTR_MASK_BIT24_IP_ROUTERS (0x800000)
#define LAN_IPV4_ATTR_MASK_BIT25_USE_ALLOC_WAN (0x1000000)
#define LAN_IPV4_ATTR_MASK_BIT26_ASSOC_CONN (0x2000000)
#define LAN_IPV4_ATTR_MASK_BIT27_PASS_LEASE (0x4000000)
#define LAN_IPV4_ATTR_MASK_BIT28_PASS_MAC (0x8000000)
#define LAN_IPV4_ATTR_MASK_BIT29_ALLOW_MAC (0x10000000)
#define LAN_IPV4_ATTR_MASK_BIT30_OPTION125_ENABLE (0x20000000)
#define LAN_IPV4_ATTR_MASK_BIT31_OPTION60_ENABLE (0x40000000)
#define LAN_IPV4_ATTR_MASK_ALL (0x7fffffff)
	uword32 ulBitmap;
#define LAN_IPV4_ATTR_MASK1_BIT0_DNS_SOURCE (0x01)
#define LAN_IPV4_ATTR_MASK1_BIT1_DNS_WAN_NAME (0x02)
#define LAN_IPV4_ATTR_MASK1_ALL (0x3)
	uword32 ulBitmap1;
} __PACK__ IgdLanIPv4AttrConfTab;


/***************************LAN侧IPv6属性表*********************************/
typedef struct {
#define LAN_DNS_STRING_LEN_64 (64)
#define LAN_DNS_STRING_LEN_256 (256)
	uword32 ulStateAndIndex;

	uword8 aucIPv6Address[CM_IPV6_ADDR_LEN];/*IPv6地址*/

	/*DHCP Server*/
#define LAN_DNS_SOURCE_HGU_PROXY (1)
#define LAN_DNS_SOURCE_WAN_CONNECTION (2)
#define LAN_DNS_SOURCE_STATIC (3)
	uword8 ucDnsSource;/*1家庭网关代理 | 2网络连接 | 3静态配置*/
#define LAN_DHCPV6_DISABLE (0)
#define LAN_DHCPV6_ENABLE (1)
	uword8 ucDhcpV6Enable; /*启用DHCPv6服务器*/
#define LAN_STATEFUL_DHCPV6_DISABLE (0)
#define LAN_STATEFUL_DHCPV6_ENABLE (1)
	uword8 ucStatefulDhcpV6Enable;/*是否使能有状态的DHCPv6，默认：disable*/
	uword8 ucDHCPv6PDServerEnable;

#define LAN_DNS_LEASE_TIME_ONE_MINUTE (1)
#define LAN_DNS_LEASE_TIME_ONE_HOUR (2)
#define LAN_DNS_LEASE_TIME_ONE_DAY (3)
#define LAN_DNS_LEASE_TIME_ONE_WEEK (4)
	uword32 ulLeaseTime;/* IPv6地址租约周期，仅当IPv6地址为Static时可配置*/
#define LAN_DNS_SERVERS_LEN (256)
	word8 aucDnsServers[LAN_DNS_SERVERS_LEN];/*DNS Servers，对应于web上的首选和备选DNS Server*/
	uword32 ulDnsWanGlobalIndex;
	word8 aucDnsWanName[CM_WAN_CONNECTION_NAME_LEN];/*当DNS来源为2时，选择的wan连接*/
	word8 aucDnsWanAlias[CM_WAN_CONNECTION_NAME_LEN];/* WAN口地址别名*/
	word8 aucMinAddr[CM_IPV6_ADDR_LEN_MAX];
	word8 aucMaxAddr[CM_IPV6_ADDR_LEN_MAX];

	/* SLAAC RADVD配置对象*/
#define LAN_RADVD_CONFIG_DISABLE (0)
#define LAN_RADVD_CONFIG_ENABLE (1)
	uword8 ucRadvdEnable;/*是否使能SLAAC配置，默认：enable*/
#define LAN_DNS_ADDR_FROM_DHCP_NO (0)
#define LAN_DNS_ADDR_FROM_DHCP_YES (1)
	uword8 ucAddrInfoFromDhcp; /*地址信息是否通过DHCP获取，默认：1*/
#define LAN_DNS_ELSE_FROM_DHCP_NO (0)
#define LAN_DNS_ELSE_FROM_DHCP_YES (1)
	uword8 ucOtherFromDhcp; /*其他信息是否通过DHCP获取，默认：1*/
	uword8 ucPad1;

	uword32 ulMinRaTime; /* RA报文最小自动发送时间*/
	uword32 ulMaxRaTime; /* RA报文最大自动发送时间*/
#define LAN_RADVD_DNS_SERVERS_LEN (256)
	word8 aucRadvdDnsServers[LAN_RADVD_DNS_SERVERS_LEN];

	/*RADVD公告前缀对象*/
#define LAN_PREFIX_TYPE_WAN_DELEGATED (1)
#define LAN_PREFIX_TYPE_WAN_STATIC (2)
	uword8 ucPrifixSource;/*前缀来源: WANDelegated | Static */
	uword8 aucPad1[CM_THREE_PADS];

	word8 aucPrifix[CM_IPV6_ADDR_LEN_MAX];/*前缀获取方式为静态时使用*/
#define LAN_PRIFIX_LEN 64
	word8 aucPrifixMin[LAN_PRIFIX_LEN];
	word8 aucPrifixMax[LAN_PRIFIX_LEN];
	uword32 ulDelegatedWanGlobalIndex;
	word8 aucDelegatedWanName[CM_WAN_CONNECTION_NAME_LEN];
	uword32 ulPreferredLifeTime;/*公告前缀的preferred lifetime，单位为秒*/
	uword32 ulValidLifeTime;/*公告前缀的valid lifetime，单位为秒*/

#define LAN_IPV6_ROUTE_ANNOUNCEMENT_DISABLE (0)
#define LAN_IPV6_ROUTE_ANNOUNCEMENT_ENABLE (1)
	uword8 ucRouteAnnouncementEnable;/*路由通告启用*/
#define LAN_IPV6_POOL_DISABLE (0)
#define LAN_IPV6_POOL_ENABLE (1)
	uword8 ucIPv6PoolEnable;
#define LAN_IPV6_STB_DISABLE (0)
#define LAN_IPV6_STB_ENABLE (1)
	uword8 ucIPv6StbEnable;
#define LAN_IPV6_PHONE_DISABLE (0)
#define LAN_IPV6_PHONE_ENABLE (1)
	uword8 ucIPv6PhoneEnable;

#define LAN_IPV6_CAMERA_DISABLE (0)
#define LAN_IPV6_CAMERA_ENABLE (1)
	uword8 ucIPv6CameraEnable;
#define LAN_IPV6_COMPUTER_DISABLE (0)
#define LAN_IPV6_COMPUTER_ENABLE (1)
	uword8 ucIPv6ComputerEnable;
	uword8 aucPad[CM_TWO_PADS];

	word8 aucIPv6StbMinAddr[CM_IPV6_ADDR_LEN_MAX];
	word8 aucIPv6StbMaxAddr[CM_IPV6_ADDR_LEN_MAX];
	word8 aucIPv6PhoneMinAddr[CM_IPV6_ADDR_LEN_MAX];
	word8 aucIPv6PhoneMaxAddr[CM_IPV6_ADDR_LEN_MAX];
	word8 aucIPv6CameraMinAddr[CM_IPV6_ADDR_LEN_MAX];
	word8 aucIPv6CameraMaxAddr[CM_IPV6_ADDR_LEN_MAX];
	word8 aucIPv6ComputerMinAddr[CM_IPV6_ADDR_LEN_MAX];
	word8 aucIPv6ComputerMaxAddr[CM_IPV6_ADDR_LEN_MAX];
#define LAN_IPV6_OPTION16_DISABLE (0)
#define LAN_IPV6_OPTION16_ENABLE (1)
	uword8 ucOPTION16Enable;
#define LAN_IPV6_OPTION17_DISABLE (0)
#define LAN_IPV6_OPTION17_ENABLE (1)
	uword8 ucOPTION17Enable;
	uword8 aucPad2[CM_TWO_PADS];


#define LAN_IPV6_ATTR_MASK_BIT0_IPV6_ADDR (0x01)
#define LAN_IPV6_ATTR_MASK_BIT1_DNS_SOURCE (0x02)
#define LAN_IPV6_ATTR_MASK_BIT2_DHCPV6_ENABLE (0x04)
#define LAN_IPV6_ATTR_MASK_BIT3_STATEFULV6_ENABLE (0x08)
#define LAN_IPV6_ATTR_MASK_BIT4_LEASE_TIME (0x10)
#define LAN_IPV6_ATTR_MASK_BIT5_DNS_SERVERS (0x20)
#define LAN_IPV6_ATTR_MASK_BIT6_DNS_WAN_NAME (0x40)
#define LAN_IPV6_ATTR_MASK_BIT7_DNS_WAN_ALIAS (0x80)
#define LAN_IPV6_ATTR_MASK_BIT8_MIN_ADDR (0x100)
#define LAN_IPV6_ATTR_MASK_BIT9_MAX_ADDR (0x200)
#define LAN_IPV6_ATTR_MASK_BIT10_RADVD_ENABLE (0x400)
#define LAN_IPV6_ATTR_MASK_BIT11_ADDR_INFO_FROM (0x800)
#define LAN_IPV6_ATTR_MASK_BIT12_OTHER_INFO_FROM (0x1000)
#define LAN_IPV6_ATTR_MASK_BIT13_MIN_RA_TIME (0x2000)
#define LAN_IPV6_ATTR_MASK_BIT14_MAX_RA_TIME (0x4000)
#define LAN_IPV6_ATTR_MASK_BIT15_RA_DNS_SERVERS (0x8000)
#define LAN_IPV6_ATTR_MASK_BIT16_PRIFIX_SOURCE (0x10000)
#define LAN_IPV6_ATTR_MASK_BIT17_PRIFIX (0x20000)
#define LAN_IPV6_ATTR_MASK_BIT18_DELEGATED_WAN_NAME (0x40000)
#define LAN_IPV6_ATTR_MASK_BIT19_PREFFERED_LIFETIME (0x80000)
#define LAN_IPV6_ATTR_MASK_BIT20_VALID_LIFETIME (0x100000)
#define LAN_IPV6_ATTR_MASK_BIT21_ROUTE_ANNOUNCEMENT_ENABLE (0x200000)
#define LAN_IPV6_ATTR_MASK_BIT22_IPV6_POOL_ENABLE (0x400000)
#define LAN_IPV6_ATTR_MASK_BIT23_IPV6_STB_ENABLE (0x800000)
#define LAN_IPV6_ATTR_MASK_BIT24_IPV6_PHONE_ENABLE (0x1000000)
#define LAN_IPV6_ATTR_MASK_BIT25_IPV6_CAMERA_ENABLE (0x2000000)
#define LAN_IPV6_ATTR_MASK_BIT26_IPV6_COMPUTER_ENABLE (0x4000000)
#define LAN_IPV6_ATTR_MASK_BIT27_IPV6_STB_MIN_ADDR (0x8000000)
#define LAN_IPV6_ATTR_MASK_BIT28_IPV6_STB_MAX_ADDR (0x10000000)
#define LAN_IPV6_ATTR_MASK_BIT29_IPV6_PHONE_MIN_ADDR (0x20000000)
#define LAN_IPV6_ATTR_MASK_BIT30_IPV6_PHONE_MAX_ADDR (0x40000000)
#define LAN_IPV6_ATTR_MASK_BIT31_IPV6_CAMERA_MIN_ADDR (0x80000000)
#define LAN_IPV6_ATTR_MASK_ALL (0xffffffff)
	uword32 ulBitmap;

#define LAN_IPV6_ATTR_MASK1_BIT0_IPV6_CAMERA_MAX_ADDR (0x01)
#define LAN_IPV6_ATTR_MASK1_BIT1_IPV6_COMPUTER_MIN_ADDR (0x02)
#define LAN_IPV6_ATTR_MASK1_BIT2_IPV6_COMPUTER_MAX_ADDR (0x04)
#define LAN_IPV6_ATTR_MASK1_BIT3_DNS_WAN_INDEX (0x08)
#define LAN_IPV6_ATTR_MASK1_BIT4_DELEGATED_WAN_INDEX (0x10)
#define LAN_IPV6_ATTR_MASK1_BIT5_OPTION16_ENABLE (0x20)
#define LAN_IPV6_ATTR_MASK1_BIT6_OPTION17_ENABLE (0x40)
#define LAN_IPV6_ATTR_MASK1_BIT7_PD_SERVER_ENABLE (0x80)
#define LAN_IPV6_ATTR_MASK1_ALL (0x3f)
	uword32 ulBitmap1;
} __PACK__ IgdLanIPv6AttrConfTab;

/***************************LAN口属性表*********************************/
#define IGD_LAN_ETH_INTERFACE_NUM (8)

typedef struct {
	uword32 ulStateAndIndex;

#define LAN_ETH_INTERFACE_LAN1 (0)
#define LAN_ETH_INTERFACE_LAN2 (1)
#define LAN_ETH_INTERFACE_LAN3 (2)
#define LAN_ETH_INTERFACE_LAN4 (3)
#define LAN_ETH_INTERFACE_LAN5 (4)
#define LAN_ETH_INTERFACE_LAN6 (5)
#define LAN_ETH_INTERFACE_LAN7 (6)
#define LAN_ETH_INTERFACE_LAN8 (7)
	uword8 ucPortNum;/*端口号*/
	uword8 aucPad[CM_THREE_PADS];

#define LAN_ETH_INTERFACE_PORT_DISABLE (0)
#define LAN_ETH_INTERFACE_PORT_ENABLE (1)
	uword8 ucPortEnable;/*端口使能*/
#define LAN_ETH_INTERFACE_PORT_DUPLEX_MODE_HALF (0)
#define LAN_ETH_INTERFACE_PORT_DUPLEX_MODE_FULL (1)
#define LAN_ETH_INTERFACE_PORT_DUPLEX_MODE_AUTO (2)
	uword8 ucPortDuplexMode; /*双工模式*/
#define LAN_ETH_INTERFACE_PORT_SPEED_MODE_10M (1)
#define LAN_ETH_INTERFACE_PORT_SPEED_MODE_100M (2)
#define LAN_ETH_INTERFACE_PORT_SPEED_MODE_1000M (3)
#define LAN_ETH_INTERFACE_PORT_SPEED_MODE_AUTO (4)
#define LAN_ETH_INTERFACE_PORT_SPEED_MODE_2500M (5)
#define LAN_ETH_INTERFACE_PORT_SPEED_MODE_10000M (6)
	uword8 ucPortSpeedMode; /*速率模式*/
#define LAN_ETH_INTERFACE_MAC_ADDR_CONTROL_DISABLE (0)
#define LAN_ETH_INTERFACE_MAC_ADDR_CONTROL_ENABLE (1)
	uword8 ucPortMacAddrControlEnabled;

#define LAN_ETH_INF_ATTR_MASK_BIT0_ENABLE (0x01)
#define LAN_ETH_INF_ATTR_MASK_BIT1_DUPLEX_MODE (0x02)
#define LAN_ETH_INF_ATTR_MASK_BIT2_SPEED_MODE (0x04)
#define LAN_ETH_INF_ATTR_MASK_BIT3_MAC_ADDR_CONTROL_ENABLED (0x08)
#define LAN_ETH_INF_ATTR_MASK_ALL (0x0f)
	uword32 ulBitmap;
} __PACK__ IgdLanEthInterfaceAttrConfTab;


/***************************LAN口状态信息表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define LAN_ETH_INTERFACE_LAN1 (0)
#define LAN_ETH_INTERFACE_LAN2 (1)
#define LAN_ETH_INTERFACE_LAN3 (2)
#define LAN_ETH_INTERFACE_LAN4 (3)
#define LAN_ETH_INTERFACE_LAN5 (4)
#define LAN_ETH_INTERFACE_LAN6 (5)
#define LAN_ETH_INTERFACE_LAN7 (6)
#define LAN_ETH_INTERFACE_LAN8 (7)
	uword8 ucPortNum;/*端口号*/
#define LAN_ETH_INTERFACE_MODE_HALF_DUPLEX (0)
#define LAN_ETH_INTERFACE_MODE_FULL_DUPLEX (1)
#define LAN_ETH_INTERFACE_MODE_AUTO_DUPLEX (2)
	uword8 ucPortDuplexMode;/*网口模式*/
#define LAN_ETH_INTERFACE_SPEED_MODE_10M (1)
#define LAN_ETH_INTERFACE_SPEED_MODE_100M (2)
#define LAN_ETH_INTERFACE_SPEED_MODE_1000M (3)
#define LAN_ETH_INTERFACE_SPEED_MODE_AUTO (4)
#define LAN_ETH_INTERFACE_SPEED_MODE_2500M (5)
#define LAN_ETH_INTERFACE_SPEED_MODE_10000M (6)
	uword8 ucPortSpeed; /* 速率模式 */
#define LAN_ETH_INTERFACE_DEVICE_UP (1)
#define LAN_ETH_INTERFACE_DEVICE_NOLINK (2)
#define LAN_ETH_INTERFACE_DEVICE_ERROR (3)
#define LAN_ETH_INTERFACE_DEVICE_DISABLE (4)
	uword8 ucPortConnectStatus;/*对应web上设备连接状态，2、3、4都表示未连接*/

	uword8 aucMacAddr[CM_MAC_ADDR_LEN];
	uword8 ucPortMaxSpeed; /* 支持的最大速率模式 */
	uword8 pad;
	uword64 ulBytesSent;/*发送字节数*/
	uword64 ulBytesReceived; /*接收字节数*/
	uword64 ulRecvErrPkts; /*接收错误包数*/
	uword64 ulRecvDropPkts; /*接收丢包数*/
	uword32  uiRecvRate; /*接收速率 Kbps*/

	uword64 ulPacketsSent; /*发送帧数*/
	uword64 ulPacketsRecieved; /*接收帧数*/
	uword64 ulSendErrPkts; /*发送错误包数*/
	uword64 ulSendDropPkts; /*发送丢包数*/
	uword32  uiSendRate; /*发送速率 Kbps*/

#define LAN_ETH_INF_STATUS_INFO_MASK_BIT0_PORT_DUPLEX_MODE (0x01)
#define LAN_ETH_INF_STATUS_INFO_MASK_BIT1_PORT_SPEED_MODE (0x02)
#define LAN_ETH_INF_STATUS_INFO_MASK_BIT2_PORT_CONN_STATUS (0x04)
#define LAN_ETH_INF_STATUS_INFO_MASK_BIT3_PORT_MAC_ADDR (0x08)
#define LAN_ETH_INF_STATUS_INFO_MASK_BIT4_PORT_BYTES_SENT (0x10)
#define LAN_ETH_INF_STATUS_INFO_MASK_BIT5_PORT_BYTES_RECEIVED (0x20)
#define LAN_ETH_INF_STATUS_INFO_MASK_BIT6_PORT_PACKETS_SENT (0x40)
#define LAN_ETH_INF_STATUS_INFO_MASK_BIT7_PORT_PACKETS_RECEIVED (0x80)
#define LAN_ETH_INF_STATUS_INFO_MASK_ALL (0x8f)
	uword32 ulBitmap;
} __PACK__ IgdLanEthInterfaceStatusInfoTab;

/********************************LAN口以太网报文过滤*******************************************/
#define IGD_LAN_ETHERNET_INTERFACE_FILTER_LIST_RECORD_NUM (12)

typedef struct {
	uword32 ulStateAndIndex;
	uword8 ulIndex;
#define LAN_ETHER_INTERFACE_FILTER_TYPE_IPV4OE (0x01)
#define LAN_ETHER_INTERFACE_FILTER_TYPE_PPPOE (0x02)
#define LAN_ETHER_INTERFACE_FILTER_TYPE_ARP (0x04)
#define LAN_ETHER_INTERFACE_FILTER_TYPE_IPV6OE (0x08)
#define LAN_ETHER_INTERFACE_FILTER_TYPE_NULL (0x10)
	uword8 ucEtherTypeFilter;
	uword8 aucPad[CM_TWO_PADS];

	//uword32 ulPort;
	uword8 aucFilterMAC[CM_MAC_ADDR_LEN];
	uword8 aucFilterDestMAC[CM_MAC_ADDR_LEN];
	uword32 ulMACAddressNum;	/*MAC地址学习数限制，0表示不做限制*/

#define LAN_ETHER_INTERFACE_FILTER_MASK_BIT0_TYPE (0x01)
#define LAN_ETHER_INTERFACE_FILTER_MASK_BIT1_SRCMAC (0x02)
#define LAN_ETHER_INTERFACE_FILTER_MASK_BIT2_DSTMAC (0x04)
#define LAN_ETHER_INTERFACE_FILTER_MASK_BIT3_MACADDR_NUM (0x08)
#define LAN_ETHER_INTERFACE_FILTER_MASK_ALL (0x0f)

	uword32 ulBitmap;
} __PACK__ IgdCmLanEtherInterfaceFilterTab;


/***************************LAN侧DHCP绑定属性表*********************************/
/*表相关宏*/
#define IGD_LAN_DHCP_STATIC_LIST_NUM		32

typedef struct {
	uword32	ulStateAndIndex;
	uword32	ulIndex;
	word8	aucMACAddr[CM_MAC_CHAR_LEN];

#define LAN_DHCP_STATIC_IP_VERSION_V4 (0x00)
#define LAN_DHCP_STATIC_IP_VERSION_V6 (0x01)
	uword8	ucIPVersion;
	uword8	ucPad;
	word8	aucIPAddr[CM_IP_ADDR_STRING_LEN];

#define LAN_DHCP_STATIC_MASK_MAC_ADDR (1 << 0)
#define LAN_DHCP_STATIC_MASK_IP_VERSION (1 << 1)
#define LAN_DHCP_STATIC_MASK_IP_ADDR (1 << 2)

	uword32 ulBitmap;
} __PACK__ IgdLanDhcpStaticListTab;


/*************************** BR-LAN ipv6 addr *********************************/
typedef struct {
	uword8            ac_ip[CM_IPV6_ADDR_LEN_MAX * CM_BRLAN_IPV6_ADDR_NUM];
	uword8            ac_prefix[CM_IPV6_ADDR_LEN_MAX * CM_BRLAN_IPV6_ADDR_NUM];
} IgdLanBrLanIpv6AddrListTab;


#define  IGD_LAN_DHCP_COND_SERVING_POOL_MAXNUM                           (8)
typedef struct {
	uint32_t state_and_index;
	uint32_t instance_index;
	uint8_t  enable;
	uint8_t  vendor_classid_exclude;
	uint8_t  pad[CM_TWO_PADS];
	uint32_t pool_order;
#define LAN_DHCP_COND_SERVING_POOL_SOURCE_INTERFACE_STRING_LEN           (1024)
	uint8_t  source_interface[LAN_DHCP_COND_SERVING_POOL_SOURCE_INTERFACE_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_VENDOR_CLASSID_STRING_LEN             (256)
	uint8_t  vendor_classid[LAN_DHCP_COND_SERVING_POOL_VENDOR_CLASSID_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_VENDOR_CLASSID_MODE_STRING_LEN        (16)
	uint8_t  vendor_classid_mode[LAN_DHCP_COND_SERVING_POOL_VENDOR_CLASSID_MODE_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_MIN_ADDRESS_STRING_LEN                (32)
	uint8_t  min_address[LAN_DHCP_COND_SERVING_POOL_MIN_ADDRESS_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_MAX_ADDRESS_STRING_LEN                (32)
	uint8_t  max_address[LAN_DHCP_COND_SERVING_POOL_MAX_ADDRESS_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_RESERVED_ADDRESSES_STRING_LEN         (512)
	uint8_t  reserved_addresses[LAN_DHCP_COND_SERVING_POOL_RESERVED_ADDRESSES_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_SUBNET_MASK_STRING_LEN                (32)
	uint8_t  subnet_mask[LAN_DHCP_COND_SERVING_POOL_SUBNET_MASK_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_DNS_SERVERS_STRING_LEN                (64)
	uint8_t  dns_servers[LAN_DHCP_COND_SERVING_POOL_DNS_SERVERS_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_DOMAIN_NAME_STRING_LEN                (64)
	uint8_t  domain_name[LAN_DHCP_COND_SERVING_POOL_DOMAIN_NAME_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_IP_ROUTERS_STRING_LEN                 (64)
	uint8_t  ip_routers[LAN_DHCP_COND_SERVING_POOL_IP_ROUTERS_STRING_LEN];
	int32_t  dhcp_lease_time;
#define LAN_DHCP_COND_SERVING_POOL_USE_ALLOCATED_WAN_STRING_LEN          (16)
	uint8_t  use_allocated_wan[LAN_DHCP_COND_SERVING_POOL_USE_ALLOCATED_WAN_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_ASSOCIATED_CONNECTION_STRING_LEN      (256)
	uint8_t  associated_connection[LAN_DHCP_COND_SERVING_POOL_ASSOCIATED_CONNECTION_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_DHCP_SERVER_IP_ADDRESS_STRING_LEN     (32)
	uint8_t  dhcp_server_ip_address[LAN_DHCP_COND_SERVING_POOL_DHCP_SERVER_IP_ADDRESS_STRING_LEN];
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_ENABLE                      (1 << 0)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_VENDOR_CLASSID_EXCLUDE      (1 << 1)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_POOL_ORDER                  (1 << 2)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_SOURCE_INTERFACE            (1 << 3)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_VENDOR_CLASSID              (1 << 4)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_VENDOR_CLASSID_MODE         (1 << 5)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_MIN_ADDRESS                 (1 << 6)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_MAX_ADDRESS                 (1 << 7)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_RESERVED_ADDRESSES          (1 << 8)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_SUBNET_MASK                 (1 << 9)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_DNS_SERVERS                 (1 << 10)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_DOMAIN_NAME                 (1 << 11)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_IP_ROUTERS                  (1 << 12)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_DHCP_LEASE_TIME             (1 << 13)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_USE_ALLOCATED_WAN           (1 << 14)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_ASSOCIATED_CONNECTION       (1 << 15)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_DHCP_SERVER_IP_ADDRESS      (1 << 16)
#define LAN_DHCP_COND_SERVING_POOL_ATTR_MASK_ALL                        ((1 << 17) - 1)
	uint32_t bit_map;
} __PACK__ igd_lan_dhcp_cond_serving_pool_attr_conf_tab_t;


/***************************Storage black list tab*********************************/
#define IGD_STORAGE_BLACK_LIST_NUM		64
#define MAC_ADDRESS_LEN				12

typedef struct {
	uint32_t	state_and_index;
	uint32_t	index;
	uint8_t		mac_address[CM_MAC_ADDR_LEN];
	uint8_t		pad[CM_TWO_PADS];

#define STORAGE_BLACK_LIST_MASK_MAC (1 << 0)
#define STORAGE_BLACK_LIST_MASK_ALL ((1 << 1) - 1)

	uint32_t bit_map;
} __PACK__ cm_storage_black_list_tab_t;

#endif
