/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab transfer service obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_TRANSFER_SERVICE_H
#define HI_ODL_TAB_TRANSFER_SERVICE_H
#include "hi_odl_basic_type.h"

#define IGD_TRANSFER_MAX_NUM (256)
#define TRAF_REMOTE_ADDR_LEN (128)
#define TRAF_REMOTE_PORT_LEN (32)
#define TRAF_HOST_MAC_LEN (32)
#define TRAF_TO_IP_LEN (64)
typedef struct {
	uword32						ulStateAndIndex;
	word32						ulRuleIndex ; /*规则索引0-255*/
	word8                       aucRemoteAddress[TRAF_REMOTE_ADDR_LEN]; /*目的IP段或域名*/
	word8						aucRemotePort[TRAF_REMOTE_PORT_LEN]; /*报文目的端口范围*/
#define IP_PROTOCOL_TCP (0)
#define IP_PROTOCOL_UDP (1)
	uword32                                     ulProtocol; /*协议类型*/
	word8						 aucHostMAC[TRAF_HOST_MAC_LEN]; /*下挂设备MAC*/
	word8						 aucForwardToIP[TRAF_TO_IP_LEN]; /*重定向目的IP*/
	uword32						 ulForwardToPort ; /*重定向目的端口*/
#define TRANSFER_SERVICES_FORWARD_MASK_BIT0_RADDR				(0x01)
#define TRANSFER_SERVICES_FORWARD_MASK_BIT1_RPORT				(0x02)
#define TRANSFER_SERVICES_FORWARD_MASK_BIT2_PROTOCOL				(0x04)
#define TRANSFER_SERVICES_FORWARD_MASK_BIT3_HOSTMAC				(0x08)
#define TRANSFER_SERVICES_FORWARD_MASK_BIT4_FORWIP			(0x10)
#define TRANSFER_SERVICES_FORWARD_MASK_BIT5_FORWPORT			(0x20)
	uword32						ulBitmap;
}  IgdCmTranferSerForwardConfTab;

/* Mirror Class Start */
typedef struct {
	uword32						ulStateAndIndex;
	word32						ulMirrRuleIndex ; /*规则索引0-255*/
	word8                       aucMirrRemoteAddr[TRAF_REMOTE_ADDR_LEN]; /*目的IP段或域名*/
	word8						aucMirrRemotePort[TRAF_REMOTE_PORT_LEN]; /*报文目的端口范围*/
#define STREAM_DIRECTION_UP (0)
#define STREAM_DIRECTION_DOWN (1)
#define STREAM_DIRECTION_ALL (2)
	uword32                                     ulMirrDirection; /*协议类型*/
#define IP_PROTOCOL_TCP (0)
#define IP_PROTOCOL_UDP (1)
	uword32                                     ulMirrProtocol; /*协议类型*/
	word8						 aucMirrHostMAC[TRAF_HOST_MAC_LEN]; /*下挂设备MAC*/
	word8						 aucMirrorToIP[TRAF_TO_IP_LEN]; /*重定向目的IP*/
	uword32						 ulMirrorToPort ; /*重定向目的端口*/
	uword32						 ultcpheadonly ;
	uword32						 ultcprule ;
	uword32						 ulpacketnum ;
	uword32						 ulPayloadType;
#define TRANSFER_SERVICES_MIRROR_MASK_BIT0_RADDR				(0x01)
#define TRANSFER_SERVICES_MIRROR_MASK_BIT1_RPORT				(0x02)
#define TRANSFER_SERVICES_MIRROR_MASK_BIT2_PROTOCOL				(0x04)
#define TRANSFER_SERVICES_MIRROR_MASK_BIT3_HOSTMAC				(0x08)
#define TRANSFER_SERVICES_MIRROR_MASK_BIT4_MIRRORIP			(0x10)
#define TRANSFER_SERVICES_MIRROR_MASK_BIT5_MIRRORPORT			(0x20)
#define TRANSFER_SERVICES_MIRROR_MASK_BIT6_MIRRDIREC			(0x40)
	uword32						ulBitmap;
}  IgdCmTranferSerMirrorConfTab;


/* TrafficMonitor Class Start */
#define IGD_TRANSFER_MONITOR_MAX_NUM (256)  /* 要求1024 ,暂定256 */
typedef struct {
	uword32						ulStateAndIndex;
	word32						ulRuleIndex ;           /*规则索引0-255*/
#define 	TRANSFER_SERVICES_MONITOR_RADDR_LEN  (128)
	word8
	aucRemoteAddress[TRANSFER_SERVICES_MONITOR_RADDR_LEN];  /*目的IP段或域名*/
#define     TRANSFER_SERVICES_MONITOR_BUNDLE_NAME_LEN  (128)
	word8
	aucBundleName[TRANSFER_SERVICES_MONITOR_BUNDLE_NAME_LEN];      /* 监控的bundle 名称 */
#define TRANSFER_SERVICES_MONITOR_MASK_BIT0_RADDR				(0x01)
#define TRANSFER_SERVICES_MONITOR_MASK_BUNDLE_NAME				(0x02)
#define TRANSFER_SERVICES_MONITOR_MASK_ALL          			(0x03)
	uword32						ulBitmap;
}  IgdCmTranferSerMonitorCfgTab;

/* Traffic Detail Process Service Class Start */
#define IGD_TRAFFIC_DETAIL_SRV_MAX_NUM (256)
typedef struct {
	word32  ulStateAndIndex;
	word32  ulTrafficServiceId;
	word8  aucRemoteAddress[128];
	word8  aucRemotePort[12];

#define STREAM_DIRECTION_UP (0)
#define STREAM_DIRECTION_DOWN (1)
#define STREAM_DIRECTION_ALL (2)
	uword32	ulDirection; /*方向*/

	word8  aucHostMAC[18];
	uword8  aucPad[2];
	word8  aucMethod[128]; //"options","get","head", "post", "put", "delete", "trace", "connect", "extension"
	word8  aucStatusCodeList[128];
	word8  aucHeaderList[256];
	word8  aucContextName[128];

#define TRAFFICDETAILPROCESSSERVICEATTR_BIT0_REMOTEADDRESS (0x01)
#define TRAFFICDETAILPROCESSSERVICEATTR_BIT1_REMOTEPORT    (0x02)
#define TRAFFICDETAILPROCESSSERVICEATTR_BIT2_DIRECTION     (0x04)
#define TRAFFICDETAILPROCESSSERVICEATTR_BIT3_HOSTMAC       (0x08)
#define TRAFFICDETAILPROCESSSERVICEATTR_BIT4_METHOD        (0x10)
#define TRAFFICDETAILPROCESSSERVICEATTR_BIT5_STATUS_CODE   (0x20)
#define TRAFFICDETAILPROCESSSERVICEATTR_BIT6_HEADER        (0x40)
#define TRAFFICDETAILPROCESSSERVICEATTR_BIT7_CONTEXTNAME   (0x80)
#define TRAFFICDETAILPROCESSSERVICEATTR_MASK_ALL           (0xFF)
	uword32 ulBitmap;
} __PACK__ IgdCmTrafficDetailProcessServiceConfTab;

/* Traffic Qos Service Class */
#define IGD_TRAFFIC_QOS_FLOW_MAX_NUM 512

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulFlowId;
	word32 ulRuleId;
	word8  aucRemoteAddress[128];
	word8  aucRemotePort[32];

#define TRAFFICQOSFLOWINFO_BIT0_REMOTEADDRESS (0x01)
#define TRAFFICQOSFLOWINFO_BIT1_REMOTEPORT    (0x02)
#define TRAFFICQOSFLOWINFO_MASK_ALL           (0x03)
	uword32 ulBitmap;
} __PACK__ IgdCmTrafficQosFlowInfoTab;


typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulFlowId;
	uword64 ullUsStats;
	uword64 ullDsStats;
	uword32 ulUsSpeed;
	uword32 ulDsSpeed;

#define TRAFFICQOSFLOWATTR_BIT0_BYTE_STATS     (0x01)
#define TRAFFICQOSFLOWATTR_BIT1_SPEED_STATS    (0x02)
#define TRAFFICQOSFLOWATTR_ALL                 (0x03)
	uword32 ulBitmap;
} __PACK__ IgdCmTrafficQosFlowStatsAttrTab;

#define IGD_TRAFFIC_QOS_FLOW_ATTR_MAX_NUM 8
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulFlowId;
	uword32 ulUpSpeedLimit;
	uword32 ulDnSpeedLimit;
#define DSCP_DISABLE                (0xFF)
	uword32 ulRemarkDscp;

#define TRAFFICQOSFLOWATTR_MASK_BIT0_UPSPEEDLIMIT (0x01)
#define TRAFFICQOSFLOWATTR_MASK_BIT1_DNSPEEDLIMIT (0x02)
#define TRAFFICQOSFLOWATTR_MASK_BIT2_REMARKDSCP   (0x04)
#define TRAFFICQOSFLOWATTR_MASK_ALL     (0x07)
	uword32 ulBitmap;
} __PACK__ IgdCmTrafficQosFlowAttrTab;

#endif
