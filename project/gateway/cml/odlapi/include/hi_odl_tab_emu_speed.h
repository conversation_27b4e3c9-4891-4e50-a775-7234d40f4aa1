/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm emu speed obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_EMU_SPEED_H
#define HI_ODL_TAB_EMU_SPEED_H
#include "hi_odl_basic_type.h"
#include "hi_emu_speed.h"

typedef struct {
	uword32 ulStateAndIndex;
#define DOWNLOAD_DIAGNOSTIC_STATE_NONE  HI_EMU_SPEED_STATE_NONE_E
#define DOWNLOAD_DIAGNOSTIC_STATE_REQUESTED HI_EMU_SPEED_STATE_REQ_E
#define DOWNLOAD_DIAGNOSTIC_STATE_COMPLETE HI_EMU_SPEED_STATE_COMPLETE_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_INITCONNFAILED HI_EMU_SPEED_STATE_ERR_INIT_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_NORESPONSE HI_EMU_SPEED_STATE_ERR_RESPONSE_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_TRANSFERFAILED HI_EMU_SPEED_STATE_ERR_TRANSFER_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_PSDREQUESTFAILED HI_EMU_SPEED_STATE_ERR_PASSWORD_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_LOGINFAILED HI_EMU_SPEED_STATE_ERR_LOGIN_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_NOTRANSFERMODE HI_EMU_SPEED_STATE_ERR_MODE_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_NOPASV HI_EMU_SPEED_STATE_ERR_PASV_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_INCORRECTSIZE HI_EMU_SPEED_STATE_ERR_SIZE_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_TIMEOUT HI_EMU_SPEED_STATE_ERR_TIMEOUT_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_NOTRANSFERCOMPLETE HI_EMU_SPEED_STATE_ERR_NOCOMPLETE_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_NOCWD	HI_EMU_SPEED_STATE_ERR_NOCWD_E
#define DOWNLOAD_DIAGNOSTIC_STATE_ERROR_NOSTOR	HI_EMU_SPEED_STATE_ERR_NOSTOR_E
	uword8 ucState;
#define DOWNLOAD_DIAGNOSTIC_DSCP_MIN (0)
#define DOWNLOAD_DIAGNOSTIC_DSCP_MAX (63)
	uword8 ucDSCP;
#define DOWNLOAD_DIAGNOSTIC_PRIORITY_MIN (0)
#define DOWNLOAD_DIAGNOSTIC_PRIORITY_MAX (16)
	uword8 ucEthernetPriority;

	uword8 ucPad;

#define DOWNLOAD_DIAGNOSTIC_INTERFACE_LEN (256)
	uword8 aucInterface[DOWNLOAD_DIAGNOSTIC_INTERFACE_LEN];
	uword8 aucDownloadURL[CM_URL_LEN];
	uword32 ulMaxTime;
	uword32 ulNumOfCons;
	uword32 ulMeasurementOffset;
	uword32 ulFinishMode;

#define DOWNLOAD_DIAGNOSTIC_ATTR_MASK_BIT0_STATE (0x01)
#define DOWNLOAD_DIAGNOSTIC_ATTR_MASK_BIT1_INTERFACE (0x02)
#define DOWNLOAD_DIAGNOSTIC_ATTR_MASK_BIT2_DOWNLOADURL (0x04)
#define DOWNLOAD_DIAGNOSTIC_ATTR_MASK_BIT3_DSCP (0x08)
#define DOWNLOAD_DIAGNOSTIC_ATTR_MASK_BIT4_PRIORITY (0x10)
#define DOWNLOAD_DIAGNOSTIC_ATTR_MASK_BIT5_MAXTIME (0x20)
#define DOWNLOAD_DIAGNOSTIC_ATTR_MASK_BIT6_NUMCONS (0x40)
#define DOWNLOAD_DIAGNOSTIC_ATTR_MASK_BIT7_OFFSET  (0x80)
#define DOWNLOAD_DIAGNOSTIC_ATTR_MASK_BIT8_FINISH_MODE (0x100)
#define DOWNLOAD_DIAGNOSTIC_ATTR_MASK_ALL (0xff)
	uword32 ulBitmap;
} __PACK__ IgdPMDownloadDiagAttrConfTab;


/***************************下载诊断统计表*********************************/

typedef struct {
	uword32 ulStateAndIndex;
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_NONE  HI_EMU_SPEED_STATE_NONE_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_REQUESTED HI_EMU_SPEED_STATE_REQ_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_COMPLETE HI_EMU_SPEED_STATE_COMPLETE_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_ERROR_INITCONNFAILED HI_EMU_SPEED_STATE_ERR_INIT_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_ERROR_NORESPONSE HI_EMU_SPEED_STATE_ERR_RESPONSE_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_ERROR_TRANSFERFAILED HI_EMU_SPEED_STATE_ERR_TRANSFER_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_ERROR_PSDREQUESTFAILED HI_EMU_SPEED_STATE_ERR_PASSWORD_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_ERROR_LOGINFAILED HI_EMU_SPEED_STATE_ERR_LOGIN_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_ERROR_NOTRANSFERMODE HI_EMU_SPEED_STATE_ERR_MODE_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_ERROR_NOPASV HI_EMU_SPEED_STATE_ERR_PASV_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_ERROR_INCORRECTSIZE HI_EMU_SPEED_STATE_ERR_SIZE_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_ERROR_NOTRANSFERCOMPLETE HI_EMU_SPEED_STATE_ERR_NOCOMPLETE_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_ERROR_TIMEOUT       HI_EMU_SPEED_STATE_ERR_TIMEOUT_E
#define DOWNLOAD_DIAGNOSTIC_RETURN_STATE_TRANSFERING		 HI_EMU_SPEED_STATE_TRANSFERING_E
	uword8 ucState;
	uword8 aucPad[CM_THREE_PADS];

	lword64	ulTestBytesReceived;		/*接收字节数，包括控制头*/
	lword64	ulTotalBytesReceived;		/*接收字节数*/

	hi_os_timeval_s	ulROMTime;
	hi_os_timeval_s	ulBOMTime;
	hi_os_timeval_s	ulEOMTime;
	hi_os_timeval_s	ulTCPOpenRequestTime;
	hi_os_timeval_s	ulTCPOpenResponseTime;
#define DOWNLOAD_DIAGNOSTIC_SAMPLE_VAL_LEN (160)
	word8 aucSampleVal[DOWNLOAD_DIAGNOSTIC_SAMPLE_VAL_LEN];
	word8 aucTotalSampleVal[DOWNLOAD_DIAGNOSTIC_SAMPLE_VAL_LEN];

	uword32 ulBitmap;
} __PACK__ IgdPMDownloadDiagStatisticsAttrConfTab;


typedef IgdPMDownloadDiagAttrConfTab IgdPMUploadDiagAttrConfTab;

/* 先PPP仿真再下载测速 */
typedef IgdPMPPPOEEmulatorAttrConfTab IgdPMDownloadPPPDiagAttrConfTab;

typedef IgdPMDownloadDiagStatisticsAttrConfTab IgdPMUploadDiagStatisticsAttrConfTab;

/*******************************DNS报文限速********************************/
/*表相关宏*/
typedef struct {
	uword32 ulStateAndIndex;

#define DNS_SPEED_LIMIT_DOMAIN_LIMITACTION_ALERT (0)	/* 当发现有新的域名域超过阈值时，向ITMS上报域名超限Inform消息*/
#define DNS_SPEED_LIMIT_DOMAIN_LIMITACTION_DROP (1)	/*超出阈值的DNS请求被直接丢弃*/
	uword8 ucLimitAction;		/*越限控制策略*/
	uword8 ucPad;
	uword8 aucHgwWanMac[CM_MAC_ADDR_LEN];	/*网关WAN侧MAC地址*/
	uword8 aucHgwWanIp[CM_IP_ADDR_LEN];		/*网关WAN侧IP地址*/

#define DNS_SPEED_LIMIT_DEVICEINFO_LEN (1024)
	word8 aucDeviceInfo[DNS_SPEED_LIMIT_DEVICEINFO_LEN];
#define DNS_SPEED_LIMIT_DOMAIN_LEN (1024)
	word8 aucDomain[DNS_SPEED_LIMIT_DOMAIN_LEN];

#define DBUS_DNS_SPEED_LIMIT_ATTR_MASK_BIT0_DOAMIN       (0x01)
#define DBUS_DNS_SPEED_LIMIT_ATTR_MASK_BIT1_LIMIT_ACTION (0x02)
#define DBUS_DNS_SPEED_LIMIT_ATTR_MASK_BIT2_HGWWANIP     (0x04)
#define DBUS_DNS_SPEED_LIMIT_ATTR_MASK_BIT3_HGWWANMAC    (0x08)
#define DBUS_DNS_SPEED_LIMIT_ATTR_MASK_BIT4_DEVICEINFO   (0x10)
#define DBUS_DNS_SPEED_LIMIT_ATTR_MASK_ALL               (0x1f)

	uword32 ulBitmap;
} __PACK__ IgdDbusDnsSpeedLimitAttrTab;

/*******************************DNS报文限速********************************/


/*******************************speedtestff 测速********************************/
typedef enum __SPEEDTESTFF_SPEEDTYPE {
	SPEEDTESTFF_SPEEDTYPE_DOWN = 1,
	SPEEDTESTFF_SPEEDTYPE_UP = 2,
	SPEEDTESTFF_SPEEDTYPE_UPDOWN = 3,
	SPEEDTESTFF_SPEEDTYPE_PING = 4,
	SPEEDTESTFF_SPEEDTYPE_BOTTOM
} SPEEDTESTFF_SPEEDTYPE;
typedef struct {
	uword32 ulStateAndIndex;

#define SPEEDTESTFF_TESTINGSTATUS_TESTING 		(1)
#define SPEEDTESTFF_TESTINGSTATUS_TESTEND 		(0)
	/*内部通知使用*/
#define SPEEDTESTFF_TESTINGSTATUS_TESTNOTIFY 	(2)
	uword8 ucTestingStatus;
	uword8 ucSpeedType;
	uword8 ucPad[2];

#define SPEEDTESTFF_RESULT_SUCC (0)
#define SPEEDTESTFF_RESULT_FAIL (1)
	uword32 ucResult;

#define SPEEDTESTFF_TICKET_STRING_LEN (80)
	uword8  ucTicket[SPEEDTESTFF_TICKET_STRING_LEN];

#define SPEEDTESTFF_URL_STRING_LEN 		(1024)
	word8  ucURL[SPEEDTESTFF_URL_STRING_LEN];

	uword32 uldwSpeedMaxRate;
	uword32 uldwSpeedRate;
	uword32 ulupSpeedMaxRate;
	uword32 ulupSpeedRate;

#define SPEEDTESTFF_BEGIN_TIME_STRING_LEN 		(32)
	uword8  ucBeginTime[SPEEDTESTFF_BEGIN_TIME_STRING_LEN];

#define SPEEDTESTFF_END_TIME_STRING_LEN 		(32)
	uword8	ucEndTime[SPEEDTESTFF_END_TIME_STRING_LEN];

#define SPEEDTESTFF_MAX_TIME_DELAY_LEN (16)
#define SPEEDTESTFF_MIN_TIME_DELAY_LEN (16)
#define SPEEDTESTFF_AVG_TIME_DELAY_LEN (16)
#define SPEEDTESTFF_PACKET_LOSS_LEN (16)
#define SPEEDTESTFF_PACKET_SHAKE_LEN (16)
	uword8 max_time_delay[SPEEDTESTFF_MAX_TIME_DELAY_LEN]; /*最大时延ms*/
	uword8 min_time_delay[SPEEDTESTFF_MIN_TIME_DELAY_LEN]; /*最小时延ms*/
	uword8 avg_time_delay[SPEEDTESTFF_AVG_TIME_DELAY_LEN]; /*平均时延ms*/
	uword8 packet_loss[SPEEDTESTFF_PACKET_LOSS_LEN]; /*丢包%*/
	uword8 packet_shake[SPEEDTESTFF_PACKET_SHAKE_LEN]; /*抖动ms*/

#define SPEEDTESTFF_ATTR_MASK_BIT0_TESTINGSTATUS 	(0x01)
#define SPEEDTESTFF_ATTR_MASK_BIT1_RESULT    	 	(0x02)
#define SPEEDTESTFF_ATTR_MASK_BIT2_TICKET    	 	(0x04)
#define SPEEDTESTFF_ATTR_MASK_BIT3_SPEEDTYPE    	(0x08)
#define SPEEDTESTFF_ATTR_MASK_BIT4_URL    	 		(0x10)
#define SPEEDTESTFF_ATTR_MASK_BIT5_DWMAXRATE    	(0x20)
#define SPEEDTESTFF_ATTR_MASK_BIT6_DWRATE    	 	(0x40)
#define SPEEDTESTFF_ATTR_MASK_BIT7_UPMAXRATE    	(0x80)
#define SPEEDTESTFF_ATTR_MASK_BIT8_UPRATE   	 	(0x100)
#define SPEEDTESTFF_ATTR_MASK_BIT9_BEGINGTIME  	 	(0x200)
#define SPEEDTESTFF_ATTR_MASK_BIT10_ENDTIME    	 	(0x400)
#define SPEEDTESTFF_ATTR_MASK_MAX_TIME_DELAY        (0x800)
#define SPEEDTESTFF_ATTR_MASK_MIN_TIME_DELAY        (0x1000)
#define SPEEDTESTFF_ATTR_MASK_AVG_TIME_DELAY        (0x2000)
#define SPEEDTESTFF_ATTR_MASK_PACKET_LOSS           (0x4000)
#define SPEEDTESTFF_ATTR_MASK_PACKET_SHAKE          (0x8000)
#define SPEEDTESTFF_ATTR_MASK_ALL                   (0xffffff)
	uword32 ulBitmap;
} __PACK__ IgdPMSpeedTestFFAttrConfTab;


/*******************************http speed test 测速********************************/
/* IGD_HTTP_SPEED_TEST_ATTR_TAB */
#define IGD_HTTP_SPEED_TEST_ATTR_MAX 1
typedef struct {
	uint32_t state_and_index;
	uint8_t  enabled_for_test;
	uint8_t  pad[CM_THREE_PADS];
#define HTTP_SPEED_TEST_TYPE_STRING_LEN                          (256)
	uint8_t  type[HTTP_SPEED_TEST_TYPE_STRING_LEN];
#define HTTP_SPEED_TEST_TEST_URL_STRING_LEN                      (256)
	uint8_t  test_url[HTTP_SPEED_TEST_TEST_URL_STRING_LEN];
#define HTTP_SPEED_TEST_EU_PPPOE_NAME_STRING_LEN                 (256)
	uint8_t  eu_pppoe_name[HTTP_SPEED_TEST_EU_PPPOE_NAME_STRING_LEN];
#define HTTP_SPEED_TEST_EU_PASSWORD_STRING_LEN                   (256)
	uint8_t  eu_password[HTTP_SPEED_TEST_EU_PASSWORD_STRING_LEN];
#define HTTP_SPEED_TEST_WAN_INTERFACE_STRING_LEN                 (256)
	uint8_t  wan_interface[HTTP_SPEED_TEST_WAN_INTERFACE_STRING_LEN];
#define HTTP_SPEED_TEST_ATTR_MASK_ENABLED_FOR_TEST               (1 << 0)
#define HTTP_SPEED_TEST_ATTR_MASK_TYPE                           (1 << 1)
#define HTTP_SPEED_TEST_ATTR_MASK_TEST_URL                       (1 << 2)
#define HTTP_SPEED_TEST_ATTR_MASK_EU_PPPOE_NAME                  (1 << 3)
#define HTTP_SPEED_TEST_ATTR_MASK_EU_PASSWORD                    (1 << 4)
#define HTTP_SPEED_TEST_ATTR_MASK_WAN_INTERFACE                  (1 << 5)
#define HTTP_SPEED_TEST_ATTR_MASK_ALL                           ((1 << 6) - 1)
	uint32_t bit_map;
} __PACK__ igd_http_speed_test_attr_conf_tab_t;

/* IGD_RMS_SPEED_TEST_ATTR_TAB */
#define IGD_RMS_SPEED_TEST_ATTR_MAX 1
typedef struct {
	uint32_t state_and_index;
#define RMS_SPEED_TEST_DIAGNOSTICS_STATE_NONE                    (0)
#define RMS_SPEED_TEST_DIAGNOSTICS_STATE_REQUESTED               (1)
#define RMS_SPEED_TEST_DIAGNOSTICS_STATE_COMPLETED               (2)
	uint8_t  diagnostics_state;
#define RMS_SPEED_TEST_TYPE_DOWNLINK                             (0)
#define RMS_SPEED_TEST_TYPE_UPLINK                               (1)
	uint8_t  type;
#define RMS_SPEED_TEST_TEST_MODE_SERVERMODE                      (0)
#define RMS_SPEED_TEST_TEST_MODE_DOWNLOADMODE                    (1)
#define RMS_SPEED_TEST_TEST_MODE_UPLOADMODE                      (2)
	uint8_t  test_mode;
#define RMS_SPEED_TEST_SOURCE_APP                                (0)
#define RMS_SPEED_TEST_SOURCE_PLATFORM                           (1)
	uint8_t  source;
#define RMS_SPEED_TEST_DOWNLOAD_URL_STRING_LEN                   (256)
	uint8_t  download_url[RMS_SPEED_TEST_DOWNLOAD_URL_STRING_LEN];
#define RMS_SPEED_TEST_UPLOAD_URL_STRING_LEN                     (256)
	uint8_t  upload_url[RMS_SPEED_TEST_UPLOAD_URL_STRING_LEN];
#define RMS_SPEED_TEST_TEST_URL_STRING_LEN                       (256)
	uint8_t  test_url[RMS_SPEED_TEST_TEST_URL_STRING_LEN];
#define RMS_SPEED_TEST_REPORT_URL_STRING_LEN                     (256)
	uint8_t  report_url[RMS_SPEED_TEST_REPORT_URL_STRING_LEN];
#define RMS_SPEED_TEST_STATUS_STRING_LEN                         (32)
	uint8_t  status[RMS_SPEED_TEST_STATUS_STRING_LEN];
#define RMS_SPEED_TEST_PPPOE_IP_STRING_LEN                       (128)
	uint8_t  pppoe_ip[RMS_SPEED_TEST_PPPOE_IP_STRING_LEN];
#define RMS_SPEED_TEST_PPPOE_NAME_STRING_LEN                     (128)
	uint8_t  pppoe_name[RMS_SPEED_TEST_PPPOE_NAME_STRING_LEN];
#define RMS_SPEED_TEST_C_SPEED_STRING_LEN                        (32)
	uint8_t  c_speed[RMS_SPEED_TEST_C_SPEED_STRING_LEN];
#define RMS_SPEED_TEST_A_SPEED_STRING_LEN                        (32)
	uint8_t  a_speed[RMS_SPEED_TEST_A_SPEED_STRING_LEN];
#define RMS_SPEED_TEST_B_SPEED_STRING_LEN                        (32)
	uint8_t  b_speed[RMS_SPEED_TEST_B_SPEED_STRING_LEN];
#define RMS_SPEED_TEST_MAX_SPEED_STRING_LEN                      (32)
	uint8_t  max_speed[RMS_SPEED_TEST_MAX_SPEED_STRING_LEN];
#define RMS_SPEED_TEST_START_TIME_STRING_LEN                     (32)
	uint8_t  start_time[RMS_SPEED_TEST_START_TIME_STRING_LEN];
#define RMS_SPEED_TEST_END_TIME_STRING_LEN                       (32)
	uint8_t  end_time[RMS_SPEED_TEST_END_TIME_STRING_LEN];
#define RMS_SPEED_TEST_TOTAL_SIZE_STRING_LEN                     (32)
	uint8_t  total_size[RMS_SPEED_TEST_TOTAL_SIZE_STRING_LEN];
#define RMS_SPEED_TEST_BACKGROUND_SIZE_STRING_LEN                (32)
	uint8_t  background_size[RMS_SPEED_TEST_BACKGROUND_SIZE_STRING_LEN];
#define RMS_SPEED_TEST_FAIL_CODE_STRING_LEN                      (32)
	uint8_t  fail_code[RMS_SPEED_TEST_FAIL_CODE_STRING_LEN];
#define RMS_SPEED_TEST_EU_PPPOE_NAME_STRING_LEN                  (256)
	uint8_t  eu_pppoe_name[RMS_SPEED_TEST_EU_PPPOE_NAME_STRING_LEN];
#define RMS_SPEED_TEST_EU_PASSWORD_STRING_LEN                    (256)
	uint8_t  eu_password[RMS_SPEED_TEST_EU_PASSWORD_STRING_LEN];
#define RMS_SPEED_TEST_WAN_INTERFACE_STRING_LEN                  (256)
	uint8_t  wan_interface[RMS_SPEED_TEST_WAN_INTERFACE_STRING_LEN];
#define RMS_SPEED_TEST_ATTR_MASK_DIAGNOSTICS_STATE               (1 << 0)
#define RMS_SPEED_TEST_ATTR_MASK_TYPE                            (1 << 1)
#define RMS_SPEED_TEST_ATTR_MASK_TEST_MODE                       (1 << 2)
#define RMS_SPEED_TEST_ATTR_MASK_SOURCE                          (1 << 3)
#define RMS_SPEED_TEST_ATTR_MASK_DOWNLOAD_URL                    (1 << 4)
#define RMS_SPEED_TEST_ATTR_MASK_UPLOAD_URL                      (1 << 5)
#define RMS_SPEED_TEST_ATTR_MASK_TEST_URL                        (1 << 6)
#define RMS_SPEED_TEST_ATTR_MASK_REPORT_URL                      (1 << 7)
#define RMS_SPEED_TEST_ATTR_MASK_STATUS                          (1 << 8)
#define RMS_SPEED_TEST_ATTR_MASK_PPPOE_IP                        (1 << 9)
#define RMS_SPEED_TEST_ATTR_MASK_PPPOE_NAME                      (1 << 10)
#define RMS_SPEED_TEST_ATTR_MASK_C_SPEED                         (1 << 11)
#define RMS_SPEED_TEST_ATTR_MASK_A_SPEED                         (1 << 12)
#define RMS_SPEED_TEST_ATTR_MASK_B_SPEED                         (1 << 13)
#define RMS_SPEED_TEST_ATTR_MASK_MAX_SPEED                       (1 << 14)
#define RMS_SPEED_TEST_ATTR_MASK_START_TIME                      (1 << 15)
#define RMS_SPEED_TEST_ATTR_MASK_END_TIME                        (1 << 16)
#define RMS_SPEED_TEST_ATTR_MASK_TOTAL_SIZE                      (1 << 17)
#define RMS_SPEED_TEST_ATTR_MASK_BACKGROUND_SIZE                 (1 << 18)
#define RMS_SPEED_TEST_ATTR_MASK_FAIL_CODE                       (1 << 19)
#define RMS_SPEED_TEST_ATTR_MASK_EU_PPPOE_NAME                   (1 << 20)
#define RMS_SPEED_TEST_ATTR_MASK_EU_PASSWORD                     (1 << 21)
#define RMS_SPEED_TEST_ATTR_MASK_WAN_INTERFACE                   (1 << 22)
#define RMS_SPEED_TEST_ATTR_MASK_ALL                            ((1 << 23) - 1)
	uint32_t bit_map;
} __PACK__ igd_rms_speed_test_attr_conf_tab_t;

typedef struct {
    unsigned int  ulStateAndIndex;
    unsigned char ucState;
    unsigned char ucDSCP;
    unsigned char ucEthernetPriority;
    unsigned char ucPad;
	unsigned char aucInterface[DOWNLOAD_DIAGNOSTIC_INTERFACE_LEN];
	unsigned char aucUploadURL[CM_URL_LEN];
	unsigned int  ulMaxTime;
	unsigned int ulMeasurementOffset;
    unsigned int  ulBitmap;
} __PACK__ igd_pm_upload_diag_entry_t;

#endif
