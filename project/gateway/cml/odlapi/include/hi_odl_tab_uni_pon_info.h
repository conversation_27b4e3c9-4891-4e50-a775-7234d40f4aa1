/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2025. All rights reserved.
 * Description: cm downlink pon info obj attribute
 * Author: HSAN
 * Create: 2024-07-29
 */
#ifndef HI_ODL_TAB_DOWNLINK_PON_INFO_H
#define HI_ODL_TAB_DOWNLINK_PON_INFO_H
#include <stdbool.h>
#include "hi_odl_basic_type.h"

/*********************用户侧PON口信息及配置表***************************/
typedef struct {
	uint32_t state_and_index;
#define PON_CONFIG_ENABLE					(1)
#define PON_CONFIG_DISABLE					(0)
	uint8_t pon_enable;	/*是否启用，缺省值：true*/

#define PON_CONFIG_BINDING_WITH_WAN			(1)
#define PON_CONFIG_BINDING_WITH_PORT 		(0)
	uint8_t binding_mode;/*缺省为：0, 0：通过端口和WAN连接绑定。1：通过VLAN和WAN连接绑定*/

#define PON_CONFIG_STATUS_UP				(1)
#define PON_CONFIG_STATUS_DISABLED 			(0)
	uint8_t pon_status;	/*用户侧PON接口状态：Up Disabled*/
	uint8_t pad;
#define PON_CONFIG_VLAN_MOD_LENGTH 			(8)
	char vlan_mod[PON_CONFIG_VLAN_MOD_LENGTH];	/*在.X_CT-COM_Mode为1时，该参数生效。以“m1/n1,m2/n2”方式设置，其中”m”为接收到的VLAN，“n”为映射到的目标VLAN*/

	uint32_t bit_map;
#define PON_CONFIG_MASK_BIT0_PON_ENABLE 	(0x01)
#define PON_CONFIG_MASK_BIT1_BINDING_MODE 	(0x02)
#define PON_CONFIG_MASK_BIT2_PON_STATUS 	(0x04)
#define PON_CONFIG_MASK_BIT3_VLAN_MOD 		(0x08)
#define PON_CONFIG_MASK_ALL 				(0x0f)
} __PACK__ idg_pon_config_info_tab;

/*********************光模块参数信息***************************/
typedef struct {
	uint32_t state_and_index;
	uint32_t opt_temperature; /*光模块温度，单位：℃*/
	uint32_t opt_current; /*光发送机的偏置电流，单位：mA*/
	uint32_t opt_voltage; /*光模块的供电电压，单位：V*/
	uint32_t opt_tx_power; /*光模块的发射光功率，单位：dBm*/
	uint32_t periodical_opt; /*是否启用分时发光，缺省值：false*/
	uint32_t on_period; /*当光纤拔出后，激光器探测的周期，发光时间，单位s*/
	uint32_t off_period; /*当光纤拔出后，激光器探测的周期，关光时间，单位s*/

	uint32_t bit_map;
#define OPT_MODULE_PARAMETER_MASK_BIT0_PERIODICAL_OPT 	(0x01)
#define OPT_MODULE_PARAMETER_MASK_BIT1_ON_PERIOD 		(0x02)
#define OPT_MODULE_PARAMETER_MASK_BIT2_OFF_PERIOD 		(0x04)
#define OPT_MODULE_PARAMETER_MASK_ALL 					(0x07)
} __PACK__ idg_opt_module_parameter_info_tab;

/*********************注册的从设备信息***************************/
#define CONNECTED_ONT_DEVICE_MAX_NUM 	(8)
typedef struct {
	uint32_t state_and_index;
	uint32_t connected_ont_index;
#define DEVICE_PON_PRODUCTCLASS_LEN 	(64)
    char product_class[DEVICE_PON_PRODUCTCLASS_LEN]; /*从设备型号*/
#define DEVICE_PON_CTEI_NUM_LEN			(16)
	char ctei_num[DEVICE_PON_CTEI_NUM_LEN]; /*从设备的CTEI*/
#define DEVICE_MAC_LEN 					(32)
    char mac[DEVICE_MAC_LEN]; /*从设备的MAC地址，格式为0123456789AB*/
#define DEVICE_PON_MANUFACTURER_LEN 	(64)
    char manufacturer[DEVICE_PON_MANUFACTURER_LEN]; /*从设备制造商*/
#define DEVICE_SN_NUM_LEN 				(32)
    char dev_sn[DEVICE_SN_NUM_LEN]; /*设备序列号（OUI-SN）*/
#define DEVICE_HD_VERSION_LEN 			(32)
    char hd_version[DEVICE_HD_VERSION_LEN]; /*从设备硬件版本号*/
#define DEVICE_SW_VERSION_LEN 			(32)
    char sw_version[DEVICE_SW_VERSION_LEN]; /*从设备软件版本号*/
#define DEVICE_STATUS_ONLINE 			(0) /*“Online”  在线状态*/
#define DEVICE_STATUS_OFFLINE 			(1) /*“Offline”  离线状态*/
#define DEVICE_STATUS_DISABLED 			(2) /*“Disabled”  关闭状态（由于检测到长发光被关闭）*/
	uint8_t status; /*从设备的状态*/
    uint32_t ont_distance; /*从设备测距距离，单位：米*/
#define DEVICE_LAST_DOWN_CAUSE_AUTHFAIL (0) /*AuthFail:  认证失败*/
#define DEVICE_LAST_DOWN_CAUSE_FIBER_BROKEN 	(1) /*FiberBroken：断纤*/
#define DEVICE_LAST_DOWN_CAUSE_DEACTIVE 		(2) /*Deactive:去激活，被主设备踢下线*/
#define DEVICE_LAST_DOWN_CAUSE_DYINGGASP 		(3) /*Dying-gasp：掉电*/
#define DEVICE_LAST_DOWN_CAUSE_DISABLE 			(4) /*Disabled：因异常发光被主设备关闭*/
#define DEVICE_LAST_DOWN_CAUSE_REBOOT 			(5) /*Reboot：正常重启（从主网关发起）*/
#define DEVICE_LAST_DOWN_CAUSE_OTHER 			(6) /*Other：其它*/
    uint8_t last_down_cause; /*从设备最后一次光路下线原因*/
#define DEVICE_TIME_LEN 				(16)
    char last_up_time[DEVICE_TIME_LEN]; /*从设备最后一次上线时间，UTC时间字符串，如2021-04-10 10:30:23*/
    char last_down_time[DEVICE_TIME_LEN]; /*从设备最后一次光路下线时间，UTC时间字符串*/
    char Last_dying_gasp_time[DEVICE_TIME_LEN]; /*从设备最后一次光路掉电时间，UTC时间字符串*/
    uint32_t online_duration; /*从设备在线的累计时长，单位秒*/
    uint32_t tx_power; /*从设备PON口的发射光功率(单位是：0.1微瓦)*/
    uint32_t rx_power; /*从设备PON口的接收光功率(单位是：0.1微瓦)*/
    uint32_t dn_opt_rx_power; /*表示在主设备上查询的接收功率，按照从设备来区分，dBm(单位是：0.1微瓦)*/
#define DEVICE_IPTV_ENABLE				(1)
#define DEVICE_IPTV_DISABLE				(0)
    uint8_t iptv_enable; /*子设备IPTV业务是否启用,缺省：True*/
#define DEVICE_ETH_PORT_LEN 			(8)
    char eth_port[DEVICE_ETH_PORT_LEN]; /*从设备上绑定IPTV业务的LAN口，默认为2*/

	uint32_t bit_map;
#define CONNECTED_ONT_DEVICE_MASK_BIT0_IPTV_ENABLE	(0x01)

} __PACK__ idg_connected_ont_info_tab;

#endif
