/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab timer jobs obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_TIMER_H
#define HI_ODL_TAB_TIMER_H
#include "hi_odl_basic_type.h"

#define IGD_SLEEP_TIMER_MAX_NUM 6
#define IGD_TIMER_TIME_MAX_LENGTH 6
#define IGD_TIMER_CYCLE_MAX_LENGTH 16
/***************************系统Sleep Timer属性表 多实例*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulSleepIndex;
	word8  aucSleepDay[IGD_TIMER_CYCLE_MAX_LENGTH];          /*休眠开机的重复周期，取值0～7。如：1代表星期一、2代表星期二；如果为0，表示立即开启或者关闭休眠功能*/
	word8  aucSleepTime[IGD_TIMER_TIME_MAX_LENGTH];           /*表示休眠开/关的时间*/
	uword8  ucSleepActive;                                    /*表示定时器启用/关闭，0表示关闭，1表示启用*/
	uword8  ucSleepEnable;                                   /*表示休眠定时开关启用/关闭功能，FALSE表示功能关闭，TRUE表示功能开启*/
	uword8  ucSleepReverse;					/*0: 表示要反转状态其他值不做动作，不保存到MIB中 额外分析出此参数用以实现aucSleepDay参数为0 时*/
	uword8  aucPad[3];
#define SLEEP_TIMER_ATTR_CFG_MASK_DAY          (0x01)
#define SLEEP_TIMER_ATTR_CFG_MASK_TIME        (0x02)
#define SLEEP_TIMER_ATTR_CFG_MASK_ACTIVE     (0x04)
#define SLEEP_TIMER_ATTR_CFG_MASK_ENABLE     (0x08)
#define SLEEP_TIMER_ATTR_CFG_MASK_REVERSE (0x10)
#define SLEEP_TIMER_ATTR_CFG_MASK_ALL (SLEEP_TIMER_ATTR_CFG_MASK_DAY | \
                                        SLEEP_TIMER_ATTR_CFG_MASK_TIME | \
										SLEEP_TIMER_ATTR_CFG_MASK_ACTIVE | \
										SLEEP_TIMER_ATTR_CFG_MASK_ENABLE | \
										SLEEP_TIMER_ATTR_CFG_MASK_REVERSE)
	uword32 ulBitmap;
} __PACK__ IgdSleepTimerAttrCfgTab;

typedef struct {
	uword32 ulStateAndIndex;
	uword8  ucSleepState;  /*网关休眠的状态0-未休眠，1-休眠*/
	uword8  aucPad[3];

} __PACK__ IgdSleepTimerStateTab;

/***************************WiFi Timer属性表 单实例*********************************/
typedef struct {
	uword32 ulStateAndIndex;
#define WIFI_TIMER_DISABLE 0
#define WIFI_TIMER_ENABLE  1
	uword8  ucWiftTimerEnable;         /*表示wifi定时总开关*/
	uword8  aucPad[3];
#define WIFI_TIMER_BASIC_MASK_ENABLE 		(0x01)
#define WIFI_TIMER_BASIC_MASK_ALL     		(0x01)
	uword32 ulBitmap;
} __PACK__ IgdWifiTimerBASICConfTab;


#define IGD_WIFI_TIMER_MAX_NUM 32
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulWiFiTimerIndex;
	uword32 ulSSIDIndex;                                   /*IN&OUT  SSID索引*/

	word8 aucStartTime[IGD_TIMER_TIME_MAX_LENGTH];                                 /*WiFiTime: Wifi开机的时间*/
	word8 aucEndTime[IGD_TIMER_TIME_MAX_LENGTH];                                   /*WiFiTime: Wifi关机的时间*/
	word8 aucControlCycle[IGD_TIMER_CYCLE_MAX_LENGTH];                             /*WiFiTime: 表示控制周期: 取值范围为DAY*/
	uword8 ucTimerEnable;                                    /*WiFiTime: 定时开关启用/关闭功能*/
	uword8 aucPad[3];

#define WIFI_TIMER_ATTR_CFG_MASK_SSIDINDEX (0x01)
#define WIFI_TIMER_ATTR_CFG_MASK_STARTTIME       (0x02)
#define WIFI_TIMER_ATTR_CFG_MASK_ENDTIME     (0x04)
#define WIFI_TIMER_ATTR_CFG_MASK_CONTROLCYCLE  (0x08)
#define WIFI_TIMER_ATTR_CFG_MASK_ENABLE (0x10)

	uword32 ulBitmap;
} __PACK__ IgdWiFiTimerAttrCfgTab;

/***************************WiFi Timer1属性表 多实例*********************************/
#define IGD_WIFI_TIMER1_MAX_NUM 6
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulWiFiTimer1Index;
	uword32 ulSSIDIndex;                                   /*IN&OUT  SSID索引*/

	word8 aucWeekDay[IGD_TIMER_CYCLE_MAX_LENGTH];                             /*WiFiTime1: 表示控制周期: 取值1～7。如：1代表星期一、2代表星期二*/
	uword8 ucTimer1Active;                                    /*WiFiTime1: 定时开关启用/关闭功能*/
	word8 aucTimer1Time[IGD_TIMER_TIME_MAX_LENGTH];                                       /*WiFiTime1: Wifi开 关机的时间*/
	uword8 ucTimer1Enable;                                    /*WiFiTime1: 此SSID 启用/关闭功能*/

#define WIFI_TIMER1_ATTR_CFG_MASK_SSIDINDEX (0x01)
#define WIFI_TIMER1_ATTR_CFG_MASK_WEEKDAY       (0x02)
#define WIFI_TIMER1_ATTR_CFG_MASK_ACTIVE (0x04)
#define WIFI_TIMER1_ATTR_CFG_MASK_TIME     (0x08)
#define WIFI_TIMER1_ATTR_CFG_MASK_ENABLE (0x10)

	uword32 ulBitmap;
} __PACK__ IgdWiFiTimer1AttrCfgTab;
word32 igdCmWiFiTimer1CfgAttrGet(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimer1CfgAttrAdd(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimer1CfgAttrSet(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimer1CfgAttrDel(uword8 *pucInfo, uword32 len);
word32 igdCmWiFiTimer1EntryNumGet(uword32 *pulEntryNum);
word32 igdCmWiFiTimer1EntryGetAll(uword8 *pucInfo, uword32 len);

/***************************LED Timer 属性表 单实例*********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulLedIndex;
	word8  aucLedStartTime[IGD_TIMER_TIME_MAX_LENGTH];                           /*表示LED指示灯开机的时间*/
	word8  aucLedEndTime[IGD_TIMER_TIME_MAX_LENGTH];                            /*表示LED指示灯开机的时间*/
	word8  aucLedControlCycle[IGD_TIMER_CYCLE_MAX_LENGTH];                           /*表示控制周期！取值范围为DAY */
	uword8  ucLedEnable;                                      /*表示LED定时开关启用/关闭功能；TRUE表示成功，FALSE表示失败*/
	uword8  aucPad[3];
	word8  aucLedStatus[4];                                      /*设置LED灯状态；取值范围OFF关闭，ON开启 */
#define LED_TIMER_ATTR_CFG_MASK_STARTTIME (0x01)
#define LED_TIMER_ATTR_CFG_MASK_ENDTIME (0x02)
#define LED_TIMER_ATTR_CFG_MASK_CONTROLCYCLE (0x04)
#define LED_TIMER_ATTR_CFG_MASK_ENABLE (0x08)
#define LED_TIMER_ATTR_CFG_MASK_STATUS (0x10)
#define LED_TIMER_ATTR_CFG_MASK_ALL (LED_TIMER_ATTR_CFG_MASK_STARTTIME | \
									LED_TIMER_ATTR_CFG_MASK_ENDTIME | \
									LED_TIMER_ATTR_CFG_MASK_CONTROLCYCLE | \
									LED_TIMER_ATTR_CFG_MASK_ENABLE | \
									LED_TIMER_ATTR_CFG_MASK_STATUS)
	uword32 ulBitmap;
} __PACK__ IgdLedTimerAttrCfgTab;

/***************************Network Timer属性表 单实例*********************************/
#define IGD_NETWORK_TIMER_MAX_NUM 32

typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulTimerIndex;
	uword32 ulSSIDIndex;
	word8 aucStartTime[IGD_TIMER_TIME_MAX_LENGTH];     /* hh:ss */
	word8 aucEndTime[IGD_TIMER_TIME_MAX_LENGTH];       /* hh:ss */
	word8 aucControlCycle[IGD_TIMER_CYCLE_MAX_LENGTH]; /* 1234567 */
	uword8 ucTimerEnable;
	uword8 ucAction;                                   /* 1:reboot 2:wifi */
	uword8 aucPad[2];

#define NETWORK_TIMER_ATTR_CFG_MASK_SSIDINDEX (0x01)
#define NETWORK_TIMER_ATTR_CFG_MASK_STARTTIME (0x02)
#define NETWORK_TIMER_ATTR_CFG_MASK_ENDTIME (0x04)
#define NETWORK_TIMER_ATTR_CFG_MASK_CONTROLCYCLE (0x08)
#define NETWORK_TIMER_ATTR_CFG_MASK_ENABLE (0x10)
#define NETWORK_TIMER_ATTR_CFG_MASK_ACTION (0x20)
#define NETWORK_TIMER_ATTR_CFG_MASK_ALL (0x3F)
	uword32 ulBitmap;
} __PACK__ IgdNetworkTimerAttrCfgTab;

#endif
