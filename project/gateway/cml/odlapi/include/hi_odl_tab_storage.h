/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm storage obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_STORAGE_H
#define HI_ODL_TAB_STORAGE_H
#include "hi_odl_basic_type.h"


/***************************usb设备属性表*********************************/
#define IGD_PM_STORAGE_USB_ATTR_NUM 32
typedef struct {
	uword32 ulStateAndIndex;

#define USB_DEVICEINFO_STR_LEN                      (32)
	word8  aucDeviceName[USB_DEVICEINFO_STR_LEN];      /* OUT  usb设备名称 */
	word8  aucDeviceType[USB_DEVICEINFO_STR_LEN];      /* OUT  usb设备类型 */
	word8  aucDeviceVendor[USB_DEVICEINFO_STR_LEN];    /* OUT  usb设备厂商 */
	word8  aucDeviceModel[USB_DEVICEINFO_STR_LEN];     /* OUT  usb设备型号 */
	word8  aucDeviceID[USB_DEVICEINFO_STR_LEN];        /* OUT  usb设备ID */
#define USB_WRITEPROTECTION_NOTSUPPORT              (0)
#define USB_WRITEPROTECTION_SUPPORT                 (1)
	uword8  ucWriteProtection;                          /* OUT  usb设备是否具有写保护功能 */
	uword8  aucPad1[3];

#define USB_OPTIONS_STR_LEN                         (128)
	uword64 ulTotalSize;                                /* OUT      usb设备总容量 */
	uword64 ulUsedSize;                                 /* OUT      usb设备已占用容量 */
	uword64 ulFreeSize;                                 /* OUT      usb设备空闲容量 */

#define USB_METHOD_DEACTIVE                         (0)
#define USB_METHOD_ACTIVE                           (1)
	uword8  ucMount;                                    /* IN       usb设备挂载操作 */
	uword8  aucPad2[3];
	word8  aucMountOptions[USB_OPTIONS_STR_LEN];       /* IN&OUT   usb设备挂载操作参数: 操作选项 */
	word8  aucMountPoint[USB_OPTIONS_STR_LEN];         /* IN&OUT   usb设备挂载操作参数: 挂载路径 */

	uword8  ucUnmount;                                  /* IN       usb设备取消挂载操作 */
	uword8  aucPad3[3];
	word8  aucUnmountOptions[USB_OPTIONS_STR_LEN];     /* IN&OUT   usb设备取消挂载操作参数: 操作选项 */

	uword8  ucSetlabel;                                 /* IN&OUT   usb设备设置挂载标签操作 */
	uword8  aucPad4[3];
#define USB_DEVICEINFO_LABEL_LEN                    (128)
	word8  aucLabel[USB_DEVICEINFO_LABEL_LEN];           /* IN&OUT   usb设备设置挂载标签操作参数: 挂载标签 */

	uword8  ucFormat;                                   /* IN       usb设备格式化操作 */
	uword8  aucPad5[3];
	word8  aucFormatOptions[USB_OPTIONS_STR_LEN];      /* IN       usb设备格式化操作参数: 操作选项 */

	uword32 ulTextfiles_number;
	uword32 ulTextfiles_size;

	uword32 ulVideos_number;
	uword32 ulVideos_size;

	uword32 ulAudios_number;
	uword32 ulAudios_size;

	uword32 ulPhotos_number;
	uword32 ulPhotos_size;

#define STORAGE_USB_ATTR_MASK_BIT0_MOUNT            (1<<0)
#define STORAGE_USB_ATTR_MASK_BIT1_UNMOUNT          (1<<1)
#define STORAGE_USB_ATTR_MASK_BIT2_SETLABEL         (1<<2)
#define STORAGE_USB_ATTR_MASK_BIT3_FORMAT           (1<<3)
#define STORAGE_USB_ATTR_MASK_ALL                   (0xf)
	uword32 ulBitmap;
} __PACK__ IgdStorageUSBAttrTab;

/***************************usb设备DEVNAME表*********************************/
#define IGD_PM_STORAGE_USB_DEVNAME_MAX 2
typedef struct {
	uword32 ulStateAndIndex;
#define USB_DEVICENAME_STR_LEN                      (32)
	word8  aucDeviceName[USB_DEVICENAME_STR_LEN];      /* OUT  usb设备名称 */
} __PACK__ IgdStorageUSBDevNameTab;


/***************************NASaccess 信息表**************************/
#define NAS_DIRFILE_NAME_STR_LEN                    (32)
#define NAS_DIRFILE_NUM_MAX                         (32)
#define NAS_DIRFILE_FULL_PATH_STR_LEN               (128)

typedef struct {
	uword8  aucFileName[NAS_DIRFILE_NAME_STR_LEN];                /* OUT 子文件(夹)名 */
	uword8  aucPhysicalFolderName[NAS_DIRFILE_FULL_PATH_STR_LEN]; /* OUT 子文件夹物理名(链接源文件夹) */
	uword32 ulIsFolder;                                           /* OUT 是否为文件夹 */
	uword32 ulFileSize;                                           /* OUT 子文件(夹)大小 */
#define NAS_DIRFILE_MODIFYTIME_STR_LEN              (32)
	uword8  aucModifiedTime[NAS_DIRFILE_MODIFYTIME_STR_LEN];      /* OUT 子文件(夹)修改时间 */
} NASSubFileInfo;

typedef struct {
	uword32 ulStateAndIndex;

	word8  aucDirFilePath[NAS_DIRFILE_FULL_PATH_STR_LEN];    /* IN  文件、目录路径 */

#define NAS_METHOD_DEACTIVE                         (0)
#define NAS_METHOD_ACTIVE                           (1)
	uword8  ucGetFileNum;                                     /* IN  获取子文件个数 */
	uword8  aucPad1[3];
	uword32 ulFileNum;                                        /* OUT 子文件个数 */

	uword8  ucListFolder;                                     /* IN  列出所有子文件 */
	uword8  aucPad2[3];
	uword32 ulStartIndex;                                     /* IN  起始位置 */
	uword32 ulEndIndex;                                       /* IN  结束为止 */

	uword8  ucCreateFolder;                                   /* IN  新建文件 */
	uword8  aucPad3[3];

	uword8  ucRename;                                         /* IN  重命名文件(夹) */
	uword8  aucPad4[3];
	word8  aucRNDirFilePath[NAS_DIRFILE_FULL_PATH_STR_LEN];  /* IN  新文件(夹)名 */

	uword8  ucRemove;                                         /* IN  删除文件(夹) */
	uword8  aucPad5[3];

	uword8  ucMove;                                           /* IN  移动文件(夹) */
	uword8  aucPad6[3];
	word8  aucMVDirFilePath[NAS_DIRFILE_FULL_PATH_STR_LEN];  /* IN  目的文件(夹)名 */

	uword8  ucCopy;                                           /* IN  拷贝文件(夹) */
	uword8  aucPad7[3];
	word8  aucCPDirFilePath[NAS_DIRFILE_FULL_PATH_STR_LEN];  /* IN  目的文件(夹)名 */
	uword32 ulTransactionIdOut;                               /* OUT 拷贝操作进程ID */

	uword8  ucGetCopyProgress;                                /* IN  获取拷贝操作进度*/
	uword8  aucPad8[3];
	uword32 ulTransactionIdIn;                                /* IN  拷贝操作进程ID */
	uword32 ulPercent;                                        /* OUT 拷贝操作进度百分比 */

#define STORAGE_NAS_OPERATION_MASK_BIT0_GETFILENUM      (1<<0)
#define STORAGE_NAS_OPERATION_MASK_BIT1_LISTFOLDER      (1<<1)
#define STORAGE_NAS_OPERATION_MASK_BIT2_CREATEFOLDER    (1<<2)
#define STORAGE_NAS_OPERATION_MASK_BIT3_RENAME          (1<<3)
#define STORAGE_NAS_OPERATION_MASK_BIT4_REMOVE          (1<<4)
#define STORAGE_NAS_OPERATION_MASK_BIT5_MOVE            (1<<5)
#define STORAGE_NAS_OPERATION_MASK_BIT6_COPY            (1<<6)
#define STORAGE_NAS_OPERATION_MASK_BIT7_GETCOPYPROGRESS (1<<7)
#define STORAGE_NAS_OPERATION_MASK_ALL                  (0xff)
	uword32 ulBitmap;
} __PACK__ IgdStorageNASOperationTab;

/*****************************Samba用户参数属性表*************************************/
/* IGD_PM_SAMBA_USER_ATTR_TAB */
#define IGD_PM_SAMBA_USER_TAB_RECORD_NUM (16)

typedef struct {
	uword32 ulStateAndIndex;

	uword8 ucIndex;
#define USER_FUNC_SAMBA_WRITE	(0x01)	//写权限
#define USER_FUNC_SAMBA_READONLY	(0x02)	//只读
#define USER_FUNC_SAMBA_BROWSEABLE	(0x04)	//浏览使能
	uword8 ucRights;
	uword8 aucPad[CM_TWO_PADS];

	word8 aucUsername[CM_USERNAME_LEN];
	word8 aucPasword[CM_PASSWORD_LEN];
	uword8 aucPath[CM_URL_LEN];

#define SAMBA_USER_ATTR_MASK_BIT0_RIGHTS (0x01)
#define SAMBA_USER_ATTR_MASK_BIT1_USERNAME (0x02)
#define SAMBA_USER_ATTR_MASK_BIT2_PASSWORD (0x04)
#define SAMBA_USER_ATTR_MASK_BIT3_PATH (0x08)
#define SAMBA_USER_ATTR_MASK_ALL (0x0f)

	uword32 ulBitmap;
} __PACK__ IgdPMSambaUserAttrConfTab;

#endif
