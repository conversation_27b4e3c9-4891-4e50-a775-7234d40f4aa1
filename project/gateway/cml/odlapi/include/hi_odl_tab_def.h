/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm global obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_DEF_H
#define HI_ODL_TAB_DEF_H
#include "igdCmFeatureDef.h"

#define IGD_DEVICE_TAB_START (423)

enum cm_tab_id {
	IGD_TAB_ID_BEGIN = IGD_DEVICE_TAB_START,
	IGD_DEV_CAPABILITY_TAB,
	IGD_DEVINFO_TAB,
	IGD_GLOBAL_ATTR_TAB,
	IGD_DEV_STATUS_INFO_TAB,
	IGD_SYSCMD_CONFIG_TAB,
	IGD_PON_LINK_STATUS_TAB,
	IGD_PON_STATISTICS_TAB,
	IDG_PON_CONFIG_INFO_TAB,
	IDG_OPT_MODULE_PARAMETER_INFO_TAB,
	IDG_CONNECTED_ONT_INFO_TAB,
	IGD_LAN_IP_ADDR_TAB,
	IGD_LAN_IPV4_ATTR_TAB,
	IGD_LAN_IPV6_ATTR_TAB,
	IGD_LAN_ETH_INTERFACE_ATTR_TAB,
	IGD_LAN_ETH_INTERFACE_INFO_TAB,
	IGD_LAN_HOSTS_NAME_ATTR_TAB,
	IGD_LAN_HOSTS_STATE_IFNO_TAB,
	IGD_LAN_DHCP_HOSTS_IFNO_TAB,
	IGD_LAN_ETHERNET_INTERFACE_FILTER_TAB,
	IGD_LAN_DHCP_STATIC_LIST_TAB,
	IGD_LAN_HOSTS_MNG_ATTR_TAB,
	IGD_LAN_BRLAN_IPV6_ADDR_LIST_TAB,
	IGD_LAN_STB_NUMBER_TAB,
	IGD_LAN_HOSTS_DEV_SAMPLE_INFO_TAB,
	IGD_WAN_INFO_TAB,
	IGD_WAN_COMMON_INTERFACE_ATTR_TAB,
	IGD_WAN_CONNECTION_ATTR_TAB,
	IGD_WAN_CONNECTION_STATE_TAB,
	IGD_WAN_CONNECTION_PORTMAPPING_ATTR_TAB,
	IGD_WAN_CONNECTION_DDNS_ATTR_TAB,
	IGD_WAN_CONNECTION_OPTION60_ATTR_TAB,
	IGD_WAN_CONNECTION_OPTION125_ATTR_TAB,
	IGD_WAN_CONNECTION_OPTION16_ATTR_TAB,
	IGD_WAN_CONNECTION_OPTION17_ATTR_TAB,
	IGD_WAN_CONNECTION_STATISTIC_INFO_TAB,
	IGD_WAN_CONNECTION_BIND_ATTR_TAB,
	IGD_WAN_CONNECTION_INDEX_ATTR_TAB,
	IGD_WAN_CONNECTION_PORTMAPPING_INDEX_ATTR_TAB,
	IGD_WAN_CONNECTION_GEPON_LINK_ATTR_TAB,
	IGD_WAN_COMMON_INTERFACE_INFO_TAB,
	IGD_WAN_CONNECTION_DNS_STATUS_TAB,
	IGD_WAN_CONNECTION_DNSTUNNEL_INDEX_ATTR_TAB,
	IGD_WAN_CONNECTION_IPTVDEF_TAB,
	IGD_VOICE_CAPBILITY_TAB,
	IGD_VOICE_CODEC_CAPBILITY_TAB,
	IGD_VOICE_USER_REG_INFO_TAB,
	IGD_VOICE_GENERAL_ATTR_TAB,
	IGD_VOICE_SIP_BASIC_ATTR_TAB,
	IGD_VOICE_SIP_USER_ATTR_TAB,
	IGD_VOICE_H248_BASIC_ATTR_TAB,
	IGD_VOICE_ADVANCED_ATTR_TAB,
	IGD_VOICE_PROCESSING_ATTR_TAB,
	IGD_VOICE_HOTLINE_SERVICE_ATTR_TAB,
	IGD_VOICE_RTP_SERVICE_ATTR_TAB,
	IGD_VOICE_FAX_ATTR_TAB,
	IGD_VOICE_LINE_CODEC_ATTR_TAB,
	IGD_VOICE_LINE_DETECT_TAB,
	IGD_VOICE_SIMULATE_TEST_ATTR_TAB,
	IGD_VOICE_SIMULATE_TEST_STATE_INFO_TAB,
	IGD_VOICE_IAD_TEST_STATE_INFO_TAB,
	IGD_VOICE_PHY_INTERFACE_TEST_ATTR_TAB,
	IGD_VOICE_PHY_INTERFACE_TEST_STATE_INFO_TAB,
	IGD_VOICE_H248_USER_ATTR_TAB,
	IGD_VOICE_LINE_LAST_REG_ERROR_INFO_TAB,
	IGD_VOICE_PHY_INTERFACE_POOR_QUALITY_STATS_TAB,
	IGD_VOICE_PHY_INTERFACE_POOR_QUALITY_LIST_TAB,
	IGD_VOICE_LINE_IMS_ATTR_TAB,
	IGD_VOICE_LINE_STATS_ATTR_TAB,
	IGD_SECUR_URL_FILTER_ATTR_TAB,
	IGD_SECUR_URL_FILTER_LIST_TAB,
	IGD_SECUR_URL_FIREWALL_ATTR_TAB,
	IGD_SECUR_MAC_FILTER_ATTR_TAB,
	IGD_SECUR_MAC_FILTER_LIST_TAB,
	IGD_SECUR_IP_FILTER_ATTR_TAB,
	IGD_SECUR_IP_FILTER_LIST_TAB,
	IGD_SECUR_USER_LIMIT_ATTR_TAB,
	IGD_SECUR_PORTAL_MGT_ATTR_TAB,
	IGD_SECUR_LAN_IP_FILTER_ATTR_TAB,
	IGD_SECUR_WAN_IP_FILTER_LIST_TAB,
	IGD_SECUR_STATIC_ARP_ATTR_TAB,
	IGD_SECUR_STATIC_ARP_LIST_TAB,
	IGD_SECUR_REMOTE_CONTROL_LIST_TAB,
	IGD_APP_ALG_ATTR_TAB,
	IGD_APP_DMZ_ATTR_TAB,
	IGD_APP_UPNP_ATTR_TAB,
	IGD_APP_IGMP_ATTR_TAB,
	IGD_APP_ROUTE_ATTR_TAB,
	IGD_APP_TIME_ATTR_TAB,
	IGD_APP_SERVICE_MANAGE_ATTR_TAB,
	IGD_APP_IGMP_VLAN_INFO_TAB,
	IGD_IP_LAYER3_FORWARDING_INFO_TAB,
	IGD_APP_V4_LAYER3_ROUTE_TAB,
	IGD_APP_V6_LAYER3_ROUTE_TAB,
	IGD_APP_TELNET_ACCOUNT_TAB,
	IGD_APP_VPN_ATTR_TAB,
	IGD_APP_VPN_ATTACH_TAB,
	IGD_VPN_ATTR_TAB,
	IGD_VPN_ATTACH_TAB,
	IGD_APP_FTP_ACCOUNT_TAB,
	IGD_APP_PASSWORD_COMPLEXITY_TAB,
	IGD_SECUR_IPV6_SESSION_WHITELIST_TAB,
	IGD_REMOTEMGT_TR069_ATTR_TAB,
	IGD_REMOTEMGT_TR069_INFO_TAB,
	IGD_REMOTEMGT_MIDDWARE_ATTR_TAB,
	IGD_REMOTEMGT_LOID_REG_ATTR_TAB,
	IGD_REMOTEMGT_ITMS_REG_STATE_TAB,
	IGD_REMOTEMGT_PASSWORD_REG_ATTR_TAB,
	IGD_REMOTEMGT_WEB_ATTR_TAB,
	IGD_REMOTEMGT_DATA_UPLOAD_ATTR_TAB,
	IGD_REMOTEMGT_RESTORE_ATTR_TAB,
	IGD_REMOTEMGT_DATA_UPLOAD_RESULT_TAB,
	IGD_QOS_BASIC_ATTR_TAB,
	IGD_QOS_CLF_ATTR_TAB,
	IGD_QOS_CLF_TYPE_ATTR_TAB,
	IGD_QOS_APP_ATTR_TAB,
	IGD_QOS_QUEUE_ATTR_TAB,
	IGD_DOWNLINK_QOS_BASIC_ATTR_TAB,
	IGD_DOWNLINK_QOS_CLF_ATTR_TAB,
	IGD_DOWNLINK_QOS_CLF_TYPE_ATTR_TAB,
	IGD_DOWNLINK_QOS_QUEUE_ATTR_TAB,
	IGD_DNS_TUNNEL_ATTR_TAB,
	IGD_QOS_FLOW_LIMIT_ATTR_TAB,
	IGD_QOS_CLF_TYPE_INDEX_ATTR_TAB,
	IGD_QOS_ITEM_ATTR_TAB,
	IGD_INTERFACE_SPEED_ATTR_CFG_TAB,
	IGD_SYSMNG_ACCOUNT_ATTR_TAB,
	IGD_SYSMNG_SYSLOG_ATTR_TAB,
	IGD_SYSMNG_ALARM_ATTR_TAB,
	IGD_SYSMNG_ALARM_CONFIG_ATTR_TAB,
	IGD_SYSMNG_MONITOR_ATTR_TAB,
	IGD_SYSMNG_MONITOR_CONFIG_ATTR_TAB,
	IGD_SYSMNG_PING_ATTR_TAB,
	IGD_SYSMNG_PING_CONFIG_ATTR_TAB,
	IGD_SYSMNG_LOOPDETECT_ATTR_TAB,
	IGD_SYSMNG_LOOPDETECT_STATE_TAB,
	IGD_SYSMNG_TRACEROUTE_CONFIG_TAB,
	IGD_SYSMNG_IPPING_DIAGNOSTIC_CONFIG_TAB,
	IGD_SYSMNG_UPGRADE_INFO_TAB,
	IGD_SYSMNG_DBUS_UPGRADE_CONFIG_TAB,
	IGD_SYSMNG_MEMALARM_ATTR_TAB,
	IGD_SYSMNG_OMDIAG_ATTR_TAB,
	IGD_SYSMNG_OMDIAG_RESOURCE_ATTR_TAB,
	IGD_SYSMNG_TEST_MODE_TAB,
	IGD_SYSMNG_OMDIAG_PROCESS_ATTR_TAB,
	IGD_SYSMNG_UPGRADE_FIRMWARE_DATA_TAB,
	IGD_INFORM_REPORT_TAB,
	IGD_MAINTENCE_TAB,
	IGD_LOID_ITMS_REG_STATUS_TAB,
	IGD_PM_HTTP_FTP_DOWNLOAD_TEST_CFG_ATTR_TAB,
	IGD_PM_MONITOR_COLLECTOR_ATTR_TAB,
	IGD_PM_MONITOR_CONFIG_ATTR_TAB,
	IGD_PM_DOWNLOAD_DIAG_ATTR_TAB,
	IGD_PM_DOWNLOAD_DIAG_STATISTICS_ATTR_TAB,
	IGD_PM_FTP_CLIENT_DOWNLOAD_CFG_ATTR_TAB,
	IGD_PM_FTP_CLIENT_DOWNLOAD_STOP_ATTR_TAB,
	IGD_PM_USB_GLOBAL_CFG_ATTR_TAB,
	IGD_PM_USB_CFG_ATTR_TAB,
	IGD_PM_PPPOE_EMULATOR_ATTR_TAB,
	IGD_WIFI_TIMER_BASIC_TAB,
	IGD_PM_SAMBA_USER_ATTR_TAB,
	IGD_IPOE_EMULATOR_ATTR_TAB,
	IGD_WLAN_GLOBAL_CFG_ATTR_TAB,
	IGD_WLAN_GLOBAL_INFO_ATTR_TAB,
	IGD_WLAN_SSID_PKT_STAT_TAB,
	IGD_WLAN_SSID_CFG_ATTR_TAB,
	IGD_WLAN_SSID_ENTRY_TAB,
	IGD_WLAN_MLD_ATTR_TAB,
	IGD_WLAN_AP_STA_INFO_ENTRY_TAB,
	IGD_WLAN_SSID_SHARE_CFG_ATTR_TAB,
	IGD_WLAN_SSID_FILTER_LIST_TAB,
	IGD_WLAN_SSID_BLACK_FILTER_LIST_TAB,
	IGD_SLEEP_TIMER_TAB,
	IGD_WIFI_TIMER_TAB,
	IGD_WIFI_TIMER1_TAB,
	IGD_LED_TIMER_TAB,
	IGD_PLATFORMSRV_ATTR_TAB,
	IGD_WLAN_EASYMESH_ATTR_CFG_TAB,
	IGD_PM_STORAGE_USB_ATTR_TAB,
	IGD_PM_STORAGE_NAS_ATTR_TAB,
	IGD_L2DETECT_ATTR_TAB,
	IGD_EMU_MC_DETECTIONS_ATTR_TAB,
	IGD_EMU_IPTV_DETECTIONS_ATTR_TAB,
	IGD_SECUR_INCOMING_FILTER_TAB,
	IGD_APP_VPN_STATE_TAB,
	IGD_VPN_STATE_TAB,
	IGD_EMU_TRACERT_ATTR_TAB,
	IGD_EMU_IPPING_ATTR_TAB,
	IGD_EMU_TCP_PING_ATTR_TAB,
	IGD_PM_UPLOAD_DIAG_ATTR_TAB,
	IGD_IPOE_EMULATOR_STATICS_TAB,
	IGD_EMU_IPPING_STATICS_TAB,
	IGD_EMU_TRACERT_INFO_TAB,
	IGD_EMU_TRACERT_HOP_TAB,
	IGD_DPI_UPLOAD_ATTR_CFG_TAB,
	IGD_DPI_BASIC_ATTR_CFG_TAB,
	IGD_DPI_DNS_QUERY_ATTR_CFG_TAB,
	IGD_DPI_HTTP_GET_ATTR_CFG_TAB,
	IGD_DPI_TCP_CONNECT_ATTR_CFG_TAB,
	IGD_PM_UPLOAD_DIAG_STATISTICS_ATTR_TAB,
	IGD_DPI_APP_ATTR_CFG_TAB,
	IGD_PM_DOWNLOAD_PPP_DIAG_ATTR_TAB,
	IGD_DPI_CLASSIFICATION_ATTR_CFG_TAB,
	IGD_NET_MONITOR_ATTR_TAB,
	IGD_APP_PORT_MAC_TAB,
	IGD_TRANSFERSERVICES_MONITOR_ATTR_TAB,
	IGD_NET_MONITOR_BRIDGE_DATA_TAB,
	IGD_NET_MONITOR_BRIDGE_DATA_ACL_TAB,
	IGD_TRANSFERSERVICES_FORWARD_ATTR_TAB,
	IGD_TRANSFERSERVICES_MIRROR_ATTR_TAB,
	IGD_BUNDLE_NOTIFY_ATTR_TAB,
	IGD_PM_USB_SERIAL_HANDLE_TAB,
	IGD_PM_USB_SERIAL_DATA_TAB,
	IGD_PM_USB_SERIAL_ATTR_TAB,
	IGD_WLAN_AP_NEIGHBOR_INFO_ENTRY_TAB,
	IGD_WLAN_RESTORE_TAB,
	IGD_WLAN_ASSOCIATE_SSID_CONF_TAB,
	IGD_SWM_GLOBLE_ATTR_CFG_TAB,
	IGD_SWM_EXECENV_ATTR_CFG_TAB,
	IGD_SWM_DEPLOYMENTUNIT_ATTR_CFG_TAB,
	IGD_SWM_EXECUTIONUNIT_ATTR_CFG_TAB,
	IGD_SWM_DUPERMISSION_ATTR_CFG_TAB,
	IGD_SWM_APICAPABILITES_ATTR_CFG_TAB,
	IGD_SWM_DUPERMISSION_API_CFG_TAB,
	IGD_DBUS_INCOMING_FILTER_ATTR_TAB,
	IGD_TRAFFIC_DETAIL_PROCESS_SERVICE_ATTR_TAB,
	IGD_TRAFFIC_QOS_FLOW_0_INFO_TAB,
	IGD_TRAFFIC_QOS_FLOW_1_INFO_TAB,
	IGD_TRAFFIC_QOS_FLOW_2_INFO_TAB,
	IGD_TRAFFIC_QOS_FLOW_3_INFO_TAB,
	IGD_TRAFFIC_QOS_FLOW_4_INFO_TAB,
	IGD_TRAFFIC_QOS_FLOW_5_INFO_TAB,
	IGD_TRAFFIC_QOS_FLOW_6_INFO_TAB,
	IGD_TRAFFIC_QOS_FLOW_7_INFO_TAB,
	IGD_TRAFFIC_QOS_FLOW_STATS_ATTR_TAB,
	IGD_TRAFFIC_QOS_FLOW_ATTR_TAB,
	IGD_FACTORY_ATTR_TAB,
	IGD_OMCI_ATTR_TAB,
	IGD_VOICE_LINE_RTP_STATS_ATTR_TAB,
	IGD_VOICE_IAD_OPERATION_ATTR_TAB,
	IGD_VOICE_LINE_CALL_STATS_ATTR_TAB,
	IGD_VOICE_IAD_INFO_TAB,
	IGD_VOICE_SIP_USAGE_STATS_ATTR_TAB,
	IGD_VOICE_RECENT_CALL_STATS_TAB,
	IGD_WLAN_GLOBAL_CFG_ATTR_TR069_TAB,
	IGD_WLAN_ASSOCIATE_SSID_START_TAB,
	IGD_PARENTAL_CTRIL_MAC_TAB,
	IGD_PARENTAL_CTRIL_TEMPLATES_TAB,
	IGD_PARENTAL_CTRIL_DURATION_TAB,
	IGD_PARENTAL_CTRIL_URL_FILTER_TAB,
	IGD_SECURE_PROTO_CAR_TAB,
	IGD_DBUS_DNS_SPEED_LIMIT_ATTR_TAB,
	IGD_DBUS_DNS_SERVICE_ATTR_TAB,
	IGD_SECUR_URL_FILTER_DBUS_LIST_TAB,
	IGD_PARENTAL_CTRL_BASIC_TAB,
	IGD_DBUS_DIAG_SPEEDTEST_HTTPDOWNLOAD_ATTR_TAB,
	IGD_DBUS_DIAG_SPEEDTEST_FF_ATTR_TAB,
	IGD_DBUS_DIAG_SPEEDTEST_LOCAL_ATTR_TAB,
	IGD_DBUS_PM_SPEEDTESTFF_ATTR_TAB,
	IGD_WLAN_BEACONTXVSIE_MNG_TAB,
	IGD_WLAN_BEACONTXVSIE_ATTR_TAB,
	IGD_WLAN_PROBERXVSIE_MNG_TAB,
	IGD_WLAN_PROBERXVSIE_ATTR_TAB,
	IGD_WLAN_PROBERXVSIE_REC_MNG_TAB,
	IGD_WLAN_PROBERXVSIE_REC_TAB,
	IGD_WLAN_ROAMING_ATTR_CFG_TAB,
	IGD_WLAN_BANDSTEERING_ATTR_CFG_TAB,
	IGD_WLAN_PROBERSPVSIE_ATTR_TAB,
	IGD_SLEEP_TIMER_STATE_TAB,
	IGD_PM_STORAGE_USB_DEVNAME_TAB,
	IGD_DNS_FILTER_CONFIG_TAB,
	IGD_UPLINK_RATE_INFO_TAB,
	IGD_UPLINK_CFG_TAB,
	IGD_AWIFI_PPPOE_DOMAIN_ATTR_TAB,
	IGD_AWIFI_DEFAULT_SERVER_TAB,
	IGD_AWIFI_CUSTOM_SITE_SERVER_TAB,
	IGD_AWIFI_AUTO_UPGRADE_CONF_TAB,
	IGD_AWIFI_FIRMWARE_INFO_TAB,
	IGD_AWIFI_LAN_AUTH_CONF_TAB,
	IGD_VXLAN_VNI_ATTR_TAB,
	IGD_EM_CTRL_BASIC_CFG_TAB,
	IGD_EM_CTRL_STEERING_POLICY_TAB,
	IGD_EM_CTRL_MANDATE_STEERING_TAB,
	IGD_EM_CTRL_TOPOLOGY_QUERY_LIST,
	IGD_EM_CTRL_TOPOLOGY_QUERY_DEV,
	IGD_EM_CTRL_TOPOLOGY_QUERY_CLIENT,
	IGD_EM_CTRL_CHANNEL_SELECT_TAB,
	IGD_EM_CTRL_MAC_FILTER_SYNC_TAB,
	IGD_EM_CTRL_TRIGGER_WPS_EVENT,
	IGD_EM_CTRL_UNBIND_AGENT,
	IGD_LACP_DEV_CFG_TAB,
	IGD_PACKETCAPTURE_ATTR_TAB,
	IGD_PORTMIIROR_ATTR_TAB,
	IGD_NETWORK_TIMER_TAB,
	IGD_WLAN_5GPREFER_CFG_TAB,
	IGD_WLAN_CONNECT_AP_RESULT_TAB,
	IGD_WLAN_STA_WPS_PROC_TAB,
	IGD_EXIT_EASYMESH_NET,
	IGD_HTTP_SPEED_TEST_ATTR_TAB,
	IGD_RMS_SPEED_TEST_ATTR_TAB,

	IGD_IGMP_DEVICE_AUTHENTICATION_ATTR_TAB,

	IGD_X_CU_EXTEND_ATTR_TAB,
	IGD_RESPONSE_MAP_ATTR_TAB,
	IGD_X_CU_LINE_TEST_ATTR_TAB,
	IGD_LAN_DHCP_COND_SERVING_POOL_ATTR_CFG_TAB,
	IGD_WLANCFG_WPS_ATTR_CFG_TAB,
	IGD_WLANCFG_WPS_REGISTRAR_ATTR_CFG_TAB,
	IGD_WLANCFG_AP_WMM_PARAMETER_ATTR_CFG_TAB,
	IGD_WLANCFG_STA_WMM_PARAMETER_ATTR_CFG_TAB,

	IGD_DSCP_APP_ROUTE_CFG_TAB,
	IGD_APP_FILTER_CFG_TAB,
	IGD_HARD_ACC_CANCEL_MGR_CFG_TAB,
	IGD_HARD_ACC_CANCEL_CFG_TAB,
	IGD_VSIE_PROBE_RSP_TX_VSIE_CFG_TAB,
	IGD_VSIE_PROBE_RSP_TX_VSIE_MGR_CFG_TAB,

	IGD_WAN_PORT_TRIGGER_ATTR_CFG_TAB,
	IGD_USER_MANAGEMENT_ATTR_CFG_TAB,
	IGD_PM_FTP_SERVER_ATTR_CFG_TAB,

	IGD_TRANSFER_AUTO_DIAG_REDIRECT_ATTR_CFG_TAB,
	IGD_TRANSFER_AUTO_DIAG_MONITOR_ATTR_CFG_TAB,
	IGD_TRANSFER_FAST_PATH_SPEED_UP_ATTR_CFG_TAB,
	IGD_TRANSFER_L2TP_VPN_ATTR_CFG_TAB,
	IGD_FIREWALL_TRAFFIC_CONTROL_ATTR_CFG_TAB,
	IGD_FIREWALL_NETWORK_ACCELERATION_ATTR_CFG_TAB,
	IGD_SYSMNG_ALARM_INFO_ATTR_CFG_TAB,
	IGD_FIREWALL_IPSET_CONFIG_ATTR_CFG_TAB,
	IGD_FIREWALL_IPSET_ENTRY_ATTR_CFG_TAB,
	IGD_STORAGE_BLACK_LIST_TAB,
	IGD_WMM_CONFIGURATION_ATTR_CFG_TAB,
	IGD_WMM_SERVICE_IPSET_ATTR_CFG_TAB,

	IGD_H248_INTERFACE_STATE_ATTR_TAB,
	IGD_SPECIAL_SERVICE_VR_ATTR_CFG_TAB,
	IGD_CLOUDVR_ATTR_CFG_TAB,

	IGD_NEIGHBOR_DETECTION_CONFIG_TAB,
	IGD_EM_AGENT_SEND_VENDOR_SPEC_MSG_TO_CTRL,

	IGD_EM_AGENT_CTC_INFO,
	IGD_EM_AGENT_CONFIG_STATE,
	IGD_EM_AGENT_DEV_INFO,
	IGD_EM_AGENT_CLIENT_INFO,

	IGD_WLAN_ROAMING_OPTIMIZATION_ATTR_TAB,
	IGD_IPTV_FLOW_TAB,
	IGD_TAB_ID_MAX,
};

#define IGD_DEVICE_TAB_END (IGD_TAB_ID_MAX)
#define IGD_DEVICE_TAB_NUM_MAX (IGD_DEVICE_TAB_END - IGD_DEVICE_TAB_START + 1)

#endif
