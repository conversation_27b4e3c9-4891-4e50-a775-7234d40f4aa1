/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm emu mc object attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_EMU_MC_H
#define HI_ODL_TAB_EMU_MC_H
#include "hi_odl_basic_type.h"

/*****************************拉流测试属性表*************************************/
typedef struct {
	uword32 ulStateAndIndex;

	word8 aucState[CM_MC_LEN];
	word8 aucInterface[CM_MC_LEN];
	word8 aucMulticastIp[CM_IP_ADDR_STRING_LEN];
	word8 aucUserMAC[CM_MAC_CHAR_LEN];
	word8 aucUserIP[CM_IP_ADDR_STRING_LEN];
	word32 ulResult;
	word32 ulStreamRate;
	word32 ulResponseTime;

#define MC_DETECTIONS_ATTR_MASK_BIT0_STATE (0x01)
#define MC_DETECTIONS_ATTR_MASK_BIT1_INTERFACE (0x02)
#define MC_DETECTIONS_ATTR_MASK_BIT2_MULTICASTIP (0x04)
#define MC_DETECTIONS_ATTR_MASK_BIT3_USERMAC (0x8)
#define MC_DETECTIONS_ATTR_MASK_BIT4_USERIP (0x10)
#define MC_DETECTIONS_ATTR_MASK_ALL (0x1f)
	uword32 ulBitmap;
} __PACK__ IgdEmuMcDetectionsAttrConfTab;


/*****************************IPTV侦测属性表*************************************/
typedef struct {
	uword32 ulStateAndIndex;

	word32 ulState;
	word32 ulDuration;

#define ITV_DETECTIONS_ATTR_MASK_BIT0_STATE (0x01)
#define ITV_DETECTIONS_ATTR_MASK_BIT1_DURATION (0x02)
#define ITV_DETECTIONS_ATTR_MASK_ALL (0x3)
	uword32 ulBitmap;
} __PACK__ IgdEmuIptvDetectionsAttrConfTab;

#endif
