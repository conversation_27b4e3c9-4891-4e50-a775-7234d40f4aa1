/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm extern device obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_EXT_DEVICE_H
#define HI_ODL_TAB_EXT_DEVICE_H
#include "hi_odl_basic_type.h"

/***************************USB 全局配置属性表*********************************/
typedef struct {
	uword32 ulStateAndIndex;

#define USB_RAPID_RECOVERY_DISABLE (0)
#define USB_RAPID_RECOVERY_ENABLE (1)
	uword8 ucRapidRecoveryEnable; 	/*USB快速恢复*/
#define PM_USB_BACKUP (0)
#define PM_USB_RECOVERY (1)
	uword8 ucBackupConfig;			/*备份配置*/
#define USB_SHARE_DISABLE (0)           //0：表示服务关闭
#define USB_SHARE_LOCAL_ENABLE (1)      //1：表示本地开启，远程关闭
#define USB_SHARE_REMOTE_ENABLE (2)     //2：表示本地关闭，远程开启
#define USB_SHARE_ENABLE (3)            //3：表示本地、远程开启
	uword8 ucUsbShare;			/*USB共享功能*/
#define USB_PRINT_SHARE_DISABLE (0)
#define USB_PRINT_SHARE_ENABLE (1)
	uword8 ucUsbPrintShare;				/*USB打印共享*/
	uword8 aucpartselect[CM_URL_LEN] ;		/*USB分区选择*/
#define USB_PRINTER_INFO_LEN (256)
	uword8 aucPrinterInfo[USB_PRINTER_INFO_LEN];	/*显示打印机信息*/

#define PM_SAMBA_ANYMOUS_DISABLE (0)
#define PM_SAMBA_ANYMOUS_ENABLE (1)
	uword8 ucAnymous;//samba使能1为匿名，0为非匿名
	uword8 aucPad[CM_THREE_PADS];
#define PM_USB_GLOBAL_CFG_ATTR_MASK_BIT0_RAPIDRECOVERYENABLE (0x01)
#define PM_USB_GLOBAL_CFG_ATTR_MASK_BIT1_BACKUPCONFIG (0x02)
#define PM_USB_GLOBAL_CFG_ATTR_MASK_BIT2_SBSHARE (0x04)
#define PM_USB_GLOBAL_CFG_ATTR_MASK_BIT3_SBPRINTSHARE (0x08)
#define PM_USB_GLOBAL_CFG_ATTR_MASK_BIT4_ARTSELECT (0x10)
#define PM_USB_GLOBAL_CFG_ATTR_MASK_BIT5_RINTERINFO (0x20)
#define PM_USB_GLOBAL_CFG_ATTR_MASK_BIT6_ANYMOUS (0x40)
#define PM_USB_GLOBAL_CFG_ATTR_MASK_ALL (0x7f)

	uword32 ulBitmap;
} __PACK__ IgdPMUsbGlobalCfgAttrTab;


/***************************接入USB配置信息属性表*********************************/
/*表相关宏*/

typedef struct {
	uword32 ulStateAndIndex;
	uword8 ucUsbIndex;		/*根据索引对应底层指定挂载的USB口*/
#define PM_USB_DISABLE (0)
#define PM_USB_ENABLE (1)
	uword8 ucUsbEnable;	/*对存在的多个USB设备进行启用/禁用操作*/
#define USB_INFO_CONNECT_STATUS_NONE (0)  /*未使用*/
#define USB_INFO_CONNECT_STATUS_PRINTER (1)  /*连接打印机*/
#define USB_INFO_CONNECT_STATUS_3G_CARD (2)  /*连接3G上网卡*/
#define USB_INFO_CONNECT_STATUS_HARDDISK (3)  /*连接存储设备*/
#define USB_INFO_CONNECT_STATUS_UNKOWN (4)  /*其他设备*/
	uword8 ucPortStatus;	/*各个USB口的链接状态*/
	uword8 ucpad;
#define USB_INFO_ATTR_MASK_BIT0_USBENABLE (0x01)
#define USB_INFO_ATTR_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdPMUsbCfgAttrTab;

/***************************USB 信息表**************************************************/
/***************************USB 设备消息表*******************************************/
#define   BUNDLE_NUM_MAX (10)
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulBundleIndex ;
#define   BUNDLE_CONTEXT_LEN (128)
	word8  aucBundleContext[BUNDLE_CONTEXT_LEN];		/*调用此接口的插件上下文*/
#define USB_TO_SERIAL_TYPE 0x1                                        /*USB转串口设备*/
#define USB_VIRT_SERIAL_TPYE 0x2                                     /*USB 虚拟串口设备*/
#define USB_HID_TYPE 0x4                                                   /*USB HID设备*/
#define USB_STORAGE_TYPE 0x8                                           /*USB 存储设备*/
#define USB_INTERNAL_SERIAL 0x10                                      /*内置串口设备*/
	uword32 ulusbdevType ; /*感兴趣的的USB设备类型*/
#define BUNDLE_VOIP_LIST_LEN (256)
	word8   aucVoiceEventList[BUNDLE_VOIP_LIST_LEN];
	uword32   aulTrafMonitorIndex[32];
	uword32   aulTrafDetailIndex[8];

#define BUNDLE_NOTIFY_ATTR_MASK_BIT0_CONTEXT (0x01)
#define BUNDLE_NOTIFY_ATTR_MASK_BIT1_USBTYPE (0x02)
#define BUNDLE_NOTIFY_ATTR_MASK_BIT2_VOIPLIST (0x04)
#define BUNDLE_NOTIFY_ATTR_MASK_BIT3_MONITOR (0x08)
#define BUNDLE_NOTIFY_ATTR_MASK_BIT4_DETAIL (0x10)
	uword32 ulBitmap;
} __PACK__ IgdBundleNotifyCfgAttrTab;


/***************************USB 串口设备HANDLE*******************************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulUSBindex;		/*USB设备标识*/
#define USB_UNLOCK (0)
#define USB_LOCK     (1)
	uword32 ulUSBlockState ;    /*设备是否锁定，获取的时候需要从适配层获取*/
	uword32 ulOpenDev ;          /*为1的时候，打开设备，返回ulUSBOperHandle*/
	uword32 ulUSBOperHandle;             /*设备操作句柄*/
	uword32 ulCloseDev ; /*关闭USB设备，需要输入ulUSBOperHandle*/
	uword32 ulDataAvailable;    /*是否有数据可以读取*/
#define USB_SERIAL_HANDLE_MASK_BIT0_LOCKSTATE (0x01)
#define USB_SERIAL_HANDLE_MASK_BIT1_OPEN (0x02)
#define USB_SERIAL_HANDLE_MASK_BIT2_OPERHANDLE (0x04)
#define USB_SERIAL_HANDLE_MASK_BIT3_CLOSE (0x08)
#define USB_SERIAL_HANDLE_MASK_BIT4_DATAAVA (0x10)
	uword32 ulBitmap;
} __PACK__ IgdPMUsbSerialHandleCfgAttrTab;

/***************************USB 串口设备数据操作****************************************/
#define  USB_DATA_BUFF_LEN (1024)
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulUSBOperHandle;		 /*设备操作句柄*/
	uword8   aucBuf[USB_DATA_BUFF_LEN] ;    /*存放数据的缓冲区*/
	uword32 ulOffset ;          /*缓冲区偏移量*/
	uword32 ulCount;           /*需要写或者写入长度*/
	uword32 ulTimeout
	; /*等待数据可用的超时时间，单位为秒，0表示不等待，-1表示一直等待*/
	uword32 ulSetType ; /*0: read 1: write*/
	uword32 ulreadState
	; /*read的状态  1:表示read成功，主要是对应ultimeout上层判断状态进行数据read*/
#define USB_SERIAL_DATA_MASK_BIT0_BUF (0x01)
#define USB_SERIAL_DATA_MASK_BIT1_OFFSET (0x02)
#define USB_SERIAL_DATA_MASK_BIT2_COUNT (0x04)
#define USB_SERIAL_DATA_MASK_BIT3_TIMEOUT (0x08)
	uword32 ulBitmap;
} __PACK__ IgdPMUsbSerialDataCfgAttrTab;

/***************************USB 串口属性****************************************/

#define  USB_DATA_BUFF_LEN (1024)
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulUSBOperHandle;		 /*设备操作句柄*/
	uword32 ulBandRate ;          /*波特率*/
	uword32 ulParity;           /*奇偶校验 0表示不开启,1表示偶校验,2表示奇校验,3表示校验位始终为1,4表示校验位始终为0*/
	uword32 ulDataBits ;             /* 数据位*/
	uword32 ulStopBits ;             /* 停止位*/
	uword32 ulhwFlowCtrl ;             /*是否开启硬件流控 */
	uword32 ulswFlowCtrl ;             /*是否开启软件流控 */
#define USB_SERIAL_ATTR_MASK_BIT0_RATE (0x01)
#define USB_SERIAL_ATTR_MASK_BIT1_PARITY (0x02)
#define USB_SERIAL_ATTR_MASK_BIT2_DATABIT (0x04)
#define USB_SERIAL_ATTR_MASK_BIT3_STOPBIT (0x08)
#define USB_SERIAL_ATTR_MASK_BIT4_HWCTRL (0x10)
#define USB_SERIAL_ATTR_MASK_BIT5_SWCTRL (0x20)
	uword32 ulBitmap;
} __PACK__ IgdPMUsbSerialAttrCfgTab;

#endif
