/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab uplink obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_UPLINK_H
#define HI_ODL_TAB_UPLINK_H
#include "hi_odl_basic_type.h"

#define LANHOST_DEV_INIT_CFG_STATEANDINDEX		(0xFFFFFFFF)

typedef struct {
	uword32 ulStateAndIndex;

	uint64_t totalBytesSent;
	uint64_t totalBytesReceived;
	uword32 totalPacketsSent;
	uword32 totalPacketsReceived;
	uword32 errorsSent;
	uword32 errorsReceived;
	uword32 discardPacketsSent;
	uword32 discardPacketsReceived;
	uint64_t rx_bc_bytes;
	uword32 rxRateRT;
	uword32 txRateRT;
	uword32 maxRxRate;
	uword32 maxTxRate;
	uword32 averRxRate;
	uword32 averTxRate;
	uword32 reportInterval;
	uword32 sampleInterval;

} __PACK__ IgdUplinkInfoStats;

/* IGD_UPLINK_CFG_TAB */
enum igd_cm_eth_link_id {
	CM_LINK_NONE,
	CM_LINK_ETH1 = 1,
	CM_LINK_ETH2,
	CM_LINK_ETH3,
	CM_LINK_ETH4,
	CM_LINK_ETH5,
	CM_LINK_ETH6,
	CM_LINK_ETH7,
	CM_LINK_ETH8,
	CM_LINK_PON,
	CM_LINK_MAX,
};

enum igd_cm_link_type {
	IGD_CM_UPLINK_2G4,
	IGD_CM_UPLINK_5G,
	IGD_CM_UPLINK_MLD,
	IGD_CM_UPLINK_ETHERNET,
};

typedef struct {
	uint32_t state_and_index;
#define IGD_UPLINK_CFG_DISABLE 0
#define IGD_UPLINK_CFG_STATIC 1
#define IGD_UPLINK_CFG_AUTO_DETECT 2
	uint8_t cfg_mode;
	uint8_t cfg_linkid;

	uint8_t detect_en; /* read only */
	uint8_t detected_linkid; /* read only */

	uint8_t current_uplinkid; /* read only */
	uint8_t current_uplink_type; /* read only */
	uint8_t pad[0x2];
#define UPLINK_CFG_BIT_MODE  (0x1 << 0)
#define UPLINK_CFG_BIT_PORTID (0x1 << 1)
#define UPLINK_CFG_BIT_DETECT_EN (0x1 << 2)
#define UPLINK_CFG_BIT_DETECT_PORTID (0x1 << 3)
#define UPLINK_CFG_BIT_CUR_UPLINKID (0x1 << 4)
#define UPLINK_CFG_BIT_CUR_UPLINKTYPE (0x1 << 5)
	uint32_t bit_map;
} __PACK__ igd_uplink_cfg;

#endif
