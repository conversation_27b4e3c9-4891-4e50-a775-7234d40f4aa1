/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm packet capture obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_PACKET_CAPTURE_H
#define HI_ODL_TAB_PACKET_CAPTURE_H
#include "hi_odl_basic_type.h"

typedef struct {
	uword32						ulStateAndIndex;
	word8						ucSwitch; /*是否开启测试*/
#define NET_MONITOR_TYPE_PING (0)
	word8						ucType;
	word8                                       aucPad[2];
	word8						acWanIf[256]; /*wan接口*/
	word8						acHost[128]; /*主机地址或域名*/
	uword32						ulBlkSize; /*测试包大小*/
	uword32						ulTimerinterval; /*测试周期*/
	uword32						ulPingCnt; /*测试次数*/
	uword32						ulStartTimehour; /*开始时间: 小时*/
	uword32						ulStartTimeMin; /*开始时间:分钟*/
	uword32						ulEndTimehour; /*结束时间: 小时*/
	uword32						ulEndTimeMin; /*结束时间:分钟*/
	uword32						ulResv1;
	uword32						ulResv2;
#define NET_MONITOR_ATTR_MASK_BIT0_SWITCH				(0x01)
#define NET_MONITOR_ATTR_MASK_BIT1_TYPE					(0x02)
#define NET_MONITOR_ATTR_MASK_BIT2_WANIF				(0x04)
#define NET_MONITOR_ATTR_MASK_BIT3_HOST				(0x08)
#define NET_MONITOR_ATTR_MASK_BIT4_BLKSIZE				(0x10)
#define NET_MONITOR_ATTR_MASK_BIT5_INTERVAL			(0x20)
#define NET_MONITOR_ATTR_MASK_BIT6_CNT					(0x40)
#define NET_MONITOR_ATTR_MASK_BIT7_STARTH				(0x80)
#define NET_MONITOR_ATTR_MASK_BIT8_STARTM				(0x100)
#define NET_MONITOR_ATTR_MASK_BIT9_ENDH				(0x200)
#define NET_MONITOR_ATTR_MASK_BIT10_ENDM				(0x400)
#define EMU_PING_ATTR_MASK_ALL							(0x7ff)
	uword32						ulBitmap;
}  IgdCmNetMonitorConfTab;

/* IGD_NET_MONITOR_BRIDGE_DATA_TAB */
#define IGD_NET_MONITOR_BRIDGE_DATA_MAX 1
#define BRIDGE_DATA_INTERFACE_NAME_LEN 32
#define BRIDGE_DATA_MAC_LEN 16
#define BRIDGE_DATA_IP_LEN 16
#define BRIDGE_DATA_DNS_LEN 16
#define BRIDGE_DATA_PORT_LEN 12
#define BRIDGE_DATA_PORT_MIN 1
#define BRIDGE_DATA_PORT_MAX 65535


#define BRIDGE_DATA_PROTOCOL_LEN 12
#define BRIDGE_DATA_PROTOCOL_ALL  "ALL"
#define BRIDGE_DATA_PROTOCOL_TCP  "TCP"
#define BRIDGE_DATA_PROTOCOL_UDP  "UDP"
#define BRIDGE_DATA_PROTOCOL_ICMP "ICMP"
#define BRIDGE_DATA_PROTOCOL_TCP_VALUE  0x6
#define BRIDGE_DATA_PROTOCOL_UDP_VALUE  0x11
#define BRIDGE_DATA_PROTOCOL_ICMP_VALUE 0x1
#define BRIDGE_DATA_PROTOCOL_ALL_VALUE  0xFF

typedef struct {
	uword32 ulStateAndIndex;

	uword8 aucInterfaceName[BRIDGE_DATA_INTERFACE_NAME_LEN];
	uword8 aucSMAC[BRIDGE_DATA_MAC_LEN];
	uword8 aucDMAC[BRIDGE_DATA_MAC_LEN];
	uword32 ulSessionId;
	uword8 aucIP[BRIDGE_DATA_IP_LEN];
	uword8 aucDNS[BRIDGE_DATA_DNS_LEN];

	uword32 ulBitmap;
} __PACK__ IgdNetMonitorBridgeDataTab;


/* IGD_NET_MONITOR_BRIDGE_DATA_ACL_TAB */
#define IGD_NET_MONITOR_BRIDGE_DATA_ACL_RECORD_NUM 16
typedef struct {
	uword32 ulStateAndIndex;

	uword32 ulIndex;
	uword8 aucInterfaceName[BRIDGE_DATA_INTERFACE_NAME_LEN];
	uword8 aucIP[BRIDGE_DATA_IP_LEN];
	uword8 aucPort[BRIDGE_DATA_PORT_LEN];
	uword8 aucProtocol[BRIDGE_DATA_PROTOCOL_LEN];

#define BRIDGE_DATA_MASK_BIT0_IP  (0x01)
#define BRIDGE_DATA_MASK_BIT1_PORT  (0x02)
#define BRIDGE_DATA_MASK_BIT2_PROTOCOL  (0x04)

#define BRIDGE_DATA_MASK_MASK_ALL  (0x7)
	uword32 ulBitmap;

} __PACK__ IgdNetMonitorBridgeDataAclTab;


#endif
