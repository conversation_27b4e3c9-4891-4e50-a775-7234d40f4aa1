/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab parent ctrl obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_PARENT_CTRL_H
#define HI_ODL_TAB_PARENT_CTRL_H
#include "hi_odl_basic_type.h"


/***************************家长控制儿童设备MAC地址列表*********************************/
typedef struct {
	uword32 ulStateAndIndex;
#define PARENTAL_CONTROL_DISABLE 0
#define PARENTAL_CONTROL_ENABLE  1
	uword8  ucParentalCtrlEnable;         /*表示家长控制总开关*/
	uword8  aucPad[3];

#define PARENTAL_CONTROL_BASIC_MASK_PARENTALCONTROL 		(0x01)
#define PARENTAL_CONTROL_BASIC_MASK_ALL 		    		(0x01)
	uword32 ulBitmap;
} __PACK__ IgdParentalCtrlBASICConfTab;


/***************************家长控制儿童设备MAC地址列表*********************************/
#define IGD_PARENTAL_CTRIL_MAC_NUM 32
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;
#define PARENTAL_CTRIL_MACADDR_LEN (32)
	word8 aucMACAddress[PARENTAL_CTRIL_MACADDR_LEN];
#define PARENTAL_CTRIL_DESCRIPTION_LEN (64)
	word8 aucDescription[PARENTAL_CTRIL_DESCRIPTION_LEN];
	uword32 ulTemplateInst;

#define PARENTAL_CTRIL_MAC_MASK_BIT0_MAC_ADDR (0x01)
#define PARENTAL_CTRIL_MAC_MASK_BIT1_DESCRIPTION (0x02)
#define PARENTAL_CTRIL_MAC_MASK_BIT2_TEMPLATE_INS (0x04)
#define PARENTAL_CTRIL_MAC_MASK_ALL (0x07)
	uword32 ulBitmap;
} __PACK__ IgdParentalCtrlMACConfTab;

/***************************家长控制儿童设备MAC地址列表********************************/
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;
#define PARENTAL_CTRIL_TEMPLATES_NAME_LEN (32)
	word8 aucName[PARENTAL_CTRIL_TEMPLATES_NAME_LEN];
#define PARENTAL_CTRL_TEMPLATES_URL_FFILTER_BLACKLIST 0
#define PARENTAL_CTRL_TEMPLATES_URL_FFILTER_WHITELIST 1
	uword8 ucUrlFilterPolicy;
#define PARENTAL_CTRL_TEMPLATES_URL_FFILTER_DISABLE 0
#define PARENTAL_CTRL_TEMPLATES_URL_FFILTER_ENABLE  1
	uword8 ucUrlFilterRight;
#define PARENTAL_CTRL_TEMPLATES_DURATION_BLACKLIST 0
#define PARENTAL_CTRL_TEMPLATES_DURATION_WHITELIST 1
	uword8 ucDurationPolicy;
#define PARENTAL_CTRL_TEMPLATES_DURATION_DISABLE 0
#define PARENTAL_CTRL_TEMPLATES_DURATION_ENABLE  1
	uword8 ucDurationRight;

	uword32 ulEnable;
#define PARENTAL_CTRIL_TEMPLATES_MASK_BIT0_NAME (0x01)
#define PARENTAL_CTRIL_TEMPLATES_MASK_BIT1_URLFILTER_POLICY (0x02)
#define PARENTAL_CTRIL_TEMPLATES_MASK_BIT2_URLFILTER_RIGHT (0x04)
#define PARENTAL_CTRIL_TEMPLATES_MASK_BIT3_DURATION_POLICY (0x08)
#define PARENTAL_CTRIL_TEMPLATES_MASK_BIT4_DURATION_RIGHT (0x10)
#define PARENTAL_CTRIL_TEMPLATES_MASK_BIT5_ENABLE (0x20)
#define PARENTAL_CTRIL_TEMPLATES_MASK_ALL (0x3f)
	uword32 ulBitmap;
} __PACK__ IgdParentalCtrlTemplatesConfTab;

/***************************家长控制上网时间段列表*********************************/
/* IGD_PARENTAL_CTRIL_DURATION_TAB */
#define IGD_PARENTAL_CTRIL_DURATION_NUM  32
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;
	uword32 ulTemIndex;
#define PARENTAL_CTRIL_STAR_TIME_LEN (16)
	word8 aucStartTime[PARENTAL_CTRIL_STAR_TIME_LEN];
#define PARENTAL_CTRIL_END_TIME_LEN (16)
	word8 aucEndTime[PARENTAL_CTRIL_END_TIME_LEN];
#define PARENTAL_CTRIL_REPEAT_DAY_LEN (16)
	word8 aucRepeatDay[PARENTAL_CTRIL_REPEAT_DAY_LEN];
	uint8_t  ucenable;
	uint8_t  pad[CM_THREE_PADS];

#define PARENTAL_CTRIL_DURATION_MASK_BIT0_START_TIME (0x01)
#define PARENTAL_CTRIL_DURATION_MASK_BIT1_END_TIME (0x02)
#define PARENTAL_CTRIL_DURATION_MASK_BIT2_REPEAT_DAY (0x04)
#define PARENTAL_CTRIL_DURATION_MASK_BIT3_ENABLE                                (1 << 3)
#define PARENTAL_CTRIL_DURATION_MASK_ALL                                       ((1 << 4) - 1)
	uword32 ulBitmap;
} __PACK__ IgdParentalCtrlDuraConfTab;

/***************************家长控制URL过滤*********************************/
/* IGD_PARENTAL_CTRIL_URL_FILTER_TAB */
typedef struct {
	uword32 ulStateAndIndex;
	uword32 ulIndex;
	uword32 ulTemIndex;
#define PARENTAL_CTRIL_URL_ADDR_LEN (64)
	word8 aucUrlAddress[PARENTAL_CTRIL_URL_ADDR_LEN];

#define PARENTAL_CTRIL_URL_FILTER_MASK_BIT1_ADDR (0x01)
#define PARENTAL_CTRIL_URL_FILTER_MASK_ALL (0x01)
	uword32 ulBitmap;
} __PACK__ IgdParentalCtrlUrlConfTab;


#endif
