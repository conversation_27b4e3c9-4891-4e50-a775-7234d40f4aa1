/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm listened notify
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_NOTIFY_MSG_H
#define HI_ODL_NOTIFY_MSG_H
#include "hi_odl_basic_type.h"
#include "hi_sysdef.h"

/* odl message listenling */
#define HI_ODL_MSGBUF_SIZE 1152
#define HI_ODL_MSG_KEY HI_SUBMODULE_CM

enum cm_notify_type {
	NOTIFY_MSG_DNSSPEEDLIMIT,
	NOTIFY_MSG_WIFI_SET,
	NOTIFY_MSG_LED_STATE,
	NOTIFY_MSG_URLFILTER_SET,
	NOTIFY_MSG_DNSFILTER_SET,
	NOTIFY_MSG_NTP_SUCC,
	NOTIFY_MSG_INFORM_RESULT,
};

typedef enum {
    NO_INFORM,
    NO_INFORM_INVALID_WAN,
    NO_INFORM_INVALID_ACS,
    NO_INFORM_GET_ACS_ADDR_FAIL,
    INFORM_NO_RESPONSE,
    INFORM_OK,
    NO_INFORM_REMOTE_MNG_WAN,
} hi_cwmp_inform_result;

typedef struct hi_cm_notify {
	int32_t notify_type;
	uint8_t content[HI_ODL_MSGBUF_SIZE - sizeof(int32_t)];
} cm_notify;

struct hi_odl_msg {
	long mtype;
	union {
		uint8_t raw[HI_ODL_MSGBUF_SIZE];
		cm_notify notify;
	};
};
#endif
