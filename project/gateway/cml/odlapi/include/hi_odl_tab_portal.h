/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: cm tab portal obj attribute
 * Author: HSAN
 * Create: 2022-10-27
 */
#ifndef HI_ODL_TAB_PORTAL_H
#define HI_ODL_TAB_PORTAL_H
#include "hi_odl_basic_type.h"

typedef struct {
	uword32 ulStateAndIndex;

#define PORTAL_MANAGEMENT_DISABLE (0)
#define PORTAL_MANAGEMENT_ENABLE (1)
	uword8 ucEnable;/*默认：不启用*/
	uword8 pushWeb;
	uword8 aucPad[CM_TWO_PADS];

	word8 aucPortalUrlComputer[CM_URL_LEN];
	word8 aucPortalUrlStb[CM_URL_LEN];
	word8 aucPortalUrlPhone[CM_URL_LEN];

#define PORTAL_MGT_ATTR_MASK_BIT0_ENABLE (0x01)
#define PORTAL_MGT_ATTR_MASK_BIT1_URL_COMPUTER (0x02)
#define PORTAL_MGT_ATTR_MASK_BIT2_URL_STB (0x04)
#define PORTAL_MGT_ATTR_MASK_BIT3_URL_PHONE (0x08)
#define PORTAL_MGT_ATTR_MASK_PUSHWEB (0x10)
#define PORTAL_MGT_ATTR_MASK_ALL (0x0f)
	uword32 ulBitmap;
} __PACK__ IgdSecurPortalMgtAttrConfTab;

#endif
