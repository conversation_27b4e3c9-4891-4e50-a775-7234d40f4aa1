include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

# 组件名称
set(USERAPP_NAME hi_odlapi)

# 组件类型
set(USERAPP_TARGET_TYPE so)

# 依赖的源文件
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source ODLAPI_SOURCE_DIR)
set(USERAPP_PRIVATE_SRC
    ${ODLAPI_SOURCE_DIR}
)

# 依赖的头文件
set(USERAPP_PRIVATE_INC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_FWK_DIR}/util/include
    ${HGW_FWK_DIR}/include
    ${HGW_BASIC_DIR}/include
    ${HGW_SERVICE_DIR}/network/emu/u_space/include

    ${HGW_SERVICE_DIR}/network/easymesh/include
    ${HGW_SERVICE_DIR}/network/easymesh/public_apis/include
    ${HGW_CML_DIR}/odl/include
)

set(USERAPP_PRIVATE_LIB
    hi_ipc
    hi_basic
    hi_embase
)

# 自定义宏
set(USERAPP_PRIVATE_DEFINE)

build_app_feature()
