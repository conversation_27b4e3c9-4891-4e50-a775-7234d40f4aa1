include $(HI_EXT_CONFIG)

#===============================================================================
# local varible
#===============================================================================
HI_LOC_U_INCLUDE += -I$(HI_LOC_FWK_DIR)/ipc/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_FWK_DIR)/util/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_FWK_DIR)/timer/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_FWK_DIR)/notifier/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_FWK_DIR)/include

HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/easymesh/agent_api
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/easymesh/controller_api
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/easymesh/libs/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/wlan/libwlan/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/emu/u_space/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/wan/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/lan/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/lanhost/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/netapp/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/qos/u_space/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/multicast/u_space/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/multicast/k_space/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/common/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/security/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/samba/include

HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/encrypt/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/sysinfo/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/board/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/syslog/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/crontab/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/sysstats/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/sys_env/libenv/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/upgrade/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/download/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/alarm/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/led/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/dms/key

HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/voice/adpt/incl

HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/mib/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/odl/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/odlapi/include

HI_EXT_CFLAGS += -Wno-address-of-packed-member
#===============================================================================
# sub target
#===============================================================================
hi_subdir-y += odlapi
hi_subdir-y += $(HI_LOC_SERVICE_DIR)/network/easymesh
hi_subdir-y += mib
hi_subdir-y += storage
hi_subdir-y += odl

include $(HI_HISI_GW_APP_SCRIPT_DIR)/subdir.mk
