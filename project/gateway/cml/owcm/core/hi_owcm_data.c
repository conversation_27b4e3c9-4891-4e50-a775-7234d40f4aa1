/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Create: 2024-11-02
 */

#include "hi_owcm_data.h"
#include "hi_basic.h"

#define OWCM_HEX_STRING_BYTE 2
#define OWCM_HEX_BASE 16
#define OWCM_ASCII_ADDEND 48
#define OWCM_DECIMAL 10
#define OWCM_MAC_ADDR_LEN 6

typedef int32_t (*str_to_data)(void *des, char *src, uint32_t size);
typedef int32_t (*data_to_str)(void *src, char *des, uint32_t size);

struct tbl_convert {
	str_to_data set;
	data_to_str get;
};

static int32_t convert_to_int(void *des, char *src, uint32_t size)
{
	int32_t *data = des;

	(void)size;
	*data = strtol(src, NULL, 0);
	return HI_RET_SUCC;
}

static int32_t convert_to_uint(void *des, char *src, uint32_t size)
{
	uint32_t *data = des;

	(void)size;
	*data = strtoul(src, NULL, 0);
	return HI_RET_SUCC;
}

static int32_t convert_to_string(void *des, char *src, uint32_t size)
{
	char *data = des;

	if (strcpy_s(data, size, src) != EOK)
		return HI_RET_FAIL;
	return HI_RET_SUCC;
}

static int32_t convert_to_hex(void *des, char *src, uint32_t size)
{
	uint32_t i, j;
	char tmp[OWCM_HEX_STRING_BYTE + 1] = {0};
	char *data = des;

	if (strlen(src) % OWCM_HEX_STRING_BYTE != 0)
		return HI_RET_FAIL;

	(void)memset_s(des, size, 0, size);
	for (i = 0, j = 0; i < size && j < strlen(src); i++, j += OWCM_HEX_STRING_BYTE) {
		(void)memcpy_s(tmp, OWCM_HEX_STRING_BYTE, &src[j], OWCM_HEX_STRING_BYTE);
		data[i] = strtoul(tmp, NULL, OWCM_HEX_BASE);
	}

	if (j != strlen(src))
		return HI_RET_FAIL;

	return HI_RET_SUCC;
}

static int32_t convert_to_mac(void *des, char *src, uint32_t size)
{
	uint32_t data[OWCM_MAC_ADDR_LEN];
	uint32_t num;
	uint8_t *mac = (uint8_t *)des;
	uint32_t i;
	if (size != OWCM_MAC_ADDR_LEN)
		return HI_RET_FAIL;
	num = sscanf_s(src, "%2x:%2x:%2x:%2x:%2x:%2x",
		&data[0x0], &data[0x1], &data[0x2],
		&data[0x3], &data[0x4], &data[0x5]);
	if (num != OWCM_MAC_ADDR_LEN)
		return HI_RET_FAIL;
	
	for (i = 0; i < OWCM_MAC_ADDR_LEN; i++) {
		if (data[i] > 0xFF)
			return HI_RET_FAIL;
		mac[i] = data[i] & 0xFF;
	}
	return HI_RET_SUCC;
}

static void number_to_string(uint64_t data, char *des, uint32_t size)
{
	uint32_t rem, i = 0;
	int32_t j;
	char buf[32] = {0};

	while (data != 0) {
		rem = data % OWCM_DECIMAL;
		buf[i++] = rem + OWCM_ASCII_ADDEND;
		data /= OWCM_DECIMAL;
	}
	if (buf[0] == 0)
		buf[0] = OWCM_ASCII_ADDEND;

	for (i = 0, j = strlen(buf) - 1; i < size - 1 && j >= 0; i++, j--)
		des[i] = buf[j];

	des[i] = 0;
	if (i == size - 1)
		printf("copy to string error, size = %d\n", size);
}

static int32_t convert_from_uint(void *src, char *des, uint32_t size)
{
	uint32_t *data = src;

	number_to_string((uint64_t)*data, des, size);
	return HI_RET_SUCC;
}

static int32_t convert_from_ushort(void *src, char *des, uint32_t size)
{
	uint16_t *data = src;

	number_to_string((uint64_t)*data, des, size);
	return HI_RET_SUCC;
}

static int32_t convert_from_float(void *src, char *des, uint32_t size)
{
	float *data = src;

	if (sprintf_s(des, size, "%f", *data) < 0) {
		printf("sprintf_s to string error, size = %d, data = %f\n", size, *data);
		return HI_RET_FAIL;
	}
	return HI_RET_SUCC;
}

static int32_t convert_from_uchar(void *src, char *des, uint32_t size)
{
	uint8_t *data = src;

	number_to_string((uint64_t)*data, des, size);
	return HI_RET_SUCC;
}

static int32_t convert_from_string(void *src, char *des, uint32_t size)
{
	uint8_t *data = src;

	if (strcpy_s(des, size, data) != EOK)
		return HI_RET_FAIL;
	return HI_RET_SUCC;
}

static struct tbl_convert g_owcm_tbl_convert[] = {
	[HI_OWCM_PARA_INT] = {convert_to_int, NULL},
	[HI_OWCM_PARA_UINT] = {convert_to_uint, convert_from_uint},
	[HI_OWCM_PARA_USHORT] = {NULL, convert_from_ushort},
	[HI_OWCM_PARA_UCHAR] = {NULL, convert_from_uchar},
	[HI_OWCM_PARA_FLOAT] = {NULL, convert_from_float},
	[HI_OWCM_PARA_STRING] = {convert_to_string, convert_from_string},
	[HI_OWCM_PARA_HEX] = {convert_to_hex, NULL},
	[HI_OWCM_PARA_MAC] = {convert_to_mac, NULL},
};

int32_t hi_owcm_data_convert(enum hi_owcm_data_type type, enum hi_owcm_data_act act,
	void *data, char *value, uint32_t size)
{
	switch (act) {
		case STRING_TO_DATA:
			if (g_owcm_tbl_convert[type].set == NULL)
				return HI_RET_FAIL;
			return g_owcm_tbl_convert[type].set(data, value, size);
		case DATA_TO_STRING:
			if (g_owcm_tbl_convert[type].get == NULL)
				return HI_RET_FAIL;
			return g_owcm_tbl_convert[type].get(data, value, size);
		default:
			return HI_RET_FAIL;
	}
}
