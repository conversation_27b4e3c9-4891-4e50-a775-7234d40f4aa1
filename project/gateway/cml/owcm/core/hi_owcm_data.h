/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Create: 2024-11-02
 */

#ifndef __HI_OWCM_DATA_H__
#define __HI_OWCM_DATA_H__

#include <stdint.h>

enum hi_owcm_data_type {
	HI_OWCM_PARA_INT,
	HI_OWCM_PARA_UINT,
	HI_OWCM_PARA_USHORT,
	HI_OWCM_PARA_UCHAR,
	HI_OWCM_PARA_FLOAT,
	HI_OWCM_PARA_STRING,
	HI_OWCM_PARA_HEX,
	HI_OWCM_PARA_MAC,
};

enum hi_owcm_data_act {
	STRING_TO_DATA,
	DATA_TO_STRING,
};

int32_t hi_owcm_data_convert(enum hi_owcm_data_type type, enum hi_owcm_data_act act,
	void *data, char *value, uint32_t size);

#endif /* __HI_OWCM_DATA_H__ */
