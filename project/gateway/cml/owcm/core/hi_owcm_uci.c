/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Create: 2024-11-02
 */

#include "hi_owcm_uci.h"
#include <uci.h>
#include <ulog.h>
#include "hi_basic.h"
#include "hi_owcm_common.h"

#define TMPDIR_CAP 0777

static struct uci_context *g_owcm_uci_ctx = NULL;

static int32_t hi_owcm_uci_make_path(const char *file)
{
	char tmpdir[OWCM_TMP_BUFLEN];

	if (sprintf_s(tmpdir, sizeof(tmpdir), "/tmp/%s", file) < 0) {
		ULOG_ERR("dir name too long, %s\n", file);
		return HI_RET_FAIL;
	}

	mkdir(tmpdir, TMPDIR_CAP);
	return HI_RET_SUCC;
}

static void hi_owcm_uci_write_val(const char *file, char *option, char *value)
{
	char cmd[OWCM_TMP_BUFLEN];

	if (sprintf_s(cmd, sizeof(cmd), "echo %s > /tmp/%s/%s", value, file, option) < 0) {
		ULOG_ERR("file name too long, %s.%s\n", file, option);
		return;
	}

	system(cmd);
}

static int32_t hi_owcm_uci_get_options(struct uci_section *s, char *options[], char *values[])
{
	uint32_t i = 0;
	struct uci_element *e, *tmp;
	struct uci_option *o;

	uci_foreach_element_safe(&s->options, tmp, e) {
		o = uci_to_option(e);
		if (o->type != UCI_TYPE_STRING)
			continue;
		if (i >= UCI_OPT_MAXNUM) {
			ULOG_WARN("out of option num\n");
			break;
		}

		options[i] = e->name;
		values[i] = o->v.string;
		if (options[i] == NULL || values[i] == NULL) {
			ULOG_ERR("load options fail\n");
			return HI_RET_FAIL;
		}
		i++;
	}

	return HI_RET_SUCC;
}

uint32_t hi_owcm_uci_get_section_tatol(void *fp, const char *section)
{
	struct uci_package *pkg = fp;
	struct uci_section *s = NULL;
	struct uci_element *e, *tmp;
	uint32_t total = 0;

	uci_foreach_element_safe(&pkg->sections, tmp, e) {
		s = uci_to_section(e);
		if (strcmp(s->type, section) != 0)
			continue;

		total++;
	}
	return total;
}

int32_t hi_owcm_uci_get_section(void *fp, const char *section, char *options[], char *values[])
{
	struct uci_package *pkg = fp;
	struct uci_section *s = NULL;
	struct uci_element *e, *tmp;

	uci_foreach_element_safe(&pkg->sections, tmp, e) {
		if (strcmp(e->name, section) != 0)
			continue;

		s = uci_to_section(e);
		break;
	}
	if (s == NULL) {
		ULOG_ERR("invalid section [%s]\n", section);
		return HI_RET_FAIL;
	}

	return hi_owcm_uci_get_options(s, options, values);
}

void hi_owcm_uci_put_section(char *options[], char *values[])
{
}

int32_t hi_owcm_uci_get_file(const char *file, void **fp)
{
	struct uci_package *pkg = NULL;

	if (uci_load(g_owcm_uci_ctx, file, &pkg) != UCI_OK) {
		ULOG_ERR("uci load [%s] fail\n", file);
		return HI_RET_FAIL;
	}

	*fp = pkg;
	return HI_RET_SUCC;
}

void hi_owcm_uci_put_file(void *fp)
{
	struct uci_package *pkg = fp;
	uci_unload(g_owcm_uci_ctx, pkg);
}

int32_t hi_owcm_uci_make_file(const char *file, char *options[], char *values[])
{
	uint32_t i;

	if (hi_owcm_uci_make_path(file) != HI_RET_SUCC)
		return HI_RET_FAIL;

	for (i = 0; options[i] != NULL && values[i] != NULL && i < UCI_OPT_MAXNUM; i++) {
		hi_owcm_uci_write_val(file, options[i], values[i]);
		free(options[i]);
		free(values[i]);
	}
	return HI_RET_SUCC;
}

int32_t hi_owcm_uci_init(void)
{
	g_owcm_uci_ctx = uci_alloc_context();
	if (g_owcm_uci_ctx == NULL) {
		ULOG_ERR("uci alloc context fail\n");
		return HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

void hi_owcm_uci_exit(void)
{
	if (g_owcm_uci_ctx != NULL)
		uci_free_context(g_owcm_uci_ctx);

	g_owcm_uci_ctx = NULL;
}
