include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

# 组件名称
set(USERAPP_NAME hi_owcm)

# 组件类型
set(USERAPP_TARGET_TYPE so)

# 依赖的源文件
set(USERAPP_PRIVATE_SRC
    ${CMAKE_CURRENT_SOURCE_DIR}/core/hi_owcm_uci.c
    ${CMAKE_CURRENT_SOURCE_DIR}/core/hi_owcm_api.c
    ${CMAKE_CURRENT_SOURCE_DIR}/core/hi_owcm_core.c
    ${CMAKE_CURRENT_SOURCE_DIR}/core/hi_owcm_tbl.c
    ${CMAKE_CURRENT_SOURCE_DIR}/core/hi_owcm_data.c
    ${CMAKE_CURRENT_SOURCE_DIR}/dist/hi_owcm_ddos.c
)

if ("ON" IN_LIST CONFIG_GATEWAY_SERVICE_VOICE)
    list(APPEND USERAPP_PRIVATE_SRC
        ${CMAKE_CURRENT_SOURCE_DIR}/dist/hi_owcm_voice.c
    )
endif()

if ("ON" IN_LIST CONFIG_GATEWAY_NET_PON)
    list(APPEND USERAPP_PRIVATE_SRC
        ${CMAKE_CURRENT_SOURCE_DIR}/dist/hi_owcm_pon.c
    )
endif()
# 依赖的头文件
set(USERAPP_PRIVATE_INC
    ${CMAKE_CURRENT_SOURCE_DIR}/core
    ${CONFIG_LINUX_DRV_DIR}/net/pon/include
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_FWK_DIR}/include
    ${HGW_FWK_DIR}/adapt/owal/include
    ${HGW_SERVICE_DIR}/dms/board/include
    ${HGW_SERVICE_DIR}/dms/optical
    ${HGW_MML_DIR}/omci/omci_tapi/include
	${HGW_MML_DIR}/oam/uoam/include
	${HGW_MML_DIR}/oam/oam_service/include
    ${HGW_BASIC_DIR}/include
    ${HGW_SERVICE_DIR}/voice/adpt/incl
    ${HGW_SERVICE_DIR}/network/common/include
    ${CONFIG_LINUX_SRV_DIR}/cfe/include

)

set(USERAPP_PRIVATE_LIB
    hi_owal_ssf openwrt_lib
    hi_optical hi_basic
)

# 自定义宏
set(USERAPP_PRIVATE_DEFINE)

# 自定义编译选项
set(USERAPP_PRIVATE_COMPILE
    -Wno-error
)

build_app_feature()
