include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

# 组件名称
set(USERAPP_NAME hi_omci_ctl)

# 组件类型
set(USERAPP_TARGET_TYPE so)

# 依赖的源文件
aux_source_directory(source/me/ani OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source/me/equipment OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source/me/layer2 OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source/me/layer3 OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source/me/ethernet OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source/me/general OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source/me/voip OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source/me/alarm OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source/me/stat OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source/me/miscellaneous OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source/init OMCI_CTIL_SOURCE_DIR)
aux_source_directory(source OMCI_CTIL_SOURCE_DIR)
set(USERAPP_PRIVATE_SRC
    ${OMCI_CTIL_SOURCE_DIR}
)

# 依赖的头文件
set(USERAPP_PRIVATE_INC
    include
    ../omci_sql/include
    ../omci_tapi/include
    ${HGW_SERVICE_DIR}/dms/sysinfo/include
    ${HGW_SERVICE_DIR}/dms/board/include
    ${HGW_SERVICE_DIR}/pon/api/include
    ${HGW_SERVICE_DIR}/pon/sml/include
    ${HGW_SERVICE_DIR}/network/common/include
    ${HGW_SERVICE_DIR}/dms/upgrade/include
    ${CONFIG_LINUX_DRV_DIR}/net/pon/include
    ${HGW_CML_DIR}/odl/include
    ${HGW_CML_DIR}/odlapi/include
    ${HGW_BASIC_DIR}/include
    ${HGW_BASIC_DIR}/include/os
    ${HGW_FWK_DIR}/timer/include
    ${HGW_FWK_DIR}/notifier/include
    ${HGW_FWK_DIR}/include
    ${HGW_FWK_DIR}/util/include
    ${HGW_FWK_DIR}/ipc/ipc_lib
    ${HGW_FWK_DIR}/ioreactor/include
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_MML_DIR}/omci/omci_tapi/include
    ${HGW_MML_DIR}/omci/omci_sql/include
    ${OPEN_SOURCE_SQLITE_BINARY_DIR}/sqlite-autoconf-${CONFIG_OPENSRC_VERSION_SQLITE}
    ${HGW_SERVICE_DIR}/dms/cfm/cfm_lib
)

set(USERAPP_PRIVATE_LIB
    hi_basic hi_timer hi_notifier
    sqlite3  hi_omci_tapi hi_omci_sql
    hi_ioreactor hi_util hi_ipc
    hi_owal_ssf openwrt_lib
)

# 自定义宏
set(USERAPP_PRIVATE_DEFINE)

# 自定义编译选项
set(USERAPP_PRIVATE_COMPILE)

build_app_feature()
