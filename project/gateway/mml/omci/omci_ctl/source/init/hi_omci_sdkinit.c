/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_sdkinit.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_10_27
  功能描述   : 对omci库函数进行初始化
******************************************************************************/

#include "hi_omci_lib.h"
#include "hi_ioreactor.h"
#include "hi_notifier.h"
#include "hi_omci_me_lib.h"
#include "hi_timer.h"
#include "igdCmApi.h"
#include "hi_cfm_api.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define HI_ROGUEFILE_LEN 32
#define HI_ROGUEFILE    HI_CONF_WORK_DIR"/rogue"

/* OMCI消息接收netlink socket */
static hi_int32 gi_sock = 0;
static hi_char8 g_roguefile[HI_ROGUEFILE_LEN];
static hi_uint32 g_initflag = HI_FALSE;

/* OMCI module configuration completion notification */
#define HI_OMCI_MOD_ONU_REGSTATE_OPERATION  5
#define HI_OMCI_MOD_DETECT_START_INTERVAL   1000
#define HI_OMCI_MOD_DETECT_INTERVAL         3000
struct hi_omci_mod_notify {
	uint32_t status; /* onu ploam status */
	uint32_t omci_rx_num;
	uint32_t timer;
};
static struct hi_omci_mod_notify g_omci_mod_notify = {0};
/*****************************************************************************
 函 数 名  : hi_omci_sdkrecv
 功能描述  : omci消息入口函数
 输入参数  : hi_void *pv_data
             hi_uint32 ui_inlen
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_recv(hi_void *pv_data, hi_uint32 ui_inlen)
{
	hi_uint32  ui_ret;

	/*judg first online*/
	hi_omci_proc_first();

	/* process the omci msg */
	ui_ret = hi_omci_proc_msg(pv_data, ui_inlen);
	(void)ui_ret;
	hi_omci_systrace(ui_ret, ui_inlen, 0, 0, 0);
	return;
}

/*****************************************************************************
 函 数 名  : hi_onustatus_recv
 功能描述  : ONU注册状态跳变处理
 输入参数  : hi_void *pv_data
             hi_uint32 ui_inlen
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_onustatus_recv(hi_void *pv_data, hi_uint32 ui_inlen)
{
	hi_int32 i_ret;
	hi_uint32 ui_onustatus = *(hi_uint32 *)pv_data;
	hi_omci_notifier_data_s st_notifier_data;

	hi_omci_debug("onu status : %u \n", ui_onustatus);

	hi_omci_dbg_onusta_file(ui_onustatus);
	if (HI_GPON_RPTYPE_SN == ui_onustatus && g_initflag == HI_TRUE) {
		hi_omci_me_deactive_exit();
	}
	st_notifier_data.em_type = HI_OMCI_NOTIFY_ONUSTATE_E;
	st_notifier_data.ui_data = ui_onustatus;
	i_ret = hi_notifier_call(HI_OMCI_NOTIFIY_NAME, &st_notifier_data);
	if (i_ret != HI_RET_SUCC) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
	}
#ifdef CONFIG_PLATFORM_OPENWRT
    HI_IPC_CALL("hi_sml_owal_led_gpon_proc", &st_notifier_data);
#endif

	hi_omci_systrace(HI_RET_SUCC, ui_onustatus, 0, 0, 0);
	return;
}

/******************************************************************************
 Function    : hi_omci_msg_init
 Description : 初始化OMCI消息通道
 Input Parm  : 无
 Output Parm : hi_int32 *pi_sock
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_msg_init(hi_int32 *pi_sock)
{
	return hi_os_netlink_init(pi_sock, HI_SRCMODULE_PONLINK_PLOAM_K);
}

/******************************************************************************
 Function    : hi_omci_msg_exit
 Description : 注销OMCI消息通道
 Input Parm  : hi_int32 i_sock
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_msg_exit(hi_int32 i_sock)
{
	return hi_os_netlink_exit(i_sock, HI_SRCMODULE_PONLINK_PLOAM_K);
}

static void hi_omci_proc_rouge(hi_uint32 data)
{
	hi_int32 ret;
	switch (data) {
	case HI_GPON_ROGUE_POWER_OFF:
		printf("\r\n turn off tx power \r\n");
		break;
	case HI_GPON_ROGUE_POWER_ON:
		printf("\r\n turn on tx power \r\n");
		break;
	case HI_GPON_ROGUE_STATUS_OFF:
		printf("\r\n mask as not rogue onu \r\n");
		ret = unlink(g_roguefile);
		break;
	case HI_GPON_ROGUE_STATUS_ON:
		printf("\r\n mask as rogue onu \r\n");
		ret = creat(g_roguefile, 0);
		if (-1 != ret) {
			close(ret);
		}
		break;
	default:
		break;
	}
}
/******************************************************************************
 Function    : hi_omci_msg_rcv
 Description : PLOAM netlink消息接收
 Input Parm  : hi_int32 i_fd
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_msg_rcv(hi_int32 i_sock)
{
	hi_int32 i_ret;
	hi_uint32 *pui_data;
	struct hi_pon_reportmsg st_msg;
	hi_omci_notifier_data_s st_notifier_data;

	HI_OS_MEMSET_S(&st_msg, sizeof(st_msg), 0, sizeof(st_msg));
	i_ret = hi_os_netlink_recv(i_sock, &st_msg, sizeof(st_msg), 0);
	if (i_ret < 0) {
		hi_omci_systrace(HI_RET_FAIL, 0, 0, 0, 0);
		return (hi_int32)HI_RET_FAIL;
	}

	switch (st_msg.msgtype) {
	case (hi_uint32)HI_GPON_NETLINK_OMCIMSG:
		if (g_initflag == HI_TRUE) {
			hi_omci_recv((hi_void *)st_msg.content, st_msg.len);
			g_omci_mod_notify.omci_rx_num++;
		}
		break;

	case (hi_uint32)HI_GPON_NETLINK_REGSTA:
		hi_onustatus_recv((hi_void *)st_msg.content, sizeof(hi_uint32));
		break;

	case (hi_uint32)HI_GPON_NETLINK_ROGUESTA:
		st_notifier_data.em_type = HI_OMCI_NOTIFY_ROGUE_ONU_E;
		pui_data = (hi_uint32 *)st_msg.content;
		st_notifier_data.ui_data = *pui_data;
		hi_omci_proc_rouge(*pui_data);
		i_ret = hi_notifier_call(HI_OMCI_NOTIFIY_NAME, (hi_void *)&st_notifier_data);
		if (i_ret != HI_RET_SUCC) {
			hi_omci_systrace(i_ret, 0, 0, 0, 0);
		}
#ifdef CONFIG_PLATFORM_OPENWRT
        HI_IPC_CALL("hi_sml_owal_led_gpon_proc", &st_notifier_data);
#endif
		break;

	default:
		break;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

static hi_int32 hi_omci_check_roguesta(void)
{
	hi_int32 status;
	HI_OS_MEMCPY_S(g_roguefile, sizeof(g_roguefile), HI_ROGUEFILE, sizeof(HI_ROGUEFILE));
	status = access(g_roguefile, 0);
	if (0 == status) {
		status = 1;
	} else {
		status = 0;
	}
	HI_IPC_CALL("hi_gpon_set_rogue_flag", &status);
	return HI_RET_SUCC;
}

static void hi_omci_mod_notify_timer(void *data)
{
	static uint32_t last_omci_rx_num = 0;
	hi_module_state_s status = {IGD_INIT_STATE_OMCI, HI_TRUE};

	if (last_omci_rx_num != g_omci_mod_notify.omci_rx_num) {
		last_omci_rx_num = g_omci_mod_notify.omci_rx_num;
		hi_timer_mod_wo_lock(g_omci_mod_notify.timer, HI_OMCI_MOD_DETECT_INTERVAL);
		return;
	}

	if (g_omci_mod_notify.status == HI_OMCI_MOD_ONU_REGSTATE_OPERATION) {
		hi_os_printf("OMCI configuration completion\n");
		if (HI_IPC_CALL("IPC_igdSetCmInitState", &status))
			hi_os_printf("%s call IPC_igdSetCmInitState fail\n", __func__);
		return;
	}

	hi_timer_mod_wo_lock(g_omci_mod_notify.timer, HI_OMCI_MOD_DETECT_INTERVAL);
}

static int32_t hi_omci_mod_notify_start(void *data, hi_uint32 len)
{
	hi_omci_notifier_data_s *omci_data = data;

	if (omci_data == NULL || sizeof(*omci_data) != len)
		return -HI_RET_INVALID_PARA;

	g_omci_mod_notify.status = omci_data->ui_data;
	if (omci_data->ui_data == HI_OMCI_MOD_ONU_REGSTATE_OPERATION)
		hi_timer_mod(g_omci_mod_notify.timer, HI_OMCI_MOD_DETECT_START_INTERVAL);

	return HI_RET_SUCC;
}

static void hi_omci_mod_notify_init(void)
{
	if (g_omci_mod_notify.timer == 0) {
		hi_timer_create(hi_omci_mod_notify_timer, NULL, &g_omci_mod_notify.timer);
		hi_notifier_reg(HI_OMCI_NOTIFIY_NAME, "hi_omci_mod_notify_start", hi_omci_mod_notify_start);
	}
}

static void hi_omci_mod_notify_exit(void)
{
	if (g_omci_mod_notify.timer != 0) {
		hi_timer_destroy(g_omci_mod_notify.timer);
		hi_notifier_unreg(HI_OMCI_NOTIFIY_NAME, "hi_omci_mod_notify_start");
		g_omci_mod_notify.timer = 0;
	}
}

/*****************************************************************************
 函 数 名  : hi_omci_start
 功能描述  : OMCI启动函数
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
HI_DEF_IPC(hi_omci_start)
{
	hi_uint32 ui_ret;
	if (g_initflag == HI_TRUE) {
		return HI_RET_ALREADYINIT;
	}
	ui_ret = init_logger();
	if (ui_ret != 0) {
		hi_os_printf("initialize logger failed\n");
		return ui_ret;
	}

	ui_ret = hi_omci_msg_init(&gi_sock);
	if (ui_ret != HI_RET_SUCC) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_ioreactor_reg(gi_sock, hi_omci_msg_rcv);
	if (ui_ret != HI_RET_SUCC) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	/*protocol sql mib*/
	ui_ret = hi_omci_proc_init();
	if (ui_ret != HI_RET_SUCC) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_omci_me_init();
	if (ui_ret != HI_RET_SUCC) {
		hi_os_printf("\r\nomci me init error...\r\n");
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_omci_libext_init();
	if (ui_ret != HI_RET_SUCC) {
		hi_os_printf("\r\nomci extended lib init error...\r\n");
	}

	hi_omci_mib_backup();

	hi_omci_check_roguesta();
	hi_omci_mod_notify_init();
	hi_os_printf("\r\nhsan omci start success ...\r\n");
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	g_initflag = HI_TRUE;
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sdkexit
 功能描述  : omci sdk退出函数
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
HI_DEF_IPC(hi_omci_stop)
{
	if (g_initflag == HI_FALSE) {
		return HI_RET_UNINIT;
	}
	hi_omci_mod_notify_exit();
	hi_omci_me_exit();
	hi_ioreactor_unreg(gi_sock);
	hi_omci_msg_exit(gi_sock);
	hi_omci_mib_exit();
	g_initflag = HI_FALSE;
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	HI_IPC_CALL("hi_sml_gpon_map_list_clr");
	return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
