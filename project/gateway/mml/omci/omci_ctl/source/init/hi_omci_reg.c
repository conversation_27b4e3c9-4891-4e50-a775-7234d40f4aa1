/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_reg.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_10_27
  功能描述   : 该文件只存放所有omci注册调用函数
******************************************************************************/

#include "hi_omci_lib.h"
#include "hi_sysinfo.h"
#include "hi_board.h"
#include "hi_notifier.h"
#include "hi_ipc.h"
#include "hi_ipc_def.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

typedef struct {
	hi_void   *pv_callback;
	hi_uchar8  uc_funcname[64];
} hi_ifm_cmdcallback_s;

typedef struct {
	hi_uint32 ui_meid;
	hi_void *pv_callback;
} hi_omci_reg_me_callback_s;

#define HI_IFM_CMDTYPE_REGENTRY( func )    { func, #func }
hi_omci_tapi_voip_extvlan_s g_st_extvlan_table;
static hi_ifm_cmdcallback_s g_a_st_omci_cmdtype[] = {

	//ANI
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_anig_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_anig_test),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_anig_get),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_fecpm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_fecpm_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_fecpm_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_fecpm_getcurrent),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_tcont_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_gem_ctp_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_gem_ctp_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_gem_ctp_delete),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_gem_iwtp_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_gemctppm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_gemctppm_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_gemctppm_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_gemctppm_delete),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_galethpm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_galethpm_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_galethpm_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_galethpm_delete),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_traffic_desc_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_traffic_desc_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_traffic_desc_delete),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_pq_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_multiwtp_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_multiwtp_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_multiwtp_set),

	//eth uni
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_pptpethuni_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_pptpethuni_get),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethpm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethpm_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethpm_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethpm_delete),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethpm3_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethpm3_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethpm3_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethpm3_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_veip_set),

	//layer2
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_exvlantag_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_exvlantag_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_exvlantag_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_mac_serv_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_mac_serv_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_mac_serv_delete),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_8021p_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_vlan_filt_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_vlan_filt_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_vlan_filt_delete),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_macportcfg_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_macportcfg_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_macportcfg_delete),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_macportfilt_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_macport_table_get),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_multi_oper_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_multi_oper_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_multi_oper_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_multisubs_cfg_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_multisubs_cfg_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_multisubs_cfg_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_bridgepm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_bridgepm_delete),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_brportpm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_brportpm_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_brportpm_getcurrent),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethextpm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethextpm_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethextpm_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethextpm_stat_getcurrent),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethextpm_64b_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethextpm_64b_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethextpm_64b_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethextpm_64b_stat_getcurrent),

	//equip
	//HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_softimage_activate),
	//HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_softimage_get_bef),
	//HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_softimage_commit),
	//HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_rmtdbg_set_after),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_softwareimage_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_onudata_reset),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_powermng_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_rmtdbg_set_after),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_loid_auth_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_loid_auth_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_onug_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_onug_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_onu2g_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_pptp_pots_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_pptp_pots_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_pptp_pots_test),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_cfg_data_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_cfg_data_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_iphost_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_iphost_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_user_data_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_dial_plan_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_dial_plan_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_dial_plan_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_voice_ctp_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_agent_data_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_agent_data_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_line_status_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_rtppm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_rtppm_delete),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_media_profile_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_media_profile_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_media_profile_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_large_string_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_agent_data_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_agent_data_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_tr069_management_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_rtppm_stat_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_agentpm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_agentpm_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_agentpm_stat_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_callinitpm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_callinitpm_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_callinitpm_stat_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_call_control_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_call_control_pm_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_call_control_stat_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_user_data_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_user_data_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethfrmpm_up_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethfrmpm_up_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethfrmpm_up_stat_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethfrmpm_down_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethfrmpm_down_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_ethfrmpm_down_stat_getcurrent),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_auth_security_set),
#if 0
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_rtp_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_rtp_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_sip_agent_data_create),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_voice_ctp_set),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_voice_ctp_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_voip_voice_ctp_delete),
#endif
	//misc
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_esc_set),

	//layer3
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_oltg_set),

	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgpontcpm_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgpontcpm_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgpontcpm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgpontcpm_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgpondownmngpm_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgpondownmngpm_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgpondownmngpm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgpondownmngpm_delete),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgponupmngpm_get),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgponupmngpm_getcurrent),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgponupmngpm_create),
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_xgponupmngpm_delete),
	//alarm
	HI_IFM_CMDTYPE_REGENTRY(hi_omci_me_local_alarm_restart),
};

static hi_omci_reg_me_callback_s g_a_st_omci_alarm[] = {
	{HI_OMCI_PRO_ME_ANI_G_E, hi_omci_me_anig_alarm_get},
	{HI_OMCI_PRO_ME_FEC_PM_E, hi_omci_me_fecpm_alarm_get},
	{HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, hi_omci_me_pptpethuni_alarm_get},
	{HI_OMCI_PRO_ME_ETH_PM_E, hi_omci_me_ethpm_alarm_get},
	{HI_OMCI_PRO_ME_ETH_PM3_E, hi_omci_me_ethpm3_alarm_get},
	{HI_OMCI_PRO_ME_MAC_PM_E, hi_omci_me_bridgepm_alarm_get},
	{HI_OMCI_PRO_ME_MAC_PORT_PM_E, hi_omci_me_brportpm_alarm_get},
	{HI_OMCI_PRO_ME_ETH_EXT_PM_E, hi_omci_me_ethextpm_alarm_get},
	{HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, hi_omci_me_ethextpm_64b_alarm_get},
	{HI_OMCI_PRO_ME_ETHFRM_PM_UP_E, hi_omci_me_ethfrmpm_up_alarm_get},
	{HI_OMCI_PRO_ME_ETHFRM_PM_DOWN_E, hi_omci_me_ethfrmpm_down_alarm_get},
};

static hi_omci_reg_me_callback_s g_a_st_omci_stat[] = {
	{HI_OMCI_PRO_ME_FEC_PM_E, hi_omci_me_fecpm_stat_get},
	{HI_OMCI_PRO_ME_GEM_PORT_PM_E, hi_omci_me_gemctppm_stat_get},
	{HI_OMCI_PRO_ME_ETH_PM_E, hi_omci_me_ethpm_stat_get},
	{HI_OMCI_PRO_ME_ETH_PM3_E, hi_omci_me_ethpm3_stat_get},
	{HI_OMCI_PRO_ME_MAC_PM_E, hi_omci_me_bridgepm_stat_get},
	{HI_OMCI_PRO_ME_MAC_PORT_PM_E, hi_omci_me_brportpm_stat_get},
	{HI_OMCI_PRO_ME_ETH_EXT_PM_E, hi_omci_me_ethextpm_stat_get},
	{HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, hi_omci_me_ethextpm_64b_stat_get},
	{HI_OMCI_PRO_ME_RTP_PMH_DATA_E, hi_omci_me_rtppm_stat_get},
	{HI_OMCI_PRO_ME_SIP_AGENT_PM_HISTORY_DATA_E, hi_omci_me_sip_agentpm_stat_get},
	{HI_OMCI_PRO_ME_SIP_CALL_INIT_PMH_DATA_E, hi_omci_me_sip_callinitpm_stat_get},
	{HI_OMCI_PRO_ME_CALL_CTRL_PMH_DATA_E, hi_omci_me_call_control_stat_get},
	{HI_OMCI_PRO_ME_ETHFRM_PM_UP_E, hi_omci_me_ethfrmpm_up_stat_get},
	{HI_OMCI_PRO_ME_ETHFRM_PM_DOWN_E, hi_omci_me_ethfrmpm_down_stat_get},
	{HI_OMCI_PRO_ME_XGPON_TC_PM_E, hi_omci_me_xgpontcpm_stat_get},
	{HI_OMCI_PRO_ME_XGPON_DOWNMNG_PM_E, hi_omci_me_xgpondownmngpm_stat_get},
	{HI_OMCI_PRO_ME_XGPON_UPMNG_PM_E, hi_omci_me_xgponupmngpm_stat_get},
	{HI_OMCI_PRO_ME_GAL_ETH_PM_E, hi_omci_me_galethpm_stat_get},
};


/*****************************************************************************
 函 数 名  : hi_omci_mib_saveapi
 功能描述  : 将omci的api信息保存到数据库callbacktable表
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_saveapi(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_uint32 ui_ret, ui_indx = 0;

	for (ui_indx = 0; ui_indx < sizeof(g_a_st_omci_cmdtype) / sizeof(hi_ifm_cmdcallback_s) ; ui_indx++) {
		ui_ret = hi_omci_sql_entity_set(HI_OMCI_FUNC_ME_ACTION_E,
						(hi_uint32)g_a_st_omci_cmdtype[ui_indx].pv_callback,
						(hi_uchar8 *)HI_OMCI_CALLBACK_MAIN_LIB,
						g_a_st_omci_cmdtype[ui_indx].uc_funcname);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_printf("\r\nOmci RegApi Err=%0x08x ...!", ui_ret);

		}
	}

	/*保存告警检测函数*/
	for (ui_indx = 0; ui_indx < sizeof(g_a_st_omci_alarm) / sizeof(hi_omci_reg_me_callback_s) ; ui_indx++) {
		ui_ret = hi_omci_sql_entity_alarm_set(g_a_st_omci_alarm[ui_indx].ui_meid,
						      (hi_uint32)g_a_st_omci_alarm[ui_indx].pv_callback,
						      HI_OMCI_CALLBACK_MAIN_LIB);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_printf(" %d  %s \n", __LINE__, __func__);
		}
	}

	for (ui_indx = 0; ui_indx < sizeof(g_a_st_omci_stat) / sizeof(hi_omci_reg_me_callback_s) ; ui_indx++) {
		ui_ret = hi_omci_sql_entity_stat_set(g_a_st_omci_stat[ui_indx].ui_meid,
						     (hi_uint32)g_a_st_omci_stat[ui_indx].pv_callback,
						     HI_OMCI_CALLBACK_MAIN_LIB);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_printf(" %d  %s \n", __LINE__, __func__);
		}
	}

	return HI_RET_SUCC;
}
#if 0
HI_DEF_NCALL(HI_SYSINFO_NT_NAME, hi_sysinfo_nt_data *, pst_data) /*lint !e550 !e569*/
{
	switch (pst_data->ui_type) {
	case HI_SYSINFO_NT_LOID_STOP_E:
	case HI_SYSINFO_NT_LOPWD_STOP_E:
		break;

	case HI_SYSINFO_NT_LOID_START_E:
	case HI_SYSINFO_NT_LOPWD_START_E:
		hi_omci_me_loid_auth_init();
		break;

	default:
		break;
	}

	return HI_RET_SUCC;
}
#endif
/*****************************************************************************
 函 数 名  : hi_omci_me_reg_service
 功能描述  : 注册ME业务处理函数，将函数指针写入数据库
             二次开发可以通过此接口将一个ME的原始业务处理重新定义，需要提供
             更新的业务处理函数
 输入参数  : hi_uint32 ui_meid MEID
             hi_omci_proc_msg_type_e em_msg_type OMCI消息类型
             hi_uint32 ui_before 更新MIB前处理
             HI_FUNCCALLBACK_EXT pf_callback ME业务处理函数指针
             hi_char8 *pc_funcname 函数名
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_me_reg_service(hi_uint32 ui_meid, hi_omci_proc_msg_type_e em_msg_type,
				hi_uint32 ui_before, HI_FUNCCALLBACK_EXT pf_callback, hi_char8 *pc_funcname)
{
	hi_int32 i_ret;
	hi_char8 *pc_tablename;
	//hi_uchar8 auc_filename[256];

	//hi_omci_libext_name_get((hi_char8 *)auc_filename);

	i_ret = hi_omci_sql_entity_set(HI_OMCI_FUNC_ME_ACTION_E,
				       (hi_uint32)pf_callback, (hi_uchar8 *)HI_OMCI_CALLBACK_MAIN_LIB, (hi_uchar8 *)pc_funcname);
	HI_OMCI_RET_CHECK(i_ret);

	if (ui_before) {
		pc_tablename = HI_OMCI_MIB_ENTITYFWDTABLE_NAME;
	} else {
		pc_tablename = HI_OMCI_MIB_ENTITYNEXTTABLE_NAME;
	}

	i_ret = hi_omci_sql_entity_table_set(ui_meid, (hi_uint32)em_msg_type, pc_tablename, pc_funcname);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_me_release_service
 功能描述  : 注销ME业务处理函数，将函数指针从数据库删除
             二次开发可以通过此接口注销一个ME的业务处理
 输入参数  : hi_uint32 ui_meid MEID
             hi_omci_proc_msg_type_e em_msg_type OMCI消息类型
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_me_release_service(hi_uint32 ui_meid, hi_omci_proc_msg_type_e em_msg_type)
{
	hi_int32 i_ret;
	hi_void *pv_callback;

	i_ret = hi_omci_sql_entity_get((hi_uint32)em_msg_type, (hi_ushort16)ui_meid, HI_OMCI_MIB_ENTITYFWDTABLE_NAME,
				       &pv_callback);
	if (i_ret == HI_RET_SUCC) {
		i_ret = hi_omci_sql_entity_del(pv_callback);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_sql_entity_table_set(ui_meid, (hi_uint32)em_msg_type, HI_OMCI_MIB_ENTITYFWDTABLE_NAME, " ");
		HI_OMCI_RET_CHECK(i_ret);
	} else {
		i_ret = hi_omci_sql_entity_get((hi_uint32)em_msg_type, (hi_ushort16)ui_meid, HI_OMCI_MIB_ENTITYNEXTTABLE_NAME,
					       &pv_callback);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_sql_entity_del(pv_callback);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_sql_entity_table_set(ui_meid, (hi_uint32)em_msg_type, HI_OMCI_MIB_ENTITYNEXTTABLE_NAME, " ");
		HI_OMCI_RET_CHECK(i_ret);
	}

	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_me_reg_alarm
 功能描述  : 注册ME告警处理函数，将函数指针写入数据库
             二次开发可以通过此接口将一个ME的原始告警处理重新定义，需要提供
             更新的告警处理函数
 输入参数  : hi_uint32 ui_meid MEID
             HI_FUNCCALLBACK_EXT callback ME告警处理函数指针
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_me_reg_alarm(hi_uint32 ui_meid, HI_FUNCCALLBACK_EXT callback)
{
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_me_release_alarm
 功能描述  : 注销ME告警处理函数，将函数指针从数据库删除
             二次开发可以通过此接口注销一个ME的告警处理
 输入参数  : hi_uint32 ui_meid MEID
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_me_release_alarm(hi_uint32 ui_meid)
{
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_me_reg_stat
 功能描述  : 注册ME统计处理函数，将函数指针写入数据库
             二次开发可以通过此接口将一个ME的原始统计处理重新定义，需要提供
             更新的统计处理函数
 输入参数  : hi_uint32 ui_meid MEID
             HI_FUNCCALLBACK_EXT callback ME统计处理函数指针
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_me_reg_stat(hi_uint32 ui_meid, HI_FUNCCALLBACK_EXT callback)
{
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_me_release_alarm
 功能描述  : 注销ME统计处理函数，将函数指针从数据库删除
             二次开发可以通过此接口注销一个ME的统计处理
 输入参数  : hi_uint32 ui_meid MEID
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_me_release_stat(hi_uint32 ui_meid)
{
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_init()
{
	hi_omci_tapi_sysinfo_s st_info;
	hi_int32 i_ret;
	hi_uint32 ui_tcontnum;
	hi_uint32 ui_tcontid;
	hi_uint32 ui_qnum;
	hi_uint32 ui_qid;
	hi_uint32 ui_portid = 0;
	hi_uint32 id;
	hi_omci_debug("omci me init start ...\n");

	hi_omci_me_stat_init();
	hi_omci_me_alarm_init();

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_tcont_num_get(&ui_tcontnum);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_mib_saveapi(0, 0, 0);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_onudata_init();
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_onu_capability_init();
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_onu_optical_info_init();
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_loid_auth_init();
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_onug_init();
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_onu2g_init();
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_cardholder_init(HI_TRUE, 0, HI_FALSE);
	HI_OMCI_RET_CHECK(i_ret);
	i_ret = hi_omci_me_cardholder_init(HI_FALSE, 0, HI_FALSE);
	HI_OMCI_RET_CHECK(i_ret);
	i_ret = hi_omci_me_cardholder_init(HI_FALSE, 1, HI_FALSE);
	HI_OMCI_RET_CHECK(i_ret);
	i_ret = hi_omci_me_cardholder_init(HI_FALSE, 2, HI_FALSE);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_circuitpack_init(HI_TRUE, 0, HI_FALSE);
	HI_OMCI_RET_CHECK(i_ret);
	i_ret = hi_omci_me_circuitpack_init(HI_FALSE, 0, HI_FALSE);
	HI_OMCI_RET_CHECK(i_ret);
	i_ret = hi_omci_me_circuitpack_init(HI_FALSE, 1, HI_FALSE);
	HI_OMCI_RET_CHECK(i_ret);
	i_ret = hi_omci_me_circuitpack_init(HI_FALSE, 2, HI_FALSE);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
		i_ret = hi_omci_me_cardholder_init(HI_FALSE, 1, HI_TRUE);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_me_circuitpack_init(HI_FALSE, 1, HI_TRUE);
		HI_OMCI_RET_CHECK(i_ret);
	}

	i_ret = hi_omci_me_softwareimage_init(0);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_softwareimage_init(1);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_oltg_init();
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_rmtdbg_init();
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_powermng_init();
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_debug("omci equipment init success ...\n");

	i_ret = hi_omci_me_anig_init();
	HI_OMCI_RET_CHECK(i_ret);

	/*OMCC通道的tcont不应该上报*/
	for (ui_tcontid = 1; ui_tcontid < ui_tcontnum; ui_tcontid++) {
		i_ret = hi_omci_me_tcont_init(ui_tcontid);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_tapi_queue_num_get((hi_omci_tapi_egress_type_e)(HI_OMCI_TAPI_EGRESS_TCONT0_E + ui_tcontid), &ui_qnum);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_qid = 0; ui_qid < ui_qnum; ui_qid++) {
			i_ret = hi_omci_me_pq_init(HI_TRUE, ui_tcontid, 0, ui_qid);
			HI_OMCI_RET_CHECK(i_ret);
		}
	}

	hi_omci_map_init(st_info.ui_product_mode);

	hi_omci_debug("omci ani init success ...\n");

	for (ui_portid = 0; ui_portid < st_info.ui_port_num; ui_portid++) {
		i_ret = hi_omci_me_pptpethuni_init(ui_portid);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_me_unig_init(ui_portid);
		HI_OMCI_RET_CHECK(i_ret);
		i_ret = hi_omci_tapi_queue_num_get((hi_omci_tapi_egress_type_e)(HI_OMCI_TAPI_EGRESS_UNI0_E + ui_portid), &ui_qnum);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_qid = 0; ui_qid < ui_qnum; ui_qid++) {
			i_ret = hi_omci_me_pq_init(HI_FALSE, 0, ui_portid, ui_qid);
			HI_OMCI_RET_CHECK(i_ret);
		}
	}

	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
		/*HGU只上报VEIP，当做一个端口来处理*/
		i_ret = hi_omci_me_veip_init();
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_me_tr069_management_init();
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_tapi_queue_num_get(HI_OMCI_TAPI_EGRESS_VEIP_E, &ui_qnum);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_qid = 0; ui_qid < ui_qnum; ui_qid++) {
			i_ret = hi_omci_me_pq_init(HI_FALSE, 0, st_info.ui_port_num, ui_qid);//关联veip的pq实例号紧接着关联pptp eth的pq实例号
			HI_OMCI_RET_CHECK(i_ret);
		}

		i_ret = hi_omci_me_unig_init(HI_OMCI_TAPI_VEIP_E);
		HI_OMCI_RET_CHECK(i_ret);
		i_ret = hi_omci_me_unig_init(HI_OMCI_TAPI_VOIP_E);
		HI_OMCI_RET_CHECK(i_ret);
		for (id = 0; id < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; id++) {
			i_ret = hi_omci_me_iphost_init(id);
			HI_OMCI_RET_CHECK(i_ret);
		}
	}

	for (ui_portid = 1; ui_portid <= st_info.ui_pots_num; ui_portid++) {
		i_ret = hi_omci_me_pptp_pots_init(ui_portid);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret =  hi_omci_me_voip_line_status_init(ui_portid);
		HI_OMCI_RET_CHECK(i_ret);
	}

	i_ret = hi_omci_me_esc_init();
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_debug("omci uni init success ...\n");

	i_ret = hi_omci_me_traffic_desc_init();
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_voip_cfg_data_init();
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_voip_msg();
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&g_st_extvlan_table, sizeof(hi_omci_tapi_voip_extvlan_s), 0, sizeof(hi_omci_tapi_voip_extvlan_s));
	for (id = 0; id < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; id++) {
		g_st_extvlan_table.st_extvlan[id].ui_index = id;
		g_st_extvlan_table.st_extvlan[id].us_vlan = 0x1fff;
		g_st_extvlan_table.st_extvlan[id].us_pri = 0xf;
	}
	i_ret = hi_omci_me_gemctppm_init();
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_reset()
{
	hi_omci_me_extvlantag_op_s st_extvlantag;
	hi_uint32 ui_index = 0;
	hi_uint32 ui_instnum = 0;
	hi_uint32 aui_instid[4] = {0};
	hi_int32 i_ret = 0;

	hi_omci_me_stat_exit();
	hi_omci_me_alarm_exit(HI_FALSE);
	hi_omci_me_iphost_reset();

	/* 删除映射规则 */
	hi_omci_map_exit();
	/* 删除Extended VLAN tagging operation configuration data实体 */
	i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, aui_instid, 4, &ui_instnum);
	HI_OMCI_RET_CHECK(i_ret);

	for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
		st_extvlantag.st_msghead.us_instid = (hi_ushort16)aui_instid[ui_index];
		i_ret = hi_omci_me_exvlantag_delete(&st_extvlantag, sizeof(st_extvlantag), NULL);
		HI_OMCI_RET_CHECK(i_ret);
	}
	/* 删除car资源 */
	hi_omci_me_traffic_desc_exit();

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_exit()
{
	hi_omci_me_traffic_desc_exit();
	hi_omci_voip_msg_eixt();
	hi_omci_me_reset();
	hi_omci_me_alarm_exit(HI_TRUE);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_deactive_exit()
{
	return hi_omci_me_esc_exit();
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
