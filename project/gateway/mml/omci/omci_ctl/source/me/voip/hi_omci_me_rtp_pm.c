/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_ethfrm_ext_pm.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_omci_tapi_voip_rtppm_s gst_rtppm_history;
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_rtppm_stat_get
 Description :
 Input Parm  : hi_void *pv_data       历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_rtppm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_me_rtp_pm_s st_entity;
	hi_omci_tapi_voip_rtppm_s st_rtppm_curr;
	//hi_uint32 ui_time = 0;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	/* 读取数据库的control block */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_RTP_PMH_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_voip_rtppm_get(&st_rtppm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_interval++;
	st_entity.ui_rtp_error = (st_rtppm_curr.ui_rtp_errors > gst_rtppm_history.ui_rtp_errors) ?
				 (st_rtppm_curr.ui_rtp_errors - gst_rtppm_history.ui_rtp_errors) : 0;
	st_entity.ui_pack_loss = st_rtppm_curr.ui_packet_loss;
	if (st_rtppm_curr.ui_maxi_jitter) {
		st_entity.ui_max_jitter = st_rtppm_curr.ui_maxi_jitter;
	}
	if (st_rtppm_curr.ui_maxi_betw_rtcp) {
		st_entity.ui_max_rtcp = st_rtppm_curr.ui_maxi_betw_rtcp;
	}
	if (st_rtppm_curr.ui_buffer_under) {
		st_entity.ui_buff_under = (st_rtppm_curr.ui_buffer_under > gst_rtppm_history.ui_buffer_under) ?
					  (st_rtppm_curr.ui_buffer_under - gst_rtppm_history.ui_buffer_under) : 0;
	}
	if (st_rtppm_curr.ui_buffer_over) {
		st_entity.ui_buff_over = (st_rtppm_curr.ui_buffer_over > gst_rtppm_history.ui_buffer_over) ?
					 (st_rtppm_curr.ui_buffer_over - gst_rtppm_history.ui_buffer_over) : 0;
	}
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_RTP_PMH_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	/* 更新历史统计 */
	//HI_OS_MEMCPY_S(&gst_rtppm_history, sizeof(gst_rtppm_history), &st_rtppm_curr, sizeof(gst_rtppm_history));

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_rtppm_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_rtp_pm_s *pst_timer = (hi_omci_me_rtp_pm_s *)pv_data;
	hi_omci_me_rtp_pm_s st_entity;
	hi_omci_tapi_voip_rtppm_s st_rtppm_curr;
	//hi_uint32 ui_time = 0;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_timer->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	/* 读取数据库的control block */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_RTP_PMH_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_voip_rtppm_get(&st_rtppm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.ui_rtp_error = (st_rtppm_curr.ui_rtp_errors > gst_rtppm_history.ui_rtp_errors) ?
				 (st_rtppm_curr.ui_rtp_errors - gst_rtppm_history.ui_rtp_errors) : 0;
	st_entity.ui_pack_loss = st_rtppm_curr.ui_packet_loss;
	if (st_rtppm_curr.ui_maxi_jitter) {
		st_entity.ui_max_jitter = st_rtppm_curr.ui_maxi_jitter;
	}
	if (st_rtppm_curr.ui_maxi_betw_rtcp) {
		st_entity.ui_max_rtcp = st_rtppm_curr.ui_maxi_betw_rtcp;
	}
	if (st_rtppm_curr.ui_buffer_under) {
		st_entity.ui_buff_under = (st_rtppm_curr.ui_buffer_under > gst_rtppm_history.ui_buffer_under) ?
					  (st_rtppm_curr.ui_buffer_under - gst_rtppm_history.ui_buffer_under) : 0;
	}
	if (st_rtppm_curr.ui_buffer_over) {
		st_entity.ui_buff_over = (st_rtppm_curr.ui_buffer_over > gst_rtppm_history.ui_buffer_over) ?
					 (st_rtppm_curr.ui_buffer_over - gst_rtppm_history.ui_buffer_over) : 0;
	}
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_RTP_PMH_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_me_rtppm_create
 Description : RTP PM history data create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_rtppm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_rtp_pm_s *pst_entry = (hi_omci_me_rtp_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_rtppm_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		hi_omci_ext_del_inst(pst_entry->st_msghead.us_meid, pst_entry->st_msghead.us_instid);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&gst_rtppm_history, sizeof(gst_rtppm_history), 0, sizeof(gst_rtppm_history));

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_rtppm_stat_timer, sizeof(st_rtppm_stat_timer), 0, sizeof(st_rtppm_stat_timer));
	st_rtppm_stat_timer.ui_meid = HI_OMCI_PRO_ME_RTP_PMH_DATA_E;
	st_rtppm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_rtppm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_rtppm_delete
 Description : RTP PM history data delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_rtppm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_rtp_pm_s *pst_entry = (hi_omci_me_rtp_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_rtppm_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&st_rtppm_stat_timer, sizeof(st_rtppm_stat_timer), 0, sizeof(st_rtppm_stat_timer));
	st_rtppm_stat_timer.ui_meid = HI_OMCI_PRO_ME_RTP_PMH_DATA_E;
	st_rtppm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_rtppm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
