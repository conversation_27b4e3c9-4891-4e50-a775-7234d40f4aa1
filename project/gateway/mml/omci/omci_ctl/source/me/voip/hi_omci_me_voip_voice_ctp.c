/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_voip_voice_ctp.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
extern hi_uint32 gui_omci_voip_flag;
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_voip_voice_ctp_create
 Description : VoIP voice CTP create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_voice_ctp_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_voip_voice_ctp_s *pst_entry = (hi_omci_me_voip_voice_ctp_s *)pv_data;
	hi_omci_me_media_s st_media;
	hi_omci_me_rtp_s st_rtp;
	hi_omci_tapi_voip_rtp_s st_tapi_rtp;
	hi_short16 us_vmp = 0;
	hi_int32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_outlen = 0;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		hi_omci_ext_del_inst(pst_entry->st_msghead.us_meid, pst_entry->st_msghead.us_instid);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&st_media, sizeof(st_media), 0, sizeof(st_media));
	HI_OS_MEMSET_S(&st_rtp, sizeof(st_rtp), 0, sizeof(st_rtp));
	if (0 != gui_omci_voip_flag) {
		HI_OS_MEMCPY_S(&us_vmp, sizeof(us_vmp), pst_entry->uc_vmp_pointer, sizeof(hi_short16));
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VOIP_MEDIA_PROFILE_E, us_vmp, &st_media);
		if (HI_RET_SUCC == i_ret) {
			st_media.st_msghead.us_instid = us_vmp;
			st_media.st_msghead.us_attmask = 0xffff;
			hi_omci_me_voip_media_profile_set(&st_media, sizeof(st_media), &ui_outlen);

			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_RTP_RPOFILE_DATA_E, st_media.uc_rtp_pointer[0], &st_rtp);
			HI_OMCI_RET_CHECK(i_ret);
			st_tapi_rtp.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(pst_entry->us_pptp_pointer);
			st_tapi_rtp.ui_dscp = st_rtp.uc_dscpmark;
			st_tapi_rtp.ui_portstart = st_rtp.us_loacl_portmin;
			st_tapi_rtp.ui_portend = st_rtp.us_loacl_portmax;
			i_ret = hi_omci_tapi_voip_rtp_set(&st_tapi_rtp);
			HI_OMCI_RET_CHECK(i_ret);
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
