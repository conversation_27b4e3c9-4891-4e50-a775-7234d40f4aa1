/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_sip_agent_config_data.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
hi_uint32 gui_omci_agentdata_mask = 0;
extern hi_uint32 gui_omci_voip_flag;
hi_uint32 gui_omci_voip_extvlan_mask = 0;
extern hi_omci_tapi_voip_extvlan_s g_st_extvlan_table;
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_me_sip_agent_config_data_set
 Description : SIP agent config data
               在数据库操作后处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_sip_agent_data_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 i_ret = HI_RET_SUCC, ui_index = 0;
	hi_omci_me_sip_agentdata_s *pst_entity = (hi_omci_me_sip_agentdata_s *)pv_data;
	hi_omci_me_sip_agentdata_s st_entity;
	hi_omci_me_udptcp_cfg_s st_udptcp;
	hi_omci_me_extvlantag_op_s st_extvlan;
	//omci_extvlantag_s st_srcvlantagtable;
	hi_omci_tapi_voip_iphost_s st_tapi_iphost;
	hi_omci_tapi_voip_udptcp_cfg_s st_data;
	hi_omci_me_large_string_s st_string;
	hi_omci_tapi_voip_server_s st_server;
	hi_omci_me_network_addr_s st_network;
	hi_omci_tapi_voip_time_s st_time;
	hi_omci_mac_bri_port_type_e em_type = HI_MAC_BRI_PORT_IP_E;
	hi_ushort16 us_instid = 0;
	hi_ushort16 us_mask = 0;
	hi_ushort16 us_sipdata_instid, us_pptp_instid, us_macport_instid, us_vlanext_instid;
	hi_char8 *puc_server = HI_NULL;
	hi_char8 *savePtr = HI_NULL;
	hi_uchar8  uc_ptr[HI_OMCI_TAPI_VOIP_STR_LEN];
	HI_OS_MEMSET_S(&st_udptcp, sizeof(st_udptcp), 0, sizeof(st_udptcp));
	us_instid  = pst_entity->st_msghead.us_instid;
	us_mask    = pst_entity->st_msghead.us_attmask;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);

	hi_omci_debug("[INFO]:psa_pointer=%hu,opa_pointer=%hu,ps_dns=%u,ss_dns =%u,tu_pointer=%hu,sre_time=%u,srhs_time=%u\n",
			 pst_entity->us_psa_pointer, pst_entity->us_opa_pointer, pst_entity->ui_ps_dns, pst_entity->ui_ss_dns,
			 pst_entity->us_tu_pointer, pst_entity->ui_sre_time, pst_entity->ui_srhs_time);
	hi_omci_debug("[INFO]:hp_uri=%hu,sip_status=%hhu,sip_registrar=%hu,softswitch =%u,sot_control =%hhu,su_format =%hhu,\
			 rsa_pointer =%hu\n", pst_entity->us_hp_uri, pst_entity->uc_sip_status, pst_entity->us_sip_registrar,
			 pst_entity->ui_softswitch, pst_entity->us_sot_control, pst_entity->uc_su_format, pst_entity->us_rsa_pointer);

	if (HI_RET_SUCC != hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_SIP_USER_DATA_E, HI_OMCI_ATTR1, &us_instid,
			&us_sipdata_instid)) {
		us_pptp_instid = HI_OMCI_GET_INSTID_FORM_VOIP_PORTID(1);
	} else {
		i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_SIP_USER_DATA_E, us_sipdata_instid, HI_OMCI_ATTR10, &us_pptp_instid);
		HI_OMCI_RET_CHECK(i_ret);
	}
	if (0xffff == us_pptp_instid) {
		us_pptp_instid = HI_OMCI_GET_INSTID_FORM_VOIP_PORTID(1);
	}
	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR5)) {
		if (0 != gui_omci_voip_flag) {
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, pst_entity->us_tu_pointer, &st_udptcp);
			HI_OMCI_RET_CHECK(i_ret);
			st_data.us_lineidx =  HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
			st_data.us_portid = st_udptcp.us_portid;
			st_data.us_protocol = st_udptcp.uc_protocol;
			st_data.us_tosfield = st_udptcp.uc_tosfield;
			i_ret = hi_omci_tapi_voip_udptcp_set(&st_data);
			HI_OMCI_RET_CHECK(i_ret);
			for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
				if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == st_udptcp.us_iphost_ptr) {
					break;
				}
			}
			i_ret = hi_omci_ext_get_instid_byattr2(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR3, &em_type, HI_OMCI_ATTR4,
							       &st_udptcp.us_iphost_ptr, &us_macport_instid);
			i_ret += hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, HI_OMCI_ATTR7, &us_macport_instid,
							       &us_vlanext_instid);
			/*创建voip wan*/
			i_ret += hi_omci_ext_get_inst(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_vlanext_instid, &st_extvlan);
			if (HI_RET_SUCC == i_ret) {
				if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM != ui_index) {
					g_st_extvlan_table.st_extvlan[ui_index].ui_mode = 1;
					HI_OS_MEMSET_S(&st_tapi_iphost, sizeof(st_tapi_iphost), 0, sizeof(st_tapi_iphost));
					st_tapi_iphost.us_flag = 0;
					if (0x1fff != g_st_extvlan_table.st_extvlan[ui_index].us_vlan) {
						st_tapi_iphost.ui_vlan = g_st_extvlan_table.st_extvlan[ui_index].us_vlan;
						st_tapi_iphost.ui_vlanen = 1;
					}

					st_tapi_iphost.us_8021p = g_st_extvlan_table.st_extvlan[ui_index].us_pri;
					st_tapi_iphost.us_8021pen = 1;

					/* 通过iphost实例，获取wan信息 */
					hi_omci_debug("[INFO] iphost us_instid=0x%x\n", st_udptcp.us_iphost_ptr);
					hi_omci_me_set_iphost_attr(&st_tapi_iphost, g_st_extvlan_table.st_extvlan[ui_index].ui_index);
					i_ret = hi_omci_tapi_voip_wan_set(&st_tapi_iphost);
					HI_OMCI_RET_CHECK(i_ret);
				}
			} else {
				if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM != ui_index) {
					g_st_extvlan_table.st_extvlan[ui_index].ui_mode = 1;
				}
			}
		} else {
			if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, pst_entity->us_tu_pointer, &st_udptcp)) {
				for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
					if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == st_udptcp.us_iphost_ptr) {
						break;
					}
				}

				if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM != ui_index) {
					g_st_extvlan_table.st_extvlan[ui_index].ui_mode = 1;
					i_ret = hi_omci_ext_get_instid_byattr2(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR3, &em_type, HI_OMCI_ATTR4,
									       &st_udptcp.us_iphost_ptr, &us_macport_instid);
					i_ret += hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, HI_OMCI_ATTR7, &us_macport_instid,
									       &us_vlanext_instid);
					i_ret += hi_omci_ext_get_inst(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_vlanext_instid, &st_extvlan);
					if (HI_RET_SUCC ==  i_ret) {
						gui_omci_voip_extvlan_mask += (g_st_extvlan_table.st_extvlan[ui_index].us_pri << 16);
						gui_omci_voip_extvlan_mask += g_st_extvlan_table.st_extvlan[ui_index].us_vlan;
					}
				}
			}
			HI_OMCI_BIT_SET(gui_omci_agentdata_mask, (16 - HI_OMCI_ATTR5));
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR1)) {
		if (0 != pst_entity->us_psa_pointer) {
			if (0 != gui_omci_voip_flag) {
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, pst_entity->us_psa_pointer, &st_string);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, us_instid, &st_entity);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_entity.us_tu_pointer, &st_udptcp);
				HI_OMCI_RET_CHECK(i_ret);
				HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
				HI_OS_MEMSET_S(uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);
				HI_OS_STRCAT_S((hi_char8 *)uc_ptr, sizeof(uc_ptr), (hi_char8 *)st_string.uc_part[0]);

				puc_server = hi_os_strtok_r((hi_char8 *)uc_ptr, ":", &savePtr);
				if (HI_NULL != puc_server) {
					HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server), (hi_char8 *)puc_server);
					puc_server = hi_os_strtok_r(HI_NULL, ":", &savePtr);
					if (HI_NULL != puc_server) {
						HI_OS_SSCANF_S(puc_server, "%u", &st_server.us_portid);
					}
				}

				st_server.us_protocol = st_udptcp.uc_protocol;
				i_ret = hi_omci_tapi_voip_proxyserver_set(&st_server);
				HI_OMCI_RET_CHECK(i_ret);

				/*如没配置reg服务器信息，则和proxy服务器信息一致*/
				i_ret = hi_omci_tapi_voip_regserver_set(&st_server);
				HI_OMCI_RET_CHECK(i_ret);
			} else {
				HI_OMCI_BIT_SET(gui_omci_agentdata_mask, (16 - HI_OMCI_ATTR1));
			}
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR2)) {
		if (0 != pst_entity->us_opa_pointer) {
			if (0 != gui_omci_voip_flag) {
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, pst_entity->us_opa_pointer, &st_string);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, us_instid, &st_entity);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_entity.us_tu_pointer, &st_udptcp);
				HI_OMCI_RET_CHECK(i_ret);
				HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
				HI_OS_MEMSET_S(uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);

				HI_OS_STRCAT_S((hi_char8 *)uc_ptr, sizeof(uc_ptr), (hi_char8 *)st_string.uc_part[0]);

				puc_server = hi_os_strtok_r((hi_char8 *)uc_ptr, ":", &savePtr);
				if (HI_NULL != puc_server) {
					HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server), (hi_char8 *)puc_server);
					puc_server = hi_os_strtok_r(HI_NULL, ":", &savePtr);
					if (HI_NULL != puc_server) {
						HI_OS_SSCANF_S(puc_server, "%u", &st_server.us_portid);
					}
				}
				st_server.us_protocol = st_udptcp.uc_protocol;
				i_ret = hi_omci_tapi_voip_outbserver_set(&st_server);
				HI_OMCI_RET_CHECK(i_ret);
			} else {
				HI_OMCI_BIT_SET(gui_omci_agentdata_mask, (16 - HI_OMCI_ATTR2));
			}
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR10)) {
		if (0 != pst_entity->us_sip_registrar) {
			if (0 != gui_omci_voip_flag) {
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_NETWORK_ADDR_E, pst_entity->us_sip_registrar, &st_network);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, st_network.us_addr_ptr, &st_string);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, us_instid, &st_entity);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_entity.us_tu_pointer, &st_udptcp);
				HI_OMCI_RET_CHECK(i_ret);
				HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
				HI_OS_MEMSET_S(uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);

				HI_OS_STRCAT_S((hi_char8 *)uc_ptr, sizeof(uc_ptr), (hi_char8 *)st_string.uc_part[0]);
				puc_server = hi_os_strtok_r((hi_char8 *)uc_ptr, ":", &savePtr);
				if (HI_NULL != puc_server) {
					HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server), (hi_char8 *)puc_server);
					puc_server = hi_os_strtok_r(HI_NULL, ":", &savePtr);
					if (HI_NULL != puc_server) {
						HI_OS_SSCANF_S(puc_server, "%u", &st_server.us_portid);
					}
				}
				st_server.us_protocol = st_udptcp.uc_protocol;
				i_ret = hi_omci_tapi_voip_regserver_set(&st_server);
				HI_OMCI_RET_CHECK(i_ret);
			} else {
				HI_OMCI_BIT_SET(gui_omci_agentdata_mask, (16 - HI_OMCI_ATTR10));
			}
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR8)) {
		if ((0 != pst_entity->us_hp_uri) && (0xffff != pst_entity->us_hp_uri)) {
			if (0 != gui_omci_voip_flag) {
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, pst_entity->us_hp_uri, &st_string);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, us_instid, &st_entity);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_entity.us_tu_pointer, &st_udptcp);
				HI_OMCI_RET_CHECK(i_ret);
				HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));

				HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server), (hi_char8 *)st_string.uc_part[0]);
				st_server.us_portid = st_udptcp.us_portid;
				st_server.us_protocol = st_udptcp.uc_protocol;
				i_ret = hi_omci_tapi_voip_agent_set(&st_server);
				HI_OMCI_RET_CHECK(i_ret);
			} else {
				HI_OMCI_BIT_SET(gui_omci_agentdata_mask, (16 - HI_OMCI_ATTR8));
			}
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR6)) {
		if (0 != gui_omci_voip_flag) {
			st_time.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
			i_ret = hi_omci_tapi_voip_regtime_get(&st_time);
			HI_OMCI_RET_CHECK(i_ret);
			st_time.us_reg_time = (hi_ushort16)pst_entity->ui_sre_time;
			i_ret = hi_omci_tapi_voip_regtime_set(&st_time);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			HI_OMCI_BIT_SET(gui_omci_agentdata_mask, (16 - HI_OMCI_ATTR6));
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR7)) {
		if (0 != gui_omci_voip_flag) {
			st_time.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
			i_ret = hi_omci_tapi_voip_regtime_get(&st_time);
			HI_OMCI_RET_CHECK(i_ret);
			st_time.us_rereg_time = (hi_ushort16)pst_entity->ui_srhs_time;
			i_ret = hi_omci_tapi_voip_regtime_set(&st_time);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			HI_OMCI_BIT_SET(gui_omci_agentdata_mask, (16 - HI_OMCI_ATTR7));
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_sip_agent_data_get
 Description : SIP agent config  get
               在数据库操作前处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_sip_agent_data_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_omci_me_sip_agentdata_s *pst_entity = (hi_omci_me_sip_agentdata_s *)pv_data;
	hi_omci_me_sip_agentdata_s st_entity;
	hi_ushort16 us_instid;
	hi_ushort16 us_sipdata_instid = 0;
	hi_ushort16 us_pptp_instid = 0;
	hi_omci_tapi_voip_time_s st_time;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	if (0 != gui_omci_voip_flag) {
		/* get the instance ID */
		us_instid  = pst_entity->st_msghead.us_instid;

		hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, pst_entity->st_msghead.us_attmask);
		if (HI_RET_SUCC != hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_SIP_USER_DATA_E, HI_OMCI_ATTR1, &us_instid,
				&us_sipdata_instid)) {
			hi_omci_debug("[INFO]:cannpt find sip userdata me\n");
			return HI_RET_SUCC;
		}
		i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_SIP_USER_DATA_E, us_sipdata_instid, HI_OMCI_ATTR10, &us_pptp_instid);
		HI_OMCI_RET_CHECK(i_ret);
		if (0xffff == us_pptp_instid) {
			us_pptp_instid = HI_OMCI_GET_INSTID_FORM_VOIP_PORTID(1);
		}
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);

		st_time.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
		i_ret = hi_omci_tapi_voip_regtime_get(&st_time);
		HI_OMCI_RET_CHECK(i_ret);
		st_entity.ui_srhs_time = st_time.us_rereg_time;
		st_entity.ui_sre_time = st_time.us_reg_time;
		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_me_sip_agent_data_delete
 Description : SIP agent config delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_sip_agent_data_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_sip_agentdata_s *pst_entity = (hi_omci_me_sip_agentdata_s *)pv_data;
	hi_omci_me_sip_agentdata_s st_entity;
	hi_omci_me_udptcp_cfg_s st_udptcp;
	hi_omci_me_extvlantag_op_s st_extvlan;
	hi_omci_mac_bri_port_type_e em_type = HI_MAC_BRI_PORT_IP_E;
	hi_ushort16 us_vlanext_instid = 0;
	hi_ushort16 us_macport_instid = 0;
	//hi_uchar8 *puc_pos=HI_NULL;
	//hi_uchar8 *puc_data=HI_NULL;
	//hi_uchar8    uc_count;
	hi_int32 i_ret;
	hi_uint32 ui_index;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&st_udptcp, sizeof(st_udptcp), 0, sizeof(st_udptcp));
	/*删除wan*/
	if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, pst_entity->st_msghead.us_instid,
						&st_entity)) {
		if (HI_RET_SUCC != hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_entity.us_tu_pointer, &st_udptcp)) {
			return HI_RET_SUCC;
		}
		i_ret = hi_omci_ext_get_instid_byattr2(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR3, &em_type, HI_OMCI_ATTR4,
						       &st_udptcp.us_iphost_ptr, &us_macport_instid);
		i_ret += hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, HI_OMCI_ATTR7, &us_macport_instid,
						       &us_vlanext_instid);
		i_ret += hi_omci_ext_get_inst(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_vlanext_instid, &st_extvlan);
		if (HI_RET_SUCC != i_ret) {
			return HI_RET_SUCC;
		}
		for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
			if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == st_udptcp.us_iphost_ptr) {
				break;
			}
		}
		if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM != ui_index) {
			if (0x1fff != g_st_extvlan_table.st_extvlan[ui_index].us_vlan) {
				i_ret = hi_omci_tapi_voip_wan_del(0);
				HI_OMCI_RET_CHECK(i_ret);
				g_st_extvlan_table.st_extvlan[ui_index].ui_mode = 0;
			}
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
hi_int32 hi_omci_me_sip_agent_data_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_sip_agentdata_s *pst_entity = (hi_omci_me_sip_agentdata_s *)pv_data;
	hi_omci_me_large_string_s st_string;
	hi_omci_tapi_voip_server_s st_server;
	hi_omci_me_network_addr_s st_network;
	hi_int32 i_ret;
	hi_char8 *puc_server = HI_NULL;
	hi_uchar8  uc_ptr[HI_OMCI_TAPI_VOIP_STR_LEN];
	hi_char8 *savePtr = HI_NULL;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		hi_omci_ext_del_inst(pst_entity->st_msghead.us_meid, pst_entity->st_msghead.us_instid);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	if (0 != gui_omci_voip_flag) {
		if (0 != pst_entity->us_psa_pointer) {
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, pst_entity->us_psa_pointer, &st_string);
			HI_OMCI_RET_CHECK(i_ret);
			HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
			HI_OS_MEMSET_S(uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);
			HI_OS_STRCAT_S((hi_char8 *)uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, (hi_char8 *)st_string.uc_part[0]);

			puc_server = hi_os_strtok_r((hi_char8 *)uc_ptr, ":", &savePtr);
			if (HI_NULL != puc_server) {
				HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server), (hi_char8 *)puc_server);
				puc_server = hi_os_strtok_r(HI_NULL, ":", &savePtr);
				if (HI_NULL != puc_server) {
					HI_OS_SSCANF_S(puc_server, "%u", &st_server.us_portid);
				}
			}

			st_server.us_protocol = 0xFFFF;
			i_ret = hi_omci_tapi_voip_proxyserver_set(&st_server);
			HI_OMCI_RET_CHECK(i_ret);

			/*如没配置reg服务器信息，则和proxy服务器信息一致*/
			i_ret = hi_omci_tapi_voip_regserver_set(&st_server);
			HI_OMCI_RET_CHECK(i_ret);
		}

		if (0 != pst_entity->us_opa_pointer) {
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, pst_entity->us_opa_pointer, &st_string);
			HI_OMCI_RET_CHECK(i_ret);
			HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
			HI_OS_MEMSET_S(uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);

			HI_OS_STRCAT_S((hi_char8 *)uc_ptr, sizeof(uc_ptr), (hi_char8 *)st_string.uc_part[0]);

			puc_server = hi_os_strtok_r((hi_char8 *)uc_ptr, ":", &savePtr);
			if (HI_NULL != puc_server) {
				HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server), (hi_char8 *)puc_server);
				puc_server = hi_os_strtok_r(HI_NULL, ":", &savePtr);
				if (HI_NULL != puc_server) {
					HI_OS_SSCANF_S(puc_server, "%u", &st_server.us_portid);
				}
			}
			st_server.us_protocol = 0xFFFF;
			i_ret = hi_omci_tapi_voip_outbserver_set(&st_server);
			HI_OMCI_RET_CHECK(i_ret);
		}

		if (0 != pst_entity->us_sip_registrar) {
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_NETWORK_ADDR_E, pst_entity->us_sip_registrar, &st_network);
			HI_OMCI_RET_CHECK(i_ret);
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, st_network.us_addr_ptr, &st_string);
			HI_OMCI_RET_CHECK(i_ret);
			HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
			HI_OS_MEMSET_S(uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);

			HI_OS_STRCAT_S((hi_char8 *)uc_ptr, sizeof(uc_ptr), (hi_char8 *)st_string.uc_part[0]);
			puc_server = hi_os_strtok_r((hi_char8 *)uc_ptr, ":", &savePtr);
			if (HI_NULL != puc_server) {
				HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server), (hi_char8 *)puc_server);
				puc_server = hi_os_strtok_r(HI_NULL, ":", &savePtr);
				if (HI_NULL != puc_server) {
					HI_OS_SSCANF_S(puc_server, "%u", &st_server.us_portid);
				}
			}
			st_server.us_protocol = 0xFFFF;
			i_ret = hi_omci_tapi_voip_regserver_set(&st_server);
			HI_OMCI_RET_CHECK(i_ret);
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
