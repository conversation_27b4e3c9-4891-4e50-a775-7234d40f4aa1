/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_voip_line_status.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
extern hi_uint32 gui_omci_voip_flag;
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_me_voip_line_status_init
 Description : VoIP line status
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_line_status_init(hi_uint32 ui_pots)
{
	hi_omci_me_line_status_s st_entry;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)HI_OMCI_GET_INSTID_FORM_VOIP_PORTID(ui_pots);

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_VOIP_LINE_STATUS_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_VOIP_LINE_STATUS_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_voip_line_status_get
 Description : VoIP line status get
               在数据库操作前处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_line_status_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_line_status_s *pst_entity = (hi_omci_me_line_status_s *)pv_data;
	hi_omci_me_line_status_s st_entity;
	hi_omci_tapi_line_status_s st_info;
	hi_uint32    i_ret = HI_RET_SUCC, ui_conf = 0;
	hi_ushort16 us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	if (0 != gui_omci_voip_flag) {
		us_instid  = pst_entity->st_msghead.us_instid;

		i_ret = hi_omci_tapi_voip_conf_get(&ui_conf);
		HI_OMCI_RET_CHECK(i_ret);
		if (0 == ui_conf) {
			hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, pst_entity->st_msghead.us_attmask);
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VOIP_LINE_STATUS_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);

		HI_OS_MEMSET_S(&st_info, sizeof(st_info), 0, sizeof(st_info));
		st_info.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_instid);
		i_ret = hi_omci_tapi_voip_line_status_get(&st_info);

		st_entity.uc_server_status = (hi_uchar8)st_info.ui_server_status;
		st_entity.uc_session_type = (hi_uchar8)st_info.ui_session_type;
		st_entity.uc_line_status = (hi_uchar8)st_info.ui_line_state;
		st_entity.uc_codec_used[0] = (hi_uchar8)st_info.ui_codec_used;
		hi_omci_tapi_voip_emergency_get(&st_entity.uc_emergency_call_status);
		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_VOIP_LINE_STATUS_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
