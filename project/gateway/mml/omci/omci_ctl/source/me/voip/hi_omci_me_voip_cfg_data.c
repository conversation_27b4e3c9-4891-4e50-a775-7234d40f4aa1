/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_voip_cfg_data.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
hi_uint32 gui_omci_cfgdata_mask = 0;
extern hi_uint32 gui_omci_voip_flag;
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_voip_cfg_data_init
 Description : ONU2-G init
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_cfg_data_init()
{
	hi_omci_me_config_data_s st_entry;
	hi_ushort16 us_instid = 0;
	hi_int32 i_ret;

	hi_omci_tapi_voip_cnfig_method_set(HI_OMCI_VOIP_CONFIG_METHOD_ONU_DEFAULT);

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_VOIP_CFG_DATA_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;
	st_entry.us_confaddr_point = 0xFFFF;
	st_entry.uc_as_proto = 1;
	st_entry.ui_avc_methods = 5;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_VOIP_CFG_DATA_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_me_voip_cfg_data_set
 Description : VoIP config data
               在数据库操作后处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_cfg_data_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_config_data_s *pst_entity = (hi_omci_me_config_data_s *)pv_data;
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;
	hi_uint32    i_ret = HI_RET_SUCC;
	hi_uint32 ui_proto = pst_entity->uc_as_proto, ui_proto_tmp = 0;
	hi_uint32 ui_en = pst_entity->uc_method_used;

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", pst_entity->st_msghead.us_instid, us_mask);

	hi_omci_debug("[INFO]:as_proto=%hhu,proto_used=%hhu,avc_methods=%u,method_used =%hhu,confaddr_point =%hu\n",
		      pst_entity->uc_as_proto, pst_entity->uc_proto_used, pst_entity->ui_avc_methods, pst_entity->uc_method_used,
		      pst_entity->us_confaddr_point);
	hi_omci_debug("[INFO]:conf_state=%hhu,retrieve_prof=%hhu,prof_vers=%s\n",
		      pst_entity->uc_conf_state, pst_entity->uc_retrieve_prof, pst_entity->uc_prof_vers);

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR4)) {
		hi_omci_tapi_voip_cnfig_method_set(pst_entity->uc_method_used);
		if (0 != gui_omci_voip_flag) {
			i_ret = hi_omci_tapi_voip_conf_set(&ui_en);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			HI_OMCI_BIT_SET(gui_omci_cfgdata_mask, (16 - HI_OMCI_ATTR4));
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR2)) {
		if (0 != gui_omci_voip_flag) {
			i_ret = hi_omci_tapi_voip_type_get(&ui_proto_tmp);
			HI_OMCI_RET_CHECK(i_ret);

			if (ui_proto_tmp != ui_proto) {
				i_ret = hi_omci_tapi_voip_type_set(&ui_proto);
				HI_OMCI_RET_CHECK(i_ret);
			}
		} else {
			HI_OMCI_BIT_SET(gui_omci_cfgdata_mask, (16 - HI_OMCI_ATTR2));
		}
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_voip_cfg_data_get
 Description : VoIP config data
               在数据库操作前处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_cfg_data_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_config_data_s *pst_entity = (hi_omci_me_config_data_s *)pv_data;
	hi_omci_me_config_data_s st_entity;
	hi_uint32    i_ret = HI_RET_SUCC;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;
	hi_uint32 ui_proto = 0;

	if (0 != gui_omci_voip_flag) {
		hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, pst_entity->st_msghead.us_attmask);
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VOIP_CFG_DATA_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
		i_ret = hi_omci_tapi_voip_type_get(&ui_proto);
		HI_OMCI_RET_CHECK(i_ret);
		st_entity.uc_proto_used = (hi_uchar8)ui_proto;
		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_VOIP_CFG_DATA_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
