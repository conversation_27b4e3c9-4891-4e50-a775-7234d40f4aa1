/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_voip_proc.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
#include "igdGlobalTypeDef.h"
#include "igdCmMsgq.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
hi_uint32 gui_omci_voip_flag = 0;
static hi_uint32 g_omci_mibreset_flag = HI_FALSE;
hi_int32 gi_omci_voip_qid = -1;
hi_int32 g_omci_exit_flag = 0;
extern hi_uint32 gui_omci_iphost_mask[HI_OMCI_TAPI_EXTVLAN_TABLE_NUM];
extern hi_uint32 gui_omci_voip_extvlan_mask;
extern hi_uint32 gui_omci_pptppots_mask;
extern hi_uint32 gui_omci_cfgdata_mask;
extern hi_uint32 gui_omci_agentdata_mask;
extern hi_uint32 gui_omci_dialplan_mask;
extern hi_uint32 gui_omci_tr069_extvlan_mask;
extern hi_uint32 gui_omci_veip_mask;
extern hi_omci_tapi_voip_extvlan_s g_st_extvlan_table;
extern hi_uint32 gui_omci_tr069_management_mask;
extern hi_uint32 gui_omci_sipuser_mask;
typedef void *(*GLFUNCPTR)(void *);
pthread_t g_omci_msg = 0;

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
hi_int32 hi_omci_voiptask_create(pthread_t *task, hi_char8 *name, GLFUNCPTR taskEntry, hi_int32  argc, hi_void *argv,
				 hi_uchar8 priority, hi_uint32  stackSize)
{
	pthread_attr_t attr;
	pthread_attr_t *pattr = &attr;
	//struct sched_param schedParam;
	//struct sched_param *pSchedParam = &schedParam;

	pthread_attr_init(pattr);
	pthread_attr_setdetachstate(pattr, PTHREAD_CREATE_DETACHED);
	/* pthread_attr_setschedpolicy(pattr, SCHED_RR);
	pSchedParam->sched_priority = priority;
	pthread_attr_setschedparam(pattr, pSchedParam);
	pthread_attr_setstacksize(pattr, stackSize);*/

	if (pthread_create(task, pattr, taskEntry, argv) == 0) {
		pthread_attr_destroy(pattr);
		return HI_RET_SUCC;
	} else {
		pthread_attr_destroy(pattr);
		return -1;
	}
}

hi_uint32 ConfigWanExtvlan()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_ushort16 us_vlan = 0;
	hi_ushort16 us_pri = 0;
	hi_omci_tapi_voip_iphost_s st_tapi_iphost;

	if (0 != gui_omci_voip_extvlan_mask) {
		us_vlan = gui_omci_voip_extvlan_mask & 0xffff;
		us_pri = (gui_omci_voip_extvlan_mask >> 16) & 0xf;
		HI_OS_MEMSET_S(&st_tapi_iphost, sizeof(st_tapi_iphost), 0, sizeof(st_tapi_iphost));
		st_tapi_iphost.us_flag = 0;  // Voip wan
		if (0x1fff != us_vlan) {
			st_tapi_iphost.ui_vlan = us_vlan;
			st_tapi_iphost.ui_vlanen = 1;
		}
		st_tapi_iphost.us_8021p = us_pri;
		st_tapi_iphost.us_8021pen = 1;
		i_ret = hi_omci_tapi_voip_wan_set(&st_tapi_iphost);
		HI_OMCI_RET_CHECK(i_ret);
	}

	if (0 != gui_omci_tr069_extvlan_mask) {
		us_vlan = gui_omci_tr069_extvlan_mask & 0xffff;
		us_pri = (gui_omci_tr069_extvlan_mask >> 16) & 0xf;
		HI_OS_MEMSET_S(&st_tapi_iphost, sizeof(st_tapi_iphost), 0, sizeof(st_tapi_iphost));
		st_tapi_iphost.us_flag = 1; // Tr069 wan
		if (0x1fff != us_vlan) {
			st_tapi_iphost.ui_vlan = us_vlan;
			st_tapi_iphost.ui_vlanen = 1;
		}
		st_tapi_iphost.us_8021p = us_pri;
		st_tapi_iphost.us_8021pen = 1;

		i_ret = hi_omci_tapi_voip_wan_set(&st_tapi_iphost);
		HI_OMCI_RET_CHECK(i_ret);
	}
	return HI_RET_SUCC;
}

hi_uint32 ConfigIp()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index, ui_index1;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[HI_OMCI_TAPI_EXTVLAN_TABLE_NUM] = {0};
	hi_uint32 ui_outlen = 0;
	hi_uint32 flag = HI_FALSE;
	hi_omci_me_iphost_cfg_s st_iphost;

	for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
		if (gui_omci_iphost_mask[ui_index] != 0) {
			flag = HI_TRUE;
			break;
		}
	}
	if (flag == HI_FALSE) {
		return i_ret;
	}
	i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E, aui_instid, HI_OMCI_TAPI_EXTVLAN_TABLE_NUM,
					&ui_instnum);
	HI_OMCI_RET_CHECK(i_ret);

	if ((sizeof(aui_instid) / sizeof(hi_uint32)) < ui_instnum) {
		return HI_RET_FAIL;
	}
	for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
		for (ui_index1 = 0; ui_index1 < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index1++) {
			if (g_st_extvlan_table.st_extvlan[ui_index1].ui_index == aui_instid[ui_index]) {
				break;
			}
		}
		if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM == ui_index1) {
			continue;
		} else {
			if (0 == gui_omci_iphost_mask[ui_index1]) {
				continue;
			}
		}
		(void)memset_s(&st_iphost, sizeof(st_iphost), 0, sizeof(st_iphost));
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E, (hi_ushort16)aui_instid[ui_index], &st_iphost);
		HI_OMCI_RET_CHECK(i_ret);
		/* set ip mask for instid */
		st_iphost.st_msghead.us_instid = (hi_ushort16)aui_instid[ui_index];
		st_iphost.st_msghead.us_attmask = gui_omci_iphost_mask[ui_index1] & 0xffff;
		hi_omci_me_iphost_set(&st_iphost, sizeof(st_iphost), &ui_outlen);
	}
	return i_ret;
}

hi_uint32 ConfigPptpPots()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_pptp_pots_uni_s st_pptp;

	if (0 != gui_omci_pptppots_mask) {
		i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_PPTP_POTS_UNI_E, aui_instid, 4, &ui_instnum);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
			HI_OS_MEMSET_S(&st_pptp, sizeof(st_pptp), 0, sizeof(st_pptp));
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_PPTP_POTS_UNI_E, (hi_ushort16)aui_instid[ui_index], &st_pptp);
			HI_OMCI_RET_CHECK(i_ret);
			st_pptp.st_msghead.us_instid = (hi_ushort16)aui_instid[ui_index];
			st_pptp.st_msghead.us_attmask = gui_omci_pptppots_mask & 0xffff;
			hi_omci_me_pptp_pots_set(&st_pptp, sizeof(st_pptp), &ui_outlen);
		}
	}
	return i_ret;
}

hi_uint32 ConfigCfgData()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_config_data_s st_confdata;

	if (0 != gui_omci_cfgdata_mask) {
		i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_VOIP_CFG_DATA_E, aui_instid, 4, &ui_instnum);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
			HI_OS_MEMSET_S(&st_confdata, sizeof(st_confdata), 0, sizeof(st_confdata));
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VOIP_CFG_DATA_E, (hi_ushort16)aui_instid[ui_index], &st_confdata);
			HI_OMCI_RET_CHECK(i_ret);
			st_confdata.st_msghead.us_instid = (hi_ushort16)aui_instid[ui_index];
			st_confdata.st_msghead.us_attmask = gui_omci_cfgdata_mask & 0xffff;
			hi_omci_me_voip_cfg_data_set(&st_confdata, sizeof(st_confdata), &ui_outlen);
		}
	}
	return i_ret;
}

hi_uint32 ConfigAgentData()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_sip_agentdata_s st_agentdata;

	if (0 != gui_omci_agentdata_mask) {
		i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, aui_instid, 4, &ui_instnum);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
			HI_OS_MEMSET_S(&st_agentdata, sizeof(st_agentdata), 0, sizeof(st_agentdata));
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, (hi_ushort16)aui_instid[ui_index], &st_agentdata);
			HI_OMCI_RET_CHECK(i_ret);
			st_agentdata.st_msghead.us_instid = (hi_ushort16)aui_instid[ui_index];
			st_agentdata.st_msghead.us_attmask = gui_omci_agentdata_mask & 0xffff;
			hi_omci_me_sip_agent_data_set(&st_agentdata, sizeof(st_agentdata), &ui_outlen);
		}
	}
	return i_ret;
}

hi_uint32 ConfigVeip()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_veip_s st_veip;

	if (0 != gui_omci_veip_mask) {
		i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_VEIP_E, aui_instid, 4, &ui_instnum);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
			HI_OS_MEMSET_S(&st_veip, sizeof(st_veip), 0, sizeof(st_veip));
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VEIP_E, (hi_ushort16)aui_instid[ui_index], &st_veip);
			HI_OMCI_RET_CHECK(i_ret);
			st_veip.st_msghead.us_instid = (hi_ushort16)aui_instid[ui_index];
			st_veip.st_msghead.us_attmask = gui_omci_veip_mask & 0xffff;
			hi_omci_me_veip_set(&st_veip, sizeof(st_veip), &ui_outlen);
		}
	}
	return i_ret;
}

hi_uint32 CreatVoipDialPlan()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_dial_plan_s st_plan;

	i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_NETWORK_DIAL_PLAN_TAB_E, aui_instid, 4, &ui_instnum);
	HI_OMCI_RET_CHECK(i_ret);

	for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
		HI_OS_MEMSET_S(&st_plan, sizeof(st_plan), 0, sizeof(st_plan));
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_NETWORK_DIAL_PLAN_TAB_E, (hi_ushort16)aui_instid[ui_index], &st_plan);
		HI_OMCI_RET_CHECK(i_ret);
		hi_omci_me_voip_dial_plan_create(&st_plan, sizeof(st_plan), &ui_outlen);
	}
	return i_ret;
}

hi_uint32 CreatSipUserData()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_sip_userdata_s st_userdata;

	i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_SIP_USER_DATA_E, aui_instid, 4, &ui_instnum);
	HI_OMCI_RET_CHECK(i_ret);

	for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
		HI_OS_MEMSET_S(&st_userdata, sizeof(st_userdata), 0, sizeof(st_userdata));
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_USER_DATA_E, (hi_ushort16)aui_instid[ui_index], &st_userdata);
		HI_OMCI_RET_CHECK(i_ret);
		hi_omci_me_sip_user_data_create(&st_userdata, sizeof(st_userdata), &ui_outlen);
	}
	return i_ret;
}

hi_uint32 CreatAgData()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_sip_agentdata_s st_agentdata;

	i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, aui_instid, 4, &ui_instnum);
	HI_OMCI_RET_CHECK(i_ret);

	for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
		HI_OS_MEMSET_S(&st_agentdata, sizeof(st_agentdata), 0, sizeof(st_agentdata));
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, (hi_ushort16)aui_instid[ui_index], &st_agentdata);
		HI_OMCI_RET_CHECK(i_ret);
		hi_omci_me_sip_agent_data_create(&st_agentdata, sizeof(st_agentdata), &ui_outlen);
	}
	return i_ret;
}

hi_uint32 CreatVoipVoice()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_voip_voice_ctp_s st_ctp;

	i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_VOICE_CTP_E, aui_instid, 4, &ui_instnum);
	HI_OMCI_RET_CHECK(i_ret);

	for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
		HI_OS_MEMSET_S(&st_ctp, sizeof(st_ctp), 0, sizeof(st_ctp));
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VOICE_CTP_E, (hi_ushort16)aui_instid[ui_index], &st_ctp);
		HI_OMCI_RET_CHECK(i_ret);
		hi_omci_me_voip_voice_ctp_create(&st_ctp, sizeof(st_ctp), &ui_outlen);
	}
	return i_ret;
}

hi_uint32 ConfigDialPlan()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_dial_plan_s st_plan;

	if (0 != gui_omci_dialplan_mask) {
		i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_NETWORK_DIAL_PLAN_TAB_E, aui_instid, 4, &ui_instnum);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
			HI_OS_MEMSET_S(&st_plan, sizeof(st_plan), 0, sizeof(st_plan));
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_NETWORK_DIAL_PLAN_TAB_E, (hi_ushort16)aui_instid[ui_index], &st_plan);
			HI_OMCI_RET_CHECK(i_ret);
			st_plan.st_msghead.us_instid = (hi_ushort16)aui_instid[ui_index];
			st_plan.st_msghead.us_attmask = gui_omci_dialplan_mask & 0xffff;
			hi_omci_me_voip_dial_plan_set(&st_plan, sizeof(st_plan), &ui_outlen);
		}
	}
	return i_ret;
}

hi_uint32 ConfigTr069()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_tr069_mng_server_s st_tr069;

	if (0 != gui_omci_tr069_management_mask) {
		i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_TR069_MANAGEMENT_SERVER_E, aui_instid, 4, &ui_instnum);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
			HI_OS_MEMSET_S(&st_tr069, sizeof(st_tr069), 0, sizeof(st_tr069));
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TR069_MANAGEMENT_SERVER_E, (hi_ushort16)aui_instid[ui_index], &st_tr069);
			HI_OMCI_RET_CHECK(i_ret);
			st_tr069.st_msghead.us_instid = (hi_ushort16)aui_instid[ui_index];
			st_tr069.st_msghead.us_attmask = gui_omci_tr069_management_mask & 0xffff;
			hi_omci_me_tr069_management_set(&st_tr069, sizeof(st_tr069), &ui_outlen);
		}
	}
	return i_ret;
}

hi_uint32 ConfigSipuser()
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_instnum;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_outlen = 0;
	hi_omci_me_sip_userdata_s st_userdata;

	if (0 != gui_omci_sipuser_mask) {
		i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_SIP_USER_DATA_E, aui_instid, 4, &ui_instnum);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
			hi_os_memset(&st_userdata, 0, sizeof(st_userdata));
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_USER_DATA_E, (hi_ushort16)aui_instid[ui_index], &st_userdata);
			HI_OMCI_RET_CHECK(i_ret);
			st_userdata.st_msghead.us_instid = (hi_ushort16)aui_instid[ui_index];
			st_userdata.st_msghead.us_attmask = gui_omci_sipuser_mask & 0xffff;
			hi_omci_me_sip_user_data_set(&st_userdata, sizeof(st_userdata), &ui_outlen);
		}
	}
	return i_ret;
}

/*启动第一次，由于cm模块还在配置恢复，先保留voip配置，等cm模块配置恢复完成，再下发voip配置*/
static hi_int32 hi_omci_voip_proc()
{
	hi_uint32 i_ret = HI_RET_SUCC;

	ConfigWanExtvlan();
	ConfigIp();
	ConfigPptpPots();
	ConfigCfgData();
	ConfigAgentData();
	ConfigVeip();
	CreatVoipDialPlan();
	CreatSipUserData();
	CreatAgData();
	CreatVoipVoice();
	ConfigDialPlan();
	ConfigTr069();
	ConfigSipuser();

	return i_ret;
}

hi_void hi_omci_set_mibreset_flag(hi_uint32 flag)
{
	g_omci_mibreset_flag = flag;
}

static hi_void *hi_omci_voip_recv()
{
	CM_msg msg;
	hi_int32 i_ret;

	while (g_omci_exit_flag != 1) {
		HI_OS_MEMSET_S(&msg, sizeof(CM_msg), 0, sizeof(CM_msg));
		i_ret = msgrcv(gi_omci_voip_qid, &msg, sizeof(CM_msg) - sizeof(int), 0, 0);
		if (i_ret < 0) {
			sleep(1);
			continue;
		}
		if (msg.msg_type ==  CM_T_INIT_STEP) {
			gui_omci_voip_flag = 1;
			if (g_omci_mibreset_flag != HI_FALSE)
				hi_omci_voip_proc();
		}
		usleep(10000);
	}
	return NULL;
}
hi_int32 hi_omci_voip_msg()
{
	key_t st_key;

	/* Create unique key via call to ftok() */
	st_key = ftok(CM_T_OMCI_MSG_KEY, 'w');

	gi_omci_voip_qid = msgget(st_key, IPC_CREAT | 0666); /*创建消息队列*/
	if (gi_omci_voip_qid < 0) {
		return -1;
	}
	g_omci_exit_flag = 0;
	/* Create configuration thread */
	if (hi_omci_voiptask_create(&g_omci_msg, "VoipMsgConfigurationThread", (GLFUNCPTR) hi_omci_voip_recv, 0, 0, 80,
				    0x2800) != 0) {
		return -1;
	}
	return HI_RET_SUCC;
}

hi_void hi_omci_voip_msg_eixt()
{
	if (gi_omci_voip_qid >= 0) {
		msgctl(gi_omci_voip_qid, IPC_RMID, 0);
		gi_omci_voip_qid = -1;
	}

	if (g_omci_msg != 0) {
		g_omci_exit_flag = 1;
		g_omci_msg = 0;
	}
}
