/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_pptp_pots_uni.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
hi_uint32 gui_omci_pptppots_mask = 0;
hi_uint32 gui_pptppots_tstrslt_proc = HI_DISABLE;
extern hi_uint32 gui_omci_voip_flag;

enum hi_omci_me_pptp_pots_oper_state {
	HI_OMCI_ME_PPTP_POTS_OPER_ENABLE = 0,
	HI_OMCI_ME_PPTP_POTS_OPER_DISABLE,
};
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_me_onu2g_init
 Description : ONU2-G init
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_pptp_pots_init(hi_uint32 ui_pots)
{
	hi_omci_me_pptp_pots_uni_s st_entry;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)HI_OMCI_GET_INSTID_FORM_VOIP_PORTID(ui_pots);

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_PPTP_POTS_UNI_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;
	st_entry.uc_admini_state = 1;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_PPTP_POTS_UNI_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_pptp_pots_set
 Description : Physical path termination point POTS UNI set
               在数据库操作后处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_pptp_pots_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_pptp_pots_uni_s *pst_entity = (hi_omci_me_pptp_pots_uni_s *)pv_data;
	hi_omci_tapi_voip_adminstate_s st_state;
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;
	hi_omci_tapi_voip_gain_s st_gain = {0};
	hi_uint32    i_ret = HI_RET_SUCC;

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);
	hi_omci_debug("[INFO]:adminstate=%hhu,tppointer=%hu,arc=%hhu,arcintvl =%hhu,impedance =%hhu\n",
		      pst_entity->uc_admini_state, pst_entity->us_itp, pst_entity->uc_arc, pst_entity->uc_arc_interval,
		      pst_entity->uc_impedance);
	hi_omci_debug("[INFO]:transpath=%hhu,rxgain=%hhu,txgain=%hhu,operstate =%hhu,hookstate =%hhu,holdovertime =%hu\n",
		      pst_entity->uc_tras_path, pst_entity->uc_rx_gain, pst_entity->uc_tx_gain, pst_entity->uc_oper_state,
		      pst_entity->uc_hook_state, pst_entity->us_holdover_time);

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	//阻抗目前不可配置，cm不支持

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR7)) {
		if (0 != gui_omci_voip_flag) {
			st_gain.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_instid);
			i_ret = hi_omci_tapi_voip_gain_get(&st_gain);
			HI_OMCI_RET_CHECK(i_ret);
			st_gain.ui_rxgain = pst_entity->uc_rx_gain;
			i_ret = hi_omci_tapi_voip_gain_set(&st_gain);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			HI_OMCI_BIT_SET(gui_omci_pptppots_mask, (16 - HI_OMCI_ATTR7));
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR8)) {
		if (0 != gui_omci_voip_flag) {
			st_gain.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_instid);
			i_ret = hi_omci_tapi_voip_gain_get(&st_gain);
			HI_OMCI_RET_CHECK(i_ret);
			st_gain.ui_txgain = pst_entity->uc_tx_gain;
			i_ret = hi_omci_tapi_voip_gain_set(&st_gain);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			HI_OMCI_BIT_SET(gui_omci_pptppots_mask, (16 - HI_OMCI_ATTR8));
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR1)) {
		if (0 != gui_omci_voip_flag) {
			st_state.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_instid);
			st_state.ui_state = pst_entity->uc_admini_state;
			i_ret = hi_omci_tapi_voip_adminstate_set(&st_state);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			HI_OMCI_BIT_SET(gui_omci_pptppots_mask, (16 - HI_OMCI_ATTR1));
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_pptp_pots_get
 Description : Physical path termination point POTS UNI get
               在数据库操作前处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_pptp_pots_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_pptp_pots_uni_s *pst_entity = (hi_omci_me_pptp_pots_uni_s *)pv_data;
	hi_omci_me_pptp_pots_uni_s st_entity;
	hi_omci_tapi_voip_hookstatus_s st_info;
	hi_omci_tapi_voip_adminstate_s st_state;
	hi_omci_tapi_voip_gain_s gain = {0};
	hi_uint32    i_ret = HI_RET_SUCC, ui_conf = 0;
	hi_ushort16 us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	if (0 != gui_omci_voip_flag) {
		us_instid  = pst_entity->st_msghead.us_instid;

		i_ret = hi_omci_tapi_voip_conf_get(&ui_conf);
		HI_OMCI_RET_CHECK(i_ret);
		if (0 == ui_conf) {
			hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, pst_entity->st_msghead.us_attmask);
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_PPTP_POTS_UNI_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);

		HI_OS_MEMSET_S(&gain, sizeof(gain), 0, sizeof(gain));
		gain.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_instid);
		i_ret = hi_omci_tapi_voip_gain_get(&gain);
		HI_OMCI_RET_CHECK(i_ret);
		st_entity.uc_tx_gain = (uint8_t)gain.ui_txgain;
		st_entity.uc_rx_gain = (uint8_t)gain.ui_rxgain;

		HI_OS_MEMSET_S(&st_info, sizeof(st_info), 0, sizeof(st_info));
		st_info.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_instid);
		i_ret = hi_omci_tapi_voip_hookstatus_get(&st_info);
		HI_OMCI_RET_CHECK(i_ret);
		st_entity.uc_hook_state = (hi_uchar8)st_info.ui_status;

		HI_OS_MEMSET_S(&st_state, sizeof(st_state), 0, sizeof(st_state));
		st_state.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_instid);
		i_ret = hi_omci_tapi_voip_adminstate_get(&st_state);
		HI_OMCI_RET_CHECK(i_ret);
		st_entity.uc_admini_state = (hi_uchar8)st_state.ui_state;

		st_entity.uc_oper_state = (st_entity.uc_admini_state == HI_OMCI_TAPI_VOIP_ADMIN_UNLOCK) ?
					  HI_OMCI_ME_PPTP_POTS_OPER_ENABLE : HI_OMCI_ME_PPTP_POTS_OPER_DISABLE;
		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_PPTP_POTS_UNI_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_pptp_pots_test
 Description :
               数据库操作后处理
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_pptp_pots_test(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_ushort16 us_meid;
	hi_uchar8 *puc_data = HI_NULL;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	us_meid = (hi_ushort16)HI_OMCI_PROC_GET_ENTITY((hi_uchar8 *)pv_data);
	puc_data = (hi_uchar8 *)((hi_uchar8 *)pv_data + 8);
	if ((HI_OMCI_PRO_ME_PPTP_POTS_UNI_E == us_meid) && (0x7 == *puc_data)) {
		gui_pptppots_tstrslt_proc = HI_ENABLE;
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
