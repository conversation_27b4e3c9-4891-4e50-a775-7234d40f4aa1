/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_call_control_pm.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_omci_tapi_voip_callctrlpm_s gst_callctrlpm_history;
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_call_control_stat_get
 Description :
 Input Parm  : hi_void *pv_data       历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_call_control_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_me_call_control_pm_s st_entity;
	hi_omci_tapi_voip_callctrlpm_s st_callctrlpm_curr;
	//hi_uint32 ui_time = 0;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	/* 读取数据库的control block */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_CALL_CTRL_PMH_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_voip_callctrlpm_get(&st_callctrlpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_interval++ ;
	st_entity.ui_callsetup_fail = (st_callctrlpm_curr.ui_callsetup_fail > gst_callctrlpm_history.ui_callsetup_fail) ?
				      (st_callctrlpm_curr.ui_callsetup_fail - gst_callctrlpm_history.ui_callsetup_fail) : 0;
	st_entity.ui_callsetup_timer = st_callctrlpm_curr.ui_callsetup_timer;
	st_entity.ui_callterm_fail = (st_callctrlpm_curr.ui_callterm_fail > gst_callctrlpm_history.ui_callterm_fail) ?
				     (st_callctrlpm_curr.ui_callterm_fail - gst_callctrlpm_history.ui_callterm_fail) : 0;
	st_entity.ui_analogport_releases = (st_callctrlpm_curr.ui_analogport_releases >
					    gst_callctrlpm_history.ui_analogport_releases) ? (st_callctrlpm_curr.ui_analogport_releases -
							    gst_callctrlpm_history.ui_analogport_releases) : 0;
	st_entity.ui_analogport_offhooktimer = st_callctrlpm_curr.ui_analogport_offhooktimer;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_CALL_CTRL_PMH_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	/* 更新历史统计 */
	//hi_os_memcpy(&gst_callctrlpm_history, &st_callctrlpm_curr, sizeof(gst_callctrlpm_history));

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_call_control_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_call_control_pm_s *pst_timer = (hi_omci_me_call_control_pm_s *)pv_data;
	hi_omci_me_call_control_pm_s st_entity;
	hi_omci_tapi_voip_callctrlpm_s st_callctrlpm_curr;
	//hi_uint32 ui_time = 0;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_timer->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	/* 读取数据库的control block */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_CALL_CTRL_PMH_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_voip_callctrlpm_get(&st_callctrlpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.ui_callsetup_fail = (st_callctrlpm_curr.ui_callsetup_fail > gst_callctrlpm_history.ui_callsetup_fail) ?
				      (st_callctrlpm_curr.ui_callsetup_fail - gst_callctrlpm_history.ui_callsetup_fail) : 0;
	st_entity.ui_callsetup_timer = st_callctrlpm_curr.ui_callsetup_timer;
	st_entity.ui_callterm_fail = (st_callctrlpm_curr.ui_callterm_fail > gst_callctrlpm_history.ui_callterm_fail) ?
				     (st_callctrlpm_curr.ui_callterm_fail - gst_callctrlpm_history.ui_callterm_fail) : 0;
	st_entity.ui_analogport_releases = (st_callctrlpm_curr.ui_analogport_releases >
					    gst_callctrlpm_history.ui_analogport_releases) ? (st_callctrlpm_curr.ui_analogport_releases -
							    gst_callctrlpm_history.ui_analogport_releases) : 0;
	st_entity.ui_analogport_offhooktimer = st_callctrlpm_curr.ui_analogport_offhooktimer;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_CALL_CTRL_PMH_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_me_call_control_create
 Description : Call control PM history data  create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_call_control_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_call_control_pm_s *pst_entry = (hi_omci_me_call_control_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_callctrlpm_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		hi_omci_ext_del_inst(pst_entry->st_msghead.us_meid, pst_entry->st_msghead.us_instid);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&gst_callctrlpm_history, sizeof(gst_callctrlpm_history), 0, sizeof(gst_callctrlpm_history));

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_callctrlpm_stat_timer, sizeof(st_callctrlpm_stat_timer), 0, sizeof(st_callctrlpm_stat_timer));
	st_callctrlpm_stat_timer.ui_meid = HI_OMCI_PRO_ME_CALL_CTRL_PMH_DATA_E;
	st_callctrlpm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_callctrlpm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_call_control_pm_delete
 Description : Call control PM history data delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_call_control_pm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_call_control_pm_s *pst_entry = (hi_omci_me_call_control_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_callctrlpm_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&st_callctrlpm_stat_timer, sizeof(st_callctrlpm_stat_timer), 0, sizeof(st_callctrlpm_stat_timer));
	st_callctrlpm_stat_timer.ui_meid = HI_OMCI_PRO_ME_CALL_CTRL_PMH_DATA_E;
	st_callctrlpm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_callctrlpm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
