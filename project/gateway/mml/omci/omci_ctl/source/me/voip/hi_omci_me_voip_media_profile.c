/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_voip_media_profile.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
extern hi_uint32 gui_omci_voip_flag;

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/******************************************************************************
Function    : hi_omci_me_voip_media_profile_set
Description : VoIP media profile
              在数据库操作后处理
Input Parm  : hi_void*pv_data
              hi_uint32 ui_in
Output Parm : hi_uint32 *pui_outlen
Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_media_profile_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_media_s *pst_entity = (hi_omci_me_media_s *)pv_data;
	hi_omci_tapi_voip_codec_s st_codec;
	hi_omci_tapi_voip_silence_s st_tapi_silen;
	hi_omci_me_voip_voice_ctp_s st_ctp;
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid, us_vmp = 0;
	hi_uint32    i_ret = HI_RET_SUCC;
	hi_uint32 ui_instnum = 0;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_index = 0;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);

	hi_omci_debug("[INFO]:fax=%hhu,voice poi=%s,crit codec1=%hhu,park1=%hhu,silence1=%hhu,crit codec2=%hhu,park2=%hhu,silence2=%hhu,crit codec3=%hhu,park3=%hhu,silence3=%hhu,crit codec4=%hhu,park4=%hhu,silence4=%hhu,oob=%hhu,rtp=%s\n",
		      \
		      pst_entity->uc_fax_mode, pst_entity->uc_vsp_pointer, pst_entity->uc_codec_selec1, pst_entity->uc_packperi_selec1,
		      pst_entity->uc_silen_suppre1, pst_entity->uc_codec_selec2,
		      pst_entity->uc_packperi_selec2, \
		      pst_entity->uc_silen_suppre2, pst_entity->uc_codec_selec3, pst_entity->uc_packperi_selec3, pst_entity->uc_silen_suppre3,
		      pst_entity->uc_codec_selec4, pst_entity->uc_packperi_selec4,
		      pst_entity->uc_silen_suppre4, \
		      pst_entity->uc_oob_dtmf, pst_entity->uc_rtp_pointer);

	if (0 == gui_omci_voip_flag) {
		return HI_RET_SUCC;
	}
	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR1)) {
		i_ret = hi_omci_tapi_voip_faxmode_set((hi_uint32 *)(&pst_entity->uc_fax_mode));
		HI_OMCI_RET_CHECK(i_ret);
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR3)) {
		HI_OS_MEMSET_S(&st_codec, sizeof(hi_omci_tapi_voip_codec_s), 0, sizeof(hi_omci_tapi_voip_codec_s));
		st_codec.uc_entryid = 1;
		i_ret = hi_omci_tapi_voip_codec_get(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
		st_codec.uc_codec = pst_entity->uc_codec_selec1;
		st_codec.uc_pri = 1;
		i_ret = hi_omci_tapi_voip_codec_set(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR4)) {
		HI_OS_MEMSET_S(&st_codec, sizeof(hi_omci_tapi_voip_codec_s), 0, sizeof(hi_omci_tapi_voip_codec_s));
		st_codec.uc_entryid = 1;
		i_ret = hi_omci_tapi_voip_codec_get(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
		st_codec.uc_packperi = pst_entity->uc_packperi_selec1;
		i_ret = hi_omci_tapi_voip_codec_set(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR5)) {
		i_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_VOICE_CTP_E, aui_instid, 4, &ui_instnum);
		HI_OMCI_RET_CHECK(i_ret);

		st_tapi_silen.ui_lineidx = 1;
		for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
			HI_OS_MEMSET_S(&st_ctp, sizeof(st_ctp), 0, sizeof(st_ctp));
			i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VOICE_CTP_E, (hi_ushort16)aui_instid[ui_index], &st_ctp);
			HI_OMCI_RET_CHECK(i_ret);
			HI_OS_MEMCPY_S(&us_vmp, sizeof(us_vmp), st_ctp.uc_vmp_pointer, sizeof(hi_short16));
			if (us_vmp == us_instid) {
				st_tapi_silen.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(st_ctp.us_pptp_pointer);
			}
		}
		st_tapi_silen.ui_silence_en = pst_entity->uc_silen_suppre1 ;
		i_ret = hi_omci_tapi_voip_silence_set(&st_tapi_silen);
		HI_OMCI_RET_CHECK(i_ret);
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR6)) {
		HI_OS_MEMSET_S(&st_codec, sizeof(hi_omci_tapi_voip_codec_s), 0, sizeof(hi_omci_tapi_voip_codec_s));
		st_codec.uc_entryid = 2;
		i_ret = hi_omci_tapi_voip_codec_get(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
		st_codec.uc_codec = pst_entity->uc_codec_selec2;
		st_codec.uc_pri = 2;
		i_ret = hi_omci_tapi_voip_codec_set(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR7)) {
		HI_OS_MEMSET_S(&st_codec, sizeof(hi_omci_tapi_voip_codec_s), 0, sizeof(hi_omci_tapi_voip_codec_s));
		st_codec.uc_entryid = 2;
		i_ret = hi_omci_tapi_voip_codec_get(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
		st_codec.uc_packperi = pst_entity->uc_packperi_selec2;
		i_ret = hi_omci_tapi_voip_codec_set(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR9)) {
		HI_OS_MEMSET_S(&st_codec, sizeof(hi_omci_tapi_voip_codec_s), 0, sizeof(hi_omci_tapi_voip_codec_s));
		st_codec.uc_entryid = 3;
		i_ret = hi_omci_tapi_voip_codec_get(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
		st_codec.uc_codec = pst_entity->uc_codec_selec3;
		st_codec.uc_pri = 3;
		i_ret = hi_omci_tapi_voip_codec_set(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR10)) {
		HI_OS_MEMSET_S(&st_codec, sizeof(hi_omci_tapi_voip_codec_s), 0, sizeof(hi_omci_tapi_voip_codec_s));
		st_codec.uc_entryid = 3;
		i_ret = hi_omci_tapi_voip_codec_get(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
		st_codec.uc_packperi = pst_entity->uc_packperi_selec3;
		i_ret = hi_omci_tapi_voip_codec_set(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR12)) {
		HI_OS_MEMSET_S(&st_codec, sizeof(hi_omci_tapi_voip_codec_s), 0, sizeof(hi_omci_tapi_voip_codec_s));
		st_codec.uc_entryid = 4;
		i_ret = hi_omci_tapi_voip_codec_get(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
		st_codec.uc_codec = pst_entity->uc_codec_selec4;
		st_codec.uc_pri = 4;
		i_ret = hi_omci_tapi_voip_codec_set(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR13)) {
		HI_OS_MEMSET_S(&st_codec, sizeof(hi_omci_tapi_voip_codec_s), 0, sizeof(hi_omci_tapi_voip_codec_s));
		st_codec.uc_entryid = 4;
		i_ret = hi_omci_tapi_voip_codec_get(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
		st_codec.uc_packperi = pst_entity->uc_packperi_selec4;
		i_ret = hi_omci_tapi_voip_codec_set(&st_codec);
		HI_OMCI_RET_CHECK(i_ret);
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_voip_media_profile_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_debug("[%s][%d]\n", __FUNCTION__, __LINE__);

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_voip_media_profile_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_media_s *pst_entity = (hi_omci_me_media_s *)pv_data;
	hi_uint32 ui_outlen = 0;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		hi_omci_ext_del_inst(pst_entity->st_msghead.us_meid, pst_entity->st_msghead.us_instid);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	if (0 != gui_omci_voip_flag) {
		pst_entity->st_msghead.us_attmask = 0xffff;
		hi_omci_me_voip_media_profile_set(pst_entity, sizeof(hi_omci_me_media_s), &ui_outlen);
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
