/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_sip_agent_pm.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_omci_tapi_voip_sip_agentpm_s gst_sippm_history;
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_sip_agentpm_stat_get
 Description :
 Input Parm  : hi_void *pv_data       历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_sip_agentpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_me_sipagent_pm_s st_entity;
	hi_omci_tapi_voip_sip_agentpm_s st_sippm_curr;
	//hi_uint32 ui_time = 0;
	hi_int32 i_ret = 0;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	HI_OS_MEMSET_S(&st_sippm_curr, sizeof(st_sippm_curr), 0, sizeof(st_sippm_curr));

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	/* 读取数据库的control block */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_PM_HISTORY_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_voip_sip_agentpm_get(&st_sippm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_interval++;
	st_entity.ui_transactions = (st_sippm_curr.ui_initiated_cnt > gst_sippm_history.ui_initiated_cnt) ?
				    (st_sippm_curr.ui_initiated_cnt - gst_sippm_history.ui_initiated_cnt) : 0;
	st_entity.ui_rx_invite_reqs = (st_sippm_curr.ui_rx_invite_cnt > gst_sippm_history.ui_rx_invite_cnt) ?
				      (st_sippm_curr.ui_rx_invite_cnt - gst_sippm_history.ui_rx_invite_cnt) : 0;
	st_entity.ui_rx_invite_retrans = (st_sippm_curr.ui_rx_reinvite_cnt > gst_sippm_history.ui_rx_reinvite_cnt) ?
					 (st_sippm_curr.ui_rx_reinvite_cnt - gst_sippm_history.ui_rx_reinvite_cnt) : 0;
	st_entity.ui_rx_noninvite_reqs = (st_sippm_curr.ui_rx_noinvite_cnt > gst_sippm_history.ui_rx_noinvite_cnt) ?
					 (st_sippm_curr.ui_rx_noinvite_cnt - gst_sippm_history.ui_rx_noinvite_cnt) : 0;
	st_entity.ui_rx_noninvite_retrans = (st_sippm_curr.ui_rx_renoinvite_cnt > gst_sippm_history.ui_rx_renoinvite_cnt) ?
					    (st_sippm_curr.ui_rx_renoinvite_cnt - gst_sippm_history.ui_rx_renoinvite_cnt) : 0;
	st_entity.ui_rx_response = (st_sippm_curr.ui_rx_response_cnt > gst_sippm_history.ui_rx_response_cnt) ?
				   (st_sippm_curr.ui_rx_response_cnt - gst_sippm_history.ui_rx_response_cnt) : 0;
	st_entity.ui_rx_response_retrans = (st_sippm_curr.ui_rx_reresponse_cnt > gst_sippm_history.ui_rx_reresponse_cnt) ?
					   (st_sippm_curr.ui_rx_reresponse_cnt - gst_sippm_history.ui_rx_reresponse_cnt) : 0;
	st_entity.ui_tx_invite_reqs = (st_sippm_curr.ui_tx_invite_cnt > gst_sippm_history.ui_tx_invite_cnt) ?
				      (st_sippm_curr.ui_tx_invite_cnt - gst_sippm_history.ui_tx_invite_cnt) : 0;
	st_entity.ui_tx_invite_retrans = (st_sippm_curr.ui_tx_reinvite_cnt > gst_sippm_history.ui_tx_reinvite_cnt) ?
					 (st_sippm_curr.ui_tx_reinvite_cnt - gst_sippm_history.ui_tx_reinvite_cnt) : 0;
	st_entity.ui_tx_noninvite_reqs = (st_sippm_curr.ui_tx_noinvite_cnt > gst_sippm_history.ui_tx_noinvite_cnt) ?
					 (st_sippm_curr.ui_tx_noinvite_cnt - gst_sippm_history.ui_tx_noinvite_cnt) : 0;
	st_entity.ui_tx_noninvite_retrans = (st_sippm_curr.ui_tx_renoinvite_cnt > gst_sippm_history.ui_tx_renoinvite_cnt) ?
					    (st_sippm_curr.ui_tx_renoinvite_cnt - gst_sippm_history.ui_tx_renoinvite_cnt) : 0;
	st_entity.ui_tx_response = (st_sippm_curr.ui_tx_response_cnt > gst_sippm_history.ui_tx_response_cnt) ?
				   (st_sippm_curr.ui_tx_response_cnt - gst_sippm_history.ui_tx_response_cnt) : 0;
	st_entity.ui_tx_response_retrans = (st_sippm_curr.ui_tx_reresponse_cnt > gst_sippm_history.ui_tx_reresponse_cnt) ?
					   (st_sippm_curr.ui_tx_reresponse_cnt - gst_sippm_history.ui_tx_reresponse_cnt) : 0;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SIP_AGENT_PM_HISTORY_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	/* 更新历史统计 */
	//HI_OS_MEMCPY_S(&gst_sippm_history, sizeof(gst_sippm_history), &st_sippm_curr, sizeof(gst_sippm_history));

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_sip_agentpm_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_sipagent_pm_s *pst_timer = (hi_omci_me_sipagent_pm_s *)pv_data;
	hi_omci_me_sipagent_pm_s st_entity;
	hi_omci_tapi_voip_sip_agentpm_s st_sippm_curr;
	//hi_uint32 ui_time = 0;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_timer->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	/* 读取数据库的control block */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_PM_HISTORY_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_voip_sip_agentpm_get(&st_sippm_curr);
	HI_OMCI_RET_CHECK(i_ret);
	st_entity.ui_transactions = (st_sippm_curr.ui_initiated_cnt > gst_sippm_history.ui_initiated_cnt) ?
				    (st_sippm_curr.ui_initiated_cnt - gst_sippm_history.ui_initiated_cnt) : 0;
	st_entity.ui_rx_invite_reqs = (st_sippm_curr.ui_rx_invite_cnt > gst_sippm_history.ui_rx_invite_cnt) ?
				      (st_sippm_curr.ui_rx_invite_cnt - gst_sippm_history.ui_rx_invite_cnt) : 0;
	st_entity.ui_rx_invite_retrans = (st_sippm_curr.ui_rx_reinvite_cnt > gst_sippm_history.ui_rx_reinvite_cnt) ?
					 (st_sippm_curr.ui_rx_reinvite_cnt - gst_sippm_history.ui_rx_reinvite_cnt) : 0;
	st_entity.ui_rx_noninvite_reqs = (st_sippm_curr.ui_rx_noinvite_cnt > gst_sippm_history.ui_rx_noinvite_cnt) ?
					 (st_sippm_curr.ui_rx_noinvite_cnt - gst_sippm_history.ui_rx_noinvite_cnt) : 0;
	st_entity.ui_rx_noninvite_retrans = (st_sippm_curr.ui_rx_renoinvite_cnt > gst_sippm_history.ui_rx_renoinvite_cnt) ?
					    (st_sippm_curr.ui_rx_renoinvite_cnt - gst_sippm_history.ui_rx_renoinvite_cnt) : 0;
	st_entity.ui_rx_response = (st_sippm_curr.ui_rx_response_cnt > gst_sippm_history.ui_rx_response_cnt) ?
				   (st_sippm_curr.ui_rx_response_cnt - gst_sippm_history.ui_rx_response_cnt) : 0;
	st_entity.ui_rx_response_retrans = (st_sippm_curr.ui_rx_reresponse_cnt > gst_sippm_history.ui_rx_reresponse_cnt) ?
					   (st_sippm_curr.ui_rx_reresponse_cnt - gst_sippm_history.ui_rx_reresponse_cnt) : 0;
	st_entity.ui_tx_invite_reqs = (st_sippm_curr.ui_tx_invite_cnt > gst_sippm_history.ui_tx_invite_cnt) ?
				      (st_sippm_curr.ui_tx_invite_cnt - gst_sippm_history.ui_tx_invite_cnt) : 0;
	st_entity.ui_tx_invite_retrans = (st_sippm_curr.ui_tx_reinvite_cnt > gst_sippm_history.ui_tx_reinvite_cnt) ?
					 (st_sippm_curr.ui_tx_reinvite_cnt - gst_sippm_history.ui_tx_reinvite_cnt) : 0;
	st_entity.ui_tx_noninvite_reqs = (st_sippm_curr.ui_tx_noinvite_cnt > gst_sippm_history.ui_tx_noinvite_cnt) ?
					 (st_sippm_curr.ui_tx_noinvite_cnt - gst_sippm_history.ui_tx_noinvite_cnt) : 0;
	st_entity.ui_tx_noninvite_retrans = (st_sippm_curr.ui_tx_renoinvite_cnt > gst_sippm_history.ui_tx_renoinvite_cnt) ?
					    (st_sippm_curr.ui_tx_renoinvite_cnt - gst_sippm_history.ui_tx_renoinvite_cnt) : 0;
	st_entity.ui_tx_response = (st_sippm_curr.ui_tx_response_cnt > gst_sippm_history.ui_tx_response_cnt) ?
				   (st_sippm_curr.ui_tx_response_cnt - gst_sippm_history.ui_tx_response_cnt) : 0;
	st_entity.ui_tx_response_retrans = (st_sippm_curr.ui_tx_reresponse_cnt > gst_sippm_history.ui_tx_reresponse_cnt) ?
					   (st_sippm_curr.ui_tx_reresponse_cnt - gst_sippm_history.ui_tx_reresponse_cnt) : 0;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SIP_AGENT_PM_HISTORY_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_me_sip_agentpm_create
 Description : SIP agent history data create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_sip_agentpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_sipagent_pm_s *pst_entry = (hi_omci_me_sipagent_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_sippm_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		hi_omci_ext_del_inst(pst_entry->st_msghead.us_meid, pst_entry->st_msghead.us_instid);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&gst_sippm_history, sizeof(gst_sippm_history), 0, sizeof(gst_sippm_history));

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_sippm_stat_timer, sizeof(st_sippm_stat_timer), 0, sizeof(st_sippm_stat_timer));
	st_sippm_stat_timer.ui_meid = HI_OMCI_PRO_ME_SIP_AGENT_PM_HISTORY_DATA_E;
	st_sippm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_sippm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_sip_agentpm_delete
 Description : SIP agent history data delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_sip_agentpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_sipagent_pm_s *pst_entry = (hi_omci_me_sipagent_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_sippm_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&st_sippm_stat_timer, sizeof(st_sippm_stat_timer), 0, sizeof(st_sippm_stat_timer));
	st_sippm_stat_timer.ui_meid = HI_OMCI_PRO_ME_SIP_AGENT_PM_HISTORY_DATA_E;
	st_sippm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_sippm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
