/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_voip_dial_plan.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
hi_uint32 ui_offhook_time = 0;
hi_uint32 gui_omci_dialplan_mask = 0;
extern hi_uint32 gui_omci_voip_flag;
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/******************************************************************************
Function    : hi_omci_me_voip_dial_plan_set
Description : Network dial plan table
              在数据库操作后处理
Input Parm  : hi_void*pv_data
              hi_uint32 ui_in
Output Parm : hi_uint32 *pui_outlen
Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_dial_plan_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_dial_plan_s *pst_entity = (hi_omci_me_dial_plan_s *)pv_data;
	hi_omci_tapi_voip_digitmap_s st_map;
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;
	hi_uint32    i_ret = HI_RET_SUCC;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", pst_entity->st_msghead.us_instid, us_mask);

	hi_omci_debug("[INFO]:dialplan num=%hu,dialplan tblsize=%hu,crit timeout=%hu,part timeout=%hu,dialplan form=%hhu\n",
		      pst_entity->us_diaplan_num, \
		      pst_entity->us_diaplan_max, pst_entity->us_cridial_timeout, pst_entity->us_pardial_timeout,
		      pst_entity->uc_diaplan_form);

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR6)) {
		if (0 != gui_omci_voip_flag) {
			HI_OS_MEMCPY_S(st_map.auc_digitmap, sizeof(st_map.auc_digitmap), &pst_entity->st_diaplantable,
				       HI_OMCI_TAPI_VOIP_DIGITMAP_LEN);
			i_ret = hi_omci_tapi_voip_digitmap_set(&st_map);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			HI_OMCI_BIT_SET(gui_omci_dialplan_mask, (16 - HI_OMCI_ATTR6));
		}
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_me_voip_dial_plan_create
 Description : Network dial plan table create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_dial_plan_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_dial_plan_s *pst_entry = (hi_omci_me_dial_plan_s *)pv_data;
	hi_int32 i_ret;
	hi_uint32 ui_time;
	//hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		hi_omci_ext_del_inst(pst_entry->st_msghead.us_meid, pst_entry->st_msghead.us_instid);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	if (0 != gui_omci_voip_flag) {
		i_ret = hi_omci_tapi_voip_offhooktime_get(&ui_time);
		HI_OMCI_RET_CHECK(i_ret);
		ui_offhook_time = ui_time;
		ui_time = pst_entry->us_pardial_timeout / 1000;
		i_ret = hi_omci_tapi_voip_offhooktime_set(&ui_time);
		HI_OMCI_RET_CHECK(i_ret)
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_voip_voice_ctp_delete
 Description : Network dial plan table delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_dial_plan_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	//hi_omci_me_voip_voice_ctp_s *pst_entry = (hi_omci_me_voip_voice_ctp_s *)pv_data;
	hi_int32 i_ret;
	hi_uint32 ui_time = ui_offhook_time;
	//hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	if (0 != gui_omci_voip_flag) {
		i_ret = hi_omci_tapi_voip_offhooktime_set(&ui_time);
		HI_OMCI_RET_CHECK(i_ret)
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
