/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_voip_rtp.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
hi_uint32 ui_rtp_dscp = 0;

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_me_voip_rtp_create
 Description : RTP profile data create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_rtp_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_rtp_s *pst_entry = (hi_omci_me_rtp_s *)pv_data;
	hi_int32 i_ret;
	hi_omci_tapi_voip_rtp_s st_dscp;
	//hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		hi_omci_ext_del_inst(pst_entry->st_msghead.us_meid, pst_entry->st_msghead.us_instid);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&st_dscp, sizeof(st_dscp), 0, sizeof(st_dscp));
	i_ret = hi_omci_tapi_voip_rtp_get(&st_dscp);
	HI_OMCI_RET_CHECK(i_ret);
	ui_rtp_dscp = st_dscp.ui_dscp;
	st_dscp.ui_dscp = pst_entry->uc_dscpmark;
	i_ret = hi_omci_tapi_voip_rtp_set(&st_dscp);
	HI_OMCI_RET_CHECK(i_ret)
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_voip_rtp_delete
 Description : RTP profile data table delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_voip_rtp_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	//hi_omci_me_rtp_s *pst_entry = (hi_omci_me_rtp_s *)pv_data;
	hi_int32 i_ret;
	hi_omci_tapi_voip_rtp_s st_dscp;
	//hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	HI_OS_MEMSET_S(&st_dscp, sizeof(st_dscp), 0, sizeof(st_dscp));

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	i_ret = hi_omci_tapi_voip_rtp_get(&st_dscp);
	HI_OMCI_RET_CHECK(i_ret);
	st_dscp.ui_dscp = ui_rtp_dscp;
	i_ret = hi_omci_tapi_voip_rtp_set(&st_dscp);
	HI_OMCI_RET_CHECK(i_ret)

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
