/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_sip_user_data.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
extern hi_uint32 gui_omci_voip_flag;
hi_uint32 gui_omci_sipuser_mask = 0;
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/******************************************************************************
Function    : hi_omci_me_sip_user_data_set
Description : SIP user data set
                    在数据库操作后处理
Input Parm  : hi_void*pv_data
              hi_uint32 ui_in
Output Parm : hi_uint32 *pui_outlen
Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_sip_user_data_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32    i_ret = HI_RET_SUCC;
	hi_omci_me_sip_userdata_s *pst_entity = pv_data;
	hi_omci_me_sip_userdata_s st_userdata = {{0}};
	hi_omci_me_auth_secur_method_s st_auth = {{0}};
	hi_omci_me_large_string_s st_string = {{0}};
	hi_omci_tapi_voip_auth_s st_tapiauth = {0};
	hi_omci_tapi_voip_adminstate_s st_state = {0};
	hi_ushort16 us_instid;
	hi_ushort16 us_mask;
	hi_ushort16 us_pptp_instid = 0;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	us_instid  = pst_entity->st_msghead.us_instid;
	us_mask    = pst_entity->st_msghead.us_attmask;

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);

	hi_omci_debug("[INFO]:sa_pointer=%hu,up_aor=%hu,sd_name=%s,username =%hu,vss_uri =%hu,vse_time =%u\n",
		      pst_entity->us_sa_pointer, pst_entity->us_up_aor, pst_entity->uc_sd_name, pst_entity->us_username,
		      pst_entity->us_vss_uri, pst_entity->ui_vse_time);
	hi_omci_debug("[INFO]:ndp_pointer=%hu,asp_pointer=%hu,fc_pointer=%hu,pptp_pointer=%hu,release_timer=%hhu,\
			  roh_timer =%hhu\n", pst_entity->us_ndp_pointer, pst_entity->us_asp_pointer, pst_entity->us_fc_pointer,
			  pst_entity->us_pptp_pointer, pst_entity->uc_release_timer, pst_entity->uc_roh_timer);

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR4)) {
		if (0 != gui_omci_voip_flag) {
			us_pptp_instid = pst_entity->us_pptp_pointer;
			if ((0xffff == us_pptp_instid) || (0 == us_pptp_instid)) {
				if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_USER_DATA_E, us_instid, &st_userdata)) {
					us_pptp_instid = st_userdata.us_pptp_pointer;
				} else {
					us_pptp_instid = HI_OMCI_GET_INSTID_FORM_VOIP_PORTID(1);
				}
			}
			HI_OS_MEMSET_S(&st_tapiauth, sizeof(st_tapiauth), 0, sizeof(st_tapiauth));
			if (HI_RET_SUCC ==
			    hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ATH_SECR_METHOD_E, pst_entity->us_username, &st_auth)) {
				if (HI_RET_SUCC ==
				    hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, pst_entity->us_up_aor, &st_string)) {
					HI_OS_STRCAT_S((hi_char8 *)st_tapiauth.uc_number, sizeof(st_tapiauth.uc_number),
						       (hi_char8 *)st_string.uc_part[0]);
				}
				st_tapiauth.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
				HI_OS_MEMCPY_S(st_tapiauth.uc_username, sizeof(st_tapiauth.uc_username), st_auth.uc_username1,
					       HI_OMCI_VOIP_STP_LEN);
				HI_OS_STRCAT_S((hi_char8 *)st_tapiauth.uc_username, sizeof(st_tapiauth.uc_username),
					       (hi_char8 *)st_auth.uc_username2);
				HI_OS_MEMCPY_S(st_tapiauth.uc_password, sizeof(st_tapiauth.uc_password), st_auth.uc_password,
					       HI_OMCI_VOIP_STP_LEN);

				i_ret = hi_omci_tapi_voip_sipauth_set(&st_tapiauth);
				HI_OMCI_RET_CHECK(i_ret);

				/* 重启后OLT不会重复下发语音激活信息，需手动恢复原有配置 */
				st_state.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
				hi_omci_tapi_voip_adminstate_get(&st_state);
				hi_omci_tapi_voip_adminstate_set(&st_state);
			}
		} else {
			HI_OMCI_BIT_SET(gui_omci_sipuser_mask, (16 - HI_OMCI_ATTR4));
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
#if 0
/******************************************************************************
 Function    : hi_omci_me_sip_user_data_get
 Description : SIP user data get
               在数据库操作前处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_sip_user_data_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_omci_me_sip_userdata_s *pst_entity = pv_data;
	hi_ushort16 us_instid;
	hi_ushort16 us_mask;

	/* get the instance ID */
	us_instid  = pst_entity->st_msghead.us_instid;
	us_mask    = pst_entity->st_msghead.us_attmask;

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);


	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
#endif
/******************************************************************************
 Function    : hi_omci_me_sip_user_data_create
 Description : SIP user data create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_sip_user_data_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_sip_userdata_s *pst_entity = (hi_omci_me_sip_userdata_s *)pv_data;
	hi_omci_me_auth_secur_method_s st_auth;
	hi_omci_tapi_voip_auth_s st_tapiauth;
	hi_omci_me_sip_agentdata_s st_agent;
	hi_omci_tapi_voip_udptcp_cfg_s st_data;
	hi_omci_me_udptcp_cfg_s st_udptcp;
	hi_omci_me_large_string_s st_string;
	hi_omci_tapi_voip_server_s st_server;
	hi_omci_me_network_addr_s st_network;
	hi_omci_tapi_voip_time_s st_time;
	hi_omci_tapi_voip_adminstate_s st_state = {0};
	hi_uchar8  uc_ptr[HI_OMCI_TAPI_VOIP_STR_LEN];
	hi_int32 i_ret;
	hi_ushort16 us_pptp_instid = 0;
	//hi_uchar8 *puc_addr = HI_NULL,*puc_port = HI_NULL;
	hi_char8 *puc_server = HI_NULL;
	hi_char8 *next_token = NULL;
	// hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		hi_omci_ext_del_inst(pst_entity->st_msghead.us_meid, pst_entity->st_msghead.us_instid);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&st_network, sizeof(st_network), 0, sizeof(st_network));
	HI_OS_MEMSET_S(&st_tapiauth, sizeof(st_tapiauth), 0, sizeof(st_tapiauth));
	HI_OS_MEMSET_S(&st_agent, sizeof(st_agent), 0, sizeof(st_agent));
	HI_OS_MEMSET_S(&st_udptcp, sizeof(st_udptcp), 0, sizeof(st_udptcp));
	if (0 != gui_omci_voip_flag) {
		us_pptp_instid = pst_entity->us_pptp_pointer;
		if (0xffff == us_pptp_instid) {
			us_pptp_instid = HI_OMCI_GET_INSTID_FORM_VOIP_PORTID(1);
		}
		if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ATH_SECR_METHOD_E, pst_entity->us_username, &st_auth)) {
			if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, pst_entity->us_up_aor, &st_string)) {
				HI_OS_STRCAT_S((hi_char8 *)st_tapiauth.uc_number, sizeof(st_tapiauth.uc_number),
					       (hi_char8 *)st_string.uc_part[0]);
			}
			st_tapiauth.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
			HI_OS_MEMCPY_S(st_tapiauth.uc_username, sizeof(st_tapiauth.uc_username), st_auth.uc_username1,
				       HI_OMCI_VOIP_STP_LEN);
			HI_OS_STRCAT_S((hi_char8 *)st_tapiauth.uc_username, sizeof(st_tapiauth.uc_username),
				       (hi_char8 *)st_auth.uc_username2);
			HI_OS_MEMCPY_S(st_tapiauth.uc_password, sizeof(st_tapiauth.uc_password), st_auth.uc_password,
				       HI_OMCI_VOIP_STP_LEN);
			i_ret = hi_omci_tapi_voip_sipauth_set(&st_tapiauth);
			HI_OMCI_RET_CHECK(i_ret);
			/* 重启后OLT不会重复下发语音激活信息，需手动恢复原有配置 */
			st_state.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
			hi_omci_tapi_voip_adminstate_get(&st_state);
			hi_omci_tapi_voip_adminstate_set(&st_state);
		}
		if (HI_RET_SUCC ==
		    hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, pst_entity->us_sa_pointer, &st_agent)) {
			if (HI_RET_SUCC ==
			    hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_agent.us_tu_pointer, &st_udptcp)) {
				st_data.us_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
				st_data.us_portid = st_udptcp.us_portid;
				st_data.us_protocol = st_udptcp.uc_protocol;
				st_data.us_tosfield = st_udptcp.uc_tosfield;
				i_ret = hi_omci_tapi_voip_udptcp_set(&st_data);
				HI_OMCI_RET_CHECK(i_ret);
			}

			if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, st_agent.us_psa_pointer, &st_string)) {
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_agent.us_tu_pointer, &st_udptcp);
				HI_OMCI_RET_CHECK(i_ret);
				HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
				HI_OS_MEMSET_S(uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);

				HI_OS_STRCAT_S((hi_char8 *)uc_ptr, sizeof(uc_ptr), (hi_char8 *)st_string.uc_part[0]);
				next_token = NULL;
				puc_server = strtok_s((hi_char8 *)uc_ptr, ":", &next_token);
				if (HI_NULL != puc_server) {
					HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server),
						       (hi_char8 *)puc_server);
					puc_server = strtok_s(HI_NULL, ":", &next_token);
					if (HI_NULL != puc_server) {
						HI_OS_SSCANF_S(puc_server, "%u", &st_server.us_portid);
					}
				}

				st_server.us_protocol = st_udptcp.uc_protocol;
				i_ret = hi_omci_tapi_voip_proxyserver_set(&st_server);
				HI_OMCI_RET_CHECK(i_ret);

				/*如没配置reg服务器信息，则和proxy服务器信息一致*/
				i_ret = hi_omci_tapi_voip_regserver_set(&st_server);
				HI_OMCI_RET_CHECK(i_ret);
			}

			if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, st_agent.us_opa_pointer, &st_string)) {
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_agent.us_tu_pointer, &st_udptcp);
				HI_OMCI_RET_CHECK(i_ret);
				HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
				HI_OS_MEMSET_S(uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);

				HI_OS_STRCAT_S((hi_char8 *)uc_ptr, sizeof(uc_ptr), (hi_char8 *)st_string.uc_part[0]);
				next_token = NULL;
				puc_server = strtok_s((hi_char8 *)uc_ptr, ":", &next_token);
				if (HI_NULL != puc_server) {
					HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server),
						       (hi_char8 *)puc_server);
					puc_server = strtok_s(HI_NULL, ":", &next_token);
					if (HI_NULL != puc_server) {
						HI_OS_SSCANF_S(puc_server, "%u", &st_server.us_portid);
					}
				}

				st_server.us_protocol = st_udptcp.uc_protocol;
				i_ret = hi_omci_tapi_voip_outbserver_set(&st_server);
				HI_OMCI_RET_CHECK(i_ret);
			}

			if (HI_RET_SUCC ==
			    hi_omci_ext_get_inst(HI_OMCI_PRO_ME_NETWORK_ADDR_E, st_agent.us_sip_registrar, &st_network)) {
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, st_network.us_addr_ptr, &st_string);
				HI_OMCI_RET_CHECK(i_ret);
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_agent.us_tu_pointer, &st_udptcp);
				HI_OMCI_RET_CHECK(i_ret);
				HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
				HI_OS_MEMSET_S(uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);

				HI_OS_STRCAT_S((hi_char8 *)uc_ptr, sizeof(uc_ptr), (hi_char8 *)st_string.uc_part[0]);
				next_token = NULL;
				puc_server = strtok_s((hi_char8 *)uc_ptr, ":", &next_token);
				if (HI_NULL != puc_server) {
					HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server),
						       (hi_char8 *)puc_server);
					puc_server = strtok_s(HI_NULL, ":", &next_token);
					if (HI_NULL != puc_server) {
						HI_OS_SSCANF_S(puc_server, "%u", &st_server.us_portid);
					}
				}
				st_server.us_protocol = st_udptcp.uc_protocol;
				i_ret = hi_omci_tapi_voip_regserver_set(&st_server);
				HI_OMCI_RET_CHECK(i_ret);
			}

			if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, st_agent.us_hp_uri, &st_string)) {
				i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_agent.us_tu_pointer, &st_udptcp);
				HI_OMCI_RET_CHECK(i_ret);
				HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));

				HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server),
					       (hi_char8 *)st_string.uc_part[0]);
				st_server.us_portid = st_udptcp.us_portid;
				st_server.us_protocol = st_udptcp.uc_protocol;
				i_ret = hi_omci_tapi_voip_agent_set(&st_server);
				HI_OMCI_RET_CHECK(i_ret);
			}

			if (0 != st_agent.ui_sre_time) {
				st_time.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
				i_ret = hi_omci_tapi_voip_regtime_get(&st_time);
				HI_OMCI_RET_CHECK(i_ret);
				st_time.us_reg_time = (hi_ushort16)st_agent.ui_sre_time;
				i_ret = hi_omci_tapi_voip_regtime_set(&st_time);
				HI_OMCI_RET_CHECK(i_ret);
			}

			if (0 != st_agent.ui_srhs_time) {
				st_time.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
				i_ret = hi_omci_tapi_voip_regtime_get(&st_time);
				HI_OMCI_RET_CHECK(i_ret);
				st_time.us_rereg_time = (hi_ushort16)st_agent.ui_srhs_time;
				i_ret = hi_omci_tapi_voip_regtime_set(&st_time);
				HI_OMCI_RET_CHECK(i_ret);
			}
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_me_sip_user_data_delete
 Description : SIP user data delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_sip_user_data_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_sip_userdata_s *pst_entity = (hi_omci_me_sip_userdata_s *)pv_data;
	hi_omci_me_sip_userdata_s st_entity;
	//hi_omci_me_auth_secur_method_s st_auth;
	//hi_omci_me_large_string_s st_string;
	hi_omci_tapi_voip_auth_s st_tapiauth;
	hi_omci_tapi_voip_udptcp_cfg_s st_data;
	hi_omci_tapi_voip_server_s st_server;
	hi_int32 i_ret;
	hi_ushort16 us_pptp_instid = 0;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;

	if (hi_omci_tapi_voip_cnfig_method_get() == HI_OMCI_VOIP_CONFIG_METHOD_TR069) {
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	HI_OS_MEMSET_S(&st_tapiauth, sizeof(st_tapiauth), 0, sizeof(st_tapiauth));
	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_USER_DATA_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	us_pptp_instid = st_entity.us_pptp_pointer;
	if (0xffff == us_pptp_instid) {
		us_pptp_instid = HI_OMCI_GET_INSTID_FORM_VOIP_PORTID(1);
	}
	st_tapiauth.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
	i_ret = hi_omci_tapi_voip_sipauth_set(&st_tapiauth);
	HI_OMCI_RET_CHECK(i_ret);

	st_data.us_lineidx =  HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);
	st_data.us_portid = 5060;
	i_ret = hi_omci_tapi_voip_udptcp_set(&st_data);
	HI_OMCI_RET_CHECK(i_ret);

	st_server.us_portid = 5060;
	HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server), "0.0.0.0");
	i_ret = hi_omci_tapi_voip_proxyserver_set(&st_server);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_voip_regserver_set(&st_server);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_voip_outbserver_set(&st_server);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_voip_agent_set(&st_server);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
