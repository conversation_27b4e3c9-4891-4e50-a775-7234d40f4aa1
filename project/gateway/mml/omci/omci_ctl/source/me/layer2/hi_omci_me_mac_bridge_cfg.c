/******************************************************************************

                  版权所有 (C), 2009-2019, 海思半导体有限公司

 ******************************************************************************
  文 件 名   : hi_omci_me_mac_bridge_cfg.c
  版 本 号   : 初稿
  作    者   : h66243
  生成日期   : D2012_02_20
  功能描述   : OMCI manager entity MAC bridge configuration data file.
******************************************************************************/
#include "hi_omci.h"
#include "hi_omci_mib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*创建后需刷新数据库*/
hi_uint32 hi_omci_me_maccfg_create_after(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 ui_ret = HI_RET_SUCC;

	hi_omci_me_mac_cfg_data_s *pst_maccfg = (hi_omci_me_mac_cfg_data_s *)pv_data;
	hi_omci_me_mac_cfg_data_s  st_maccfg;

	hi_omci_debug("[INFO]:ui_ret(%u)", ui_ret);

	/*TODO : you need to add code here*/
	HI_OS_MEMSET_S(&st_maccfg, sizeof(hi_omci_me_mac_cfg_data_s), 0, sizeof(hi_omci_me_mac_cfg_data_s));

	hi_ushort16  us_instid = pst_maccfg->st_msghead.us_instid;

	ui_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_MAC_CFG_DATA_E, us_instid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_debug("[INFO]:ui_ret(%u)", ui_ret);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_CFG_DATA_E, us_instid, &st_maccfg);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_debug("[INFO]:ui_ret(%u)", ui_ret);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	st_maccfg.us_pri = HI_OMCI_DEFAULT_BRIDGE_PRI;
	st_maccfg.ui_rootpathcost = HI_OMCI_DEFAULT_ROOT_PATH_COST;
	st_maccfg.uc_bdgportcount = HI_OMCI_UNI_CONTER;
	st_maccfg.us_rootportnum = HI_OMCI_DEFAULT_ROOT_PORT_NUM;
	st_maccfg.us_hellotime = HI_OMCI_MAC_DEFAULT_HELLO;
	st_maccfg.us_forwarddelay = HI_OMCI_MAC_DEFAULT_DELAY;

	hi_omci_ext_set_inst(HI_OMCI_PRO_ME_MAC_CFG_DATA_E, us_instid, &st_maccfg);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_debug("[INFO]:ui_ret(%d)", ui_ret);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_debug("[INFO]:ui_ret(%u)", ui_ret);
	return HI_RET_SUCC;
}

hi_uint32 hi_omci_me_maccfg_del_before(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 ui_ret = HI_RET_SUCC;

	hi_omci_me_mac_cfg_data_s *pst = (hi_omci_me_mac_cfg_data_s *)pv_data;
	hi_ushort16  us_instid = pst->st_msghead.us_instid;

	hi_omci_debug("[INFO]:ui_ret(%u)", ui_ret);

	/*  Delete MAC Bridge Config Data from MIB */
	ui_ret = hi_omci_ext_del_inst(HI_OMCI_PRO_ME_MAC_CFG_DATA_E, us_instid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

