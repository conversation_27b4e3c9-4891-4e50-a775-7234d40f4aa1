/******************************************************************************

                  版权所有 (C), 2009-2019, 华为技术有限公司

 ******************************************************************************
  文 件 名   : hi_omci_me_8021p.c
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2011_09_29
  功能描述   : G.988 OMCI manager entity 802.1p mapper service profile file.
******************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define HI_OMCI_ME_8021P_IS_GEMIWTP(mask) ((mask) & 0x7F80)       /* IWTP pointer for P-bit priority */

/*****************************************************************************
 函 数 名  : hi_omci_me_8021p_mappri
 功能描述  : 判断是否根据pri做流映射
 输入参数  : hi_ushort16 *ptr
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
#if 0
hi_uint32 hi_omci_me_8021p_check_mappri(hi_ushort16 *ptr)
{
	hi_uint32 ui_pri;

	for (ui_pri = 0; ui_pri < HI_OMCI_8021P_PRI_NUM; ui_pri++) {
		if (*ptr != *(ptr + ui_pri)) {
			return HI_OMCI_MAP_EVENT_8021P_TO_GEMIWTP_E;
		}
	}

	if (*ptr != HI_OMCI_8021P_INVALID_GEMIWTP) {
		return HI_OMCI_MAP_EVENT_8021P_TO_SAME_GEMIWTP_E;
	} else {
		return HI_OMCI_MAP_EVENT_8021P_RELEASE_E;
	}
}
#endif
hi_uint32 hi_omci_me_8021p_check_mappri(hi_omci_me_8021p_s *pst_entity)
{
	hi_uint32 ui_pri;
	hi_ushort16 us_pri[8] = {0};

	HI_OS_MEMCPY_S(us_pri, sizeof(us_pri), &pst_entity->us_iwtp_ptr0, 8 * sizeof(hi_ushort16));
	for (ui_pri = 0; ui_pri < HI_OMCI_8021P_PRI_NUM; ui_pri++) {
		if (pst_entity->us_iwtp_ptr0 != us_pri[ui_pri]) {
			return HI_OMCI_MAP_EVENT_8021P_TO_GEMIWTP_E;
		}
	}

	if (pst_entity->us_iwtp_ptr0 != HI_OMCI_8021P_INVALID_GEMIWTP) {
		return HI_OMCI_MAP_EVENT_8021P_TO_SAME_GEMIWTP_E;
	} else {
		return HI_OMCI_MAP_EVENT_8021P_RELEASE_E;
	}
}
/*****************************************************************************
 函 数 名  : hi_omci_me_8021p_set
 功能描述  : 802.1p set
             数据库操作后处理
 输入参数  : hi_void*pv_data
             hi_uint32 ui_in
             hi_uint32 *pui_outlen
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_me_8021p_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_map_event_e em_event;
	hi_omci_me_8021p_s *pst_entity = (hi_omci_me_8021p_s *)pv_data;
	hi_uint32 ui_ret;
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;;
	hi_ushort16 us_tppointer;
	hi_ushort16 us_macportinst = 0;
	hi_uchar8 uc_tptype;

	hi_omci_debug("[INFO]: %d\n", __LINE__);

	/* 配置优先级映射 */
	if (HI_OMCI_ME_8021P_IS_GEMIWTP(us_mask)) {
		uc_tptype = HI_MAC_BRI_PORT_8021P_MAP_E;
		us_tppointer = pst_entity->st_msghead.us_instid;
		ui_ret = hi_omci_ext_get_instid_byattr2(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR3, &uc_tptype,
							HI_OMCI_ATTR4, &us_tppointer, &us_macportinst);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			return HI_RET_SUCC;
		}

		em_event = (hi_omci_map_event_e)hi_omci_me_8021p_check_mappri(pst_entity);

		ui_ret = hi_omci_map_msg_hander(us_macportinst, em_event, HI_NULL);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
