/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_mac_bridge_service.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-28
  Description: OMCI manager entity mac bridge service profile managed entity file.
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_MAC_SRV_IS_LRN(mask) HI_OMCI_CHECK_ATTR(mask,HI_OMCI_ATTR2)  /* Learning ind */
#define HI_OMCI_MAC_SRV_IS_AGE(mask) HI_OMCI_CHECK_ATTR(mask,HI_OMCI_ATTR10) /* Dynamic filtering ageing time（OPT） */

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_mac_serv_create
 Description : mac bridge service profile create
               数据库操作后处理
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_mac_serv_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32  ui_ret    = HI_RET_SUCC;

	hi_omci_me_mac_service_s *pst_macserv = (hi_omci_me_mac_service_s *)pv_data;
	hi_omci_me_mac_service_s st_entity;
	hi_omci_tapi_sysinfo_s st_info;
	hi_ushort16  us_instid;
	hi_ushort16  us_portinstid;
	hi_uchar8    uc_portid;
	hi_uchar8    uc_type;

	/* get the instance ID */
	us_instid = pst_macserv->st_msghead.us_instid;
	uc_portid = (us_instid & 0xff);
	hi_omci_debug("[INFO]:us_instid=0x%x\n", us_instid);

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_SERVICE_E, us_instid, &st_entity);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_debug("[INFO]:portid=%hu,spantreeind=%hhu,learnind=%hhu,portbridging=%hhu,pri=%hud,maxage=%hu\n",
                 uc_portid, st_entity.uc_spantreeind, st_entity.uc_learnind, st_entity.uc_portbridging,
				 ntohs(st_entity.us_pri), ntohs(st_entity.us_maxage));
	hi_omci_debug("[INFO]:us_hellotime=%hu,us_forwarddelay=%hu,uc_unknownmac=%hhu,uc_learndepth=%hhu,ui_dynfilter=%hu\n",
                 ntohs(st_entity.us_hellotime), ntohs(st_entity.us_forwarddelay), st_entity.uc_unknownmac,
				 st_entity.uc_learndepth, ntohl(st_entity.ui_dynfilter));

	uc_type = HI_MAC_BRI_PORT_UNI_E;
	ui_ret = hi_omci_ext_get_instid_byattr2(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR1,
						&us_instid, HI_OMCI_ATTR3, &uc_type, &us_portinstid);
	if (HI_RET_SUCC == ui_ret) {
		ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
		HI_OMCI_RET_CHECK(ui_ret);

		/* 设置MAC学习enable和老化时间*/
		for (uc_portid = HI_OMCI_TAPI_ETH0_E; uc_portid < st_info.ui_port_num; uc_portid++) {
			ui_ret = hi_omci_tapi_br_learn_set((hi_omci_tapi_port_e)uc_portid, pst_macserv->uc_learnind);
			HI_OMCI_RET_CHECK(ui_ret);
		}

		ui_ret = hi_omci_tapi_br_learn_set(HI_OMCI_TAPI_PON_E, pst_macserv->uc_learnind);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	if (0 == pst_macserv->ui_dynfilter) {
		ui_ret = hi_omci_tapi_br_age_set(HI_OMCI_MAC_SRVPROFILE_AGE_DEF);
	} else {
		ui_ret = hi_omci_tapi_br_age_set(pst_macserv->ui_dynfilter);
	}
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_me_maccfg_create_after(pv_data, ui_in, pui_outlen);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return ui_ret;
}

/******************************************************************************
 Function    : hi_omci_me_mac_serv_set
 Description : mac bridge service profile set
               数据库操作后处理
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_mac_serv_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32  ui_ret    = HI_RET_SUCC;
	hi_omci_tapi_sysinfo_s st_info;
	hi_omci_me_mac_service_s *pst_macserv = (hi_omci_me_mac_service_s *)pv_data;
	hi_uint32    ui_enable;
	hi_ushort16  us_mask;
	hi_ushort16  us_instid = pst_macserv->st_msghead.us_instid;
	hi_ushort16  us_portinstid;
	hi_uchar8    uc_portid;
	hi_uchar8    uc_type;

	us_mask = pst_macserv->st_msghead.us_attmask;

	hi_omci_debug("[INFO]:learing=%hhu,pri=%hu,maxage=%hu,portbridging=%hhu,learndepth=%hhu,hellotime=%hu,\
			 spantreeind =%hhu\n", pst_macserv->uc_learnind, ntohs(pst_macserv->us_pri),
			 ntohs(pst_macserv->us_maxage), pst_macserv->uc_portbridging, pst_macserv->uc_learndepth,
			 ntohs(pst_macserv->us_hellotime), pst_macserv->uc_spantreeind);

	/* 只设置MAC学习enable*/
	if (HI_OMCI_TRUE == HI_OMCI_MAC_SRV_IS_LRN(us_mask)) {
		uc_type = HI_MAC_BRI_PORT_UNI_E;
		ui_ret = hi_omci_ext_get_instid_byattr2(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR1,
							&us_instid, HI_OMCI_ATTR3, &uc_type, &us_portinstid);
		if (HI_RET_SUCC == ui_ret) {
			ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
			HI_OMCI_RET_CHECK(ui_ret);

			for (uc_portid = HI_OMCI_TAPI_ETH0_E; uc_portid < st_info.ui_port_num; uc_portid++) {
				ui_ret = hi_omci_tapi_br_learn_set((hi_omci_tapi_port_e)uc_portid, pst_macserv->uc_learnind);
				HI_OMCI_RET_CHECK(ui_ret);
			}

			ui_ret = hi_omci_tapi_br_learn_set(HI_OMCI_TAPI_PON_E, pst_macserv->uc_learnind);
			HI_OMCI_RET_CHECK(ui_ret);
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR3)) {
		ui_enable = pst_macserv->uc_portbridging == 0 ? HI_FALSE : HI_TRUE;
		hi_omci_debug("[INFO]:ui_enable = %u\n", ui_enable);
		ui_ret = hi_omci_tapi_br_enable(&ui_enable);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	if (HI_OMCI_TRUE == HI_OMCI_MAC_SRV_IS_AGE(us_mask)) {
		if (0 == pst_macserv->ui_dynfilter) {
			ui_ret = hi_omci_tapi_br_age_set(HI_OMCI_MAC_SRVPROFILE_AGE_DEF);
		} else {
			ui_ret = hi_omci_tapi_br_age_set(pst_macserv->ui_dynfilter);
		}

		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return ui_ret;
}

/******************************************************************************
 Function    : hi_omci_me_mac_serv_delete
 Description : mac bridge service profile delete
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_mac_serv_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32  ui_ret    = HI_RET_SUCC;

	ui_ret = hi_omci_me_maccfg_del_before(pv_data, ui_in, pui_outlen);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return ui_ret;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
