/******************************************************************************

                  Copyright (C), 2023-2023, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_ethfrm_ext_pm_64b.c
  Version    : 初稿
  Author     : hsan
  Creation   : 2023-8-10
  Description: Ethernet frame extended PM
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_ETHEXTPM_64B_ACC_GLB_DIS 15
#define HI_OMCI_ME_ETHEXTPM_64B_ACC_GLB_CLR 16

#define HI_OMCI_ME_ETHEXTPM_64B_TCA_GLB_DIS 15

#define HI_OMCI_ME_ETHEXTPM_64B_NUM 14

#define HI_OMCI_ME_ETHEXTPM_64B_ALARM_DROP      HI_OMCI_ME_ALARM_BITMAP(1)
#define HI_OMCI_ME_ETHEXTPM_64B_ALARM_CRCERR    HI_OMCI_ME_ALARM_BITMAP(2)
#define HI_OMCI_ME_ETHEXTPM_64B_ALARM_UNDERSIZE HI_OMCI_ME_ALARM_BITMAP(3)
#define HI_OMCI_ME_ETHEXTPM_64B_ALARM_OVERSIZE  HI_OMCI_ME_ALARM_BITMAP(4)

#define HI_OMCI_ME_ETHEXTPM_64B_IS_CTRLBLOCK(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR2)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_omci_tapi_stat_eth_s gst_ethextpm_history[HI_OMCI_ETH_PM3_MAX_NUM];
static hi_omci_tapi_stat_eth_s gst_ethextpm_prev_history[HI_OMCI_ETH_PM3_MAX_NUM];

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
static hi_void hi_omci_me_ethextpm_dnstream_stat_copy(hi_omci_tapi_stat_eth_s stat, hi_ulong64 *pst_data,
		hi_uint32 data_size)
{
	hi_uint32 ui_index;
	hi_ulong64 *ptr = &stat.ui_tx_dropsevt;
	for (ui_index = 0; ui_index < data_size; ui_index++, ptr++) {
		pst_data[ui_index] = *ptr;
	}
}

static hi_void hi_omci_me_ethextpm_upstream_stat_copy(hi_omci_tapi_stat_eth_s stat, hi_ulong64 *pst_data,
		hi_uint32 data_size)
{
	hi_uint32 ui_index;
	hi_ulong64 *ptr = &stat.ui_dropsevt;
	for (ui_index = 0; ui_index < data_size; ui_index++, ptr++) {
		pst_data[ui_index] = *ptr;
	}
}

static hi_ulong64 swap_endian_64(hi_ulong64 value)
{
	hi_ulong64 result = 0;
	hi_uchar8 *p = (hi_uchar8 *)&value;
	hi_uchar8 *q = (hi_uchar8 *)&result;
	hi_uint32 i;

	for (i = 0; i < 8; i++) {
		q[i] = p[7 - i];
	}

	return result;
}
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_ethextpm_64b_alarm_get
 Description : ethernet extended PM统计告警检查
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethextpm_64b_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_alarm_timer_s *pst_timer = (hi_omci_me_alarm_timer_s *)pv_data;
	hi_omci_me_eth_ext_pm_64b_s st_entity;
	hi_omci_me_threshold_64b_s st_threshold_64b;
	hi_int32 i_ret;
	hi_uint32 ui_index;
	hi_ushort16 us_bitmap_curr = 0;
	hi_ushort16 us_bitmap_hist;
	hi_ushort16 us_bitmap_tca;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;
	hi_ushort16 us_class;
	hi_ushort16 us_thresid;
	hi_ushort16 us_tca_disable;
	hi_uint32 ui_tmp = 1;
	hi_ulong64 *ptr = &st_entity.ui_dropevents;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	HI_OS_MEMSET_S(&st_threshold_64b, sizeof(st_threshold_64b), 0, sizeof(st_threshold_64b));

	/* 读取当前芯片端口统计 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++, ptr++) {
		*ptr = swap_endian_64(*ptr);
	}

	us_class = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_class);
	us_thresid = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_thresid);
	us_tca_disable = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_tca_disable);

	/* 当前仅处理ETH口的统计 */
	if (us_class != HI_OMCI_PRO_ME_PPTP_ETH_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, us_class, 0, 0, 0);
		return HI_RET_SUCC;
	}

	/* 获取各参数阈值 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_THRESHOLD_DATA_64B_E, us_thresid, &st_threshold_64b);
	HI_OMCI_RET_CHECK(i_ret);

	/* 比较是否超出阈值，确定是告警产生，还是撤销 */
	if (st_entity.ui_dropevents > st_threshold_64b.ul_thres1) {
		us_bitmap_curr |= HI_OMCI_ME_ETHEXTPM_64B_ALARM_DROP;
	}

	if (st_entity.ui_crcerrored > st_threshold_64b.ul_thres2) {
		us_bitmap_curr |= HI_OMCI_ME_ETHEXTPM_64B_ALARM_CRCERR;
	}

	if (st_entity.ui_undersize > st_threshold_64b.ul_thres3) {
		us_bitmap_curr |= HI_OMCI_ME_ETHEXTPM_64B_ALARM_UNDERSIZE;
	}

	if (st_entity.ui_oversize > st_threshold_64b.ul_thres4) {
		us_bitmap_curr |= HI_OMCI_ME_ETHEXTPM_64B_ALARM_OVERSIZE;
	}

	/* 读取数据库当前告警状态 */
	i_ret = hi_omci_sql_alarm_inst_get(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, &us_bitmap_hist);
	HI_OMCI_RET_CHECK(i_ret);

	/* 如果告警状态发生变化，更新数据库的告警状态 */
	if (us_bitmap_curr != us_bitmap_hist) {
		i_ret = hi_omci_sql_alarm_inst_set(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);

		us_bitmap_tca = us_bitmap_curr ^ us_bitmap_hist;

		/* 获取实例的control block TCA标识，清除禁止上报的BIT */
		if (us_tca_disable & (1 << HI_OMCI_ME_ETHEXTPM_64B_TCA_GLB_DIS)) {
			/* 清空所有BIT */
			us_bitmap_tca = 0;
		} else {
			/* 清空TCA disable的BIT */
			for (ui_index = 0; ui_index < 4; ui_index++) {
				if (us_tca_disable & (ui_tmp << ui_index)) {
					us_bitmap_tca &= ~(ui_tmp << ui_index);
				}
			}
		}

		/* 如果当前不禁止告警上报则发生告警消息 */
		if (us_bitmap_tca) {
			i_ret = hi_omci_proc_sendalarm(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, (us_bitmap_curr & us_bitmap_tca));
			HI_OMCI_RET_CHECK(i_ret);
		}
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethextpm_64b_stat_get
 Description : 获取以太端口扩展统计
 Input Parm  : hi_void *pv_data       历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethextpm_64b_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_me_eth_ext_pm_64b_s st_entity;
	hi_omci_tapi_stat_eth_s st_ethextpm_curr;
	hi_int32 i_ret;
	hi_ulong64 ul_data[HI_OMCI_ME_ETHEXTPM_64B_NUM] = {0};
	hi_ulong64 ul_stat_curr[HI_OMCI_ME_ETHEXTPM_64B_NUM] = {0};
	hi_ulong64 ul_stat_hist[HI_OMCI_ME_ETHEXTPM_64B_NUM] = {0};
	hi_uint32 ui_index;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;
	hi_ushort16 us_portid;
	hi_ushort16 us_class;
	hi_ushort16 us_ctrlfield;
	hi_ushort16 us_accumula_disable;
	hi_ulong64 *ptr = &st_entity.ui_dropevents;

	/* 读取数据库的control block */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++, ptr++) {
		*ptr = swap_endian_64(*ptr);
	}

	us_class = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_class);
	us_portid = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_inst);
	us_ctrlfield = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_ctlfields);
	us_accumula_disable = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_accumula_disable);

	hi_omci_debug("Eth Ext PM[%d] ctrlblock class[%d] inst[%d] accumula_disable[0x%x] ctlfields[0x%x]\n",
		      us_instid, us_class, us_portid, us_accumula_disable, us_ctrlfield);

	/* 当前仅处理ETH口的统计 */
	if (us_class != HI_OMCI_PRO_ME_PPTP_ETH_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, us_class, 0, 0, 0);
		return HI_RET_SUCC;
	}

	us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_portid);
	if (us_portid >= HI_OMCI_ETH_PM3_MAX_NUM) {
		hi_omci_systrace(HI_RET_SUCC, us_portid, 0, 0, 0);
		return HI_RET_SUCC;
	}
	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_eth_get((hi_omci_tapi_port_e)us_portid, &st_ethextpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 确认是上行还是下行
	 * This bit indicates directionality for the collection of data. The
	 * value 0 indicates that data is to be collected for upstream traffic. The
	 * value 1 indicates that data is to be collected for downstream traffic.
	 */
	if (us_ctrlfield & 0x2) {
		hi_omci_me_ethextpm_dnstream_stat_copy(st_ethextpm_curr, ul_stat_curr, HI_OMCI_ME_ETHEXTPM_64B_NUM);
		hi_omci_me_ethextpm_dnstream_stat_copy(gst_ethextpm_history[us_portid], ul_stat_hist, HI_OMCI_ME_ETHEXTPM_64B_NUM);
	} else {
		hi_omci_me_ethextpm_upstream_stat_copy(st_ethextpm_curr, ul_stat_curr, HI_OMCI_ME_ETHEXTPM_64B_NUM);
		hi_omci_me_ethextpm_upstream_stat_copy(gst_ethextpm_history[us_portid], ul_stat_hist, HI_OMCI_ME_ETHEXTPM_64B_NUM);
	}

	HI_OS_MEMCPY_S(ul_data, sizeof(ul_data), &st_entity.ui_dropevents, HI_OMCI_ME_ETHEXTPM_64B_NUM * sizeof(hi_ulong64));

	/*
	 * 根据ACC和control field bit1确认哪些属性是累计，或时间段统计
	 * 是累计的属性，将当前统计值写入数据库；
	 * 是时间的统计的，将当前统计减去历史统计，再写入数据库
	 */
	if ((us_ctrlfield & 0x1)
	    && !(us_accumula_disable & (1 << HI_OMCI_ME_ETHEXTPM_64B_ACC_GLB_DIS))) {
		/* 累加 */
		for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++) {
			if (us_accumula_disable & (1 << ui_index)) {
				ul_data[ui_index] = ul_stat_curr[ui_index] - ul_stat_hist[ui_index];
				hi_omci_debug("curr = %lld, hist = %lld data = %lld\n", ul_stat_curr[ui_index],
					      ul_stat_hist[ui_index], ul_data[ui_index]);
			} else {
				ul_data[ui_index] = ul_stat_curr[ui_index];
				hi_omci_debug("data = %lld\n", ul_data[ui_index]);
			}
		}
	} else {
		/* 不累加 */
		for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++) {
			ul_data[ui_index] = ul_stat_curr[ui_index] - ul_stat_hist[ui_index];
			hi_omci_debug("curr = %lld, hist = %lld data = %lld\n", ul_stat_curr[ui_index],
				      ul_stat_hist[ui_index], ul_data[ui_index]);
		}
	}

	for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++) {
		ul_data[ui_index] = swap_endian_64(ul_data[ui_index]);
	}
	HI_OS_MEMCPY_S(&st_entity.ui_dropevents, sizeof(ul_data), ul_data, HI_OMCI_ME_ETHEXTPM_64B_NUM * sizeof(hi_ulong64));

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 更新历史统计 */
	HI_OS_MEMCPY_S(&gst_ethextpm_prev_history[us_portid], sizeof(hi_omci_tapi_stat_eth_s), &gst_ethextpm_history[us_portid],
		       sizeof(hi_omci_tapi_stat_eth_s));
	HI_OS_MEMCPY_S(&gst_ethextpm_history[us_portid], sizeof(hi_omci_tapi_stat_eth_s), &st_ethextpm_curr,
		       sizeof(hi_omci_tapi_stat_eth_s));

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_ethextpm_64b_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_eth_ext_pm_64b_s *pst_entry = (hi_omci_me_eth_ext_pm_64b_s *)pv_data;
	hi_omci_me_eth_ext_pm_64b_s st_entity;
	hi_omci_tapi_stat_eth_s st_ethextpm_curr;
	hi_int32 i_ret;
	hi_ulong64 ul_data[HI_OMCI_ME_ETHEXTPM_64B_NUM] = {0};
	hi_ulong64 ul_stat_curr[HI_OMCI_ME_ETHEXTPM_64B_NUM] = {0};
	hi_ulong64 ul_stat_hist[HI_OMCI_ME_ETHEXTPM_64B_NUM] = {0};
	hi_uint32 ui_index;
	hi_ushort16 us_instid = (hi_ushort16)pst_entry->st_msghead.us_instid;
	hi_ushort16 us_portid;
	hi_ushort16 us_class;
	hi_ushort16 us_ctrlfield;
	hi_ushort16 us_accumula_disable;
	hi_ulong64 *ptr = &st_entity.ui_dropevents;

	/* 读取数据库的control block */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++, ptr++) {
		*ptr = swap_endian_64(*ptr);
	}

	us_class = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_class);
	us_portid = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_inst);
	us_ctrlfield = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_ctlfields);
	us_accumula_disable = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_accumula_disable);

	hi_omci_debug("Eth Ext PM[%d] ctrlblock class[%d] inst[%d] accumula_disable[0x%x] ctlfields[0x%x]\n",
		      us_instid, us_class, us_portid, us_accumula_disable, us_ctrlfield);

	/* 当前仅处理ETH口的统计 */
	if (us_class != HI_OMCI_PRO_ME_PPTP_ETH_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, us_class, 0, 0, 0);
		return HI_RET_SUCC;
	}

	us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_portid);

	if (us_portid >= HI_OMCI_ETH_PM3_MAX_NUM) {
		hi_omci_systrace(HI_RET_SUCC, us_portid, 0, 0, 0);
		return HI_RET_SUCC;
	}

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_eth_get((hi_omci_tapi_port_e)us_portid, &st_ethextpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 确认是上行还是下行
	 * This bit indicates directionality for the collection of data. The
	 * value 0 indicates that data is to be collected for upstream traffic. The
	 * value 1 indicates that data is to be collected for downstream traffic.
	 */
	if (us_ctrlfield & 0x2) {
		hi_omci_me_ethextpm_dnstream_stat_copy(gst_ethextpm_history[us_portid], ul_stat_curr, HI_OMCI_ME_ETHEXTPM_64B_NUM);
		hi_omci_me_ethextpm_dnstream_stat_copy(gst_ethextpm_prev_history[us_portid], ul_stat_hist, HI_OMCI_ME_ETHEXTPM_64B_NUM);
	} else {
		hi_omci_me_ethextpm_upstream_stat_copy(gst_ethextpm_prev_history[us_portid], ul_stat_curr, HI_OMCI_ME_ETHEXTPM_64B_NUM);
		hi_omci_me_ethextpm_upstream_stat_copy(gst_ethextpm_history[us_portid], ul_stat_hist, HI_OMCI_ME_ETHEXTPM_64B_NUM);
	}

	HI_OS_MEMCPY_S(ul_data, sizeof(ul_data), &st_entity.ui_dropevents, HI_OMCI_ME_ETHEXTPM_64B_NUM * sizeof(hi_ulong64));

	/*
	 * 根据ACC和control field bit1确认哪些属性是累计，或时间段统计
	 * 是累计的属性，将当前统计值写入数据库；
	 * 是时间的统计的，将当前统计减去历史统计，再写入数据库
	 */
	if ((us_ctrlfield & 0x1)
	    && !(us_accumula_disable & (1 << HI_OMCI_ME_ETHEXTPM_64B_ACC_GLB_DIS))) {
		/* 累加 */
		for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++) {
			if (us_accumula_disable & (1 << ui_index)) {
				ul_data[ui_index] = ul_stat_curr[ui_index] - ul_stat_hist[ui_index];
				hi_omci_debug("curr = %lld, hist = %lld data = %lld\n", ul_stat_curr[ui_index],
					      ul_stat_hist[ui_index], ul_data[ui_index]);
			} else {
				ul_data[ui_index] = ul_stat_curr[ui_index];
				hi_omci_debug("data = %lld\n", ul_data[ui_index]);
			}
		}
	} else {
		/* 不累加 */
		for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++) {
			ul_data[ui_index] = ul_stat_curr[ui_index] - ul_stat_hist[ui_index];
			hi_omci_debug("curr = %lld, hist = %lld data = %lld\n", ul_stat_curr[ui_index],
				      ul_stat_hist[ui_index], ul_data[ui_index]);
		}
	}

	for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++) {
		ul_data[ui_index] = swap_endian_64(ul_data[ui_index]);
	}
	HI_OS_MEMCPY_S(&st_entity.ui_dropevents, sizeof(ul_data), ul_data, HI_OMCI_ME_ETHEXTPM_64B_NUM * sizeof(hi_ulong64));

	/*将清零的数据保存到ETH EXT PM实例数据中*/
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);


	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethextpm_64b_stat_getcurrent
 Description : 获取以太端口扩展统计
 Input Parm  : hi_void *pv_data       历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethextpm_64b_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_eth_ext_pm_64b_s *pst_entry = (hi_omci_me_eth_ext_pm_64b_s *)pv_data;
	hi_omci_me_eth_ext_pm_64b_s st_entity;
	hi_omci_tapi_stat_eth_s st_ethextpm_curr;
	hi_int32 i_ret;
	hi_ulong64 ul_data[HI_OMCI_ME_ETHEXTPM_64B_NUM] = {0};
	hi_ulong64 ul_stat_curr[HI_OMCI_ME_ETHEXTPM_64B_NUM] = {0};
	hi_ulong64 ul_stat_hist[HI_OMCI_ME_ETHEXTPM_64B_NUM] = {0};
	hi_uint32 ui_index;
	hi_ushort16 us_instid = (hi_ushort16)pst_entry->st_msghead.us_instid;
	hi_ushort16 us_portid;
	hi_ushort16 us_class;
	hi_ushort16 us_ctrlfield;
	hi_ushort16 us_accumula_disable;
	hi_ulong64 *ptr = &st_entity.ui_dropevents;

	/* 读取数据库的control block */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++, ptr++) {
		*ptr = swap_endian_64(*ptr);
	}

	us_class = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_class);
	us_portid = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_inst);
	us_ctrlfield = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_ctlfields);
	us_accumula_disable = (hi_ushort16)ntohs(st_entity.st_ctrlblock.us_accumula_disable);

	hi_omci_debug("Eth Ext PM[%d] ctrlblock class[%d] inst[%d] accumula_disable[0x%x] ctlfields[0x%x]\n",
		      us_instid, us_class, us_portid, us_accumula_disable, us_ctrlfield);

	/* 当前仅处理ETH口的统计 */
	if (us_class != HI_OMCI_PRO_ME_PPTP_ETH_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, us_class, 0, 0, 0);
		return HI_RET_SUCC;
	}

	us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_portid);
	if (us_portid >= HI_OMCI_ETH_PM3_MAX_NUM) {
		hi_omci_systrace(HI_RET_SUCC, us_portid, 0, 0, 0);
		return HI_RET_SUCC;
	}
	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_eth_get((hi_omci_tapi_port_e)us_portid, &st_ethextpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 确认是上行还是下行
	 * This bit indicates directionality for the collection of data. The
	 * value 0 indicates that data is to be collected for upstream traffic. The
	 * value 1 indicates that data is to be collected for downstream traffic.
	 */
	if (us_ctrlfield & 0x2) {
		hi_omci_me_ethextpm_dnstream_stat_copy(st_ethextpm_curr, ul_stat_curr, HI_OMCI_ME_ETHEXTPM_64B_NUM);
		hi_omci_me_ethextpm_dnstream_stat_copy(gst_ethextpm_history[us_portid], ul_stat_hist, HI_OMCI_ME_ETHEXTPM_64B_NUM);
	} else {
		hi_omci_me_ethextpm_upstream_stat_copy(st_ethextpm_curr, ul_stat_curr, HI_OMCI_ME_ETHEXTPM_64B_NUM);
		hi_omci_me_ethextpm_upstream_stat_copy(gst_ethextpm_history[us_portid], ul_stat_hist, HI_OMCI_ME_ETHEXTPM_64B_NUM);
	}

	HI_OS_MEMCPY_S(ul_data, sizeof(ul_data), &st_entity.ui_dropevents, HI_OMCI_ME_ETHEXTPM_64B_NUM * sizeof(hi_ulong64));

	/*
	 * 根据ACC和control field bit1确认哪些属性是累计，或时间段统计
	 * 是累计的属性，将当前统计值写入数据库；
	 * 是时间的统计的，将当前统计减去历史统计，再写入数据库
	 */
	if ((us_ctrlfield & 0x1)
	    && !(us_accumula_disable & (1 << HI_OMCI_ME_ETHEXTPM_64B_ACC_GLB_DIS))) {
		/* 累加 */
		for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++) {
			if (us_accumula_disable & (1 << ui_index)) {
				ul_data[ui_index] = ul_stat_curr[ui_index] - ul_stat_hist[ui_index];
				hi_omci_debug("curr = %lld, hist = %lld data = %lld\n", ul_stat_curr[ui_index],
					      ul_stat_hist[ui_index], ul_data[ui_index]);
			} else {
				ul_data[ui_index] = ul_stat_curr[ui_index];
				hi_omci_debug("data = %lld\n", ul_data[ui_index]);
			}
		}
	} else {
		/* 不累加 */
		for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++) {
			ul_data[ui_index] = ul_stat_curr[ui_index] - ul_stat_hist[ui_index];
			hi_omci_debug("curr = %lld, hist = %lld data = %lld\n", ul_stat_curr[ui_index],
				      ul_stat_hist[ui_index], ul_data[ui_index]);
		}
	}

	for (ui_index = 0; ui_index < HI_OMCI_ME_ETHEXTPM_64B_NUM; ui_index++) {
		ul_data[ui_index] = swap_endian_64(ul_data[ui_index]);
	}
	HI_OS_MEMCPY_S(&st_entity.ui_dropevents, sizeof(ul_data), ul_data, HI_OMCI_ME_ETHEXTPM_64B_NUM * sizeof(hi_ulong64));

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethextpm_64b_set
 Description : Ethernet frame extended PM set
               根据Accumulation disable Global clear做清空处理
               更新数据库后处理
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethextpm_64b_set(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_eth_ext_pm_64b_s *pst_entity = (hi_omci_me_eth_ext_pm_64b_s *)pv_data;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;
	hi_ushort16 us_portid;
	hi_ushort16 us_accumula_disable;

	/* 当前仅处理ETH口的统计 */
	if (ntohs(pst_entity->st_ctrlblock.us_class) != HI_OMCI_PRO_ME_PPTP_ETH_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, pst_entity->st_ctrlblock.us_class, 0, 0, 0);
		return HI_RET_SUCC;
	}
	us_portid = (hi_ushort16)ntohs(pst_entity->st_ctrlblock.us_inst);
	us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_portid);
	if (us_portid >= HI_OMCI_ETH_PM3_MAX_NUM) {
		hi_omci_systrace(HI_RET_SUCC, us_portid, 0, 0, 0);
		return HI_RET_SUCC;
	}
	us_accumula_disable = ntohs(pst_entity->st_ctrlblock.us_accumula_disable);

	if (HI_OMCI_ME_ETHEXTPM_64B_IS_CTRLBLOCK(us_mask)) {
		if (us_accumula_disable & (1 << HI_OMCI_ME_ETHEXTPM_64B_ACC_GLB_CLR)) {
			/* 清空统计 */
			i_ret = hi_omci_tapi_stat_eth_clr((hi_omci_tapi_port_e)us_portid);
			HI_OMCI_RET_CHECK(i_ret);

			HI_OS_MEMSET_S(&gst_ethextpm_history[us_portid], sizeof(hi_omci_tapi_stat_eth_s), 0, sizeof(hi_omci_tapi_stat_eth_s));
			HI_OS_MEMSET_S((hi_void *)&pst_entity->ui_dropevents,
				       (sizeof(hi_omci_me_eth_ext_pm_64b_s) - sizeof(pst_entity->st_msghead)), 0,
				       (sizeof(hi_omci_me_eth_ext_pm_64b_s) - sizeof(pst_entity->st_msghead)));

			/* 清空TCA */
			pst_entity->st_ctrlblock.us_tca_disable = 0;

			/* 清空标识 */
			us_accumula_disable &= ~(1 << HI_OMCI_ME_ETHEXTPM_64B_ACC_GLB_CLR);
			pst_entity->st_ctrlblock.us_accumula_disable = ntohs(us_accumula_disable);
		}

		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid, pst_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethextpm_64b_create
 Description : Ethernet frame extended PM create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethextpm_64b_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_eth_ext_pm_64b_s *pst_entry = (hi_omci_me_eth_ext_pm_64b_s *)pv_data;
	hi_omci_me_stat_timer_s st_ethextpm_stat_timer;
	hi_omci_me_alarm_timer_s st_ethextpm_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	HI_OS_MEMSET_S(&gst_ethextpm_history[0], sizeof(gst_ethextpm_history), 0, sizeof(gst_ethextpm_history));
	HI_OS_MEMSET_S(&gst_ethextpm_prev_history[0], sizeof(gst_ethextpm_prev_history), 0, sizeof(gst_ethextpm_prev_history));

	hi_omci_debug("Eth Ext PM ctrlblock thresid[%hu] "
		      "class[%hu] inst[%hu] accumula_disable[0x%x] tca_disable[0x%x] ctlfields[0x%x] tci[0x%x]\n",
		      pst_entry->st_ctrlblock.us_thresid, pst_entry->st_ctrlblock.us_class, pst_entry->st_ctrlblock.us_inst,
		      pst_entry->st_ctrlblock.us_accumula_disable, pst_entry->st_ctrlblock.us_tca_disable,
		      pst_entry->st_ctrlblock.us_ctlfields, pst_entry->st_ctrlblock.us_tci);

	/* 不是以太端口的实体，不支持统计告警 */
	if (ntohs(pst_entry->st_ctrlblock.us_class) != HI_OMCI_PRO_ME_PPTP_ETH_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	/* 创建ETH EXT PM告警实体 */
	i_ret = hi_omci_sql_alarm_inst_create(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_ethextpm_stat_timer, sizeof(st_ethextpm_stat_timer), 0, sizeof(st_ethextpm_stat_timer));
	st_ethextpm_stat_timer.ui_meid = HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E;
	st_ethextpm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_ethextpm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建3秒定时器，上报告警 */
	HI_OS_MEMSET_S(&st_ethextpm_alarm_timer, sizeof(st_ethextpm_alarm_timer), 0, sizeof(st_ethextpm_alarm_timer));
	st_ethextpm_alarm_timer.ui_meid = HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E;
	st_ethextpm_alarm_timer.ui_instid = us_instid;
	st_ethextpm_alarm_timer.ui_timeout = HI_OMCI_ME_ALARM_COMM_TIMEOUT;
	i_ret = hi_omci_me_alarm_start(&st_ethextpm_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethextpm_64b_delete
 Description : Ethernet frame extended PM delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethextpm_64b_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_eth_ext_pm_64b_s *pst_entry = (hi_omci_me_eth_ext_pm_64b_s *)pv_data;
	hi_omci_me_stat_timer_s st_ethextpm_stat_timer;
	hi_omci_me_alarm_timer_s st_ethextpm_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	if (ntohs(pst_entry->st_ctrlblock.us_class) != HI_OMCI_PRO_ME_PPTP_ETH_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	/* 删除ETH EXT PM告警实体 */
	i_ret = hi_omci_sql_alarm_inst_delete(HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_ethextpm_stat_timer, sizeof(st_ethextpm_stat_timer), 0, sizeof(st_ethextpm_stat_timer));
	st_ethextpm_stat_timer.ui_meid = HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E;
	st_ethextpm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_ethextpm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_ethextpm_alarm_timer, sizeof(st_ethextpm_alarm_timer), 0, sizeof(st_ethextpm_alarm_timer));
	st_ethextpm_alarm_timer.ui_meid = HI_OMCI_PRO_ME_ETH_EXT_PM_64B_E;
	st_ethextpm_alarm_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_alarm_stop(&st_ethextpm_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
