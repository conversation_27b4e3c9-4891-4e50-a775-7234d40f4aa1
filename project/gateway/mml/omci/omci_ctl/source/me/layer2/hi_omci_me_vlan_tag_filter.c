/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_vlan_tag_filter.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-28
  Description: OMCI manager entity VLAN Tagging Filter Data file.
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


extern hi_list_head g_st_map_listhead;

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/

#define HI_OMCI_VLAN_FILT_ACT_VID_INVESTIGATION1 0x4  /* Action (h) (VID investigation) */
#define HI_OMCI_VLAN_FILT_ACT_VID_INVESTIGATION2 0x10 /* Action (h) (VID investigation) */
#define HI_OMCI_VLAN_FILT_ACT_VID_INVESTIGATION3 0xf  /* Action (h) (VID investigation) */

#define HI_OMCI_VLAN_FILT_ACT_BRIDGING           0x15 /* Bridging (a) (no investigation) */

#define HI_OMCI_VLAN_FILT_IS_TBL(mask) HI_OMCI_CHECK_ATTR(mask,HI_OMCI_ATTR1)
#define HI_OMCI_VLAN_FILT_IS_OPT(mask) HI_OMCI_CHECK_ATTR(mask,HI_OMCI_ATTR2)
#define HI_OMCI_VLAN_FILT_IS_NUM(mask) HI_OMCI_CHECK_ATTR(mask,HI_OMCI_ATTR3)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
hi_uint32 hi_omci_me_vlan_filt_config_opt(hi_uchar8 uc_opt, hi_ushort16 us_vlan, hi_omci_tapi_port_e em_port)
{
	hi_uint32 ui_ret;

	switch (uc_opt) {
	case HI_OMCI_VLAN_FILT_ACT_VID_INVESTIGATION1:
	case HI_OMCI_VLAN_FILT_ACT_VID_INVESTIGATION2:
	case HI_OMCI_VLAN_FILT_ACT_VID_INVESTIGATION3:
		ui_ret = hi_omci_tapi_vlan_filter_set(em_port, HI_ENABLE);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_tapi_vlan_set(em_port, us_vlan);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_VLAN_FILT_ACT_BRIDGING:
		ui_ret = hi_omci_tapi_vlan_filter_set(em_port, HI_DISABLE);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_tapi_vlan_del(em_port, us_vlan);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	default:
		break;
	}

	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_vlan_filt_set
 Description : VLAN Tagging Filter Data set
               数据库操作前处理
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_vlan_filt_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	/*TODO : you need to add code here*/
	hi_uint32   ui_ret = HI_RET_SUCC;
	hi_omci_me_vlan_filt_s *pst_vlanfilt = (hi_omci_me_vlan_filt_s *)pv_data;
	hi_omci_me_vlan_filt_s st_vf_origin;
	static hi_omci_me_vlan_filt_s st_vf_new;
	hi_uint32 ui_vid;
	hi_ushort16 us_instid = pst_vlanfilt->st_msghead.us_instid;
	hi_ushort16 us_mask = pst_vlanfilt->st_msghead.us_attmask;
	hi_uchar8 uc_portid;
	hi_uchar8 uc_tptype;
	static hi_uchar8 uc_del_origin = 0;
	static hi_uchar8 uc_new = 0;

	hi_omci_debug("us_instid=%x,uc_forward_oper=%hhu,uc_entitynum=%hhu\n",
		      us_instid, pst_vlanfilt->uc_forward_oper, pst_vlanfilt->uc_entitynum);

	/* 没有属性要配置 */
	if (!HI_OMCI_VLAN_FILT_IS_TBL(us_mask) &&
	    !HI_OMCI_VLAN_FILT_IS_OPT(us_mask) &&
	    !HI_OMCI_VLAN_FILT_IS_NUM(us_mask)) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	ui_ret = hi_omci_me_macportcfg_get_portid(us_instid, &uc_portid);
	HI_OMCI_RET_CHECK(ui_ret);
	if (0xff == uc_portid) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}
	if (0 == uc_del_origin) {
		ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VLAN_FILT_E, us_instid, &st_vf_origin);
		HI_OMCI_RET_CHECK(ui_ret);

		/* 先清空VLAN列表 */
		for (ui_vid = 0; ui_vid < st_vf_origin.uc_entitynum; ui_vid++) {
			ui_ret = hi_omci_tapi_vlan_del((hi_omci_tapi_port_e)uc_portid,
						       HI_OMCI_GET_VLAN_FROM_TCI(ntohs(st_vf_origin.us_vlantab[ui_vid])));
			HI_OMCI_RET_CHECK(ui_ret);
		}
		if (0 != st_vf_origin.uc_forward_oper) { //创建的时候已有forward_oper，set的时候可能不会再下
			st_vf_new.uc_forward_oper = st_vf_origin.uc_forward_oper;
			uc_new |= 2;
		}
		uc_del_origin = 1;
	}

	/* 得到最新的完整实例 */
	if (HI_OMCI_VLAN_FILT_IS_TBL(us_mask)) {
		HI_OS_MEMCPY_S(st_vf_new.us_vlantab, sizeof(st_vf_new.us_vlantab), pst_vlanfilt->us_vlantab,
			       sizeof(st_vf_new.us_vlantab));
		uc_new |= 1;
	}

	if (HI_OMCI_VLAN_FILT_IS_OPT(us_mask)) {
		st_vf_new.uc_forward_oper = pst_vlanfilt->uc_forward_oper;
		uc_new |= 2;
	}

	if (HI_OMCI_VLAN_FILT_IS_NUM(us_mask)) {
		/* vlan numbe must not equal 0  and larger than 12 */
		if (HI_OMCI_VLAN_FILTER_TAB < pst_vlanfilt->uc_entitynum) {
			hi_omci_debug("erro:the entitynum is %hhu\r\n", pst_vlanfilt->uc_entitynum);
			return HI_OMCI_PRO_ERR_PARA_ERR_E;
		}

		st_vf_new.uc_entitynum = pst_vlanfilt->uc_entitynum;
		uc_new |= 4;
	}

	if (7 == uc_new) { //消息配置完全
		uc_new = 0;
		uc_del_origin = 0;

		/* 配置新的VLAN列表 */
		for (ui_vid = 0; ui_vid < st_vf_new.uc_entitynum; ui_vid++) {
			ui_ret = hi_omci_me_vlan_filt_config_opt(st_vf_new.uc_forward_oper,
					HI_OMCI_GET_VLAN_FROM_TCI(ntohs(st_vf_new.us_vlantab[ui_vid])),
					(hi_omci_tapi_port_e)uc_portid);
			HI_OMCI_RET_CHECK(ui_ret);
		}

		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, HI_OMCI_ATTR3, &uc_tptype);
		HI_OMCI_RET_CHECK(ui_ret);

		if (HI_MAC_BRI_PORT_8021P_MAP_E == uc_tptype || HI_MAC_BRI_PORT_IWTP_E == uc_tptype) {
			ui_ret = hi_omci_map_msg_hander(us_instid, HI_OMCI_MAP_EVENT_BP_TO_VF_E, &st_vf_new);
			HI_OMCI_RET_CHECK(ui_ret);
		}
		HI_OS_MEMSET_S(&st_vf_new, sizeof(st_vf_new), 0, sizeof(st_vf_new));

	}

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_vlan_filt_create
 Description : VLAN Tagging Filter Data create
               数据库操作后处理
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_vlan_filt_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	/*TODO : you need to add code here*/
	hi_uint32   ui_ret = HI_RET_SUCC;
	hi_omci_me_vlan_filt_s *pst_vlanfilt = (hi_omci_me_vlan_filt_s *)pv_data;
	hi_uint32 ui_vid;
	hi_ushort16 us_instid = pst_vlanfilt->st_msghead.us_instid;
	hi_uchar8   uc_portid;

	hi_omci_debug("us_instid=%x,uc_forward_oper=%hhu,uc_entitynum=%hhu\n",
		      us_instid, pst_vlanfilt->uc_forward_oper, pst_vlanfilt->uc_entitynum);

	if (HI_OMCI_VLAN_FILTER_TAB < pst_vlanfilt->uc_entitynum) {
		hi_omci_debug("erro:the entitynum is %hhu\r\n", pst_vlanfilt->uc_entitynum);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	ui_ret = hi_omci_me_macportcfg_get_portid(us_instid, &uc_portid);
	HI_OMCI_RET_CHECK(ui_ret);
	if (0xff == uc_portid) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}
	for (ui_vid = 0; ui_vid < pst_vlanfilt->uc_entitynum; ui_vid++) {
		ui_ret = hi_omci_me_vlan_filt_config_opt(pst_vlanfilt->uc_forward_oper,
				HI_OMCI_GET_VLAN_FROM_TCI(ntohs(pst_vlanfilt->us_vlantab[ui_vid])),
				(hi_omci_tapi_port_e)uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	/* 关联的是ANI侧的MAC bridge port; uc_forward_oper=1代表untag映射*/
	if (uc_portid == HI_OMCI_TAPI_PON_E) {
		if ((pst_vlanfilt->uc_entitynum == 0) && (pst_vlanfilt->uc_forward_oper == 1)) {
			ui_ret = hi_omci_map_msg_hander(us_instid, HI_OMCI_MAP_EVENT_BP_TO_8021P_E, pst_vlanfilt);
			HI_OMCI_RET_CHECK(ui_ret);
			return HI_RET_SUCC;
		}
		if ((pst_vlanfilt->uc_entitynum > 0) || (pst_vlanfilt->uc_forward_oper == 1)) {
			ui_ret = hi_omci_map_msg_hander(us_instid, HI_OMCI_MAP_EVENT_BP_TO_VF_E, pst_vlanfilt);
			HI_OMCI_RET_CHECK(ui_ret);
		}
		if ((pst_vlanfilt->uc_entitynum == 0) && (pst_vlanfilt->uc_forward_oper == 0)) {
			ui_ret = hi_omci_map_msg_hander(us_instid, HI_OMCI_MAP_EVENT_BP_TO_GEMIWTP_E, pst_vlanfilt);
			HI_OMCI_RET_CHECK(ui_ret);
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_vlan_filt_del
 Description : VLAN Tagging Filter Data delete
               数据库操作前处理
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_vlan_filt_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	/*TODO : you need to add code here*/
	hi_uint32 ui_ret = HI_RET_SUCC;
	hi_omci_me_vlan_filt_s *pst_vlanfilt = (hi_omci_me_vlan_filt_s *)pv_data;
	hi_omci_me_mac_port_cfg_s st_cfg;
	hi_uint32 ui_vid;
	hi_uchar8 uc_portid;
	hi_list_head *pst_list_pos = HI_NULL;
	hi_list_head *pst_list_next = HI_NULL;
	hi_omci_map_state_s *pst_state = HI_NULL;

	ui_ret = hi_omci_me_macportcfg_get_portid(pst_vlanfilt->st_msghead.us_instid, &uc_portid);
	HI_OMCI_RET_CHECK(ui_ret);

	for (ui_vid = 0; ui_vid < pst_vlanfilt->uc_entitynum; ui_vid++) {
		ui_ret = hi_omci_me_vlan_filt_config_opt(HI_OMCI_VLAN_FILT_ACT_BRIDGING,
				HI_OMCI_GET_VLAN_FROM_TCI(ntohs(pst_vlanfilt->us_vlantab[ui_vid])),
				(hi_omci_tapi_port_e)uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	/*兼容ZTE，上行映射删除时不先下发802.1p mapper service profile设置，而是先下发vlan删除，再下发mac port cfg删除*/
	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, (hi_ushort16)pst_vlanfilt->st_msghead.us_instid,
				      &st_cfg);
	if ((HI_RET_SUCC == ui_ret) && ((HI_MAC_BRI_PORT_8021P_MAP_E == st_cfg.uc_tptype) ||
					(HI_MAC_BRI_PORT_IWTP_E  == st_cfg.uc_tptype))) {
		//ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_802_1P_E, (hi_ushort16)pst_vlanfilt->st_msghead.us_instid, &st_8021p);
		//if(HI_RET_SUCC == ui_ret)
		//{
		hi_list_for_each_safe(pst_list_pos, pst_list_next, &g_st_map_listhead) {
			pst_state = (hi_omci_map_state_s *)hi_list_entry(pst_list_pos, hi_omci_map_state_s, st_listhead);
			if (HI_NULL != pst_state && pst_state->ui_bp_id == pst_vlanfilt->st_msghead.us_instid) {
				pst_state->pv_data = pv_data;
				if ((HI_OMCI_MAP_STATE_VLAN_PRI == pst_state->em_state) || (HI_MAC_BRI_PORT_8021P_MAP_E == st_cfg.uc_tptype)) {
					hi_omci_map_msg_hander(pst_vlanfilt->st_msghead.us_instid, HI_OMCI_MAP_EVENT_8021P_RELEASE_E, HI_NULL);
				} else if ((HI_OMCI_MAP_STATE_VLAN == pst_state->em_state) || (HI_MAC_BRI_PORT_IWTP_E  == st_cfg.uc_tptype)) {
					hi_omci_map_msg_hander(pst_vlanfilt->st_msghead.us_instid, HI_OMCI_MAP_EVENT_VF_RELEASE_E, HI_NULL);
				}
			}
		}
		//}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

