/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_mac_port_cfg.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-28
  Description: OMCI manager entity MAC Bridge Port Configration Data file.
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/

/* Outbound TD pointer（OPT） */
#define HI_OMCI_MAC_PORT_IS_OUTBOUND(mask) HI_OMCI_CHECK_ATTR(mask,HI_OMCI_ATTR11)

/* Inbound TD pointer（OPT） */
#define HI_OMCI_MAC_PORT_IS_INBOUND(mask) HI_OMCI_CHECK_ATTR(mask,HI_OMCI_ATTR12)

/* MAC learning depth（OPT） */
#define HI_OMCI_MAC_PORT_IS_LEARN_DEPTH(mask) HI_OMCI_CHECK_ATTR(mask,HI_OMCI_ATTR13)

#define HI_OMCI_MAC_PORT_INVALID_PTR 0XFFFF

/* MAC bridge port config */
#define HI_OMCI_MAC_PORT_CONFIG_NUM 64

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/

/*****************************************************************************
 函 数 名  : hi_omci_me_check_mapport
 功能描述  : 判断是否根据uni port做流映射
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
#if 0
static hi_uint32 hi_omci_me_check_mapport(hi_void)
{
	hi_ushort16 us_bridge = 0xFFFF, us_instid = 0;
	hi_ushort16 us_bridgetmp = 0xFFFF;
	hi_ushort16 us_macportinst = 0xFFFF;
	hi_ushort16 us_uniinst = 1;
	hi_uint32   ui_ret;
	hi_uchar8   uc_tptype = HI_MAC_BRI_PORT_UNI_E;
	hi_omci_tapi_sysinfo_s st_info;

	ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(ui_ret);

	/*查看不同uni port是否对应到不同mac bridge*/
	for (us_uniinst = 0; us_uniinst < st_info.ui_port_num; us_uniinst++) {
		ui_ret = hi_omci_tapi_port_pptpinstid_get(us_uniinst, &us_instid);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_ext_get_instid_byattr2(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR3, &uc_tptype,
							HI_OMCI_ATTR4, &us_instid, &us_macportinst);
		if (HI_RET_SUCC == ui_ret) {
			hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_macportinst, HI_OMCI_ATTR1, &us_bridgetmp);
			if (hi_omci_ext_get_instnum(HI_OMCI_PRO_ME_MAC_SERVICE_E, us_bridgetmp)) {
				if (us_bridge == 0xFFFF) {
					us_bridge = us_bridgetmp;
				} else if (us_bridge != us_bridgetmp) {
					return HI_TRUE;
				}
			}
		}
	}
	return HI_FALSE;
}
#endif
/*****************************************************************************
  Function     : hi_omci_me_macportcfg_set_mac_num
  Description  : 配置端口MAC最大数目
  Input Param  : hi_uint32 ui_port
                 hi_uint32 ui_enable
                 hi_uint32 ui_num
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
static hi_uint32 hi_omci_me_macportcfg_set_mac_num(hi_uint32 ui_port, hi_uint32 ui_enable, hi_uint32 ui_num)
{
	hi_uint32 ui_ret;
	hi_uint32 ui_lrn_enable;
	hi_uint32 ui_age_time;

	if (ui_enable == HI_DISABLE) {
		ui_ret = hi_omci_tapi_br_num_set((hi_omci_tapi_port_e)ui_port, 0);
		HI_OMCI_RET_CHECK(ui_ret);
	} else {
		ui_ret = hi_omci_tapi_br_learn_get((hi_omci_tapi_port_e)ui_port, &ui_lrn_enable);
		HI_OMCI_RET_CHECK(ui_ret);

		/* 如果当前学习不使能, MAC地址限制没有意义 */
		if (ui_lrn_enable == HI_DISABLE) {
			return HI_RET_SUCC;
		}

		ui_ret = hi_omci_tapi_br_learn_set((hi_omci_tapi_port_e)ui_port, HI_DISABLE);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_tapi_br_age_get(&ui_age_time);
		if (HI_RET_SUCC != ui_ret) {
			(void)hi_omci_tapi_br_learn_set((hi_omci_tapi_port_e)ui_port, HI_ENABLE);

			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		/* 设置MAC地址一秒老化，清除原有MAC地址 */
		ui_ret = hi_omci_tapi_br_age_set(1);
		if (HI_RET_SUCC != ui_ret) {
			(void)hi_omci_tapi_br_age_set(ui_age_time);

			(void)hi_omci_tapi_br_learn_set((hi_omci_tapi_port_e)ui_port, ui_lrn_enable);

			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		hi_os_sleep(2);

		ui_ret |= hi_omci_tapi_br_num_set((hi_omci_tapi_port_e)ui_port, ui_num);

		ui_ret |= hi_omci_tapi_br_age_set(ui_age_time);

		ui_ret |= hi_omci_tapi_br_learn_set((hi_omci_tapi_port_e)ui_port, ui_lrn_enable);

		HI_OMCI_RET_CHECK(ui_ret);
	}

	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_macportcfg_get_portid
 Description : 获取mac port cfg对应的端口号
 Input Parm  : hi_ushort16 us_instid
 Output Parm : hi_uchar8 *puc_portid
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_macportcfg_get_portid(hi_ushort16 us_instid, hi_uchar8 *puc_portid)
{
	hi_uint32 ui_ret;
	hi_ushort16 us_tp_ptr;
	hi_uchar8 uc_tptype;

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, HI_OMCI_ATTR3, &uc_tptype);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 获取端口号 */
	if (uc_tptype == HI_MAC_BRI_PORT_UNI_E) {
		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, HI_OMCI_ATTR4, &us_tp_ptr);
		HI_OMCI_RET_CHECK(ui_ret);

		*puc_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_tp_ptr);
	} else if (uc_tptype == HI_MAC_BRI_PORT_8021P_MAP_E || uc_tptype == HI_MAC_BRI_PORT_IWTP_E) {
		*puc_portid = HI_OMCI_TAPI_PON_E;
	} else if (uc_tptype == HI_MAC_BRI_PORT_VEIP_E) {
		*puc_portid = HI_OMCI_TAPI_VEIP_E;
	} else {
		*puc_portid = 0xff;
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_macportcfg_create
 Description : MAC Bridge Port Configration Data create
               数据库操作后处理
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_macportcfg_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	/* TODO : you need to add code here */
	hi_omci_me_mac_port_cfg_s *pst = (hi_omci_me_mac_port_cfg_s *)pv_data;
	hi_omci_me_mac_port_cfg_s st_macportcfg;
	hi_uint32 ui_ret = HI_RET_SUCC;
	hi_uint32 ui_instnum = 0;
	hi_uint32 aui_instid[HI_OMCI_MAC_PORT_CONFIG_NUM] = {0};
	hi_uint32 ui_i;
	hi_ushort16 us_instid = pst->st_msghead.us_instid;

	hi_omci_debug("[INFO]:us_instid=0x%x\n", pst->st_msghead.us_instid);
	HI_OS_MEMSET_S(&st_macportcfg, sizeof(st_macportcfg), 0, sizeof(st_macportcfg));
	/* 修改端口映射使能判断条件 */
#if 0
	ui_ret = hi_omci_me_check_mapport();
	if (ui_ret == HI_TRUE) {
		hi_omci_map_port_enable(HI_ENABLE);
	} else {
		hi_omci_map_port_enable(HI_DISABLE);
	}
#endif
	hi_omci_debug("[INFO]:bdgid_ptr=%hu,portnum=%hhu, tptype=%hhu, tp_ptr=%hu,pri =%hu\n",
		      pst->us_bdgid_ptr, pst->uc_portnum, pst->uc_tptype, pst->us_tp_ptr, pst->uc_pri);
	hi_omci_debug("[INFO]:pathcost=%hu,spanind=%hhu, out_ptr=%hu, in_ptr=%hu,macdepth =%hhu\n",
		      pst->us_pathcost, pst->uc_spanind, pst->us_out_ptr, pst->us_in_ptr, pst->uc_macdepth);

	/* 创建MAC bridge port bridge table data实例 */
	ui_ret = hi_omci_me_macport_table_create(us_instid);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 创建MAC bridge port filter table data实例 */
	ui_ret = hi_omci_me_macportfilt_create(us_instid);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 创建MAC bridge port filter preassign table实例 */
	ui_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_MAC_PORT_FILT_PRE_E, us_instid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}
	/* 获取MAC bridge port cfg data所有实例编号和个数 */
	ui_ret = hi_omci_ext_get_install(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, aui_instid, HI_OMCI_MAC_PORT_CONFIG_NUM,
					 &ui_instnum);
	HI_OMCI_RET_CHECK(ui_ret);
	hi_omci_debug("[INFO]:ui_instnum=0x%x\n", ui_instnum);

	switch (pst->uc_tptype) {
	case HI_MAC_BRI_PORT_UNI_E:

		/* 配置端口MAC学习最大数目 */
		ui_ret = hi_omci_tapi_br_num_set((hi_omci_tapi_port_e)HI_OMCI_GET_PORTID_FROM_INSTID(pst->us_tp_ptr),
						 pst->uc_macdepth);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_MAC_BRI_PORT_8021P_MAP_E:
		for (ui_i = 0; ui_i < ui_instnum; ui_i++) {
			hi_omci_debug("[INFO]:aui_instid[%u]=0x%x\n", ui_i, aui_instid[ui_i]);
			ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, aui_instid[ui_i], &st_macportcfg);
			if ((HI_RET_SUCC != ui_ret)) {
				hi_omci_systrace(ui_ret, 0, 0, 0, 0);
				continue;
			}
			hi_omci_debug("[INFO]:tptype=0x%x\n", st_macportcfg.uc_tptype);
			if (HI_MAC_BRI_PORT_8021P_MAP_E == st_macportcfg.uc_tptype) {
				continue;
			} else if (HI_MAC_BRI_PORT_VEIP_E == st_macportcfg.uc_tptype) {
				hi_omci_map_port_enable(HI_ENABLE);
				break;
			}
		}

		if (ui_i == ui_instnum) {
			hi_omci_map_port_enable(HI_DISABLE);
		}

		ui_ret = hi_omci_map_start(us_instid);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_map_msg_hander(us_instid, HI_OMCI_MAP_EVENT_BP_TO_8021P_E, HI_NULL);
		HI_OMCI_RET_CHECK(ui_ret);

		/* 配置PON口MAC学习最大数目 */
		ui_ret = hi_omci_tapi_br_num_set(HI_OMCI_TAPI_PON_E, pst->uc_macdepth);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_MAC_BRI_PORT_IWTP_E:
		for (ui_i = 0; ui_i < ui_instnum; ui_i++) {
			hi_omci_debug("[INFO]:aui_instid[%u]=0x%x\n", ui_i, aui_instid[ui_i]);
			ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, aui_instid[ui_i], &st_macportcfg);
			if ((HI_RET_SUCC != ui_ret)) {
				hi_omci_systrace(ui_ret, 0, 0, 0, 0);
				continue;
			}
			hi_omci_debug("[INFO]:tptype=0x%x\n", st_macportcfg.uc_tptype);
			if (HI_MAC_BRI_PORT_IWTP_E == st_macportcfg.uc_tptype) {
				continue;
			} else if (HI_MAC_BRI_PORT_VEIP_E == st_macportcfg.uc_tptype) {
				hi_omci_map_port_enable(HI_ENABLE);
				break;
			}
		}

		if (ui_i == ui_instnum) {
			hi_omci_map_port_enable(HI_DISABLE);
		}
		ui_ret = hi_omci_map_start(us_instid);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_map_msg_hander(us_instid, HI_OMCI_MAP_EVENT_BP_TO_GEMIWTP_E, HI_NULL);
		HI_OMCI_RET_CHECK(ui_ret);

		/* 配置PON口MAC学习最大数目 */
		ui_ret = hi_omci_tapi_br_num_set(HI_OMCI_TAPI_PON_E, pst->uc_macdepth);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_MAC_BRI_PORT_IP_E:
	case HI_MAC_BRI_PORT_MIWTP_E:
	case HI_MAC_BRI_PORT_ATMTP_E:
	case HI_MAC_BRI_PORT_PPTP_xDSL_E:
	case HI_MAC_BRI_PORT_PPTP_vDSL_E:
	case HI_MAC_BRI_PORT_ETHTP_E:
	case HI_MAC_BRI_PORT_PPTP_80211_E:
		break;
	/* 当前类型为VEIP，如果存在指向8021p或者IWTP的实例，则使能端口映射 */
	case HI_MAC_BRI_PORT_VEIP_E:
		for (ui_i = 0; ui_i < ui_instnum; ui_i++) {
			hi_omci_debug("[INFO]:aui_instid[%u]=0x%x\n", ui_i, aui_instid[ui_i]);
			ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, aui_instid[ui_i], &st_macportcfg);
			if ((HI_RET_SUCC != ui_ret)) {
				hi_omci_systrace(ui_ret, 0, 0, 0, 0);
				continue;
			}
			hi_omci_debug("[INFO]:tptype=0x%x\n", st_macportcfg.uc_tptype);
			if (HI_MAC_BRI_PORT_VEIP_E == st_macportcfg.uc_tptype) {
				continue;
			} else if ((HI_MAC_BRI_PORT_8021P_MAP_E == st_macportcfg.uc_tptype) ||
				   (HI_MAC_BRI_PORT_IWTP_E == st_macportcfg.uc_tptype)) {
				hi_omci_map_port_enable(HI_ENABLE);
				break;
			}
		}

		if (ui_i == ui_instnum) {
			hi_omci_map_port_enable(HI_DISABLE);
		}
		break;

	default :
		hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_macportcfg_set
 Description : MAC Bridge Port Configration Data set
               数据库操作后处理
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_macportcfg_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	/*TODO : you need to add code here*/
	hi_uint32 ui_ret = HI_RET_SUCC;
	hi_omci_me_mac_port_cfg_s *pst_inst = (hi_omci_me_mac_port_cfg_s *)pv_data;
	hi_uint32 ui_enable;
	hi_uint32 ui_cir;
	hi_uint32 ui_cbs;
	hi_uint32 ui_pir;
	hi_uint32 ui_pbs;
	hi_ushort16 us_mask = pst_inst->st_msghead.us_attmask;
	hi_uchar8 uc_tptype = pst_inst->uc_tptype;
	hi_uchar8 uc_portid;

	hi_omci_debug("[INFO]:us_instid=0x%x,us_mask =0x%x\n, port = 0x%x, depth = 0x%x",
		      pst_inst->st_msghead.us_instid, us_mask, pst_inst->uc_portnum, pst_inst->uc_macdepth);

	switch (uc_tptype) {
	case HI_MAC_BRI_PORT_UNI_E:

		uc_portid = HI_OMCI_GET_PORTID_FROM_INSTID(pst_inst->us_tp_ptr);

		/* 出口流量整形 */
		if (HI_OMCI_MAC_PORT_IS_OUTBOUND(us_mask)) {
			if (HI_OMCI_MAC_PORT_INVALID_PTR == pst_inst->us_out_ptr) {
				ui_cir = 0;
			} else {
				ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_TRAF_DESC_E, pst_inst->us_out_ptr, HI_OMCI_ATTR1, &ui_cir);
				HI_OMCI_RET_CHECK(ui_ret);
			}

			ui_cir = ui_cir * 8 / 1000;// Byte/s转换为 Kbps
			ui_ret = hi_omci_tapi_port_outcar_set((hi_omci_tapi_port_e)uc_portid, ui_cir);
			HI_OMCI_RET_CHECK(ui_ret);
		}

		/* 入口流量限速 */
		if (HI_OMCI_MAC_PORT_IS_INBOUND(us_mask)) {
			if (pst_inst->us_in_ptr == HI_OMCI_MAC_PORT_INVALID_PTR) {
				hi_omci_tapi_port_incar_set((hi_omci_tapi_port_e)uc_portid, 0, 0, 0, 0);
				HI_OMCI_RET_CHECK(ui_ret);
			} else {
				ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_TRAF_DESC_E, pst_inst->us_in_ptr, HI_OMCI_ATTR1,  &ui_cir);
				ui_ret |= hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_TRAF_DESC_E, pst_inst->us_in_ptr, HI_OMCI_ATTR2, &ui_pir);
				ui_ret |= hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_TRAF_DESC_E, pst_inst->us_in_ptr, HI_OMCI_ATTR3, &ui_cbs);
				ui_ret |= hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_TRAF_DESC_E, pst_inst->us_in_ptr, HI_OMCI_ATTR4, &ui_pbs);
				HI_OMCI_RET_CHECK(ui_ret);

				ui_cir = ui_cir * 8 / 1000;// Byte/s转换为 Kbps
				ui_pir = ui_pir * 8 / 1000;// Byte/s转换为 Kbps
				ui_cbs = ui_cbs * 8 / 1000;// Byte/s转换为 Kbps
				ui_pbs = ui_pbs * 8 / 1000;// Byte/s转换为 Kbps
				hi_omci_tapi_port_incar_set((hi_omci_tapi_port_e)uc_portid, ui_cir, ui_cbs, ui_pir, ui_pbs);
				HI_OMCI_RET_CHECK(ui_ret);
			}
		}

		/* UNI口的MAC地址数目限制 */
		if (HI_OMCI_MAC_PORT_IS_LEARN_DEPTH(us_mask)) {
			if (pst_inst->uc_macdepth == 0) {
				ui_enable = HI_DISABLE;
			} else {
				ui_enable = HI_ENABLE;
			}

			ui_ret = hi_omci_me_macportcfg_set_mac_num(uc_portid, ui_enable, pst_inst->uc_macdepth);
			HI_OMCI_RET_CHECK(ui_ret);
		}

		break;

	case HI_MAC_BRI_PORT_8021P_MAP_E:
	case HI_MAC_BRI_PORT_IWTP_E:

		/* PON口的MAC地址数目限制 */
		if (HI_OMCI_MAC_PORT_IS_LEARN_DEPTH(us_mask)) {
			if (pst_inst->uc_macdepth == 0) {
				ui_enable = HI_DISABLE;
			} else {
				ui_enable = HI_ENABLE;
			}

			ui_ret = hi_omci_me_macportcfg_set_mac_num(HI_OMCI_TAPI_PON_E, ui_enable, pst_inst->uc_macdepth);
			HI_OMCI_RET_CHECK(ui_ret);
		}
		break;

	case HI_MAC_BRI_PORT_MIWTP_E:
	case HI_MAC_BRI_PORT_IP_E:
	case HI_MAC_BRI_PORT_ATMTP_E:
	case HI_MAC_BRI_PORT_PPTP_xDSL_E:
	case HI_MAC_BRI_PORT_PPTP_vDSL_E:
	case HI_MAC_BRI_PORT_ETHTP_E:
	case HI_MAC_BRI_PORT_PPTP_80211_E:
		break;

	default :
		hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_macportcfg_delete
 Description : MAC Bridge Port Configration Data delete
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_macportcfg_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	/*TODO : you need to add code here*/
	hi_uint32  ui_ret = HI_RET_SUCC;
	hi_omci_me_mac_port_cfg_s *pst_entity = (hi_omci_me_mac_port_cfg_s *)pv_data;
	hi_omci_me_mac_port_cfg_s st_portcfg;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, pst_entity->st_msghead.us_instid, &st_portcfg);
	if ((HI_RET_SUCC != ui_ret)) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_UNKNOWN_IN_E;
	}

	/* 删除MAC bridge port bridge table data实例 */
	ui_ret = hi_omci_me_macport_table_delete(us_instid);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 删除MAC bridge port filter table data实例 */
	ui_ret = hi_omci_me_macportfilt_delete(us_instid);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 删除MAC bridge port filter preassign table实例 */
	ui_ret = hi_omci_ext_del_inst(HI_OMCI_PRO_ME_MAC_PORT_FILT_PRE_E, us_instid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	/* Judge if the mac port is ani side */
	if (HI_MAC_BRI_PORT_8021P_MAP_E == st_portcfg.uc_tptype || HI_MAC_BRI_PORT_IWTP_E == st_portcfg.uc_tptype) {
		ui_ret = hi_omci_map_msg_hander(us_instid, HI_OMCI_MAP_EVENT_BP_RELEASE_E, HI_NULL);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
