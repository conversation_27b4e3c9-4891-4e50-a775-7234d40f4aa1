/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_multi_operation.c
  Version    : 初稿
  Author     : owen
  Creation   : 2015-1-22
  Description: Multicast operations profile
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_MULTI_OPER_IS_LEAVE(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR3)
#define HI_OMCI_ME_MULTI_OPER_IS_IGMP_TCI(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR4)
#define HI_OMCI_ME_MULTI_OPER_IS_IGMP_TAG(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR5)
#define HI_OMCI_ME_MULTI_OPER_IS_IGMP_UPRATE(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR6)
#define HI_OMCI_ME_MULTI_OPER_IS_DLIST(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR7)
#define HI_OMCI_ME_MULTI_OPER_IS_DTCI(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR16)

#define HI_OMCI_ME_MULTI_OPER_DLIST_NUM 48
static hi_uint32 g_ui_igmp_uprate_carid[HI_OMCI_ETH_MAX_NUM] = {0};
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
typedef enum {
	HI_OMCI_ME_MULTI_OPER_IPV4 = 0,
	HI_OMCI_ME_MULTI_OPER_IPV6
} hi_omci_me_multi_oper_ipversion;


typedef enum {
	HI_OMCI_MC_MAP_STATE_INIT = 0,
	HI_OMCI_MC_MAP_STATE_PRI_PREVIEW,
	HI_OMCI_MC_MAP_STATE_VLAN_PREVIEW,
	HI_OMCI_MC_MAP_STATE_VLAN_PRI_PREVIEW,
	HI_OMCI_MC_MAP_STATE_PRI,
	HI_OMCI_MC_MAP_STATE_VLAN,
	HI_OMCI_MC_MAP_STATE_VLAN_PRI,
	HI_OMCI_MC_MAP_STATE_NUM,
} hi_omci_mc_map_state_e;

typedef struct {
	hi_uint32 ui_bp_id;
	hi_void *pv_data;
	hi_omci_mc_map_state_e em_state;
	hi_list_head st_listhead;
} hi_omci_mc_map_state_s;

static hi_uint32 g_ui_port_tabnum[HI_OMCI_ETH_MAX_NUM] = {0};
extern hi_uint32 g_ui_port_map;
extern hi_list_head g_st_map_listhead;
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/

/******************************************************************************
 Function    : __omci_me_multi_oper_table_printf
 Description : 表项打印
 Input Parm  : hi_uchar8 *puc_table,
               hi_uint32 ui_len
 Output Parm : N/A

 Return      : hi_void
******************************************************************************/
hi_void __omci_me_multi_oper_table_printf(hi_uchar8 *puc_table, hi_uint32 ui_len)
{
	hi_uint32 ui_size = sizeof(hi_omci_ctrl_table_s);
	hi_uint32 ui_index;

	if (!hi_log_print_on((hi_uint32)HI_SUBMODULE_OMCI_COM, HI_DBG_LEVEL_DEBUG)
	    && !hi_log_print_on((hi_uint32)HI_SYSBASE_GLB, HI_DBG_LEVEL_DEBUG)) {
		return;
	}

	hi_os_printf("================== start =====================\n");

	for (ui_index = 0; ui_index < ui_len; ui_index += ui_size, puc_table += ui_size) {
		HI_PRINT_MEM(ui_size, puc_table);
	}

	hi_os_printf("================== end =====================\n");
}

/******************************************************************************
 Function    : __omci_me_multi_oper_gemportid_get
 Description : 获取单播GEMPORT ID
 Input Parm  : ui_vlan ui_pri uc_portid
 Output Parm : N/A
 Return      : pui_gemportid
******************************************************************************/
hi_uint32 __omci_me_multi_oper_gemportid_get(hi_uint32 ui_vlan, hi_uint32 ui_pri, hi_uchar8 uc_portid)
{
	hi_omci_tapi_map_s st_mc_tapi_map;
	hi_list_head *pst_list_pos = HI_NULL;
	hi_list_head *pst_list_next = HI_NULL;
	hi_omci_mc_map_state_s *pst_state = HI_NULL;
	hi_uint32 map_state;
	hi_uint32 ui_ret;

	hi_list_for_each_safe(pst_list_pos, pst_list_next, &g_st_map_listhead) {
		pst_state = (hi_omci_mc_map_state_s *)hi_list_entry(pst_list_pos, hi_omci_mc_map_state_s, st_listhead);
		if (HI_NULL != pst_state) {
			map_state = pst_state->em_state;

			HI_OS_MEMSET_S(&st_mc_tapi_map, sizeof(hi_omci_tapi_map_s), 0, sizeof(hi_omci_tapi_map_s));

			if (HI_ENABLE == g_ui_port_map) {
				st_mc_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_IGR;
			}

			if (HI_OMCI_MC_MAP_STATE_VLAN == map_state) {
				st_mc_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_VLAN;
			} else if (HI_OMCI_MC_MAP_STATE_PRI == map_state) {
				st_mc_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_PRI;
			} else if (HI_OMCI_MC_MAP_STATE_VLAN_PRI == map_state) {
				st_mc_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_VLAN;
				st_mc_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_PRI;
			}

			st_mc_tapi_map.ui_vlan = ui_vlan;
			st_mc_tapi_map.ui_pri = ui_pri;
			st_mc_tapi_map.em_port = (hi_omci_tapi_port_e)uc_portid;
			ui_ret = hi_omci_tapi_map_get(&st_mc_tapi_map);
			if (HI_RET_SUCC == ui_ret) {
				return st_mc_tapi_map.ui_gemport;
			}
		}
	}

	hi_omci_debug("multi oper get gemport fail\r\n");
	return (hi_uint32)0xffffffff;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_portid_get
 Description : 获取multicast operation profile对应的端口号
 Input Parm  : hi_ushort16 us_instid multicast operation profile实例ID
 Output Parm : hi_uchar8 *puc_portid 端口号
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_portid_get(hi_ushort16 us_instid, hi_uchar8 *puc_portid)
{
	hi_omci_me_multisubs_pkgtab_s *pst_pkgtab;
	hi_omci_me_multisubs_pkgtab_s st_empty;
	hi_uint32 ui_ret;
	hi_uint32 ui_portnum;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_index;
	hi_ushort16 us_portid;
	hi_ushort16 us_ms_instid;
	hi_ushort16 us_mp_instid;
	hi_ushort16 us_pptp_eth_instid;

	HI_OS_MEMSET_S(&st_empty, sizeof(st_empty), 0, sizeof(st_empty));

	ui_portnum = hi_omci_ext_get_instbyme(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E);

	for (us_portid = 0; us_portid < ui_portnum; us_portid++) {
		ui_ret = hi_omci_tapi_port_pptpinstid_get(us_portid, &us_pptp_eth_instid);
		if (HI_RET_SUCC != ui_ret) {
			continue;
		}

		ui_ret = hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E,
						       HI_OMCI_ATTR4, &us_pptp_eth_instid, &us_ms_instid);
		if (HI_RET_SUCC != ui_ret) {
			continue;
		}

		ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MULTI_SUBS_CONFIG_E, HI_OMCI_ATTR6, &ui_tabnum);
		HI_OMCI_RET_CHECK(ui_ret);

		pst_pkgtab = (hi_omci_me_multisubs_pkgtab_s *)hi_os_malloc(sizeof(hi_omci_me_multisubs_pkgtab_s) * ui_tabnum);
		if (HI_NULL == pst_pkgtab) {
			hi_omci_l2_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		HI_OS_MEMSET_S(pst_pkgtab, (sizeof(hi_omci_me_multisubs_pkgtab_s) * ui_tabnum), 0,
			       (sizeof(hi_omci_me_multisubs_pkgtab_s) * ui_tabnum));

		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTI_SUBS_CONFIG_E,
					      us_ms_instid, HI_OMCI_ATTR6, pst_pkgtab);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_pkgtab);
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_omci_l2_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		for (ui_index = 0; ui_index < ui_tabnum; ui_index++, pst_pkgtab++) {
			if (0 == hi_os_memcmp(pst_pkgtab, &st_empty, sizeof(st_empty))) {
				continue;
			}

			if (us_instid == ntohs(pst_pkgtab->us_multi_oper_ptr)) {
				hi_os_free(pst_pkgtab);
				*puc_portid = (hi_uchar8)us_portid;
				hi_omci_debug("[INFO]:us_portid =0x%x\n", us_portid);
				hi_omci_l2_systrace(HI_RET_SUCC, us_portid, 0, 0, 0);
				return HI_RET_SUCC;
			}
		}

		hi_os_free(pst_pkgtab);

		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTI_SUBS_CONFIG_E,
					      us_ms_instid, HI_OMCI_ATTR2, &us_mp_instid);
		HI_OMCI_RET_CHECK(ui_ret);

		if (us_instid == us_mp_instid) {
			*puc_portid = (hi_uchar8)us_portid;
			hi_omci_debug("[INFO]:us_portid =0x%x\n", us_portid);
			hi_omci_l2_systrace(HI_RET_SUCC, us_portid, 0, 0, 0);
			return HI_RET_SUCC;
		}
	}
	*puc_portid = HI_OMCI_TAPI_VEIP_E;
	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ipversion_get
 Description : 获取IP类型, V4/V6
 Input Parm  : 无
 Output Parm : hi_uint32 *pui_ipversion
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ipversion_get(hi_ushort16 us_instid, hi_uint32 *pui_ipversion)
{
	hi_uint32 ui_ret;
	hi_uchar8 uc_version;

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTI_OPER_PROFILE_E,
				      us_instid, HI_OMCI_ATTR1, &uc_version);
	HI_OMCI_RET_CHECK(ui_ret);

	switch (uc_version) {
	case HI_OMCI_IGMPV1:
	case HI_OMCI_IGMPV2:
	case HI_OMCI_IGMPV3:
		*pui_ipversion = HI_OMCI_ME_MULTI_OPER_IPV4;
		break;

	case HI_OMCI_MLDV1:
	case HI_OMCI_MLDV2:
		*pui_ipversion = HI_OMCI_ME_MULTI_OPER_IPV6;
		break;

	default:
		hi_omci_l2_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_l2_systrace(HI_RET_SUCC, *pui_ipversion, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_v4_set
 Description : 配置IPv4动态组播访问控制列表
 Input Parm  : hi_omci_ctrl_table_s *pst_item,
               hi_omci_ctrl_table_s *pst_tab,
               hi_uint32 ui_tabnum
               hi_ushort16 us_tci   用户VLAN
               hi_uchar8 uc_portid 端口号
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_v4_set(hi_omci_ctrl_table_s *pst_item,
		hi_omci_ctrl_table_s *pst_tab, hi_uint32 *pui_tabnum, hi_ushort16 us_tci, hi_uchar8 uc_portid)
{
	hi_omci_multi_tab_ctrl_s st_tabctrl;
	hi_omci_multi_tab_ctrl_s st_itemctrl;
	hi_omci_tapi_mc_table_list_s st_tapi_list;
	hi_omci_ctrl_table_s st_empty;
	hi_omci_ctrl_table_s *pst_tmp;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum = *pui_tabnum;
	hi_uint32 ui_ret;

	HI_OS_MEMSET_S(&st_empty, sizeof(st_empty), 0, sizeof(st_empty));

	st_itemctrl.us_value = (hi_ushort16)ntohs(pst_item->st_tablectrl.us_value);

	/* 删除原有配置 */
	for (ui_index = 0, pst_tmp = pst_tab; ui_index < ui_tabnum; ui_index++, pst_tmp++) {
		st_tabctrl.us_value = (hi_ushort16)ntohs(pst_tmp->st_tablectrl.us_value);
		if (st_tabctrl.ibits.index == st_itemctrl.ibits.index) {
			HI_OS_MEMSET_S(&st_tapi_list, sizeof(st_tapi_list), 0, sizeof(st_tapi_list));
			st_tapi_list.em_port = (hi_omci_tapi_port_e)uc_portid;
			st_tapi_list.ui_gemportid = ntohs(pst_tmp->st_row.part0.us_gemport);
			st_tapi_list.ui_mcvlan = ntohs(pst_tmp->st_row.part0.us_vlan);
			st_tapi_list.st_ipaddr.start_ip.st_ipv4.ui_v4 = ntohl(pst_tmp->st_row.part0.ui_dip_start);
			st_tapi_list.st_ipaddr.end_ip.st_ipv4.ui_v4 = ntohl(pst_tmp->st_row.part0.ui_dip_end);

			ui_ret = hi_omci_tapi_mc_table_del(&st_tapi_list);
			HI_OMCI_RET_CHECK(ui_ret);

			HI_OS_MEMSET_S(pst_tmp, sizeof(hi_omci_ctrl_table_s), 0, sizeof(hi_omci_ctrl_table_s));

			*pui_tabnum = ui_tabnum - 1;

			break;
		}
	}

	/* 添加新配置 */
	HI_OS_MEMSET_S(&st_tapi_list, sizeof(st_tapi_list), 0, sizeof(st_tapi_list));
	st_tapi_list.em_port = (hi_omci_tapi_port_e)uc_portid;
	st_tapi_list.ui_gemportid = ntohs(pst_item->st_row.part0.us_gemport);
	st_tapi_list.ui_mcvlan = ntohs(pst_item->st_row.part0.us_vlan);
	st_tapi_list.ui_usrvlan = HI_OMCI_GET_VLAN_FROM_TCI(us_tci);
	st_tapi_list.st_ipaddr.start_ip.st_ipv4.ui_v4 = ntohl(pst_item->st_row.part0.ui_dip_start);
	st_tapi_list.st_ipaddr.end_ip.st_ipv4.ui_v4 = ntohl(pst_item->st_row.part0.ui_dip_end);

	ui_ret = hi_omci_tapi_mc_table_set(&st_tapi_list);
	HI_OMCI_RET_CHECK(ui_ret);

	for (ui_index = 0, pst_tmp = pst_tab; ui_index < HI_OMCI_ME_MULTI_OPER_DLIST_NUM; ui_index++, pst_tmp++) {
		if (0 == hi_os_memcmp(&st_empty, pst_tmp, sizeof(st_empty))) {
			HI_OS_MEMCPY_S(pst_tmp, sizeof(*pst_tmp), pst_item, sizeof(hi_omci_ctrl_table_s));

			*pui_tabnum = ui_tabnum + 1;

			break;
		}
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_v6_set
 Description : 配置IPv6动态组播访问控制列表
 Input Parm  : hi_omci_ctrl_table_s *pst_item,
               hi_omci_ctrl_table_s *pst_tab,
               hi_uint32 *pui_tabnum
               hi_ushort16 us_tci   用户VLAN
               hi_uchar8 uc_portid 端口号
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_v6_set(hi_omci_ctrl_table_s *pst_item,
		hi_omci_ctrl_table_s *pst_tab, hi_uint32 *pui_tabnum, hi_ushort16 us_tci, hi_uchar8 uc_portid)
{
	hi_omci_multi_tab_ctrl_s st_tabctrl;
	hi_omci_multi_tab_ctrl_s st_itemctrl;
	hi_omci_tapi_mc_table_list_s st_tapi_list;
	hi_omci_ctrl_table_s st_empty;
	hi_omci_ctrl_table_s *pst_tmp;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum = *pui_tabnum;
	hi_int32 ai_rowpart[3] = {-1, -1, -1};
	hi_uint32 ui_ret;

	HI_OS_MEMSET_S(&st_empty, sizeof(st_empty), 0, sizeof(st_empty));

	st_itemctrl.us_value = (hi_ushort16)ntohs(pst_item->st_tablectrl.us_value);

	hi_omci_debug("st_tablectrl.bits.row_partid = %hu\n", st_itemctrl.bits.row_partid);
	hi_omci_debug("st_tablectrl.bits.test = %hu\n", st_itemctrl.bits.test);
	hi_omci_debug("st_tablectrl.bits.row_key = %hu\n", st_itemctrl.bits.row_key);

	/* If the ONU does not support extended format, it
	should be possible to set the test bit to 1 and read it back with a get and get
	next operation. If the ONU does support the extended format, this bit should
	always return the value 0 under a get next operation. */
	st_itemctrl.bits.test = 0;
	pst_item->st_tablectrl.us_value = (hi_ushort16)htons(st_itemctrl.us_value);

	for (ui_index = 0, pst_tmp = pst_tab; ui_index < ui_tabnum; ui_index++, pst_tmp++) {
		st_tabctrl.us_value = (hi_ushort16)ntohs(pst_tmp->st_tablectrl.us_value);
		if (st_tabctrl.bits.row_key == st_itemctrl.bits.row_key) {
			ai_rowpart[st_tabctrl.bits.row_partid] = ui_index;
		}
	}

	/* 删除原有配置 */
	if (-1 != ai_rowpart[0]
	    && -1 != ai_rowpart[1]
	    && -1 != ai_rowpart[2]) {
		pst_tmp = pst_tab + ai_rowpart[0];

		HI_OS_MEMSET_S(&st_tapi_list, sizeof(st_tapi_list), 0, sizeof(st_tapi_list));
		st_tapi_list.em_port = (hi_omci_tapi_port_e)uc_portid;
		st_tapi_list.ui_gemportid = ntohs(pst_tmp->st_row.part0.us_gemport);
		st_tapi_list.ui_mcvlan = ntohs(pst_tmp->st_row.part0.us_vlan);

		for (ui_index = 0; ui_index < sizeof(hi_ipv4_s); ui_index++) {
			st_tapi_list.st_ipaddr.start_ip.st_ipv6.uc_v6[HI_IPV4_LEN - 1 - ui_index]
				= *((hi_uchar8 *)&pst_tmp->st_row.part0.ui_dip_start + ui_index);
			st_tapi_list.st_ipaddr.end_ip.st_ipv6.uc_v6[HI_IPV4_LEN - 1 - ui_index]
				= *((hi_uchar8 *)&pst_tmp->st_row.part0.ui_dip_end + ui_index);
		}

		pst_tmp = pst_tab + ai_rowpart[2];

		for (ui_index = 0; ui_index < HI_OMCI_MULITI_IPV6_LEADING_LEN; ui_index++) {
			st_tapi_list.st_ipaddr.start_ip.st_ipv6.uc_v6[HI_IPV6_LEN - 1 - ui_index]
				= pst_tmp->st_row.part2.auc_ipv6_dst_leading[ui_index];
			st_tapi_list.st_ipaddr.end_ip.st_ipv6.uc_v6[HI_IPV6_LEN - 1 - ui_index]
				= pst_tmp->st_row.part2.auc_ipv6_dst_leading[ui_index];
		}

		ui_ret = hi_omci_tapi_mc_table_del(&st_tapi_list);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	/* 保存新表项 */
	if (-1 != ai_rowpart[st_itemctrl.bits.row_partid]) {
		/* 替换旧表项 */
		HI_OS_MEMCPY_S((pst_tab + ai_rowpart[st_itemctrl.bits.row_partid]), sizeof(hi_omci_ctrl_table_s),
			       pst_item, sizeof(hi_omci_ctrl_table_s));
	} else {
		/* 新建表项 */
		for (ui_index = 0, pst_tmp = pst_tab; ui_index < HI_OMCI_ME_MULTI_OPER_DLIST_NUM; ui_index++, pst_tmp++) {
			if (0 == hi_os_memcmp(&st_empty, pst_tmp, sizeof(st_empty))) {
				HI_OS_MEMCPY_S(pst_tmp, sizeof(*pst_tmp), pst_item, sizeof(hi_omci_ctrl_table_s));
				break;
			}
		}

		ui_tabnum++;
		*pui_tabnum = ui_tabnum;
	}

	ai_rowpart[0] = -1;
	ai_rowpart[1] = -1;
	ai_rowpart[2] = -1;

	for (ui_index = 0, pst_tmp = pst_tab; ui_index < ui_tabnum; ui_index++, pst_tmp++) {
		st_tabctrl.us_value = (hi_ushort16)ntohs(pst_tmp->st_tablectrl.us_value);
		if (st_tabctrl.bits.row_key == st_itemctrl.bits.row_key) {
			ai_rowpart[st_tabctrl.bits.row_partid] = ui_index;
		}
	}

	/* 创建新配置 */
	if (-1 != ai_rowpart[0]
	    && -1 != ai_rowpart[1]
	    && -1 != ai_rowpart[2]) {
		pst_tmp = pst_tab + ai_rowpart[0];

		HI_OS_MEMSET_S(&st_tapi_list, sizeof(st_tapi_list), 0, sizeof(st_tapi_list));
		st_tapi_list.em_port = (hi_omci_tapi_port_e)uc_portid;
		st_tapi_list.ui_gemportid = ntohs(pst_tmp->st_row.part0.us_gemport);
		st_tapi_list.ui_mcvlan = ntohs(pst_tmp->st_row.part0.us_vlan);
		st_tapi_list.ui_usrvlan = HI_OMCI_GET_VLAN_FROM_TCI(us_tci);

		for (ui_index = 0; ui_index < sizeof(hi_ipv4_s); ui_index++) {
			st_tapi_list.st_ipaddr.start_ip.st_ipv6.uc_v6[HI_IPV4_LEN - 1 - ui_index]
				= *((hi_uchar8 *)&pst_tmp->st_row.part0.ui_dip_start + ui_index);
			st_tapi_list.st_ipaddr.end_ip.st_ipv6.uc_v6[HI_IPV4_LEN - 1 - ui_index]
				= *((hi_uchar8 *)&pst_tmp->st_row.part0.ui_dip_end + ui_index);
		}

		pst_tmp = pst_tab + ai_rowpart[2];

		for (ui_index = 0; ui_index < HI_OMCI_MULITI_IPV6_LEADING_LEN; ui_index++) {
			st_tapi_list.st_ipaddr.start_ip.st_ipv6.uc_v6[HI_IPV6_LEN - 1 - ui_index]
				= pst_tmp->st_row.part2.auc_ipv6_dst_leading[ui_index];
			st_tapi_list.st_ipaddr.end_ip.st_ipv6.uc_v6[HI_IPV6_LEN - 1 - ui_index]
				= pst_tmp->st_row.part2.auc_ipv6_dst_leading[ui_index];
		}

		ui_ret = hi_omci_tapi_mc_table_set(&st_tapi_list);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_set
 Description : 配置动态组播访问控制列表
 Input Parm  : hi_omci_ctrl_table_s *pst_item,
               hi_omci_ctrl_table_s *pst_tab,
               hi_uint32 *pui_tabnum
               hi_uint32 ui_ipversion,
               hi_ushort16 us_tci   用户VLAN
               hi_uchar8 uc_portid 端口号
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_set(hi_omci_ctrl_table_s *pst_item,
		hi_omci_ctrl_table_s *pst_tab, hi_uint32 *pui_tabnum, hi_uint32 ui_ipversion,
		hi_ushort16 us_tci, hi_uchar8 uc_portid)
{
	hi_uint32 ui_ret;

	if (ui_ipversion == HI_OMCI_ME_MULTI_OPER_IPV6) {
		ui_ret = __omci_me_multi_oper_ctrl_tab_v6_set(pst_item, pst_tab, pui_tabnum, us_tci, uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	} else {
		ui_ret = __omci_me_multi_oper_ctrl_tab_v4_set(pst_item, pst_tab, pui_tabnum, us_tci, uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_v4_del
 Description : 删除IPv4动态组播访问控制列表
 Input Parm  : hi_omci_ctrl_table_s *pst_item,
               hi_omci_ctrl_table_s *pst_tab,
               hi_uint32 ui_tabnum
               hi_uchar8 uc_portid 端口号
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_v4_del(hi_omci_ctrl_table_s *pst_item,
		hi_omci_ctrl_table_s *pst_tab, hi_uint32 *pui_tabnum, hi_uchar8 uc_portid)
{
	hi_omci_multi_tab_ctrl_s st_tabctrl;
	hi_omci_multi_tab_ctrl_s st_itemctrl;
	hi_omci_tapi_mc_table_list_s st_tapi_list;
	hi_omci_ctrl_table_s *pst_tmp;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum = *pui_tabnum;
	hi_uint32 ui_ret;

	st_itemctrl.us_value = (hi_ushort16)ntohs(pst_item->st_tablectrl.us_value);

	for (ui_index = 0, pst_tmp = pst_tab; ui_index < ui_tabnum; ui_index++, pst_tmp++) {
		st_tabctrl.us_value = (hi_ushort16)ntohs(pst_tmp->st_tablectrl.us_value);
		if (st_tabctrl.ibits.index == st_itemctrl.ibits.index) {
			HI_OS_MEMSET_S(&st_tapi_list, sizeof(st_tapi_list), 0, sizeof(st_tapi_list));
			st_tapi_list.em_port = (hi_omci_tapi_port_e)uc_portid;
			st_tapi_list.ui_gemportid = ntohs(pst_tmp->st_row.part0.us_gemport);
			st_tapi_list.ui_mcvlan = ntohs(pst_tmp->st_row.part0.us_vlan);
			st_tapi_list.st_ipaddr.start_ip.st_ipv4.ui_v4 = ntohl(pst_tmp->st_row.part0.ui_dip_start);
			st_tapi_list.st_ipaddr.end_ip.st_ipv4.ui_v4 = ntohl(pst_tmp->st_row.part0.ui_dip_end);

			ui_ret = hi_omci_tapi_mc_table_del(&st_tapi_list);
			HI_OMCI_RET_CHECK(ui_ret);

			HI_OS_MEMSET_S(pst_tmp, sizeof(hi_omci_ctrl_table_s), 0, sizeof(hi_omci_ctrl_table_s));

			*pui_tabnum = ui_tabnum - 1;

			break;
		}
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_v6_del
 Description : 删除IPv6动态组播访问控制列表
 Input Parm  : hi_omci_ctrl_table_s *pst_item,
               hi_omci_ctrl_table_s *pst_tab,
               hi_uint32 *pui_tabnum
               hi_uchar8 uc_portid 端口号
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_v6_del(hi_omci_ctrl_table_s *pst_item,
		hi_omci_ctrl_table_s *pst_tab, hi_uint32 *pui_tabnum, hi_uchar8 uc_portid)
{
	hi_omci_multi_tab_ctrl_s st_tabctrl;
	hi_omci_multi_tab_ctrl_s st_itemctrl;
	hi_omci_tapi_mc_table_list_s st_tapi_list;
	hi_omci_ctrl_table_s *pst_tmp;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum = *pui_tabnum;
	hi_int32 ai_rowpart[3] = {-1, -1, -1};
	hi_uint32 ui_ret;

	st_itemctrl.us_value = (hi_ushort16)ntohs(pst_item->st_tablectrl.us_value);

	hi_omci_debug("st_tablectrl.bits.row_partid = %hu\n", st_itemctrl.bits.row_partid);
	hi_omci_debug("st_tablectrl.bits.test = %hu\n", st_itemctrl.bits.test);
	hi_omci_debug("st_tablectrl.bits.row_key = %hu\n", st_itemctrl.bits.row_key);

	for (ui_index = 0, pst_tmp = pst_tab; ui_index < ui_tabnum; ui_index++, pst_tmp++) {
		st_tabctrl.us_value = (hi_ushort16)ntohs(pst_tmp->st_tablectrl.us_value);
		if (st_tabctrl.bits.row_key == st_itemctrl.bits.row_key) {
			ai_rowpart[st_tabctrl.bits.row_partid] = ui_index;
		}
	}

	if (-1 != ai_rowpart[0]
	    && -1 != ai_rowpart[1]
	    && -1 != ai_rowpart[2]) {
		pst_tmp = pst_tab + ai_rowpart[0];

		HI_OS_MEMSET_S(&st_tapi_list, sizeof(st_tapi_list), 0, sizeof(st_tapi_list));
		st_tapi_list.em_port = (hi_omci_tapi_port_e)uc_portid;
		st_tapi_list.ui_gemportid = ntohs(pst_tmp->st_row.part0.us_gemport);
		st_tapi_list.ui_mcvlan = ntohs(pst_tmp->st_row.part0.us_vlan);

		for (ui_index = 0; ui_index < sizeof(hi_ipv4_s); ui_index++) {
			st_tapi_list.st_ipaddr.start_ip.st_ipv6.uc_v6[HI_IPV4_LEN - 1 - ui_index]
				= *((hi_uchar8 *)&pst_tmp->st_row.part0.ui_dip_start + ui_index);
			st_tapi_list.st_ipaddr.end_ip.st_ipv6.uc_v6[HI_IPV4_LEN - 1 - ui_index]
				= *((hi_uchar8 *)&pst_tmp->st_row.part0.ui_dip_end + ui_index);
		}

		pst_tmp = pst_tab + ai_rowpart[2];

		for (ui_index = 0; ui_index < HI_OMCI_MULITI_IPV6_LEADING_LEN; ui_index++) {
			st_tapi_list.st_ipaddr.start_ip.st_ipv6.uc_v6[HI_IPV6_LEN - 1 - ui_index]
				= pst_tmp->st_row.part2.auc_ipv6_dst_leading[ui_index];
			st_tapi_list.st_ipaddr.end_ip.st_ipv6.uc_v6[HI_IPV6_LEN - 1 - ui_index]
				= pst_tmp->st_row.part2.auc_ipv6_dst_leading[ui_index];
		}

		ui_ret = hi_omci_tapi_mc_table_del(&st_tapi_list);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	if (-1 != ai_rowpart[st_itemctrl.bits.row_partid]) {
		HI_OS_MEMSET_S((pst_tab + ai_rowpart[st_itemctrl.bits.row_partid]), sizeof(hi_omci_ctrl_table_s),
			       0, sizeof(hi_omci_ctrl_table_s));

		//*pui_tabnum = ui_tabnum - 1;
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_del
 Description : 删除动态组播访问控制列表
 Input Parm  : hi_omci_ctrl_table_s *pst_item,
               hi_omci_ctrl_table_s *pst_tab,
               hi_uint32 *pui_tabnum
               hi_uint32 ui_ipversion,
               hi_uchar8 uc_portid 端口号
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_del(hi_omci_ctrl_table_s *pst_item,
		hi_omci_ctrl_table_s *pst_tab, hi_uint32 *pui_tabnum,
		hi_uint32 ui_ipversion, hi_uchar8 uc_portid)
{
	hi_uint32 ui_ret;

	if (ui_ipversion == HI_OMCI_ME_MULTI_OPER_IPV6) {
		ui_ret = __omci_me_multi_oper_ctrl_tab_v6_del(pst_item, pst_tab, pui_tabnum, uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	} else {
		ui_ret = __omci_me_multi_oper_ctrl_tab_v4_del(pst_item, pst_tab, pui_tabnum, uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_v4_clr
 Description : 清空IPv4动态组播访问控制列表
 Input Parm  : hi_omci_ctrl_table_s *pst_item,
               hi_uint32 ui_tabum
               hi_uchar8 uc_portid 端口号
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_v4_clr(hi_omci_ctrl_table_s *pst_tab,
		hi_uint32 ui_tabum, hi_uchar8 uc_portid)
{
	hi_omci_ctrl_table_s *pst_item;
	hi_omci_ctrl_table_s st_empty;
	hi_omci_tapi_mc_table_list_s st_tapi_list;
	hi_uint32 ui_index;
	hi_uint32 ui_ret;

	HI_OS_MEMSET_S(&st_empty, sizeof(st_empty), 0, sizeof(st_empty));

	for (ui_index = 0, pst_item = pst_tab; ui_index < ui_tabum; ui_index++, pst_item++) {
		if (0 == hi_os_memcmp(&st_empty, pst_item, sizeof(st_empty))) {
			continue;
		}

		HI_OS_MEMSET_S(&st_tapi_list, sizeof(st_tapi_list), 0, sizeof(st_tapi_list));
		st_tapi_list.em_port = (hi_omci_tapi_port_e)uc_portid;
		st_tapi_list.ui_gemportid = ntohs(pst_item->st_row.part0.us_gemport);
		st_tapi_list.ui_mcvlan = ntohs(pst_item->st_row.part0.us_vlan);
		st_tapi_list.st_ipaddr.start_ip.st_ipv4.ui_v4 = ntohl(pst_item->st_row.part0.ui_dip_start);
		st_tapi_list.st_ipaddr.end_ip.st_ipv4.ui_v4 = ntohl(pst_item->st_row.part0.ui_dip_end);

		ui_ret = hi_omci_tapi_mc_table_del(&st_tapi_list);
		HI_OMCI_RET_CHECK(ui_ret);

		HI_OS_MEMSET_S(pst_item, sizeof(hi_omci_ctrl_table_s), 0, sizeof(hi_omci_ctrl_table_s));
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_v6_clr
 Description : 清空IPv6动态组播访问控制列表
 Input Parm  : hi_omci_ctrl_table_s *pst_item,
               hi_uint32 ui_tabum
               hi_uchar8 uc_portid 端口号
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_v6_clr(hi_omci_ctrl_table_s *pst_tab,
		hi_uint32 ui_tabum, hi_uchar8 uc_portid)
{
	hi_omci_ctrl_table_s *pst_item;
	hi_uint32 ui_index;
	hi_uint32 ui_ret;

	for (ui_index = 0, pst_item = pst_tab; ui_index < ui_tabum; ui_index++, pst_item++) {
		ui_ret = __omci_me_multi_oper_ctrl_tab_v6_del(pst_item, pst_tab, &ui_tabum, uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_del
 Description : 清空动态组播访问控制列表
 Input Parm  : hi_omci_ctrl_table_s *pst_item,
               hi_uint32 ui_tabum
               hi_uint32 ui_ipversion,
               hi_uchar8 uc_portid 端口号
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_clr(hi_omci_ctrl_table_s *pst_tab,
		hi_uint32 ui_tabum, hi_uint32 ui_ipversion, hi_uchar8 uc_portid)
{
	hi_uint32 ui_ret;

	if (ui_ipversion == HI_OMCI_ME_MULTI_OPER_IPV6) {
		ui_ret = __omci_me_multi_oper_ctrl_tab_v6_clr(pst_tab, ui_tabum, uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	} else {
		ui_ret = __omci_me_multi_oper_ctrl_tab_v4_clr(pst_tab, ui_tabum, uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_opt
 Description : 配置动态组播访问控制列表
 Input Parm  : hi_omci_ctrl_table_s *pst_item,
               hi_omci_ctrl_table_s *pst_tab,
               hi_uint32 ui_tabnum
               hi_uint32 ui_ipversion IP版本号，v4/v6
               hi_ushort16 us_tci   用户VLAN
               hi_uchar8 uc_portid 端口号
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_opt(hi_omci_ctrl_table_s *pst_item,
		hi_omci_ctrl_table_s *pst_tab, hi_uint32 *pui_tabnum, hi_uint32 ui_ipversion,
		hi_ushort16 us_tci, hi_uchar8 uc_portid)
{
	hi_omci_multi_tab_ctrl_s st_tablectrl;
	hi_uint32 ui_ret;

	st_tablectrl.us_value = (hi_ushort16)ntohs(pst_item->st_tablectrl.us_value);

	hi_omci_debug("st_tablectrl.bits.set_ctrl = %hu\n", st_tablectrl.bits.set_ctrl);

	if (HI_OMCI_MULTI_OPER_TAB_CLR == st_tablectrl.bits.set_ctrl) {
		ui_ret = __omci_me_multi_oper_ctrl_tab_clr(pst_tab, *pui_tabnum, ui_ipversion, uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);

		*pui_tabnum = 0;
	} else if (HI_OMCI_MULTI_OPER_TAB_SET == st_tablectrl.bits.set_ctrl) {
		ui_ret = __omci_me_multi_oper_ctrl_tab_set(pst_item, pst_tab, pui_tabnum, ui_ipversion, us_tci, uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	} else if (HI_OMCI_MULTI_OPER_TAB_DEL == st_tablectrl.bits.set_ctrl) {
		ui_ret = __omci_me_multi_oper_ctrl_tab_del(pst_item, pst_tab, pui_tabnum, ui_ipversion, uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
hi_uint32 __omci_me_multi_oper_ctrl_igmp_uprate(hi_uchar8 uc_portid, hi_uint32 ui_rate)
{
	hi_omci_tapi_car_s st_car;
	hi_uint32 ui_carid = 1;
	hi_uint32 ui_ret;

	if (0 != ui_rate) {
		if (0 != g_ui_igmp_uprate_carid[uc_portid]) {
			ui_ret = hi_omci_tapi_car_release(g_ui_igmp_uprate_carid[uc_portid]);
			HI_OMCI_RET_CHECK(ui_ret);
		}
		g_ui_igmp_uprate_carid[uc_portid] = 0;
		st_car.ui_pir = ui_rate;
		ui_ret = hi_omci_tapi_car_set(&g_ui_igmp_uprate_carid[uc_portid], &st_car);
		HI_OMCI_RET_CHECK(ui_ret);
		ui_ret = hi_omci_tapi_mc_uprate_set(uc_portid, ui_rate, g_ui_igmp_uprate_carid[uc_portid]);
		HI_OMCI_RET_CHECK(ui_ret);
	} else { //不限速
		if (0 != g_ui_igmp_uprate_carid[uc_portid]) {
			ui_carid = g_ui_igmp_uprate_carid[uc_portid];

			ui_ret = hi_omci_tapi_mc_uprate_set(uc_portid, ui_rate, ui_carid);
			HI_OMCI_RET_CHECK(ui_ret);

			ui_ret = hi_omci_tapi_car_release(ui_carid);
			HI_OMCI_RET_CHECK(ui_ret);

			g_ui_igmp_uprate_carid[uc_portid] = 0;
		}
	}
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_multi_oper_ctrl_tab_tci_update
 Description : 下行组播数据TCI规则变化，需要刷新表项
 Input Parm  : hi_ushort16 us_instid 端口号
               hi_ushort16 us_tci   用户VLAN
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_multi_oper_ctrl_tab_tci_update(hi_ushort16 us_instid, hi_ushort16 us_tci, hi_uchar8 uc_portid)
{
	hi_omci_ctrl_table_s *pst_ctrl_tab;
	hi_omci_ctrl_table_s *pst_ctrlitem;
	hi_omci_ctrl_table_s st_ctrlempty;
	hi_omci_tapi_mc_vlan_s st_vlan;
	hi_uint32 ui_mptabnum;
	hi_uint32 ui_size;
	hi_uint32 ui_tabidx;
	hi_uint32 ui_ret;

	HI_OS_MEMSET_S(&st_ctrlempty, sizeof(st_ctrlempty), 0, sizeof(st_ctrlempty));

	ui_mptabnum = g_ui_port_tabnum[uc_portid];

	/* 没有表项，表面当前没有下发组播VLAN */
	if (0 == ui_mptabnum) {
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	ui_size = sizeof(hi_omci_ctrl_table_s) * HI_OMCI_ME_MULTI_OPER_DLIST_NUM;
	pst_ctrl_tab = (hi_omci_ctrl_table_s *)hi_os_malloc(ui_size);
	if (HI_NULL == pst_ctrl_tab) {
		hi_omci_l2_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	HI_OS_MEMSET_S(pst_ctrl_tab, ui_size, 0, ui_size);

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTI_OPER_PROFILE_E,
				      us_instid, HI_OMCI_ATTR7, pst_ctrl_tab);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_ctrl_tab);
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	for (ui_tabidx = 0, pst_ctrlitem = pst_ctrl_tab;
	     ui_tabidx < ui_mptabnum;
	     ui_tabidx++, pst_ctrlitem++) {
		if (0 == hi_os_memcmp(pst_ctrlitem, &st_ctrlempty, sizeof(st_ctrlempty))) {
			continue;
		}

		if (0 != pst_ctrlitem->st_tablectrl.bits.row_partid) {
			continue;
		}

		st_vlan.em_port = (hi_omci_tapi_port_e)uc_portid;
		st_vlan.ui_mcvlan = ntohs(pst_ctrlitem->st_row.part0.us_vlan);
		st_vlan.ui_usrvlan = HI_OMCI_GET_VLAN_FROM_TCI(us_tci);
		ui_ret = hi_omci_tapi_mc_vlan_set(&st_vlan);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_ctrl_tab);
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}
	}

	hi_os_free(pst_ctrl_tab);

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_multi_oper_set
 Description : Multicast operations profile create
               数据库刷新前执行此函数
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_multi_oper_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_multi_oper_profile_s *pst_entry = (hi_omci_me_multi_oper_profile_s *)pv_data;
	hi_omci_tapi_mc_attr_s st_mc_attr;
	hi_omci_tapi_mc_igmp_attr_s st_igmp_attr;
	hi_omci_ctrl_table_s *pst_ctrl_tab;
	hi_omci_multi_tci_s st_tci;

	hi_uint32 ui_tabnum;
	hi_uint32 ui_ipversion;
	hi_uint32 ui_size;
	hi_uint32 ui_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_ushort16 us_mask = pst_entry->st_msghead.us_attmask;
	hi_uchar8 uc_portid;
	hi_omci_tapi_sysinfo_s st_info;

	hi_omci_me_mac_port_cfg_s st_portcfg;
	ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(ui_ret);
	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
		return HI_RET_SUCC;
	}

#if 0
	ui_ret = __omci_me_multi_oper_portid_get(us_instid, &uc_portid);
	if (HI_RET_SUCC != ui_ret) {
		/* 不能获取端口号, 表明当前配置拓扑还未建立
		 * 无需进行后续配置 */
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}
#endif

	ui_ret = __omci_me_multi_oper_portid_get(us_instid, &uc_portid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		if ((HI_RET_SUCC != hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, &st_portcfg))) {
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}
		if (HI_MAC_BRI_PORT_UNI_E == st_portcfg.uc_tptype) {
			uc_portid = HI_OMCI_GET_PORTID_FROM_INSTID(st_portcfg.us_tp_ptr);
		}
	}
	if (HI_OMCI_TAPI_VEIP_E == uc_portid) {
		return HI_RET_SUCC;
	}

	if (HI_OMCI_ME_MULTI_OPER_IS_LEAVE(us_mask)) {
		hi_omci_debug("uc_imleave = %hhu\n", pst_entry->uc_imleave);

		ui_ret = hi_omci_tapi_mc_attr_get(&st_mc_attr);
		HI_OMCI_RET_CHECK(ui_ret);

		st_mc_attr.ui_imme_leave_en = pst_entry->uc_imleave;
		ui_ret = hi_omci_tapi_mc_attr_set(&st_mc_attr);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	if (HI_OMCI_ME_MULTI_OPER_IS_IGMP_TCI(us_mask)) {
		hi_omci_debug("us_uptci = %hu\n", pst_entry->us_uptci);

		ui_ret = hi_omci_tapi_mc_igmp_attr_get((hi_omci_tapi_port_e)uc_portid, &st_igmp_attr);
		HI_OMCI_RET_CHECK(ui_ret);

		st_igmp_attr.ui_vlan = HI_OMCI_GET_VLAN_FROM_TCI(pst_entry->us_uptci);
		st_igmp_attr.ui_pri = HI_OMCI_GET_PRI_FROM_TCI(pst_entry->us_uptci);
		st_igmp_attr.ui_gemportid = __omci_me_multi_oper_gemportid_get(st_igmp_attr.ui_vlan, st_igmp_attr.ui_pri, uc_portid);

		ui_ret = hi_omci_tapi_mc_igmp_attr_set((hi_omci_tapi_port_e)uc_portid, &st_igmp_attr);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	if (HI_OMCI_ME_MULTI_OPER_IS_IGMP_TAG(us_mask)) {
		hi_omci_debug("uc_uptagctrl = %hhu\n", pst_entry->uc_uptagctrl);

		ui_ret = hi_omci_tapi_mc_igmp_attr_get((hi_omci_tapi_port_e)uc_portid, &st_igmp_attr);
		HI_OMCI_RET_CHECK(ui_ret);

		st_igmp_attr.em_tag_act = (hi_omci_tapi_mc_igmp_tag_act_e)pst_entry->uc_uptagctrl;
		ui_ret = hi_omci_tapi_mc_igmp_attr_set((hi_omci_tapi_port_e)uc_portid, &st_igmp_attr);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	if (HI_OMCI_ME_MULTI_OPER_IS_IGMP_UPRATE(us_mask)) {
		hi_omci_debug("uc_uprate = %u\n", pst_entry->ui_rate);

		ui_ret = __omci_me_multi_oper_ctrl_igmp_uprate(uc_portid, pst_entry->ui_rate);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTI_OPER_PROFILE_E,
				      us_instid, HI_OMCI_ATTR16, &st_tci);
	HI_OMCI_RET_CHECK(ui_ret);

	if (HI_OMCI_ME_MULTI_OPER_IS_DTCI(us_mask)) {
		hi_omci_debug("uc_ctrltype = %hhu, us_tci = %x\n",
			      pst_entry->st_tci.uc_ctrltype, pst_entry->st_tci.us_tci);

		ui_ret = hi_omci_tapi_mc_vlan_clr((hi_omci_tapi_port_e)uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_tapi_mc_tagact_set((hi_omci_tapi_port_e)uc_portid,
						    (hi_omci_tapi_mc_tagact_e)pst_entry->st_tci.uc_ctrltype);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = __omci_me_multi_oper_ctrl_tab_tci_update(us_instid, (hi_ushort16)htons(pst_entry->st_tci.us_tci), uc_portid);
		HI_OMCI_RET_CHECK(ui_ret);

		HI_OS_MEMCPY_S(&st_tci, sizeof(st_tci), &pst_entry->st_tci, sizeof(st_tci));
	}

	if (HI_OMCI_ME_MULTI_OPER_IS_DLIST(us_mask)) {
		ui_tabnum = g_ui_port_tabnum[uc_portid];

		ui_ret = __omci_me_multi_oper_ipversion_get(us_instid, &ui_ipversion);
		HI_OMCI_RET_CHECK(ui_ret);

		hi_omci_debug("ui_tabnum = %u\n", ui_tabnum);
		hi_omci_debug("ui_ipversion = %u\n", ui_ipversion);

		ui_size = sizeof(hi_omci_ctrl_table_s) * HI_OMCI_ME_MULTI_OPER_DLIST_NUM;
		pst_ctrl_tab = (hi_omci_ctrl_table_s *)hi_os_malloc(ui_size);
		if (HI_NULL == pst_ctrl_tab) {
			hi_omci_l2_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		HI_OS_MEMSET_S(pst_ctrl_tab, ui_size, 0, ui_size);

		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTI_OPER_PROFILE_E,
					      us_instid, HI_OMCI_ATTR7, pst_ctrl_tab);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_ctrl_tab);
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		__omci_me_multi_oper_table_printf((hi_uchar8 *)pst_ctrl_tab, (sizeof(hi_omci_ctrl_table_s) * ui_tabnum));
		__omci_me_multi_oper_table_printf((hi_uchar8 *)&pst_entry->st_dynctrl, sizeof(hi_omci_ctrl_table_s));

		ui_ret = __omci_me_multi_oper_ctrl_tab_opt(&pst_entry->st_dynctrl,
				pst_ctrl_tab, &ui_tabnum, ui_ipversion, (hi_ushort16)htons(st_tci.us_tci), uc_portid);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_ctrl_tab);
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		__omci_me_multi_oper_table_printf((hi_uchar8 *)pst_ctrl_tab, (sizeof(hi_omci_ctrl_table_s) * ui_tabnum));

		g_ui_port_tabnum[uc_portid] = ui_tabnum;

		ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_MULTI_OPER_PROFILE_E,
					      us_instid, HI_OMCI_ATTR7, pst_ctrl_tab);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_ctrl_tab);
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		hi_os_free(pst_ctrl_tab);
	}

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/******************************************************************************
 Function    : hi_omci_me_multi_oper_create
 Description : Multicast operations profile create
               数据库刷新后执行此函数
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_multi_oper_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_multi_oper_profile_s *pst_entry = (hi_omci_me_multi_oper_profile_s *)pv_data;
	hi_omci_tapi_mc_attr_s st_mc_attr;
	hi_omci_tapi_mc_igmp_attr_s st_igmp_attr;
	hi_uint32 ui_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_uchar8 uc_portid;
	hi_omci_tapi_sysinfo_s st_info;

	hi_omci_me_mac_port_cfg_s st_portcfg;
	ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(ui_ret);
	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
		return HI_RET_SUCC;
	}

	/* 配置IGMP协议
	 * Snooping/ proxy
	 * 快速离开使能*/
	ui_ret = hi_omci_tapi_mc_attr_get(&st_mc_attr);
	HI_OMCI_RET_CHECK(ui_ret);

	st_mc_attr.em_version = (hi_omci_tapi_mc_igmp_version_e)pst_entry->uc_version;
	st_mc_attr.em_func = (hi_omci_tapi_mc_igmp_func_e)pst_entry->uc_function;
	st_mc_attr.ui_imme_leave_en = pst_entry->uc_imleave;

	ui_ret = hi_omci_tapi_mc_attr_set(&st_mc_attr);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 配置上行IGMP报文TAG处理方式 */
	ui_ret = __omci_me_multi_oper_portid_get(us_instid, &uc_portid);
	if (HI_RET_SUCC != ui_ret) {
		if ((HI_RET_SUCC != hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, &st_portcfg))) {
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}
		if (HI_MAC_BRI_PORT_UNI_E == st_portcfg.uc_tptype) {
			uc_portid = HI_OMCI_GET_PORTID_FROM_INSTID(st_portcfg.us_tp_ptr);
		}
	}

	if (HI_OMCI_TAPI_VEIP_E == uc_portid) {
		return HI_RET_SUCC;
	}

	ui_ret = hi_omci_tapi_mc_igmp_attr_get((hi_omci_tapi_port_e)uc_portid, &st_igmp_attr);
	HI_OMCI_RET_CHECK(ui_ret);

	st_igmp_attr.em_tag_act = (hi_omci_tapi_mc_igmp_tag_act_e)pst_entry->uc_uptagctrl;
	st_igmp_attr.ui_vlan = HI_OMCI_GET_VLAN_FROM_TCI((pst_entry->us_uptci));
	st_igmp_attr.ui_pri = HI_OMCI_GET_PRI_FROM_TCI((pst_entry->us_uptci));
	st_igmp_attr.ui_gemportid = __omci_me_multi_oper_gemportid_get(st_igmp_attr.ui_vlan, st_igmp_attr.ui_pri, uc_portid);

	ui_ret = hi_omci_tapi_mc_igmp_attr_set((hi_omci_tapi_port_e)uc_portid, &st_igmp_attr);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 配置组播VLAN TAG处理 */
	ui_ret = hi_omci_tapi_mc_tagact_set((hi_omci_tapi_port_e)uc_portid,
					    (hi_omci_tapi_mc_tagact_e)pst_entry->st_tci.uc_ctrltype);
	HI_OMCI_RET_CHECK(ui_ret);

	g_ui_port_tabnum[uc_portid] = 0;

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_multi_oper_delete
 Description : Multicast operations profile delete
               数据库刷新前执行此函数, 清空组播表项
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_multi_oper_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_multi_oper_profile_s *pst_entry = (hi_omci_me_multi_oper_profile_s *)pv_data;
	hi_omci_ctrl_table_s *pst_ctrl_tab;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_ipversion;
	hi_uint32 ui_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_uchar8 uc_portid;
	hi_omci_tapi_sysinfo_s st_info;

	hi_omci_me_mac_port_cfg_s st_portcfg;
	ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(ui_ret);
	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
		return HI_RET_SUCC;
	}

	ui_ret = __omci_me_multi_oper_portid_get(us_instid, &uc_portid);
	//HI_OMCI_RET_CHECK(ui_ret);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		if ((HI_RET_SUCC != hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, &st_portcfg))) {
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}
		if (HI_MAC_BRI_PORT_UNI_E == st_portcfg.uc_tptype) {
			uc_portid = HI_OMCI_GET_PORTID_FROM_INSTID(st_portcfg.us_tp_ptr);
		}
	}

	if (HI_OMCI_TAPI_VEIP_E == uc_portid) {
		return HI_RET_SUCC;
	}

	ui_tabnum = g_ui_port_tabnum[uc_portid];

	pst_ctrl_tab = (hi_omci_ctrl_table_s *)hi_os_malloc(sizeof(hi_omci_ctrl_table_s) * HI_OMCI_ME_MULTI_OPER_DLIST_NUM);
	if (HI_NULL == pst_ctrl_tab) {
		hi_omci_l2_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MULTI_OPER_PROFILE_E,
				      us_instid, HI_OMCI_ATTR7, pst_ctrl_tab);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_ctrl_tab);
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = __omci_me_multi_oper_ipversion_get(us_instid, &ui_ipversion);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_ctrl_tab);
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = __omci_me_multi_oper_ctrl_tab_clr(pst_ctrl_tab, ui_tabnum, ui_ipversion, uc_portid);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_ctrl_tab);
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_os_free(pst_ctrl_tab);
	g_ui_port_tabnum[uc_portid] = 0;
	g_ui_igmp_uprate_carid[uc_portid] = 0;

	hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
