/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_mac_port_filter_table.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-28
  Description: OMCI manager entity MAC bridge port filter table data file.
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_MAC_PORT_FILT_IS_TABLE(mask) HI_OMCI_CHECK_ATTR(mask,HI_OMCI_ATTR1)

//#define HI_OMCI_ME_MAC_PORT_FILT_FWD    0
//#define HI_OMCI_ME_MAC_PORT_FILT_DROP   1

#define HI_OMCI_ME_MAC_PORT_FILT_OPT_ADD    0x1
#define HI_OMCI_ME_MAC_PORT_FILT_OPT_CLR    0x0
#define HI_OMCI_ME_MAC_PORT_FILT_OPT_CLRALL 0x2

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
typedef union {
	struct {
		hi_uint32 ui_filter: 1;
		hi_uint32 ui_srcaddr: 1;
		hi_uint32 ui_resrv1: 4;
		hi_uint32 ui_opt: 2;
		hi_uint32 ui_resrv2: 24;
	} bits;

	hi_uint32 ui_val;
} hi_omci_me_macportfilt_tbl_u;

/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
#if 0
/*****************************************************************************
  Function     : hi_omci_me_macportfilt_mib_update
  Description  : 更新数据库MAC filter表项
  Input Param  : hi_ushort16 us_instid
                 hi_omci_me_filt_entry_s *pst_entry
  Output Param ：
  Return       :
*****************************************************************************/
static hi_uint32 hi_omci_me_macportfilt_mib_update(hi_ushort16 us_instid, hi_omci_me_filt_entry_s *pst_entry)
{
	hi_omci_me_filt_entry_s *pst_tbl;
	hi_uint32 ui_ret;
	hi_uint32 ui_tabnum;

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MAC_PORT_FILT_DATA_E, HI_OMCI_ATTR1, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	pst_tbl = (hi_omci_me_filt_entry_s *)hi_os_malloc(sizeof(hi_omci_me_filt_entry_s *) * ui_tabnum);
	if (HI_NULL == pst_tbl) {
		hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_FILT_DATA_E, us_instid, HI_OMCI_ATTR1, pst_tbl);

	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_tbl);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	/* 将当前的MAC filter表项更新到数据库 */
	pst_tbl += pst_entry->uc_entrynum;
	pst_tbl->uc_entrynum = pst_entry->uc_entrynum;
	pst_tbl->uc_filter = pst_entry->uc_filter;
	HIOS_MEMCPY_S(pst_tbl->uc_macadd, sizeof(pst_tbl->uc_macadd), pst_entry->uc_macadd, HI_MAC_LEN);

	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_MAC_PORT_FILT_DATA_E, us_instid, HI_OMCI_ATTR1, pst_tbl);
	hi_os_free(pst_tbl);
	HI_OMCI_RET_CHECK(ui_ret);

	return HI_RET_SUCC;
}
/*****************************************************************************
  Function     : hi_omci_me_macportfilt_mib_clr
  Description  : 清空数据库MAC filter表项
  Input Param  : hi_ushort16 us_instid
                 hi_omci_me_filt_entry_s *pst_entry
  Output Param ：
  Return       :
*****************************************************************************/
static hi_uint32 hi_omci_me_macportfilt_mib_clr(hi_ushort16 us_instid)
{
	hi_omci_me_filt_entry_s *pst_tbl;
	hi_uint32 ui_ret;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_index;

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MAC_PORT_FILT_DATA_E, HI_OMCI_ATTR1, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	pst_tbl = (hi_omci_me_filt_entry_s *)hi_os_malloc(sizeof(hi_omci_me_filt_entry_s *) * ui_tabnum);
	if (HI_NULL == pst_tbl) {
		hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_FILT_DATA_E, us_instid, HI_OMCI_ATTR1, pst_tbl);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_tbl);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	for (ui_index = 0; ui_index < ui_tabnum; ui_index++, pst_tbl++) {
		pst_tbl->uc_entrynum = (hi_uchar8)ui_index;
		pst_tbl->uc_filter = 0;
		HI_OS_MEMSET_S(pst_tbl->uc_macadd, HI_MAC_LEN, 0, HI_MAC_LEN);
	}

	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_MAC_PORT_FILT_DATA_E, us_instid, HI_OMCI_ATTR1, pst_tbl);
	hi_os_free(pst_tbl);
	HI_OMCI_RET_CHECK(ui_ret);

	return HI_RET_SUCC;
}
#endif

/*****************************************************************************
  Function     : hi_omci_me_macportfilt_proc
  Description  : MAC filter表项处理
  Input Param  : hi_ushort16 us_instid
                 hi_omci_me_filt_entry_s *pst_entry
  Output Param ：
  Return       :
*****************************************************************************/
static hi_uint32 hi_omci_me_macportfilt_proc(hi_ushort16 us_instid, hi_omci_me_filt_entry_s *pst_entry)
{
	hi_omci_tapi_br_filter_s st_filter;
	hi_omci_me_macportfilt_tbl_u st_tbl;
	hi_uint32 ui_ret;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum;
	hi_uchar8 uc_portid;

	/* 获取端口的MAC filter table深度 */
	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MAC_PORT_FILT_DATA_E, HI_OMCI_ATTR1, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 表项索引超出了表项深度 */
	if (pst_entry->uc_entrynum > ui_tabnum) {
		hi_omci_systrace(HI_OMCI_PRO_ERR_PARA_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	ui_ret = hi_omci_me_macportcfg_get_portid(us_instid, &uc_portid);
	HI_OMCI_RET_CHECK(ui_ret);

	if (0xff == uc_portid) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}
	if (HI_OMCI_TAPI_VEIP_E == uc_portid) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	/* 区分处理表项添加删除，以及表项清空 */
	st_tbl.ui_val = pst_entry->uc_filter;

	switch (st_tbl.bits.ui_opt) {
	case HI_OMCI_ME_MAC_PORT_FILT_OPT_ADD:

		st_filter.ui_index = pst_entry->uc_entrynum;
		st_filter.em_mode = (hi_omci_tapi_br_filter_mode_e)st_tbl.bits.ui_filter;
		HI_OS_MEMCPY_S(st_filter.auc_mac, sizeof(st_filter.auc_mac), pst_entry->uc_macadd, sizeof(st_filter.auc_mac));

		ui_ret = hi_omci_tapi_br_filter_set((hi_omci_tapi_port_e)uc_portid, &st_filter);
		HI_OMCI_RET_CHECK(ui_ret);

		//ui_ret = hi_omci_me_macportfilt_mib_update(us_instid, pst_entry);
		//HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_ME_MAC_PORT_FILT_OPT_CLR:

		st_filter.ui_index = pst_entry->uc_entrynum;
		ui_ret = hi_omci_tapi_br_filter_del((hi_omci_tapi_port_e)uc_portid, &st_filter);
		HI_OMCI_RET_CHECK(ui_ret);

		//ui_ret = hi_omci_me_macportfilt_mib_update(us_instid, pst_entry);
		//HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_ME_MAC_PORT_FILT_OPT_CLRALL:

		for (ui_index = 0; ui_index < ui_tabnum; ui_index++) {
			st_filter.ui_index = ui_index;
			ui_ret = hi_omci_tapi_br_filter_del((hi_omci_tapi_port_e)uc_portid, &st_filter);
			HI_OMCI_RET_CHECK(ui_ret);
		}

		//ui_ret = hi_omci_me_macportfilt_mib_clr(us_instid);
		//HI_OMCI_RET_CHECK(ui_ret);

		break;

	default:
		hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_macportfilt_set
 Description : MAC bridge port filter table data set
               数据库操作后处理
 Input Parm  : hi_void*pv_data,
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_macportfilt_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32  ui_ret    = HI_RET_SUCC;
	hi_omci_me_mac_port_filt_s *pst_portfilt = (hi_omci_me_mac_port_filt_s *)pv_data;
	hi_omci_me_filt_entry_s  st_msgcont[HI_OMCI_ME_MAC_PORT_FILT_TBL_SET_MAX_ENTRY];
	hi_omci_me_filt_entry_s  st_temp;

	hi_ushort16  us_count;
	hi_ushort16  us_instid = pst_portfilt->st_msghead.us_instid;

	if (!HI_OMCI_ME_MAC_PORT_FILT_IS_TABLE(pst_portfilt->st_msghead.us_attmask)) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	HI_OS_MEMSET_S(&st_temp, sizeof(hi_omci_me_filt_entry_s), 0, sizeof(hi_omci_me_filt_entry_s));
	HI_OS_MEMCPY_S(st_msgcont, sizeof(st_msgcont), &pst_portfilt->st_filtertbl,
		       HI_OMCI_ME_MAC_PORT_FILT_TBL_SET_MAX_ENTRY * sizeof(hi_omci_me_filt_entry_s));

	/*每个set消息最多可以设置3条mac*/
	for (us_count = 0; us_count < HI_OMCI_ME_MAC_PORT_FILT_TBL_SET_MAX_ENTRY; us_count++) {
		if (HI_OMCI_ZERO == hi_os_memcmp(&st_temp, &st_msgcont[us_count], sizeof(hi_omci_me_filt_entry_s))) {
			break;
		}

		ui_ret = hi_omci_me_macportfilt_proc(us_instid, &st_msgcont[us_count]);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_macportfilt_create
 Description : MAC bridge port filter table data create
 Input Parm  : hi_ushort16 us_instid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/

hi_uint32 hi_omci_me_macportfilt_create(hi_ushort16 us_instid)
{
	hi_uint32 ui_ret = HI_RET_SUCC;

	ui_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_MAC_PORT_FILT_DATA_E, us_instid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_me_macportfilt_delete
  Description  : MAC bridge port filter table data delete
  Input Param  : hi_ushort16 us_instid
                 hi_omci_me_filt_entry_s *pst_entry
  Output Param ：
  Return       :
*****************************************************************************/
hi_uint32 hi_omci_me_macportfilt_delete(hi_ushort16 us_instid)
{
	hi_omci_tapi_br_filter_s st_filter;
	hi_uint32 ui_ret = HI_RET_SUCC;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum;
	hi_uchar8 uc_portid;

	ui_ret = hi_omci_ext_del_inst(HI_OMCI_PRO_ME_MAC_PORT_FILT_DATA_E, us_instid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_MAC_PORT_FILT_DATA_E, HI_OMCI_ATTR1, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_me_macportcfg_get_portid(us_instid, &uc_portid);
	HI_OMCI_RET_CHECK(ui_ret);

	if (0xff == uc_portid) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}
	/* 清空过滤表 */
	for (ui_index = 0; ui_index < ui_tabnum; ui_index++) {
		st_filter.ui_index = ui_index;
		ui_ret = hi_omci_tapi_br_filter_del((hi_omci_tapi_port_e)uc_portid, &st_filter);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

