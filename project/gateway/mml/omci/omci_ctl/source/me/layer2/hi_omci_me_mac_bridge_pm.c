/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_mac_bridge_pm.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-16
  Description: MAC bridge performance monitoring history data
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_BRPM_ALARM_LRN_DISC HI_OMCI_ME_ALARM_BITMAP(1)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_omci_tapi_stat_bridge_s gst_bridgepm_history;

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_bridgepm_alarm_get
 Description : 桥统计告警检查
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_bridgepm_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_alarm_timer_s *pst_timer = (hi_omci_me_alarm_timer_s *)pv_data;
	hi_omci_me_br_pm_s st_entry;
	hi_omci_me_threshold1_s st_threshold1;
	hi_int32 i_ret;
	hi_ushort16 us_bitmap_curr = 0;
	hi_ushort16 us_bitmap_hist;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	/* 读取当前芯片桥统计 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PM_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	/* 获取各参数阈值 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_THRESHOLD_DATA1_E, st_entry.us_thresid, &st_threshold1);
	HI_OMCI_RET_CHECK(i_ret);

	/* 比较是否超出阈值，确定是告警产生，还是撤销 */
	if (st_entry.ui_lrn_disc > st_threshold1.ui_thres1) {
		us_bitmap_curr |= HI_OMCI_ME_BRPM_ALARM_LRN_DISC;
	}

	/* 读取数据库当前告警状态 */
	i_ret = hi_omci_sql_alarm_inst_get(HI_OMCI_PRO_ME_MAC_PM_E, us_instid, &us_bitmap_hist);
	HI_OMCI_RET_CHECK(i_ret);

	/* 如果告警状态发生变化，更新数据库的告警状态 */
	if (us_bitmap_curr != us_bitmap_hist) {
		i_ret = hi_omci_sql_alarm_inst_set(HI_OMCI_PRO_ME_MAC_PM_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_proc_sendalarm(HI_OMCI_PRO_ME_MAC_PM_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_bridgepm_stat_get
 Description : 获取桥统计
 Input Parm  : hi_void *pv_data       hi_omci_stat_macpm_s 桥历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_bridgepm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_me_br_pm_s st_entry;
	hi_omci_tapi_stat_bridge_s st_bridgepm_curr;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_bridge_get(&st_bridgepm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减，将结构保存到数据库 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PM_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	st_entry.uc_endtime++;
	st_entry.ui_lrn_disc = st_bridgepm_curr.ui_discardcnt - gst_bridgepm_history.ui_discardcnt;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_MAC_PM_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	/* 将当前统计更新到历史统计 */
	HI_OS_MEMCPY_S(&gst_bridgepm_history, sizeof(gst_bridgepm_history), &st_bridgepm_curr, sizeof(gst_bridgepm_history));

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_bridgepm_create
 Description : MAC bridge performance monitoring history data create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_bridgepm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_br_pm_s *pst_entry = (hi_omci_me_br_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_bridgepm_stat_timer;
	hi_omci_me_alarm_timer_s st_bridgepm_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	HI_OS_MEMSET_S(&gst_bridgepm_history, sizeof(gst_bridgepm_history), 0, sizeof(gst_bridgepm_history));

	/* 创建Bridge PM告警实体 */
	i_ret = hi_omci_sql_alarm_inst_create(HI_OMCI_PRO_ME_MAC_PM_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_bridgepm_stat_timer, sizeof(st_bridgepm_stat_timer), 0, sizeof(st_bridgepm_stat_timer));
	st_bridgepm_stat_timer.ui_meid = HI_OMCI_PRO_ME_MAC_PM_E;
	st_bridgepm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_bridgepm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建15分钟定时器，上报告警 */
	HI_OS_MEMSET_S(&st_bridgepm_alarm_timer, sizeof(st_bridgepm_alarm_timer), 0, sizeof(st_bridgepm_alarm_timer));
	st_bridgepm_alarm_timer.ui_meid = HI_OMCI_PRO_ME_MAC_PM_E;
	st_bridgepm_alarm_timer.ui_instid = us_instid;
	st_bridgepm_alarm_timer.ui_timeout = HI_OMCI_ME_ALARM_COMM_TIMEOUT;
	i_ret = hi_omci_me_alarm_start(&st_bridgepm_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_bridgepm_delete
 Description : MAC bridge performance monitoring history data delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_bridgepm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_br_pm_s *pst_entry = (hi_omci_me_br_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_bridgepm_stat_timer;
	hi_omci_me_alarm_timer_s st_bridgepm_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 删除Bridge PM告警实体 */
	i_ret = hi_omci_sql_alarm_inst_delete(HI_OMCI_PRO_ME_MAC_PM_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 删除定时器 */
	HI_OS_MEMSET_S(&st_bridgepm_stat_timer, sizeof(st_bridgepm_stat_timer), 0, sizeof(st_bridgepm_stat_timer));
	st_bridgepm_stat_timer.ui_meid = HI_OMCI_PRO_ME_MAC_PM_E;
	st_bridgepm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_bridgepm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_bridgepm_alarm_timer, sizeof(st_bridgepm_alarm_timer), 0, sizeof(st_bridgepm_alarm_timer));
	st_bridgepm_alarm_timer.ui_meid = HI_OMCI_PRO_ME_MAC_PM_E;
	st_bridgepm_alarm_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_alarm_stop(&st_bridgepm_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

