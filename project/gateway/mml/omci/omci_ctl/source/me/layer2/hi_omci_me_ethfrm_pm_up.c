/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_ethfrm_pm_up.c
  Version    : 初稿
  Author     :
  Creation   :
  Description: Ethernet frame PM history data upstream
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_omci_tapi_stat_eth_s gst_ethfrmpm_up_history;

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_ethfrmpm_up_alarm_get
 Description : Ethernet frame PM history data upstream统计告警检查
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethfrmpm_up_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethfrmpm_up_stat_get
 Description :
 Input Parm  : hi_void *pv_data       历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethfrmpm_up_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_me_ethpm_up_s st_entity;
	hi_omci_tapi_stat_eth_s st_ethfrmpm_up_curr;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;
	hi_uchar8 uc_portid = 0, uc_type = 0;

	HI_OS_MEMSET_S(&st_entity, sizeof(hi_omci_me_ethpm_up_s), 0, sizeof(hi_omci_me_ethpm_up_s));
	/* 当前仅处理ETH口的统计 */
	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, HI_OMCI_ATTR3, &uc_type);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_debug("Eth Frm PM UP type [%hhu] \n", uc_type);
	if (uc_type != HI_MAC_BRI_PORT_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	i_ret = hi_omci_me_macportcfg_get_portid(us_instid, &uc_portid);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_debug("Eth Frm PM UP port [%hhu] \n", uc_portid);

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETHFRM_PM_UP_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_eth_get((hi_omci_tapi_port_e)uc_portid, &st_ethfrmpm_up_curr);
	HI_OMCI_RET_CHECK(i_ret);
	st_entity.uc_endtime++;
	st_entity.ui_dropevents = (st_ethfrmpm_up_curr.ui_tx_dropsevt > gst_ethfrmpm_up_history.ui_tx_dropsevt) ?
				  (st_ethfrmpm_up_curr.ui_tx_dropsevt - gst_ethfrmpm_up_history.ui_tx_dropsevt) : 0;
	st_entity.ui_octets = (st_ethfrmpm_up_curr.ui_tx_octets > gst_ethfrmpm_up_history.ui_tx_octets) ?
			      (st_ethfrmpm_up_curr.ui_tx_octets - gst_ethfrmpm_up_history.ui_tx_octets) : 0;
	st_entity.ui_packets = (st_ethfrmpm_up_curr.ui_tx_packets > gst_ethfrmpm_up_history.ui_tx_packets) ?
			       (st_ethfrmpm_up_curr.ui_tx_packets - gst_ethfrmpm_up_history.ui_tx_packets) : 0;
	st_entity.ui_bc = (st_ethfrmpm_up_curr.ui_tx_bcpkt > gst_ethfrmpm_up_history.ui_tx_bcpkt) ?
			  (st_ethfrmpm_up_curr.ui_tx_bcpkt - gst_ethfrmpm_up_history.ui_tx_bcpkt) : 0;
	st_entity.ui_mc = (st_ethfrmpm_up_curr.ui_tx_mcpkt > gst_ethfrmpm_up_history.ui_tx_mcpkt) ?
			  (st_ethfrmpm_up_curr.ui_tx_mcpkt - gst_ethfrmpm_up_history.ui_tx_mcpkt) : 0;
	st_entity.ui_crcerrored = (st_ethfrmpm_up_curr.ui_tx_crcerror > gst_ethfrmpm_up_history.ui_tx_crcerror) ?
				  (st_ethfrmpm_up_curr.ui_tx_crcerror - gst_ethfrmpm_up_history.ui_tx_crcerror) : 0;
	st_entity.ui_undersize = (st_ethfrmpm_up_curr.ui_tx_undersizepkt > gst_ethfrmpm_up_history.ui_tx_undersizepkt) ?
				 (st_ethfrmpm_up_curr.ui_tx_undersizepkt - gst_ethfrmpm_up_history.ui_tx_undersizepkt) :
				 0;
	st_entity.ui_oversize = (st_ethfrmpm_up_curr.ui_tx_jabber > gst_ethfrmpm_up_history.ui_tx_jabber) ?
				(st_ethfrmpm_up_curr.ui_tx_jabber - gst_ethfrmpm_up_history.ui_tx_jabber) : 0;
	st_entity.ui_pkt64 = (st_ethfrmpm_up_curr.ui_tx_pkt64 > gst_ethfrmpm_up_history.ui_tx_pkt64) ?
			     (st_ethfrmpm_up_curr.ui_tx_pkt64 - gst_ethfrmpm_up_history.ui_tx_pkt64) : 0;
	st_entity.ui_pkt65to127 = (st_ethfrmpm_up_curr.ui_tx_pkt65to127 > gst_ethfrmpm_up_history.ui_tx_pkt65to127) ?
				  (st_ethfrmpm_up_curr.ui_tx_pkt65to127 - gst_ethfrmpm_up_history.ui_tx_pkt65to127) : 0;
	st_entity.ui_pkt128to255 = (st_ethfrmpm_up_curr.ui_tx_pkt128to255 > gst_ethfrmpm_up_history.ui_tx_pkt128to255) ?
				   (st_ethfrmpm_up_curr.ui_tx_pkt128to255 - gst_ethfrmpm_up_history.ui_tx_pkt128to255) :
				   0;
	st_entity.ui_pkt256to511 = (st_ethfrmpm_up_curr.ui_tx_pkt256to511 > gst_ethfrmpm_up_history.ui_tx_pkt256to511) ?
				   (st_ethfrmpm_up_curr.ui_tx_pkt256to511 - gst_ethfrmpm_up_history.ui_tx_pkt256to511) :
				   0;
	st_entity.ui_pkt512to1023 = (st_ethfrmpm_up_curr.ui_tx_pkt512to1023 > gst_ethfrmpm_up_history.ui_tx_pkt512to1023) ?
				    (st_ethfrmpm_up_curr.ui_tx_pkt512to1023 -
				     gst_ethfrmpm_up_history.ui_tx_pkt512to1023) : 0;
	st_entity.ui_pkt1024to1518 = (st_ethfrmpm_up_curr.ui_tx_pkt1024to1518 > gst_ethfrmpm_up_history.ui_tx_pkt1024to1518) ?
				     (st_ethfrmpm_up_curr.ui_tx_pkt1024to1518 -
				      gst_ethfrmpm_up_history.ui_tx_pkt1024to1518) : 0;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETHFRM_PM_UP_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 更新历史统计 */
	HI_OS_MEMCPY_S(&gst_ethfrmpm_up_history, sizeof(gst_ethfrmpm_up_history), &st_ethfrmpm_up_curr,
		       sizeof(gst_ethfrmpm_up_history));

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
hi_int32 hi_omci_me_ethfrmpm_up_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm_up_s *pst_timer = (hi_omci_me_ethpm_up_s *)pv_data;
	hi_omci_me_ethpm_up_s st_entity;
	hi_omci_tapi_stat_eth_s st_ethfrmpm_up_curr;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_timer->st_msghead.us_instid;
	hi_uchar8 uc_portid = 0, uc_type = 0;

	/* 当前仅处理ETH口的统计 */
	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, HI_OMCI_ATTR3, &uc_type);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_debug("Eth Frm PM UP type [%hhu] \n", uc_type);
	if (uc_type != HI_MAC_BRI_PORT_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	i_ret = hi_omci_me_macportcfg_get_portid(us_instid, &uc_portid);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_debug("Eth Frm PM UP port [%hhu] \n", uc_portid);

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETHFRM_PM_UP_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_eth_get((hi_omci_tapi_port_e)uc_portid, &st_ethfrmpm_up_curr);
	HI_OMCI_RET_CHECK(i_ret);
	st_entity.ui_dropevents = (st_ethfrmpm_up_curr.ui_tx_dropsevt > gst_ethfrmpm_up_history.ui_tx_dropsevt) ?
				  (st_ethfrmpm_up_curr.ui_tx_dropsevt - gst_ethfrmpm_up_history.ui_tx_dropsevt) : 0;
	st_entity.ui_octets = (st_ethfrmpm_up_curr.ui_tx_octets > gst_ethfrmpm_up_history.ui_tx_octets) ?
			      (st_ethfrmpm_up_curr.ui_tx_octets - gst_ethfrmpm_up_history.ui_tx_octets) : 0;
	st_entity.ui_packets = (st_ethfrmpm_up_curr.ui_tx_packets > gst_ethfrmpm_up_history.ui_tx_packets) ?
			       (st_ethfrmpm_up_curr.ui_tx_packets - gst_ethfrmpm_up_history.ui_tx_packets) : 0;
	st_entity.ui_bc = (st_ethfrmpm_up_curr.ui_tx_bcpkt > gst_ethfrmpm_up_history.ui_tx_bcpkt) ?
			  (st_ethfrmpm_up_curr.ui_tx_bcpkt - gst_ethfrmpm_up_history.ui_tx_bcpkt) : 0;
	st_entity.ui_mc = (st_ethfrmpm_up_curr.ui_tx_mcpkt > gst_ethfrmpm_up_history.ui_tx_mcpkt) ?
			  (st_ethfrmpm_up_curr.ui_tx_mcpkt - gst_ethfrmpm_up_history.ui_tx_mcpkt) : 0;
	st_entity.ui_crcerrored = (st_ethfrmpm_up_curr.ui_tx_crcerror > gst_ethfrmpm_up_history.ui_tx_crcerror) ?
				  (st_ethfrmpm_up_curr.ui_tx_crcerror - gst_ethfrmpm_up_history.ui_tx_crcerror) : 0;
	st_entity.ui_undersize = (st_ethfrmpm_up_curr.ui_tx_undersizepkt > gst_ethfrmpm_up_history.ui_tx_undersizepkt) ?
				 (st_ethfrmpm_up_curr.ui_tx_undersizepkt - gst_ethfrmpm_up_history.ui_tx_undersizepkt) :
				 0;
	st_entity.ui_oversize = (st_ethfrmpm_up_curr.ui_tx_jabber > gst_ethfrmpm_up_history.ui_tx_jabber) ?
				(st_ethfrmpm_up_curr.ui_tx_jabber - gst_ethfrmpm_up_history.ui_tx_jabber) : 0;
	st_entity.ui_pkt64 = (st_ethfrmpm_up_curr.ui_tx_pkt64 > gst_ethfrmpm_up_history.ui_tx_pkt64) ?
			     (st_ethfrmpm_up_curr.ui_tx_pkt64 - gst_ethfrmpm_up_history.ui_tx_pkt64) : 0;
	st_entity.ui_pkt65to127 = (st_ethfrmpm_up_curr.ui_tx_pkt65to127 > gst_ethfrmpm_up_history.ui_tx_pkt65to127) ?
				  (st_ethfrmpm_up_curr.ui_tx_pkt65to127 - gst_ethfrmpm_up_history.ui_tx_pkt65to127) : 0;
	st_entity.ui_pkt128to255 = (st_ethfrmpm_up_curr.ui_tx_pkt128to255 > gst_ethfrmpm_up_history.ui_tx_pkt128to255) ?
				   (st_ethfrmpm_up_curr.ui_tx_pkt128to255 - gst_ethfrmpm_up_history.ui_tx_pkt128to255) :
				   0;
	st_entity.ui_pkt256to511 = (st_ethfrmpm_up_curr.ui_tx_pkt256to511 > gst_ethfrmpm_up_history.ui_tx_pkt256to511) ?
				   (st_ethfrmpm_up_curr.ui_tx_pkt256to511 - gst_ethfrmpm_up_history.ui_tx_pkt256to511) :
				   0;
	st_entity.ui_pkt512to1023 = (st_ethfrmpm_up_curr.ui_tx_pkt512to1023 > gst_ethfrmpm_up_history.ui_tx_pkt512to1023) ?
				    (st_ethfrmpm_up_curr.ui_tx_pkt512to1023 -
				     gst_ethfrmpm_up_history.ui_tx_pkt512to1023) : 0;
	st_entity.ui_pkt1024to1518 = (st_ethfrmpm_up_curr.ui_tx_pkt1024to1518 > gst_ethfrmpm_up_history.ui_tx_pkt1024to1518) ?
				     (st_ethfrmpm_up_curr.ui_tx_pkt1024to1518 -
				      gst_ethfrmpm_up_history.ui_tx_pkt1024to1518) : 0;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETHFRM_PM_UP_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_me_ethfrmpm_up_create
 Description : Ethernet frame PM history data upstream Create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethfrmpm_up_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm_up_s *pst_entry = (hi_omci_me_ethpm_up_s *)pv_data;
	hi_omci_me_stat_timer_s st_ethfrmpm_up_stat_timer;
	hi_omci_me_alarm_timer_s st_ethfrmpm_up_alarm_timer;
	hi_int32 i_ret;
	hi_uchar8 uc_type = 0;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	HI_OS_MEMSET_S(&gst_ethfrmpm_up_history, sizeof(gst_ethfrmpm_up_history), 0, sizeof(gst_ethfrmpm_up_history));

	hi_omci_debug("Eth Frm PM UP instid [%hu] \n", us_instid);

	/* 不是以太端口的实体，不支持统计告警 */
	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, HI_OMCI_ATTR3, &uc_type);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_debug("Eth Frm PM UP type [%hhu] \n", uc_type);
	if (uc_type != HI_MAC_BRI_PORT_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	/* 创建ETH FRM PM UP告警实体 */
	i_ret = hi_omci_sql_alarm_inst_create(HI_OMCI_PRO_ME_ETHFRM_PM_UP_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_ethfrmpm_up_stat_timer, sizeof(st_ethfrmpm_up_stat_timer), 0, sizeof(st_ethfrmpm_up_stat_timer));
	st_ethfrmpm_up_stat_timer.ui_meid = HI_OMCI_PRO_ME_ETHFRM_PM_UP_E;
	st_ethfrmpm_up_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_ethfrmpm_up_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建3秒定时器，上报告警 */
	HI_OS_MEMSET_S(&st_ethfrmpm_up_alarm_timer, sizeof(st_ethfrmpm_up_alarm_timer), 0, sizeof(st_ethfrmpm_up_alarm_timer));
	st_ethfrmpm_up_alarm_timer.ui_meid = HI_OMCI_PRO_ME_ETHFRM_PM_UP_E;
	st_ethfrmpm_up_alarm_timer.ui_instid = us_instid;
	st_ethfrmpm_up_alarm_timer.ui_timeout = HI_OMCI_ME_ALARM_COMM_TIMEOUT;
	i_ret = hi_omci_me_alarm_start(&st_ethfrmpm_up_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethfrmpm_up_delete
 Description : Ethernet frame PM history data upstream Delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethfrmpm_up_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm_up_s *pst_entry = (hi_omci_me_ethpm_up_s *)pv_data;
	hi_omci_me_stat_timer_s st_ethfrmpm_up_stat_timer;
	hi_omci_me_alarm_timer_s st_ethfrmpm_up_alarm_timer;
	hi_int32 i_ret;
	hi_uchar8 uc_type = 0;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 不是以太端口的实体，不支持统计告警 */
	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, HI_OMCI_ATTR3, &uc_type);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_debug("Eth Frm PM UP type [%hhu] \n", uc_type);
	if (uc_type != HI_MAC_BRI_PORT_UNI_E) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	/* 删除ETH EXT PM告警实体 */
	i_ret = hi_omci_sql_alarm_inst_delete(HI_OMCI_PRO_ME_ETHFRM_PM_UP_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_ethfrmpm_up_stat_timer, sizeof(st_ethfrmpm_up_stat_timer), 0, sizeof(st_ethfrmpm_up_stat_timer));
	st_ethfrmpm_up_stat_timer.ui_meid = HI_OMCI_PRO_ME_ETHFRM_PM_UP_E;
	st_ethfrmpm_up_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_ethfrmpm_up_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_ethfrmpm_up_alarm_timer, sizeof(st_ethfrmpm_up_alarm_timer), 0, sizeof(st_ethfrmpm_up_alarm_timer));
	st_ethfrmpm_up_alarm_timer.ui_meid = HI_OMCI_PRO_ME_ETHFRM_PM_UP_E;
	st_ethfrmpm_up_alarm_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_alarm_stop(&st_ethfrmpm_up_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

