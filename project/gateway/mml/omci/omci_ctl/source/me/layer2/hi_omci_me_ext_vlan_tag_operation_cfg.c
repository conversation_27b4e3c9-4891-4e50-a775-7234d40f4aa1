/******************************************************************************

                  版权所有 (C), 2009-2019, 华为技术有限公司

 ******************************************************************************
  文 件 名   : hi_omci_me_ext_vlan_tag_operation_cfg.c
  版 本 号   : 初稿
  作    者   : h66243
  生成日期   : D2012_02_18
  功能描述   : Extended VLAN tagging operation configuration data
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
//#include "hi_hal.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
//#define HI_OMCI_EXVLAN_DEFAULT_VLAN 1

/* Input TPID */
#define HI_OMCI_ME_EXVLAN_IS_INPUT_TPID(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR3)

/* Output TPID */
#define HI_OMCI_ME_EXVLAN_IS_OUTPUT_TPID(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR4)

/* Received frame VLAN tagging operation table */
#define HI_OMCI_ME_EXVLAN_IS_TABLE(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR6)

/* DSCP to P-bit mapping */
#define HI_OMCI_ME_EXVLAN_IS_PBIT_MAP(mask) HI_OMCI_CHECK_ATTR((mask), HI_OMCI_ATTR8)

#define HI_OMCI_ME_EXVLAN_DSCP_NUM 64
#define HI_OMCI_ME_EXVLAN_DSCP_MAP_BIT_NUM 8
#define HI_OMCI_ME_EXVLAN_DSCP_GET(index, pri) (HI_OMCI_ME_EXVLAN_DSCP_NUM - 1 - ((index) * HI_OMCI_ME_EXVLAN_DSCP_MAP_BIT_NUM + (HI_OMCI_8021P_PRI_NUM - 1- (pri))))
//#define HI_OMCI_BITS_SET(reg, bit, mask, value) ((reg) = ((reg) & (~((hi_uint32)(mask) << (bit)))) | ((hi_uint32)((value) & (mask)) << (bit)))
//hi_uint32 gui_omci_extvlan_mask = 0;
extern hi_uint32 gui_omci_voip_flag;
extern hi_omci_tapi_voip_extvlan_s g_st_extvlan_table;
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
#pragma pack(1)

typedef union {
	struct {
		hi_uchar8 pri7: 3;
		hi_uchar8 pri6: 3;
		hi_uchar8 pri5: 3;
		hi_uchar8 pri4: 3;
		hi_uchar8 pri3: 3;
		hi_uchar8 pri2: 3;
		hi_uchar8 pri1: 3;
		hi_uchar8 pri0: 3;
	} bits[HI_OMCI_ME_EXVLAN_DSCP_MAP_BIT_NUM];

	hi_uchar8 dscp2pri[HI_OMCI_DSCP_2_P_LEN];
} hi_omci_me_exvlan_dscp_map_st;

#pragma pack()

typedef struct {
	omci_extvlantag_s st_vlantag;
	hi_uint32         ui_port;
} omci_vlantag_op_item_s;

static omci_vlantag_op_item_s g_st_op_item = {
	.ui_port = 0xff
};

//static hi_uint32 g_eth_trans_add[4] = {0, 0, 0, 0};
//static hi_uint32 g_eth_trans_del[4] = {0, 0, 0, 0};

/* 查询vlan tag table全局变量*/
static omci_extvlantag_s  *g_pst_vlantagtable = HI_NULL;
static hi_uint32  g_ui_vlantagtable_num = 0;
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
static hi_uint32 __omci_me_exvlantag_printf(hi_uchar8 *pnewentry)
{
	hi_omci_debug("FILTER_OUT:PRI(%u), VID(%u), TPID(%u)\n",
		      HI_OMCI_EXVLAN_GET_FILTER_OUT_PRI(pnewentry),
		      HI_OMCI_EXVLAN_GET_FILTER_OUT_VID(pnewentry),
		      HI_OMCI_EXVLAN_GET_FILTER_OUT_TPID(pnewentry));
	hi_omci_debug("FILTER_INN:PRI(%u), VID(%u), TPID(%u), ETHTYPE(%u)\n",
		      HI_OMCI_EXVLAN_GET_FILTER_INN_PRI(pnewentry),
		      HI_OMCI_EXVLAN_GET_FILTER_INN_VID(pnewentry),
		      HI_OMCI_EXVLAN_GET_FILTER_INN_TPID(pnewentry),
		      HI_OMCI_EXVLAN_GET_FILTER_ETHTYPE(pnewentry));
	hi_omci_debug("TREAT_OUT:PRI(%u), VID(%u), TPID(%u), Tag_Remove(%u)\n",
		      HI_OMCI_EXVLAN_GET_TREAT_OUT_PRI(pnewentry),
		      HI_OMCI_EXVLAN_GET_TREAT_OUT_VID(pnewentry),
		      HI_OMCI_EXVLAN_GET_TREAT_OUT_TPID(pnewentry),
		      HI_OMCI_EXVLAN_GET_TREAT_TAG_REMOVE(pnewentry));
	hi_omci_debug("TREAT_INN:PRI(%u), VID(%u), TPID(%u)\n",
		      HI_OMCI_EXVLAN_GET_TREAT_INN_PRI(pnewentry),
		      HI_OMCI_EXVLAN_GET_TREAT_INN_VID(pnewentry),
		      HI_OMCI_EXVLAN_GET_TREAT_INN_TPID(pnewentry));

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

static hi_void __omci_me_exvlantag_table_printf(hi_uchar8 *puc_table, hi_uint32 ui_len)
{
	hi_uint32 ui_index;
	hi_uchar8 uc_data;

	hi_omci_print(HI_LOG_LEVEL_INFO_E, "\n");

	for (ui_index = 0; ui_index < ui_len; ui_index++) {
		if (ui_index != 0 && ui_index % 16 == 0) {
			hi_omci_print(HI_LOG_LEVEL_INFO_E, "\n");
		}

		uc_data = *(puc_table + ui_index);
		(void)uc_data;
		hi_omci_print(HI_LOG_LEVEL_INFO_E, "%02x ", uc_data);
	}

	hi_omci_print(HI_LOG_LEVEL_INFO_E, "\n");
	return;
}

static hi_void __omci_me_exvlan_filter(hi_uint32 ui_pri, hi_uint32 ui_vlan,
				       hi_uint32 ui_tpid, hi_omci_me_exvlan_filter_type_e *em_filter)
{
	switch (ui_pri) {
	case HI_OMCI_FILTER_NO_TAG:
		ui_pri = HI_OMCI_EXVLAN_FILTER_IGNORE;
		break;

	case HI_OMCI_FILTER_DEFAULT_TAG:
		ui_pri = HI_OMCI_EXVLAN_FILTER_DEFAULT;
		break;

	case HI_OMCI_FILETER_PRIORITY_NO:
		ui_pri = HI_OMCI_EXVLAN_FILTER_NONE;
		break;

	case 0:
	case 1:
	case 2:
	case 3:
	case 4:
	case 5:
	case 6:
	case 7:
		ui_pri = HI_OMCI_EXVLAN_FILTER_PRI;
		break;

	default:
		hi_omci_systrace(HI_RET_SUCC, ui_pri, 0, 0, 0);
		*em_filter = HI_OMCI_EXVLAN_FILTER_INVALID_E;
		return;
	}

	if (HI_OMCI_INVALID_VLAN == ui_vlan) {
		ui_vlan = HI_FALSE;
	} else if (HI_OMCI_VLANID_MAX >= ui_vlan) {
		ui_vlan = HI_TRUE;
	} else {
		hi_omci_systrace(HI_RET_SUCC, ui_vlan, 0, 0, 0);

		/* 无效过滤和缺省过滤都不关心VID，如果此时下发了非法VID值，可以不报错 */
		if (ui_pri == HI_OMCI_EXVLAN_FILTER_IGNORE || ui_pri == HI_OMCI_EXVLAN_FILTER_DEFAULT) {
			ui_vlan = HI_FALSE;
		} else {
			*em_filter = HI_OMCI_EXVLAN_FILTER_INVALID_E;
			return;
		}
	}

	if (HI_OMCI_FILTER_NO_TPID == ui_tpid) {
		ui_tpid = HI_FALSE;
	} else if (ui_tpid & 0x4) {
		ui_tpid = HI_TRUE;
	} else {
		hi_omci_systrace(HI_RET_SUCC, ui_tpid, 0, 0, 0);

		/* 无效过滤和缺省过滤都不关心TPID，如果此时下发了非法TPID值，可以不报错 */
		if (ui_pri == HI_OMCI_EXVLAN_FILTER_IGNORE || ui_pri == HI_OMCI_EXVLAN_FILTER_DEFAULT) {
			ui_tpid = HI_FALSE;
		} else {
			*em_filter = HI_OMCI_EXVLAN_FILTER_INVALID_E;
			return;
		}
	}

	*em_filter = (hi_omci_me_exvlan_filter_type_e)HI_OMCI_EXVLAN_FILTER_CODING(ui_tpid, ui_vlan, ui_pri);
	return;
}

static hi_void __omci_me_exvlan_action(omci_extvlantag_s *pnewentry, hi_omci_me_exvlan_action_type_e *em_action)
{
	hi_uint32 ui_remove = HI_OMCI_EXVLAN_GET_TREAT_TAG_REMOVE(pnewentry);
	hi_uint32 ui_inner_pri = HI_OMCI_EXVLAN_GET_TREAT_INN_PRI(pnewentry);
	hi_uint32 ui_inner_vid = HI_OMCI_EXVLAN_GET_TREAT_INN_VID(pnewentry);
	hi_uint32 ui_inner_tpid = HI_OMCI_EXVLAN_GET_TREAT_INN_TPID(pnewentry);
	hi_uint32 ui_add = HI_FALSE;

	if (HI_OMCI_TREATMENT_TAG_NO_ADD == ui_inner_pri) {
		ui_add = HI_FALSE;
		ui_inner_pri = HI_FALSE;
	} else if (ui_inner_pri < HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_INNER
		   || HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP == ui_inner_pri) {
		ui_add = HI_TRUE;
		ui_inner_pri = HI_TRUE;
	} else if (ui_inner_pri < HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP) {
		ui_add = HI_TRUE;
		ui_inner_pri = HI_FALSE;
	} else {
		hi_omci_systrace(HI_RET_SUCC, ui_inner_pri, 0, 0, 0);
		*em_action = HI_OMCI_EXVLAN_ACT_INVALID_E;
		return;
	}

	if (ui_add == HI_FALSE) {
		ui_inner_vid = HI_FALSE;
	} else if (ui_inner_vid <= HI_OMCI_VLANID_MAX) {
		ui_inner_vid = HI_TRUE;
	} else if (ui_inner_vid <= HI_OMCI_TREATMENT_TAG_ADD_VLAN_COPY_OUTER) {
		ui_inner_vid = HI_FALSE;
	} else {
		hi_omci_systrace(HI_RET_SUCC, ui_inner_vid, 0, 0, 0);

		/* 不添加TAG，不用关心VID，如果此时下发了VID非法值没有关系 */
		if (ui_add == HI_FALSE) {
			ui_inner_vid = HI_FALSE;
		} else {
			*em_action = HI_OMCI_EXVLAN_ACT_INVALID_E;
			return;
		}
	}

	if (ui_add == HI_FALSE) {
		ui_inner_tpid = HI_FALSE;
	}
	if (HI_OMCI_TREATMENT_TAG_ADD_TPID_RESV == ui_inner_tpid) {
		hi_omci_systrace(HI_RET_SUCC, ui_inner_tpid, 0, 0, 0);

		/* 不添加TAG，不用关心TPID，如果此时下发了TPID非法值没有关系 */
		if (ui_add == HI_FALSE) {
			ui_inner_tpid = HI_FALSE;
		} else {
			*em_action = HI_OMCI_EXVLAN_ACT_INVALID_E;
			return;
		}
	} else if (ui_inner_tpid < HI_OMCI_TREATMENT_TAG_ADD_TPID_DEI_INNER) {
		ui_inner_tpid = HI_FALSE;
	} else {
		ui_inner_tpid = HI_TRUE;
	}

	*em_action = (hi_omci_me_exvlan_action_type_e)HI_OMCI_EXVLAN_ACTION_CODING(ui_inner_tpid, ui_inner_pri, ui_inner_vid,
			ui_add, ui_remove);
	return;
}

static hi_void __omci_me_exvlan_actiontype(omci_extvlantag_s *pnewentry, hi_omci_me_exvlan_opt_type_e *em_newaction)
{
	hi_omci_me_exvlan_filter_type_e em_inner_filter;
	hi_omci_me_exvlan_filter_type_e em_outer_filter;
	hi_omci_me_exvlan_action_type_e em_action;
	hi_uint32 em_ethtype;
	hi_uint32 ui_ethtype = HI_OMCI_EXVLAN_GET_FILTER_ETHTYPE(pnewentry);

	__omci_me_exvlantag_printf((hi_uchar8 *)pnewentry);

	__omci_me_exvlan_filter(HI_OMCI_EXVLAN_GET_FILTER_INN_PRI(pnewentry),
				HI_OMCI_EXVLAN_GET_FILTER_INN_VID(pnewentry),
				HI_OMCI_EXVLAN_GET_FILTER_INN_TPID(pnewentry),
				&em_inner_filter);

	__omci_me_exvlan_filter(HI_OMCI_EXVLAN_GET_FILTER_OUT_PRI(pnewentry),
				HI_OMCI_EXVLAN_GET_FILTER_OUT_VID(pnewentry),
				HI_OMCI_EXVLAN_GET_FILTER_OUT_TPID(pnewentry),
				&em_outer_filter);

	if (HI_OMCI_ETHERTYPE_NOFILTER == ui_ethtype) {
		em_ethtype = HI_FALSE;
	} else if (ui_ethtype <= HI_OMCI_ETHERTYPE_FILTER_IPV6) {
		em_ethtype = HI_TRUE;
	} else {
		hi_omci_systrace(HI_RET_SUCC, ui_ethtype, 0, 0, 0);
		*em_newaction = HI_OMCI_EXVLAN_OPT_INVALID_E;
		return;
	}

	__omci_me_exvlan_action(pnewentry, &em_action);

	*em_newaction = (hi_omci_me_exvlan_opt_type_e)HI_OMCI_EXVLAN_OPT_CODING(em_action, em_ethtype, em_outer_filter,
			em_inner_filter);
	return;
}


static hi_void __omci_get_vlan_opt_rule(omci_extvlantag_s *pst_extvlan, hi_omci_vlan_opt_rule_s *pst_optrule)
{
	pst_optrule->uc_filterintpid  = pst_extvlan->ui_filt_in_tpid;
	pst_optrule->uc_filterouttpid = pst_extvlan->ui_filt_outer_tpid;
	pst_optrule->us_filterinvlan  = pst_extvlan->ui_filt_in_vid;
	pst_optrule->us_filteroutvlan = pst_extvlan->ui_filt_outer_vid;
	pst_optrule->uc_filterinpri   = pst_extvlan->ui_filt_in_pri;
	pst_optrule->uc_filteroutpri  = pst_extvlan->ui_filt_outer_pri;
	pst_optrule->uc_treatintpid   = pst_extvlan->ui_treat_inner_tpid;
	pst_optrule->uc_treatouttpid  = pst_extvlan->ui_treat_outer_tpid;
	pst_optrule->us_treatinvlan   = pst_extvlan->ui_treat_inner_vid;
	pst_optrule->us_treatoutvlan  = pst_extvlan->ui_treat_outer_vid;
	pst_optrule->uc_treatinpri    = pst_extvlan->ui_treat_inner_pri;
	pst_optrule->uc_treatoutpri   = pst_extvlan->ui_treat_outer_pri;

	switch (pst_extvlan->ui_filt_ethertype) {
	case HI_OMCI_ETHERTYPE_FILTER_IPoE:
		pst_optrule->ui_filterethtype = HI_OMCI_FRM_ETHTYPE_IPOE;
		break;
	case HI_OMCI_ETHERTYPE_FILTER_PPPoE:
		pst_optrule->ui_filterethtype = HI_OMCI_FRM_ETHTYPE_PPPOE;
		break;
	case HI_OMCI_ETHERTYPE_FILTER_ARP:
		pst_optrule->ui_filterethtype = HI_OMCI_FRM_ETHTYPE_ARP;
		break;
	case HI_OMCI_ETHERTYPE_FILTER_IPV6:
		pst_optrule->ui_filterethtype = HI_OMCI_FRM_ETHTYPE_IPV6;
		break;
	default:
		pst_optrule->ui_filterethtype = HI_OMCI_FRM_ETHTYPE_NOFILTER;
		break;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return;
}

static hi_int32 __omci_vlan_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_vid, hi_uint32 ui_act)
{
	hi_int32 i_ret;

	if (ui_act == HI_TRUE) {
		i_ret = hi_omci_tapi_vlan_set(em_port, ui_vid);
	} else {
		i_ret = hi_omci_tapi_vlan_del(em_port, ui_vid);
	}

	return i_ret;
}

static hi_int32 __omci_vlan_translation_set(hi_omci_tapi_port_e em_port,
		hi_uint32 ui_cvlan, hi_uint32 ui_svlan, hi_uint32 ui_tpid_en, hi_uint32 ui_act)
{
	hi_int32 i_ret;

	if (ui_act == HI_TRUE) {
		i_ret = hi_omci_tapi_vlan_translation_set(em_port, ui_cvlan, ui_svlan, ui_tpid_en);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_tapi_vlan_set(em_port, ui_svlan);
		HI_OMCI_RET_CHECK(i_ret);
	} else {
		i_ret = hi_omci_tapi_vlan_translation_del(em_port, ui_cvlan);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_tapi_vlan_del(em_port, ui_svlan);
		HI_OMCI_RET_CHECK(i_ret);
	}

	return i_ret;
}

static hi_int32 __omci_vlan_translation_opt_set(hi_omci_vlan_opt_rule_s *pst_vlanopt, hi_omci_tapi_mark_s *pst_mark,
		hi_omci_tapi_port_e em_port, hi_uint32 ui_tpid_en, hi_uint32 ui_act)
{
	hi_int32 i_ret;
	pst_mark->ui_svlan = pst_vlanopt->us_treatinvlan;
	pst_mark->ui_spri  = pst_vlanopt->uc_treatinpri;
	pst_mark->ui_tpid_en = ui_tpid_en;

	if (ui_act == HI_TRUE) {
		if (pst_vlanopt->ui_filterethtype == HI_OMCI_FRM_ETHTYPE_PPPOE) {
			pst_mark->ui_ethtype = pst_vlanopt->ui_filterethtype & 0xffff;

			i_ret = hi_omci_tapi_vlan_translation_opt_set(em_port, pst_mark);
			HI_OMCI_RET_CHECK(i_ret);

			pst_mark->ui_ethtype = pst_vlanopt->ui_filterethtype >> 16;
			i_ret = hi_omci_tapi_vlan_translation_opt_set(em_port, pst_mark);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			i_ret = hi_omci_tapi_vlan_translation_opt_set(em_port, pst_mark);
			HI_OMCI_RET_CHECK(i_ret);
		}
	} else {
		if (pst_vlanopt->ui_filterethtype == HI_OMCI_FRM_ETHTYPE_PPPOE) {
			pst_mark->ui_ethtype = pst_vlanopt->ui_filterethtype & 0xffff;

			i_ret = hi_omci_tapi_vlan_translation_opt_del(em_port, pst_mark);
			HI_OMCI_RET_CHECK(i_ret);

			pst_mark->ui_ethtype = pst_vlanopt->ui_filterethtype >> 16;
			i_ret = hi_omci_tapi_vlan_translation_opt_del(em_port, pst_mark);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			i_ret = hi_omci_tapi_vlan_translation_opt_del(em_port, pst_mark);
			HI_OMCI_RET_CHECK(i_ret);
		}
	}

	return i_ret;
}

static hi_int32 __omci_vlan_port_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_cvlan,  hi_uint32 ui_act)
{
	hi_int32 i_ret;

	if (ui_act == HI_TRUE) {
		i_ret = hi_omci_tapi_vlan_set(em_port, ui_cvlan);
	} else {
		i_ret = hi_omci_tapi_vlan_del(em_port, ui_cvlan);
	}

	return i_ret;
}
static hi_int32 __omci_tls_ctag_set(hi_omci_tapi_port_e em_port,
				    hi_uint32 ui_svlan, hi_uint32 ui_act)
{
	hi_int32 i_ret;

	if (ui_act == HI_TRUE) {
		i_ret = hi_omci_tapi_tls_ctag_set(em_port, ui_svlan);
	} else {
		i_ret = hi_omci_tapi_tls_ctag_del(em_port, ui_svlan);
	}

	return i_ret;
}

static hi_int32 __omci_tls_stag_set(hi_omci_tapi_port_e em_port,
				    hi_uint32 ui_svlan, hi_uint32 ui_svlan_trans, hi_uint32 ui_act)
{
	hi_int32 i_ret;

	if (ui_act == HI_TRUE) {
		i_ret = hi_omci_tapi_tls_stag_set(em_port, ui_svlan, ui_svlan_trans);
	} else {
		i_ret = hi_omci_tapi_tls_stag_del(em_port, ui_svlan);
	}

	return i_ret;
}


static hi_int32 __omci_port_qinq_set(hi_omci_tapi_port_e em_port,
				     hi_uint32 ui_spri, hi_uint32 ui_svlan, hi_uint32 tpid_en, hi_uint32 ui_act)
{
	hi_int32 i_ret;

	if (ui_act == HI_TRUE) {
		i_ret = hi_omci_tapi_port_qinq_set(em_port, ui_spri, ui_svlan, tpid_en);
	} else {
		i_ret = hi_omci_tapi_port_qinq_del(em_port, ui_svlan);
	}

	return i_ret;
}


static hi_int32 __omci_qinq_set(hi_omci_tapi_port_e em_port,
				hi_uint32 ui_cvlan, hi_uint32 ui_svlan, hi_uint32 tpid_en, hi_uint32 ui_act)
{
	hi_int32 i_ret;

	if (ui_act == HI_TRUE) {
		i_ret = hi_omci_tapi_qinq_set(em_port, ui_cvlan, ui_svlan, tpid_en);
	} else {
		i_ret = hi_omci_tapi_qinq_del(em_port, ui_cvlan);
	}

	return i_ret;
}

static hi_int32 __omci_qinq_full_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_mark_s *pst_mark, hi_uint32 ui_act)
{
	hi_int32 i_ret;

	if (ui_act == HI_TRUE) {
		i_ret = hi_omci_tapi_qinq_full_set(em_port, pst_mark);
	} else {
		i_ret = hi_omci_tapi_qinq_full_del(em_port, pst_mark);
	}

	return i_ret;
}

static hi_int32 __omci_qinq_vlan_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_mark_s *pst_mark, hi_uint32 ui_act)
{
	hi_int32 i_ret;

	if (ui_act == HI_TRUE) {
		i_ret = hi_omci_tapi_qinq_vlan_set(em_port, pst_mark);
	} else {
		i_ret = hi_omci_tapi_qinq_vlan_del(em_port, pst_mark);
	}

	return i_ret;
}

static hi_int32 __omci_pri_marking_set(hi_omci_tapi_port_e em_port,
				       hi_omci_tapi_qos_pri_s *pst_mark, hi_uint32 ui_act)
{
	hi_int32 i_ret;

	if (ui_act == HI_TRUE) {
		i_ret = hi_omci_tapi_pri_marking_set(em_port, pst_mark);
	} else {
		i_ret = hi_omci_tapi_pri_marking_del(em_port, pst_mark);
	}

	return i_ret;
}

static hi_int32 __omci_opt_get_mask(hi_omci_me_exvlan_opt_type_e em_opt,
				    hi_omci_vlan_opt_rule_s *pst_vlanopt, hi_omci_tapi_mark_s *pst_mark)
{
	hi_omci_debug("exvlan_opt_type = 0x%08x\n", em_opt);

	switch (em_opt) {
	case HI_OMCI_EXVLAN_OPT_UNTAG_ETHTYPE_ADD_TAG_E:
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		pst_mark->ui_ethtype = pst_vlanopt->ui_filterethtype;
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SVLAN_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		pst_mark->ui_cpri = pst_vlanopt->uc_filterinpri;
		pst_mark->ui_cvlan = pst_vlanopt->us_filterinvlan;
		pst_mark->ui_ethtype = HI_OMCI_TAPI_ETHTYPE_INVALID;
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SVLAN_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);

		pst_mark->ui_cvlan = pst_vlanopt->us_filterinvlan;
		pst_mark->ui_ethtype = HI_OMCI_TAPI_ETHTYPE_INVALID;

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CPRI_ETHTYPE_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);

		pst_mark->ui_cpri = pst_vlanopt->uc_filterinpri;
		pst_mark->ui_ethtype = pst_vlanopt->ui_filterethtype;

		break;
	case HI_OMCI_EXVLAN_OPT_1TAG_CPRI_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);

		pst_mark->ui_cpri = pst_vlanopt->uc_filterinpri;
		pst_mark->ui_ethtype = HI_OMCI_TAPI_ETHTYPE_INVALID;

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_0TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		pst_mark->ui_cpri = pst_vlanopt->uc_filterinpri;
		pst_mark->ui_cvlan = pst_vlanopt->us_filterinvlan;
		pst_mark->ui_ethtype = pst_vlanopt->ui_filterethtype;
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_CVLAN_CPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		pst_mark->ui_cpri = pst_vlanopt->uc_filterinpri;
		pst_mark->ui_cvlan = pst_vlanopt->us_filterinvlan;
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_CVLAN_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SVLAN_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		pst_mark->ui_cvlan = pst_vlanopt->us_filterinvlan;
		pst_mark->ui_ethtype = pst_vlanopt->ui_filterethtype;
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		pst_mark->ui_ethtype = pst_vlanopt->ui_filterethtype;
		break;

	default:
		hi_omci_l2_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		break;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

static hi_int32 __omci_opt_vlan_pri_opt_rule(hi_omci_me_exvlan_opt_type_e em_opt,
		hi_omci_vlan_opt_rule_s *pst_vlanopt, hi_uint32 ui_act, hi_uchar8 uc_portid)
{
	hi_omci_tapi_qos_pri_s st_mark;
	hi_int32 i_ret;

	hi_omci_debug("exvlan_opt_type = 0x%08x\n", em_opt);

	HI_OS_MEMSET_S(&st_mark, sizeof(st_mark), 0xff, sizeof(st_mark));

	switch (em_opt) {
	case HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_E:
	case HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_VLAN_PRI_E:
	case HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_VLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		if (pst_vlanopt->uc_treatinpri != HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP) {
			if (HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_INNER == pst_vlanopt->uc_treatinpri) {
				pst_vlanopt->uc_treatinpri = 0;
			}
			i_ret = hi_omci_tapi_defpri_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->uc_treatinpri);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			st_mark.ui_cpri = HI_OMCI_TAPI_PRI_UNTAG;
			st_mark.ui_spri = HI_OMCI_TAPI_PRI_INVALID;
			i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		}

		break;

	case HI_OMCI_EXVLAN_OPT_UNTAG_ETHTYPE_ADD_TAG_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		st_mark.ui_cpri = HI_OMCI_TAPI_PRI_UNTAG;

		if (pst_vlanopt->uc_treatinpri != HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP) {
			st_mark.ui_spri = pst_vlanopt->uc_treatinpri;
		} else {
			st_mark.ui_spri = HI_OMCI_TAPI_PRI_INVALID;
		}

		if (pst_vlanopt->ui_filterethtype == HI_OMCI_FRM_ETHTYPE_PPPOE) {
			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype & 0xffff;
			i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);

			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype >> 16;
			i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype;
			i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		}

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		st_mark.ui_cpri = pst_vlanopt->uc_filterinpri;

	/* 没有break */

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		st_mark.ui_cvlan = pst_vlanopt->us_filterinvlan;
		st_mark.ui_ethtype = (hi_uint32)HI_OMCI_TAPI_ETHTYPE_INVALID;

		if (pst_vlanopt->uc_treatinpri != HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP) {
			st_mark.ui_spri = pst_vlanopt->uc_treatinpri;
		} else {
			st_mark.ui_spri = HI_OMCI_TAPI_PRI_INVALID;
		}

		i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CPRI_ETHTYPE_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		st_mark.ui_cpri = pst_vlanopt->uc_filterinpri;

		if (pst_vlanopt->uc_treatinpri != HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP) {
			st_mark.ui_spri = pst_vlanopt->uc_treatinpri;
		} else {
			st_mark.ui_spri = HI_OMCI_TAPI_PRI_INVALID;
		}

		if (pst_vlanopt->ui_filterethtype == HI_OMCI_FRM_ETHTYPE_PPPOE) {
			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype & 0xffff;
			i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);

			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype >> 16;
			i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype;
			i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		}

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CPRI_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		st_mark.ui_cpri = pst_vlanopt->uc_filterinpri;
		st_mark.ui_ethtype = (hi_uint32)HI_OMCI_TAPI_ETHTYPE_INVALID;

		if (pst_vlanopt->uc_treatinpri != HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP) {
			st_mark.ui_spri = pst_vlanopt->uc_treatinpri;
		} else {
			st_mark.ui_spri = HI_OMCI_TAPI_PRI_INVALID;
		}

		i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		st_mark.ui_cpri = pst_vlanopt->uc_filterinpri;

	/* 没有break */

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		st_mark.ui_cvlan = pst_vlanopt->us_filterinvlan;

	/* 没有break */

	case HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		if (pst_vlanopt->uc_treatinpri != HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP) {
			st_mark.ui_spri = pst_vlanopt->uc_treatinpri;
		} else {
			st_mark.ui_spri = HI_OMCI_TAPI_PRI_INVALID;
		}

		if (pst_vlanopt->ui_filterethtype == HI_OMCI_FRM_ETHTYPE_PPPOE) {
			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype & 0xffff;
			i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);

			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype >> 16;
			i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype;
			i_ret = __omci_pri_marking_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		}

		break;

	default:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		break;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

static hi_int32 __omci_opt_svlan_spri_opt_rule(hi_omci_me_exvlan_opt_type_e em_opt,
		hi_omci_vlan_opt_rule_s *pst_vlanopt, hi_uint32 ui_act, hi_uchar8 uc_portid)
{
	hi_omci_tapi_mark_s st_mark;
	hi_int32 i_ret;
	hi_uint32 ui_flag = (hi_uint32)HI_OMCI_TAPI_INVALID;

	hi_omci_debug("exvlan_opt_type = 0x%08x\n", em_opt);

	HI_OS_MEMSET_S(&st_mark, sizeof(st_mark), 0xff, sizeof(st_mark));

	switch (em_opt) {
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SVLAN_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		ui_flag = pst_vlanopt->uc_filterinpri;
	/* 没有break */

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SVLAN_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		st_mark.ui_cvlan = pst_vlanopt->us_filterinvlan;
		st_mark.ui_cpri = ui_flag;

		st_mark.ui_ethtype = HI_OMCI_TAPI_ETHTYPE_INVALID;
		st_mark.ui_svlan = pst_vlanopt->us_treatinvlan;

		if (pst_vlanopt->uc_treatinpri != HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP) {
			st_mark.ui_spri = pst_vlanopt->uc_treatinpri;
		} else {
			st_mark.ui_spri = HI_OMCI_TAPI_PRI_DSCP;
		}

		i_ret = __omci_qinq_full_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
		HI_OMCI_RET_CHECK(i_ret);

		break;



	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		ui_flag = pst_vlanopt->uc_filterinpri;

	/* 没有break */

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SVLAN_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		st_mark.ui_cvlan = pst_vlanopt->us_filterinvlan;
		st_mark.ui_cpri = ui_flag;

		st_mark.ui_svlan = pst_vlanopt->us_treatinvlan;

		if (pst_vlanopt->uc_treatinpri != HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP) {
			st_mark.ui_spri = pst_vlanopt->uc_treatinpri;
		} else {
			st_mark.ui_spri = HI_OMCI_TAPI_PRI_DSCP;
		}

		if (pst_vlanopt->ui_filterethtype == HI_OMCI_FRM_ETHTYPE_PPPOE) {
			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype & 0xffff;
			i_ret = __omci_qinq_full_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);

			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype >> 16;
			i_ret = __omci_qinq_full_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype;
			i_ret = __omci_qinq_full_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		}

		break;

	case HI_OMCI_EXVLAN_OPT_0TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		st_mark.ui_cpri = pst_vlanopt->uc_filterinpri;
		st_mark.ui_cvlan = pst_vlanopt->us_filterinvlan;

		st_mark.ui_svlan = pst_vlanopt->us_treatinvlan;

		st_mark.ui_spri = (hi_uint32)HI_OMCI_TAPI_PRI_INVALID;

		if (pst_vlanopt->ui_filterethtype == HI_OMCI_FRM_ETHTYPE_PPPOE) {
			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype & 0xffff;
			i_ret = __omci_qinq_full_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);

			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype >> 16;
			i_ret = __omci_qinq_full_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype;
			i_ret = __omci_qinq_full_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
			HI_OMCI_RET_CHECK(i_ret);
		}

		break;

	default:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		break;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*检查是否有eth口透传*/
static hi_uint32 __omci_check_eth_transparent(hi_void)
{
	hi_uint32 i;
	hi_uint32 ui_enable;
	for (i = 0; i <= HI_OMCI_TAPI_ETH7_E; i++) {
		ui_enable = HI_ENABLE;
		hi_omci_tapi_vlan_filter_get((hi_omci_tapi_port_e)i, &ui_enable);
		if (ui_enable == HI_DISABLE) {
			return HI_TRUE;
		}
	}

	return HI_FALSE;
}

static hi_uint32 __omci_eth_transparent_add(hi_uint32 ui_portid)
{
#ifdef CONFIG_TODEL
	hi_hal_ifc_key_s  st_key;
	hi_hal_ifc_act_s  st_act;
	hi_hal_sec_vlan_s st_vlan;
	hi_uint32 ui_ret;
	hi_uint32 ui_vlan;


	if (ui_portid > HI_OMCI_TAPI_ETH7_E) {
		return HI_RET_SUCC;
	}

	if (0 != g_eth_trans_add[ui_portid]) { //已经创建过下面的动作
		return HI_RET_SUCC;
	}
	HI_OS_MEMSET_S(&st_key, sizeof(hi_hal_ifc_key_s), 0, sizeof(hi_hal_ifc_key_s));
	HI_OS_MEMSET_S(&st_act, sizeof(hi_hal_ifc_act_s), 0, sizeof(hi_hal_ifc_act_s));
	st_key.ui_igr.value |= (1 << ui_portid);
	st_key.ui_label = HI_HAL_DEFAULT_FWD_LABEL_E;
	st_key.ui_entry_pri = 6;

	st_act.em_fwd_act_en = HI_HAL_TRUE_E;
	st_act.em_fwd_act = HI_HAL_FWD_TOPOINT_E;
	st_act.ui_egress.bits.pon0 = 1;

	ui_ret =  hi_hal_ifc_set(&st_key, &st_act);
	HI_OMCI_RET_CHECK(ui_ret);

	st_vlan.em_port = (hi_hal_port_e)ui_portid;
	for (ui_vlan = 0; ui_vlan <= 4095; ui_vlan++) {
		//ui_ret = hi_omci_tapi_vlan_set(ui_portid, ui_vlan);
		st_vlan.ui_vlan = ui_vlan;
		ui_ret = hi_hal_sec_vlan_add(&st_vlan);
		HI_OMCI_RET_CHECK(ui_ret);
	}
	ui_ret = hi_omci_tapi_tagact_set((hi_omci_tapi_port_e)ui_portid, HI_OMCI_TAPI_VLAN_TAG_E,
					 HI_OMCI_TAPI_TAG_EGR_E, HI_OMCI_TAPI_TAG_IGNORE_E);
	HI_OMCI_RET_CHECK(ui_ret);

	g_eth_trans_add[ui_portid] = 1;
	g_eth_trans_del[ui_portid] = 0;
#endif
	return HI_RET_SUCC;
}

static hi_uint32 __omci_eth_transparent_del(hi_uint32 ui_portid)
{
#ifdef CONFIG_TODEL
	hi_hal_ifc_key_s  st_key;
	hi_hal_ifc_act_s  st_act;
	hi_hal_sec_vlan_s st_vlan;
	hi_uint32 ui_ret;
	hi_uint32 ui_vlan;

	if (ui_portid > HI_OMCI_TAPI_ETH7_E) {
		return HI_RET_SUCC;
	}

	if (0 != g_eth_trans_del[ui_portid]) { //已经删除过下面的动作
		return HI_RET_SUCC;
	}

	HI_OS_MEMSET_S(&st_key, sizeof(hi_hal_ifc_key_s), 0, sizeof(hi_hal_ifc_key_s));
	HI_OS_MEMSET_S(&st_act, sizeof(hi_hal_ifc_act_s), 0, sizeof(hi_hal_ifc_act_s));
	st_key.ui_igr.value |= (1 << ui_portid);
	st_key.ui_label = HI_HAL_DEFAULT_FWD_LABEL_E;
	st_key.ui_entry_pri = 6;

	st_act.em_fwd_act_en = HI_HAL_TRUE_E;
	st_act.em_fwd_act = HI_HAL_FWD_TOPOINT_E;
	st_act.ui_egress.bits.pon0 = 1;

	ui_ret =  hi_hal_ifc_del(&st_key, &st_act);
	HI_OMCI_RET_CHECK(ui_ret);

	st_vlan.em_port = (hi_hal_port_e)ui_portid;
	for (ui_vlan = 0; ui_vlan <= 4095; ui_vlan++) {
		//ui_ret = hi_omci_tapi_vlan_set(ui_portid, ui_vlan);
		st_vlan.ui_vlan = ui_vlan;
		ui_ret = hi_hal_sec_vlan_del(&st_vlan);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	g_eth_trans_add[ui_portid] = 0;
	g_eth_trans_del[ui_portid] = 1;
#endif
	return HI_RET_SUCC;
}

static hi_uint32 __omci_opt_vlan_opt_rule(hi_omci_me_exvlan_opt_type_e em_opt,
		hi_omci_vlan_opt_rule_s *pst_vlanopt, hi_uint32 ui_act, hi_uchar8 uc_portid)
{
	hi_omci_tapi_tpid_type_e em_tpid;
	hi_omci_tapi_mark_s      st_mark;
	hi_omci_tapi_sysinfo_s st_info;
	omci_extvlantag_s *pst_extvlan = HI_NULL;
	hi_omci_me_exvlan_opt_type_e em_opt_temp;
	//hi_uint32 ui_vlan;
	hi_int32 i_ret;
	hi_uint32 ui_flag = HI_TRUE;
	hi_uint32 ui_vlantag_index = 0;

	hi_omci_debug("exvlan_opt_type = 0x%08x\n", em_opt);

	HI_OS_MEMSET_S(&st_mark, sizeof(hi_omci_tapi_mark_s), 0xff, sizeof(hi_omci_tapi_mark_s));

	/* eth入端口tag报文不处理*/
	i_ret = hi_omci_tapi_tagact_set(uc_portid, HI_OMCI_TAPI_VLAN_TAG_E,
					HI_OMCI_TAPI_TAG_IGR_E, HI_OMCI_TAPI_TAG_IGNORE_E);
	HI_OMCI_RET_CHECK(i_ret);

	switch (em_opt) {
	case HI_OMCI_EXVLAN_OPT_TRANSPARENT_E:
	case HI_OMCI_EXVLAN_OPT_UNTAG_TRANSPARENT_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		i_ret = hi_omci_tapi_sysinfo_get(&st_info);
		HI_OMCI_RET_CHECK(i_ret);
		if (HI_TRUE == ui_act) {
			hi_omci_tapi_vlan_filter_set(HI_OMCI_TAPI_PON_E, HI_DISABLE);
			/* pon口入口untag报文不处理 */
			i_ret = hi_omci_tapi_tagact_set(HI_OMCI_TAPI_PON_E, HI_OMCI_TAPI_UNTAG_E,
							HI_OMCI_TAPI_TAG_IGR_E, HI_OMCI_TAPI_TAG_IGNORE_E);
			HI_OMCI_RET_CHECK(i_ret);

			if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E != st_info.ui_product_mode) {
				hi_omci_tapi_vlan_filter_set((hi_omci_tapi_port_e)uc_portid, HI_DISABLE);
				i_ret = __omci_eth_transparent_add(uc_portid);
				HI_OMCI_RET_CHECK(i_ret);
			}
		} else {
			if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E != st_info.ui_product_mode) {
				hi_omci_tapi_vlan_filter_set((hi_omci_tapi_port_e)uc_portid, HI_ENABLE);
				i_ret = __omci_eth_transparent_del(uc_portid);
				HI_OMCI_RET_CHECK(i_ret);
			}
			/*如果eth口中，有一个以上是设置为透传模式，则pon口应该保持为透传模式*/
			if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_SFU_E == st_info.ui_product_mode) {
				ui_flag = __omci_check_eth_transparent();
			} else if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
				/*VEIP模式下面直接配置*/
				ui_flag = HI_FALSE;
			}

			if (ui_flag == HI_FALSE) {
				hi_omci_tapi_vlan_filter_set(HI_OMCI_TAPI_PON_E, HI_ENABLE);
				/* pon口入口untag报文丢弃*/
				i_ret = hi_omci_tapi_tagact_set(HI_OMCI_TAPI_PON_E, HI_OMCI_TAPI_UNTAG_E,
								HI_OMCI_TAPI_TAG_IGR_E, HI_OMCI_TAPI_TAG_DISC_E);
				HI_OMCI_RET_CHECK(i_ret);
			}
		}
		break;

	case HI_OMCI_EXVLAN_OPT_UNTAG_DISC_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = hi_omci_tapi_sysinfo_get(&st_info);
		HI_OMCI_RET_CHECK(i_ret);
		if (st_info.ui_product_mode == HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E) {
			break;
		}

		if (ui_act == HI_TRUE) {
			i_ret = hi_omci_tapi_tagact_set((hi_omci_tapi_port_e)uc_portid, HI_OMCI_TAPI_UNTAG_E,
							HI_OMCI_TAPI_TAG_IGR_E, HI_OMCI_TAPI_TAG_DISC_E);
			HI_OMCI_RET_CHECK(i_ret);
		}

		break;

	case HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_E:
	case HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_VLAN_PRI_E:
	case HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_VLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = hi_omci_tapi_sysinfo_get(&st_info);
		HI_OMCI_RET_CHECK(i_ret);
		if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) {
			break;
		}

		if (ui_act == HI_TRUE) {
			i_ret = hi_omci_tapi_tagact_set((hi_omci_tapi_port_e)uc_portid, HI_OMCI_TAPI_UNTAG_E,
							HI_OMCI_TAPI_TAG_IGR_E, HI_OMCI_TAPI_TAG_ADD_DEFAULT_E);
			HI_OMCI_RET_CHECK(i_ret);

			i_ret = hi_omci_tapi_tagact_set((hi_omci_tapi_port_e)uc_portid, HI_OMCI_TAPI_VLAN_TAG_E,
							HI_OMCI_TAPI_TAG_EGR_E, HI_OMCI_TAPI_TAG_DEL_DEFAULT_E);
			HI_OMCI_RET_CHECK(i_ret);

			/* 如果没有配置过port tag，则添加如端口tag报文丢弃 */
			if (HI_NULL != g_pst_vlantagtable) {
				pst_extvlan = g_pst_vlantagtable;
				hi_omci_debug("g_ui_vlantagtable_num = %u\n", g_ui_vlantagtable_num);
				for (ui_vlantag_index = 0; ui_vlantag_index < g_ui_vlantagtable_num; ui_vlantag_index++) {
					__omci_me_exvlan_actiontype((omci_extvlantag_s *)(pst_extvlan + ui_vlantag_index), &em_opt_temp);
					hi_omci_debug("em_opt_temp = 0x%08x\n", em_opt_temp);
					if (HI_OMCI_EXVLAN_OPT_1TAG_MOD_CVLAN_E == em_opt_temp) {
						break;
					}
				}

				if (ui_vlantag_index == g_ui_vlantagtable_num) {
					/* eth入端口tag报文丢弃*/
					i_ret = hi_omci_tapi_tagact_set(uc_portid, HI_OMCI_TAPI_VLAN_TAG_E,
									HI_OMCI_TAPI_TAG_IGR_E, HI_OMCI_TAPI_TAG_DISC_E);
					HI_OMCI_RET_CHECK(i_ret);
				}
			}
			/* pon口入口untag报文丢弃*/
			i_ret = hi_omci_tapi_tagact_set(HI_OMCI_TAPI_PON_E, HI_OMCI_TAPI_UNTAG_E,
							HI_OMCI_TAPI_TAG_IGR_E, HI_OMCI_TAPI_TAG_DISC_E);
			HI_OMCI_RET_CHECK(i_ret);

			if (HI_OMCI_TREATMENT_TAG_ADD_TPID_8100 == pst_vlanopt->uc_treatintpid
			    || HI_OMCI_TREATMENT_TAG_ADD_TPID_CPOY_INNER == pst_vlanopt->uc_treatintpid) {
				em_tpid = HI_OMCI_TAPI_TPID_DEFAULT_E;
			} else {
				em_tpid = HI_OMCI_TAPI_TPID_INPUT_E;
			}

			i_ret = hi_omci_tapi_vlan_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->us_treatinvlan);
			HI_OMCI_RET_CHECK(i_ret);

			i_ret = hi_omci_tapi_deftag_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->us_treatinvlan, em_tpid);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			//i_ret = hi_omci_tapi_deftag_get(uc_portid, &ui_vlan, &em_tpid);
			//HI_OMCI_RET_CHECK(i_ret);

			i_ret = hi_omci_tapi_vlan_del((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->us_treatinvlan);
			HI_OMCI_RET_CHECK(i_ret);

			/* eth入端口tag报文不处理*/
			i_ret = hi_omci_tapi_tagact_set(uc_portid, HI_OMCI_TAPI_VLAN_TAG_E,
							HI_OMCI_TAPI_TAG_IGR_E, HI_OMCI_TAPI_TAG_IGNORE_E);
			HI_OMCI_RET_CHECK(i_ret);

			i_ret = hi_omci_tapi_tagact_set((hi_omci_tapi_port_e)uc_portid, HI_OMCI_TAPI_UNTAG_E,
							HI_OMCI_TAPI_TAG_IGR_E, HI_OMCI_TAPI_TAG_IGNORE_E);
			HI_OMCI_RET_CHECK(i_ret);
		}

		i_ret = __omci_opt_vlan_pri_opt_rule(em_opt, pst_vlanopt, ui_act, uc_portid);
		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_UNTAG_ETHTYPE_ADD_TAG_E:
		if (ui_act == HI_TRUE) {
			i_ret = __omci_opt_get_mask(em_opt,  pst_vlanopt, &st_mark);
			HI_OMCI_RET_CHECK(i_ret);

			st_mark.ui_svlan = pst_vlanopt->us_treatinvlan;

			if (pst_vlanopt->ui_filterethtype == HI_OMCI_FRM_ETHTYPE_PPPOE) {
				st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype & 0xffff;

				i_ret = hi_omci_tapi_untag_addtag_set((hi_omci_tapi_port_e)uc_portid, &st_mark);
				HI_OMCI_RET_CHECK(i_ret);

				st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype >> 16;
				i_ret = hi_omci_tapi_untag_addtag_set((hi_omci_tapi_port_e)uc_portid, &st_mark);
				HI_OMCI_RET_CHECK(i_ret);
			} else {
				i_ret = hi_omci_tapi_untag_addtag_set((hi_omci_tapi_port_e)uc_portid, &st_mark);
				HI_OMCI_RET_CHECK(i_ret);
			}
		} else {
			i_ret = __omci_opt_get_mask(em_opt,  pst_vlanopt, &st_mark);
			HI_OMCI_RET_CHECK(i_ret);

			if (pst_vlanopt->ui_filterethtype == HI_OMCI_FRM_ETHTYPE_PPPOE) {
				st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype & 0xffff;

				i_ret = hi_omci_tapi_untag_addtag_del((hi_omci_tapi_port_e)uc_portid, &st_mark);
				HI_OMCI_RET_CHECK(i_ret);

				st_mark.ui_ethtype = pst_vlanopt->ui_filterethtype >> 16;
				i_ret = hi_omci_tapi_untag_addtag_del((hi_omci_tapi_port_e)uc_portid, &st_mark);
				HI_OMCI_RET_CHECK(i_ret);
			} else {
				i_ret = hi_omci_tapi_untag_addtag_del((hi_omci_tapi_port_e)uc_portid, &st_mark);
				HI_OMCI_RET_CHECK(i_ret);
			}
		}

		i_ret = __omci_opt_vlan_pri_opt_rule(em_opt, pst_vlanopt, ui_act, uc_portid);
		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_TRANSPARENT_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = __omci_vlan_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->us_filterinvlan, ui_act);
		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_MOD_SVLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = __omci_vlan_translation_set((hi_omci_tapi_port_e)uc_portid,
						    pst_vlanopt->us_filterinvlan, pst_vlanopt->us_treatinvlan, HI_TRUE, ui_act);
		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_MOD_CVLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		if (pst_vlanopt->us_filterinvlan == pst_vlanopt->us_treatinvlan) {
			i_ret = __omci_vlan_port_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->us_filterinvlan, ui_act);
		} else {
			i_ret = __omci_vlan_translation_set((hi_omci_tapi_port_e)uc_portid,
							    pst_vlanopt->us_filterinvlan, pst_vlanopt->us_treatinvlan, HI_FALSE, ui_act);
		}

		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_CVLAN_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_CVLAN_CPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = __omci_opt_get_mask(em_opt,  pst_vlanopt, &st_mark);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = __omci_vlan_translation_opt_set(pst_vlanopt, &st_mark, (hi_omci_tapi_port_e)uc_portid, HI_FALSE, ui_act);
		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_1STAG_MOD_SVLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = __omci_tls_stag_set((hi_omci_tapi_port_e)uc_portid,
					    pst_vlanopt->us_filterinvlan, pst_vlanopt->us_treatinvlan, ui_act);
		HI_OMCI_RET_CHECK(i_ret);
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CPRI_MOD_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CPRI_ETHTYPE_MOD_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = __omci_opt_vlan_pri_opt_rule(em_opt, pst_vlanopt, ui_act, uc_portid);
		HI_OMCI_RET_CHECK(i_ret);
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SVLAN_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SVLAN_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SVLAN_SPRI_E:
	case HI_OMCI_EXVLAN_OPT_0TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_E:
	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_SPRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = __omci_opt_svlan_spri_opt_rule(em_opt, pst_vlanopt, ui_act, uc_portid);
		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_ADD_TAG_VLAN_PRI_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		i_ret = __omci_port_qinq_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->uc_treatinpri,
					     pst_vlanopt->us_treatinvlan, HI_FALSE, ui_act);
		HI_OMCI_RET_CHECK(i_ret);
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_ADD_TAG_VLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		i_ret = __omci_port_qinq_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->uc_treatinpri,
					     pst_vlanopt->us_treatinvlan, HI_FALSE, ui_act);
		HI_OMCI_RET_CHECK(i_ret);
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_ADD_CVLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = __omci_qinq_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->us_filterinvlan,
					pst_vlanopt->us_treatinvlan, HI_FALSE, ui_act);
		HI_OMCI_RET_CHECK(i_ret);
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_ADD_SVLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = __omci_qinq_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->us_filterinvlan,
					pst_vlanopt->us_treatinvlan, HI_TRUE, ui_act);
		HI_OMCI_RET_CHECK(i_ret);
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ADD_SVLAN_E:
		st_mark.ui_cvlan = pst_vlanopt->us_filterinvlan;
		st_mark.ui_cpri  = pst_vlanopt->uc_filterinpri;

		st_mark.ui_svlan = pst_vlanopt->us_treatinvlan;

		i_ret = __omci_qinq_vlan_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
		HI_OMCI_RET_CHECK(i_ret);
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ADD_SVLAN_SPRI_E:
		st_mark.ui_cvlan = pst_vlanopt->us_filterinvlan;
		st_mark.ui_cpri  = pst_vlanopt->uc_filterinpri;

		st_mark.ui_svlan = pst_vlanopt->us_treatinvlan;
		st_mark.ui_spri = pst_vlanopt->uc_treatinpri;

		i_ret = __omci_qinq_vlan_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
		HI_OMCI_RET_CHECK(i_ret);
		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ADD_SVLAN_SPRI_E:
		st_mark.ui_cvlan = pst_vlanopt->us_filterinvlan;

		st_mark.ui_svlan = pst_vlanopt->us_treatinvlan;
		st_mark.ui_spri  = pst_vlanopt->uc_treatinpri;

		i_ret = __omci_qinq_vlan_set((hi_omci_tapi_port_e)uc_portid, &st_mark, ui_act);
		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_DEFAULT_ADD_SVLAN_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = __omci_tls_ctag_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->us_treatinvlan, ui_act);
		HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_EXVLAN_OPT_1TAG_DEFAULT_DISC_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		/* 无配置 */

		break;

	case HI_OMCI_EXVLAN_OPT_2TAG_TRANSPARENT_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		i_ret = __omci_tls_stag_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->us_filteroutvlan,
					    (hi_uint32)HI_OMCI_TAPI_VLAN_INVALID, ui_act);
		HI_OMCI_RET_CHECK(i_ret);

		break;
	case HI_OMCI_EXVLAN_OPT_2TAG_MOD_SVLAN_E:

		i_ret = __omci_tls_stag_set((hi_omci_tapi_port_e)uc_portid, pst_vlanopt->us_filteroutvlan, pst_vlanopt->us_treatinvlan,
					    ui_act);
		HI_OMCI_RET_CHECK(i_ret);

		break;
	case HI_OMCI_EXVLAN_OPT_2TAG_DEAFULT_DISC_E:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);

		/* 无配置 */

		break;

	default:
		hi_omci_l2_systrace(HI_RET_SUCC, ui_act, uc_portid, 0, 0);
		break;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

static hi_uint32 __omci_me_exvlan_entrycreate(omci_extvlantag_s *pnewentry, hi_uchar8 uc_portid)
{
	omci_extvlantag_s *pst_extvlan = pnewentry;
	hi_omci_me_exvlan_opt_type_e em_opt;
	hi_omci_vlan_opt_rule_s st_vlanopt;
	hi_uint32 ui_ret = HI_RET_SUCC;


	hi_omci_debug("INFO:filt_invid(%u),filt_inpri(%u),  filt_outvid(%u), filt_outpri(%u) \n",
		      pst_extvlan->ui_filt_in_vid, pst_extvlan->ui_filt_in_pri,
		      pst_extvlan->ui_filt_outer_vid, pst_extvlan->ui_filt_outer_pri);
	hi_omci_debug("INFO:treat_invid(%u),treat_inpri(%u),treat_outvid(%u),treat_outpri(%u) \n",
		      pst_extvlan->ui_treat_inner_vid, pst_extvlan->ui_treat_inner_pri,
		      pst_extvlan->ui_treat_outer_vid, pst_extvlan->ui_treat_outer_pri);
	hi_omci_debug("INFO:ui_filt_ethertype(%u)\n",
		      pst_extvlan->ui_filt_ethertype);

	__omci_me_exvlan_actiontype(pst_extvlan, &em_opt);

	__omci_get_vlan_opt_rule(pst_extvlan, &st_vlanopt);

	ui_ret = __omci_opt_vlan_opt_rule(em_opt, &st_vlanopt, HI_TRUE, uc_portid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

static hi_uint32 __omci_me_exvlan_entrydel(omci_extvlantag_s *pnewentry, hi_uchar8 uc_portid)
{
	hi_uint32 ui_ret = HI_RET_SUCC;
	omci_extvlantag_s *pst_extvlan = pnewentry;
	hi_omci_me_exvlan_opt_type_e em_opt;
	hi_omci_vlan_opt_rule_s st_vlanopt;

	hi_omci_debug("INFO:treat_invid(%u),treat_inpri(%u)\r\n",
		      pst_extvlan->ui_treat_inner_vid, pst_extvlan->ui_treat_inner_pri);
	hi_omci_debug("INFO:treat_outvid(%u),treat_outpri(%u)\r\n",
		      pst_extvlan->ui_treat_outer_vid, pst_extvlan->ui_treat_outer_pri);

	__omci_me_exvlan_actiontype(pst_extvlan, &em_opt);

	__omci_get_vlan_opt_rule(pst_extvlan, &st_vlanopt);


	ui_ret = __omci_opt_vlan_opt_rule(em_opt, &st_vlanopt, HI_FALSE, uc_portid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/*****************************************************************************
 函 数 名  : __omci_me_exvlan_synentry
 功能描述  : Synchronize an Entry to VLAN table   (maybe action is Add/Delete/Modify)
 输入参数  : hi_uchar8* pnewtable,
                void *ptable,
                hi_ushort16 us_mepointer
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
static hi_uint32 __omci_me_exvlan_synentry(omci_extvlantag_s *pnewentry,
		omci_extvlantag_s *ptable, hi_uint32 ui_tabnum, hi_uchar8 uc_portid)
{
	hi_uint32 ui_ret = HI_RET_SUCC;
	hi_uint32 ui_temp;
	omci_extvlantag_s *puc_oldentry;
	hi_omci_oper_type_e uc_opertype = HI_OMCI_ADD;
	omci_extvlantag_s  *pst_vlantagtable = ptable;
	hi_uchar8 uc_emptytable[HI_OMCI_EXVLAN_8_BYTE] = {0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF};

	/* Comparing the table, decide the entry action(ADD/DELETE/SET) */
	for (ui_temp = 0; ui_temp < ui_tabnum; ui_temp++) {
		//比较表属性前8字节
		if (HI_OMCI_ZERO == hi_os_memcmp((void *)(pst_vlantagtable + ui_temp), pnewentry, HI_OMCI_EXVLAN_8_BYTE)) {
			break;
		}
	}

	if (ui_tabnum <= ui_temp) {
		uc_opertype = HI_OMCI_ADD;
	} else {
		//比较表属性后8字节
		if (HI_OMCI_ZERO == hi_os_memcmp(((hi_uchar8 *)pnewentry + HI_OMCI_EXVLAN_8_BYTE), uc_emptytable,
						 HI_OMCI_EXVLAN_8_BYTE)) {
			uc_opertype = HI_OMCI_DELETE;
		} else {
			uc_opertype = HI_OMCI_SET;
		}
	}
	hi_omci_debug("[INFO]:uc_opertype(%d)(1:Add; 2:Delete; 3:Set)!\n", uc_opertype);

	/*Synchronize new entry to table*/
	switch (uc_opertype) {
	case HI_OMCI_ADD:
	default:
		HI_OS_MEMSET_S(uc_emptytable, HI_OMCI_EXVLAN_8_BYTE, 0, HI_OMCI_EXVLAN_8_BYTE);

		/*find the first empty entry label in table*/
		for (ui_temp = 0; ui_temp < ui_tabnum; ui_temp++) {
			if (HI_OMCI_ZERO == hi_os_memcmp((void *)(pst_vlantagtable + ui_temp), (void *)uc_emptytable, HI_OMCI_EXVLAN_8_BYTE)) {
				break;
			}
		}

		if (ui_tabnum <= ui_temp) {
			hi_omci_debug("[ERROR]Table has been full in MIB, Failed to Add a new Entry");
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		/* 获取vlan tag table 指针 */
		g_pst_vlantagtable = ptable;
		g_ui_vlantagtable_num = ui_tabnum;

		ui_ret = __omci_me_exvlan_entrycreate(pnewentry, uc_portid);
		if (HI_RET_SUCC == ui_ret) {
			HI_OS_MEMCPY_S(pst_vlantagtable + ui_temp, sizeof(*pst_vlantagtable), pnewentry, HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE);
		}

		g_st_op_item.ui_port = uc_portid;
		HI_OS_MEMCPY_S(&g_st_op_item.st_vlantag, sizeof(g_st_op_item.st_vlantag), pnewentry,
			       HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE);

		/* 清除vlan tag tables 指针 */
		g_pst_vlantagtable = HI_NULL;
		g_ui_vlantagtable_num = 0;

		break;

	case HI_OMCI_SET:
		puc_oldentry = pst_vlantagtable + ui_temp;

		/* 先删除再添加,省掉modify操作 */

		ui_ret = __omci_me_exvlan_entrydel(puc_oldentry, uc_portid);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_debug("[ERROR]:Del Old Entry From Tbl Failed!");
			return ui_ret;
		}

		/* 获取vlan tag table 指针 */
		g_pst_vlantagtable = ptable;
		g_ui_vlantagtable_num = ui_tabnum;

		ui_ret = __omci_me_exvlan_entrycreate(pnewentry, uc_portid);
		if (HI_RET_SUCC == ui_ret) {
			HI_OS_MEMCPY_S(pst_vlantagtable + ui_temp, sizeof(*pst_vlantagtable), pnewentry, HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE);

			g_st_op_item.ui_port = uc_portid;
			HI_OS_MEMCPY_S(&g_st_op_item.st_vlantag, sizeof(g_st_op_item.st_vlantag), pnewentry,
				       HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE);
		} else {
			/* 如果添加失败，则要对上面已删除的记录进行回滚恢复 */
			ui_ret = __omci_me_exvlan_entrycreate(puc_oldentry, uc_portid);
			if (HI_RET_SUCC != ui_ret) {
				hi_omci_debug("[ERROR]: Rollback create Old Entry From Tbl Failed!");
				return ui_ret;
			}
		}

		/* 清除vlan tag tables 指针 */
		g_pst_vlantagtable = HI_NULL;
		g_ui_vlantagtable_num = 0;

		break;

	case HI_OMCI_DELETE:

		/* DELETE动作需要使用old entry来匹配动作 */
		puc_oldentry = pst_vlantagtable + ui_temp;

		ui_ret = __omci_me_exvlan_entrydel(puc_oldentry, uc_portid);
		if (HI_RET_SUCC == ui_ret) {
			HI_OS_MEMSET_S((pst_vlantagtable + ui_temp), HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE, 0, HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE);
		}
		break;

	}

	return ui_ret;
}

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/*****************************************************************************
 函 数 名  : hi_omci_me_exvlantag_set
 功能描述  : Extended VLAN tagging operation configuration data set
             动作实现在配置数据库前
 输入参数  : hi_uint32 ui_cmdtype
             hi_void*pv_data :
             hi_uint32 ui_in
             hi_uint32 *pui_outlen
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_me_exvlantag_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 ui_ret = HI_RET_SUCC;
	/*TODO : you need to add code here*/

	hi_omci_me_extvlantag_op_s *pst = (hi_omci_me_extvlantag_op_s *)pv_data;
	hi_omci_me_extvlantag_op_s st_extvlantag;
	omci_extvlantag_s st_srcvlantagtable;
	omci_extvlantag_s *pst_mibtable;
	hi_omci_me_exvlan_dscp_map_st st_dscp_map;
	hi_omci_tapi_dscp_map_s st_dscp;
	hi_omci_tapi_sysinfo_s st_info;
	hi_omci_tapi_voip_iphost_s st_iphost;
	hi_omci_me_mac_port_cfg_s st_portcfg;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_size;
	hi_uint32 ui_index = 0;
	hi_ushort16  us_instid, us_macport_instid = 0;
	hi_ushort16  us_mask;
	hi_ushort16  us_assocme_ptr;
	hi_uchar8 *puc_pos = HI_NULL;
	hi_uchar8 *puc_data = HI_NULL;
	hi_uchar8    uc_portid;
	hi_uchar8    uc_count;

	HI_OS_MEMSET_S(&st_extvlantag, sizeof(hi_omci_me_extvlantag_op_s), 0, sizeof(hi_omci_me_extvlantag_op_s));
	HI_OS_MEMSET_S(&st_portcfg, sizeof(st_portcfg), 0, sizeof(st_portcfg));

	/* get the entity ID and instance ID */
	us_instid = pst->st_msghead.us_instid;
	us_mask = pst->st_msghead.us_attmask;

	hi_omci_debug("[INFO]:us_instid=0x%x,us_mask=0x%x\n", us_instid, us_mask);
	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_instid, &st_extvlantag);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}
	/*iphost做特别处理，支持voip wan的配置*/
	if ((st_extvlantag.uc_type == HI_OMCI_ASSOCIATE_TYPE_MAC_BRG_PORT_CFG_DATA) ||
	    (st_extvlantag.uc_type == HI_OMCI_ASSOCIATE_TYPE_VEIP)) {
		if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR6)) {
			if (st_extvlantag.uc_type == HI_OMCI_ASSOCIATE_TYPE_MAC_BRG_PORT_CFG_DATA) {
				ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, st_extvlantag.us_assocme_ptr, &st_portcfg);
				if (HI_RET_SUCC != ui_ret) {
					hi_omci_systrace(ui_ret, 0, 0, 0, 0);
					return ui_ret;
				}
				for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
					if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == st_portcfg.us_tp_ptr) {
						break;
					}
				}
			} else if (st_extvlantag.uc_type == HI_OMCI_ASSOCIATE_TYPE_VEIP) {
				ui_ret = hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR4, &st_extvlantag.us_assocme_ptr,
								       &us_macport_instid);
				if (HI_RET_SUCC != ui_ret) {
					hi_omci_systrace(ui_ret, 0, 0, 0, 0);
					return ui_ret;
				}
				for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
					if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == us_macport_instid) {
						break;
					}
				}
			}

			if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM == ui_index) {
				return HI_RET_SUCC;
			}
			HI_OS_MEMSET_S(&st_srcvlantagtable, sizeof(omci_extvlantag_s), 0, sizeof(omci_extvlantag_s));

			puc_pos = (hi_uchar8 *)&pst->st_vlantagtable;
			puc_data = (hi_uchar8 *)&st_srcvlantagtable;
			for (uc_count = 0; uc_count < 4; uc_count++) {
				*(hi_uint32 *)puc_data = ntohl(*(hi_uint32 *)puc_pos);
				puc_pos += sizeof(hi_uint32);
				puc_data += sizeof(hi_uint32);
			}

			g_st_extvlan_table.st_extvlan[ui_index].us_vlan = st_srcvlantagtable.ui_treat_inner_vid;
			if ((st_srcvlantagtable.ui_treat_outer_vid > 0) && (st_srcvlantagtable.ui_treat_outer_vid < 0x1000)) {
				g_st_extvlan_table.st_extvlan[ui_index].us_vlan = st_srcvlantagtable.ui_treat_outer_vid;
			}
			g_st_extvlan_table.st_extvlan[ui_index].us_pri = st_srcvlantagtable.ui_treat_inner_pri;

			/*对于还没建立wan模型的，由SIP agent config data来创建voip wan*/
			if (0 == g_st_extvlan_table.st_extvlan[ui_index].ui_mode) {
				hi_omci_debug("[INFO]:mode=%u\n", g_st_extvlan_table.st_extvlan[ui_index].ui_mode);
				return HI_RET_SUCC;
			}

			/*对于已经建立wan模型的，由记录模式创建voip wan,用于wan模型参数修改*/
			if (0 != gui_omci_voip_flag) {
				if (0x1fff != st_srcvlantagtable.ui_treat_inner_vid) {
					HI_OS_MEMSET_S(&st_iphost, sizeof(st_iphost), 0, sizeof(st_iphost));
					hi_omci_me_set_iphost_attr(&st_iphost, g_st_extvlan_table.st_extvlan[ui_index].ui_index);
					if (1 == g_st_extvlan_table.st_extvlan[ui_index].ui_mode) {
						st_iphost.us_flag = 0;
					} else {
						st_iphost.us_flag = 1;
					}
					if ((st_srcvlantagtable.ui_treat_inner_vid > 0) && (st_srcvlantagtable.ui_treat_inner_vid < 0x1000)) {
						st_iphost.ui_vlan = st_srcvlantagtable.ui_treat_inner_vid;
						st_iphost.ui_vlanen = 1;
					}

					if ((st_srcvlantagtable.ui_treat_outer_vid > 0) && (st_srcvlantagtable.ui_treat_outer_vid < 0x1000)) {
						st_iphost.ui_vlan = st_srcvlantagtable.ui_treat_outer_vid;
						st_iphost.ui_vlanen = 1;
					}
					st_iphost.us_8021p = st_srcvlantagtable.ui_treat_inner_pri;
					st_iphost.us_8021pen = 1;
					ui_ret = hi_omci_tapi_voip_wan_set(&st_iphost);
					HI_OMCI_RET_CHECK(ui_ret);
				} else {
					/*vlan不存在，删除voip wan*/
					ui_ret = hi_omci_tapi_voip_wan_del(1);
					HI_OMCI_RET_CHECK(ui_ret);
				}
			}
		}
#if 0
		ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_instid, &st_portcfg);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}
#if 0
		if (HI_MAC_BRI_PORT_IP_E != st_portcfg.uc_tptype) {
			hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
			return HI_RET_SUCC;
		}
#endif
		if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR6)) {
			HI_OS_MEMSET_S(&st_srcvlantagtable, sizeof(omci_extvlantag_s), 0, sizeof(omci_extvlantag_s));

			puc_pos = (hi_uchar8 *)&pst->st_vlantagtable;
			puc_data = (hi_uchar8 *)&st_srcvlantagtable;
			for (uc_count = 0; uc_count < 4; uc_count++) {
				*(hi_uint32 *)puc_data = ntohl(*(hi_uint32 *)puc_pos);
				puc_pos += sizeof(hi_uint32);
				puc_data += sizeof(hi_uint32);
			}

			if (0 != gui_omci_voip_flag) {
				if (0x1fff != st_srcvlantagtable.ui_treat_inner_vid) {
					HI_OS_MEMSET_S(&st_iphost, sizeof(st_iphost), 0, sizeof(st_iphost));
					st_iphost.ui_vlan = st_srcvlantagtable.ui_treat_inner_vid;
					st_iphost.ui_vlanen = 1;
					ui_ret = hi_omci_tapi_voip_wan_set(&st_iphost);
					HI_OMCI_RET_CHECK(ui_ret);
				} else {
					/*vlan不存在，删除voip wan*/
					ui_ret = hi_omci_tapi_voip_wan_del();
					HI_OMCI_RET_CHECK(ui_ret);
				}

				if (0xF != st_srcvlantagtable.ui_treat_inner_pri) {
					HI_OS_MEMSET_S(&st_iphost, sizeof(st_iphost), 0, sizeof(st_iphost));
					st_iphost.us_8021p = st_srcvlantagtable.ui_treat_inner_pri;
					st_iphost.us_8021pen = 1;
					ui_ret = hi_omci_tapi_voip_wan_set(&st_iphost);
					HI_OMCI_RET_CHECK(ui_ret);
				} else {
					HI_OS_MEMSET_S(&st_iphost, sizeof(st_iphost), 0, sizeof(st_iphost));
					st_iphost.us_8021pen = 0;
					ui_ret = hi_omci_tapi_voip_wan_set(&st_iphost);
					HI_OMCI_RET_CHECK(ui_ret);
				}
			} else {
				gui_omci_extvlan_mask += (st_srcvlantagtable.ui_treat_inner_pri << 16);
				gui_omci_extvlan_mask += st_srcvlantagtable.ui_treat_inner_vid;
			}
		}
#endif
		return HI_RET_SUCC;
	}
	/* 不支持非PPTP UNI/VEIP端口的extended vlan opt */
	if (st_extvlantag.uc_type != HI_OMCI_ASSOCIATE_TYPE_PPTP_ETH_UNI &&
	    st_extvlantag.uc_type != HI_OMCI_ASSOCIATE_TYPE_VEIP) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(ui_ret);

	us_assocme_ptr = st_extvlantag.us_assocme_ptr;

	hi_omci_debug("[INFO]:port=[0x%x],intpid=[0x%x],outtpid=[0x%x],dnmode=[%hhu],type=[%hhu]\n",
		      us_assocme_ptr, st_extvlantag.us_intpid, st_extvlantag.us_outtpid, st_extvlantag.uc_dnmode, st_extvlantag.uc_type);

	/* 设置Input TPID属性 */
	if (HI_OMCI_ME_EXVLAN_IS_INPUT_TPID(us_mask)) {
		ui_ret = hi_omci_tapi_tpid_set(HI_OMCI_TAPI_TPID_INPUT_E, pst->us_intpid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	/* 设置Output TPID属性 */
	if (HI_OMCI_ME_EXVLAN_IS_OUTPUT_TPID(us_mask)) {
		ui_ret = hi_omci_tapi_tpid_set(HI_OMCI_TAPI_TPID_OUTPUT_E, pst->us_outtpid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	/* 设置Received frame VLAN tagging operation table 属性 */
	if (HI_OMCI_ME_EXVLAN_IS_TABLE(us_mask)) {
		HI_OS_MEMSET_S(&st_srcvlantagtable, sizeof(omci_extvlantag_s), 0, sizeof(omci_extvlantag_s));

		puc_pos = (hi_uchar8 *)&pst->st_vlantagtable;
		puc_data = (hi_uchar8 *)&st_srcvlantagtable;
		for (uc_count = 0; uc_count < 4; uc_count++) {
			*(hi_uint32 *)puc_data = ntohl(*(hi_uint32 *)puc_pos);
			puc_pos += sizeof(hi_uint32);
			puc_data += sizeof(hi_uint32);
		}

		/* 获取Received frame VLAN tagging operation table深度 */
		ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, HI_OMCI_ATTR6, &ui_tabnum);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_size = sizeof(omci_extvlantag_s) * ui_tabnum;

		pst_mibtable = (omci_extvlantag_s *)hi_os_malloc(ui_size);
		if (HI_NULL == pst_mibtable) {
			hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		HI_OS_MEMSET_S(pst_mibtable, ui_size, 0, ui_size);

		/* 读出数据库保存的表项 */
		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_instid, HI_OMCI_ATTR6, pst_mibtable);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_mibtable);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		__omci_me_exvlantag_table_printf((hi_uchar8 *)pst_mibtable, ui_size);

		/* get physical port number */
		if (HI_OMCI_ASSOCIATE_TYPE_VEIP == st_extvlantag.uc_type) {
			uc_portid =  HI_OMCI_TAPI_VEIP_E;
		} else {
			uc_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_assocme_ptr);
			if (1 == st_info.ui_port_num && HI_OMCI_ME_ONU_CAPABILITY_TYPE_SFU_E == st_info.ui_product_mode) { //单口sfu
				uc_portid = 0;//规避格林伟迪局端下发port=1
			}
		}

		/*proc function*/
		ui_ret = __omci_me_exvlan_synentry(&st_srcvlantagtable, pst_mibtable, ui_tabnum, uc_portid);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_mibtable);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			hi_omci_debug("[ERROR]Synchronize Entry to Table Failed!\n");
			return ui_ret;
		}

		__omci_me_exvlantag_table_printf((hi_uchar8 *)pst_mibtable, ui_size);

		/* 将新表项保存到数据库 */
		ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_instid, HI_OMCI_ATTR6, pst_mibtable);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_free(pst_mibtable);
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		hi_os_free(pst_mibtable);
	}

	/* 设置DSCP to P-bit mapping（OPT）属性
	 * 以比特码流形式下发，DSCP0在前，高比特先发送*/
	if (HI_OMCI_ME_EXVLAN_IS_PBIT_MAP(us_mask)) {
		/* DSCP映射关系网络序转换 */
		for (ui_index = 0; ui_index < HI_OMCI_DSCP_2_P_LEN; ui_index++) {
			st_dscp_map.dscp2pri[ui_index] = pst->uc_dscp2pbit[HI_OMCI_DSCP_2_P_LEN - ui_index - 1];
		}

		for (ui_index = 0; ui_index < HI_OMCI_ME_EXVLAN_DSCP_MAP_BIT_NUM; ui_index++) {
			st_dscp.aui_pri[HI_OMCI_ME_EXVLAN_DSCP_GET(ui_index, 0)] = st_dscp_map.bits[ui_index].pri0;
			st_dscp.aui_pri[HI_OMCI_ME_EXVLAN_DSCP_GET(ui_index, 1)] = st_dscp_map.bits[ui_index].pri1;
			st_dscp.aui_pri[HI_OMCI_ME_EXVLAN_DSCP_GET(ui_index, 2)] = st_dscp_map.bits[ui_index].pri2;
			st_dscp.aui_pri[HI_OMCI_ME_EXVLAN_DSCP_GET(ui_index, 3)] = st_dscp_map.bits[ui_index].pri3;
			st_dscp.aui_pri[HI_OMCI_ME_EXVLAN_DSCP_GET(ui_index, 4)] = st_dscp_map.bits[ui_index].pri4;
			st_dscp.aui_pri[HI_OMCI_ME_EXVLAN_DSCP_GET(ui_index, 5)] = st_dscp_map.bits[ui_index].pri5;
			st_dscp.aui_pri[HI_OMCI_ME_EXVLAN_DSCP_GET(ui_index, 6)] = st_dscp_map.bits[ui_index].pri6;
			st_dscp.aui_pri[HI_OMCI_ME_EXVLAN_DSCP_GET(ui_index, 7)] = st_dscp_map.bits[ui_index].pri7;
		}

		ui_ret = hi_omci_tapi_dscp_map_set(&st_dscp);
		HI_OMCI_RET_CHECK(ui_ret);

		if (g_st_op_item.ui_port != 0xff) {

			__omci_me_exvlan_entrydel(&g_st_op_item.st_vlantag, (hi_uchar8)g_st_op_item.ui_port);
			__omci_me_exvlan_entrycreate(&g_st_op_item.st_vlantag, (hi_uchar8)g_st_op_item.ui_port);
			g_st_op_item.ui_port = 0xff;
		}
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/*****************************************************************************
 函 数 名  : hi_omci_me_exvlantag_create_aft
 功能描述  : Extended VLAN tagging operation configuration data create动作实现
             数据库操作后处理
 输入参数  : hi_void*pv_data :
             hi_uint32 ui_in
             hi_uint32 *pui_outlen
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_me_exvlantag_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 ui_ret = HI_RET_SUCC;
	hi_omci_me_extvlantag_op_s *pst = (hi_omci_me_extvlantag_op_s *)pv_data;
	hi_omci_me_extvlantag_op_s st_extvlantag;
	omci_extvlantag_s *pst_mibtable;
	hi_uint32 ui_tabnum;
	hi_ushort16  us_instid;
	hi_uchar8    uc_portid;
	omci_extvlantag_s st_vlantagtable;
	hi_omci_tapi_sysinfo_s st_info;

	HI_OS_MEMSET_S(&st_extvlantag, sizeof(st_extvlantag), 0, sizeof(st_extvlantag));
	/* get the entity ID and instance ID */
	us_instid = pst->st_msghead.us_instid;
	hi_omci_debug("[INFO]:us_instid=0x%x\n", us_instid);

	hi_omci_ext_get_inst(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_instid, &st_extvlantag);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_debug("[INFO]:uc_type=0x%x,us_assocme_ptr=0x%x\n",
		      st_extvlantag.uc_type, st_extvlantag.us_assocme_ptr);

	/* 不支持非PPTP UNI/VEIP端口的extended vlan opt */
	if (st_extvlantag.uc_type != HI_OMCI_ASSOCIATE_TYPE_PPTP_ETH_UNI &&
	    st_extvlantag.uc_type != HI_OMCI_ASSOCIATE_TYPE_VEIP) {
		hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}


	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, HI_OMCI_ATTR6, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	st_extvlantag.us_rcvvlanmax = (hi_ushort16)ui_tabnum;
	hi_omci_ext_set_inst(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_instid, &st_extvlantag);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	pst_mibtable = (omci_extvlantag_s *)hi_os_malloc(sizeof(omci_extvlantag_s) * ui_tabnum);
	if (HI_NULL == pst_mibtable) {
		hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	/* 清空数据库 */
	HI_OS_MEMSET_S(pst_mibtable, (sizeof(omci_extvlantag_s) * ui_tabnum), 0, (sizeof(omci_extvlantag_s) * ui_tabnum));

	/* get physical port number */
	ui_ret = hi_omci_tapi_sysinfo_get(&st_info);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_mibtable);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	if (HI_OMCI_ASSOCIATE_TYPE_VEIP == st_extvlantag.uc_type) {
		uc_portid =  HI_OMCI_TAPI_VEIP_E;
	} else {
		uc_portid = HI_OMCI_GET_PORTID_FROM_INSTID(st_extvlantag.us_assocme_ptr);
		if (1 == st_info.ui_port_num && HI_OMCI_ME_ONU_CAPABILITY_TYPE_SFU_E == st_info.ui_product_mode) { //单口sfu
			uc_portid = 0;//规避格林伟迪局端下发port=1
		}
	}

	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == st_info.ui_product_mode) { //HGU模式不创建默认规则
		hi_os_free(pst_mibtable);
		return HI_RET_SUCC;
	}

	/*创建默认规则*/
	HI_OS_MEMSET_S(&st_vlantagtable, HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE, 0, HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE);
	st_vlantagtable.ui_filt_outer_pri = 15;
	st_vlantagtable.ui_filt_outer_vid = 4096;
	st_vlantagtable.ui_filt_in_pri = 15;
	st_vlantagtable.ui_filt_in_vid = 4096;
	st_vlantagtable.ui_treat_outer_pri = 15;
	st_vlantagtable.ui_treat_inner_pri = 15;

	ui_ret = __omci_me_exvlan_entrycreate(&st_vlantagtable, uc_portid);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_mibtable);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	HI_OS_MEMCPY_S(pst_mibtable, sizeof(*pst_mibtable), &st_vlantagtable, HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE);

	HI_OS_MEMSET_S(&st_vlantagtable, HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE, 0, HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE);
	st_vlantagtable.ui_filt_outer_pri = 15;
	st_vlantagtable.ui_filt_outer_vid = 4096;
	st_vlantagtable.ui_filt_in_pri = 14;
	st_vlantagtable.ui_filt_in_vid = 4096;
	st_vlantagtable.ui_treat_outer_pri = 15;
	st_vlantagtable.ui_treat_inner_pri = 15;

	ui_ret = __omci_me_exvlan_entrycreate(&st_vlantagtable, uc_portid);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_mibtable);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	HI_OS_MEMCPY_S((pst_mibtable + 1), sizeof(*pst_mibtable), &st_vlantagtable, HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE);

	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_instid, HI_OMCI_ATTR6, pst_mibtable);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(pst_mibtable);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_os_free(pst_mibtable);

	g_st_op_item.ui_port = uc_portid;
	HI_OS_MEMCPY_S(&g_st_op_item.st_vlantag, sizeof(g_st_op_item.st_vlantag), &st_vlantagtable,
		       HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE);

	return HI_RET_SUCC;
}


/*****************************************************************************
 函 数 名  : hi_omci_me_exvlantag_delete
 功能描述  : Extended VLAN tagging operation configuration data delete动作实现
             数据库操作前处理
 输入参数  : hi_void*pv_data :
             hi_uint32 ui_in
             hi_uint32 *pui_outlen
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_me_exvlantag_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_extvlantag_op_s *pst_extvlantag = (hi_omci_me_extvlantag_op_s *)pv_data;
	hi_omci_me_extvlantag_op_s st_extvlantag;
	omci_extvlantag_s *pst_mibtable;
	omci_extvlantag_s st_empty;
	hi_omci_tapi_sysinfo_s st_info;
	hi_int32 i_ret;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_index;
	hi_ushort16 us_instid = pst_extvlantag->st_msghead.us_instid;
	hi_uchar8 uc_portid;

	HI_OS_MEMSET_S(&st_extvlantag, sizeof(st_extvlantag), 0, sizeof(st_extvlantag));
	hi_omci_debug("[INFO]:us_instid=0x%x\n", us_instid);

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_instid, &st_extvlantag);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	uc_portid = HI_OMCI_GET_PORTID_FROM_INSTID(st_extvlantag.us_assocme_ptr);
	if (1 == st_info.ui_port_num && HI_OMCI_ME_ONU_CAPABILITY_TYPE_SFU_E == st_info.ui_product_mode) { //单口sfu
		uc_portid = 0;//规避格林伟迪局端下发port=1
	}

	/* 获取Received frame VLAN tagging operation table深度 */
	i_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, HI_OMCI_ATTR6, &ui_tabnum);
	HI_OMCI_RET_CHECK(i_ret);

	pst_mibtable = (omci_extvlantag_s *)hi_os_malloc(sizeof(omci_extvlantag_s) * ui_tabnum);
	if (HI_NULL == pst_mibtable) {
		hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	/* 读出数据库保存的表项 */
	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_instid, HI_OMCI_ATTR6, pst_mibtable);
	if (HI_RET_SUCC != i_ret) {
		hi_os_free(pst_mibtable);
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	HI_OS_MEMSET_S(&st_empty, sizeof(st_empty), 0, sizeof(st_empty));

	/* 释放所有配置 */
	for (ui_index = 0; ui_index < ui_tabnum; ui_index++) {
		if (0 == hi_os_memcmp((pst_mibtable + ui_index), &st_empty, sizeof(st_empty))) {
			continue;
		}

		i_ret = __omci_me_exvlan_entrydel((pst_mibtable + ui_index), uc_portid);
		if (HI_RET_SUCC != i_ret) {
			hi_os_free(pst_mibtable);
			hi_omci_systrace(i_ret, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}
	}

	/* 清空数据库表项 */
	HI_OS_MEMSET_S(pst_mibtable, sizeof(omci_extvlantag_s) * ui_tabnum, 0, sizeof(omci_extvlantag_s) * ui_tabnum);
	i_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_instid, HI_OMCI_ATTR6, pst_mibtable);
	if (HI_RET_SUCC != i_ret) {
		hi_os_free(pst_mibtable);
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_os_free(pst_mibtable);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return i_ret;
}


/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
