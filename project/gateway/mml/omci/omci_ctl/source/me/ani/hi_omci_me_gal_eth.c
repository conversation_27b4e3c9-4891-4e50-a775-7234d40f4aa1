/******************************************************************************

                  版权所有 (C), 2009-2019, 海思半导体有限公司

 ******************************************************************************
  文 件 名   : hi_omci_me_gal_eth.c
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2011_10_19
  功能描述   : GAL Ethernet profile
******************************************************************************/
#include "hi_omci.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 函 数 名  : hi_omci_me_gal_eth_set_after
 功能描述  : GAL Ethernet profile set
 输入参数  : hi_uint32 ui_cmdtype
             hi_void*pv_data
             hi_uint32 ui_in
             hi_uint32 *pui_outlen
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_me_gal_eth_set_after(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	/*TODO : you need to add code here*/

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

