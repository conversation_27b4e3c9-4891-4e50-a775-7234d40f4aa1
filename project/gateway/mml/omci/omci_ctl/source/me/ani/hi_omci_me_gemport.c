/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_gemport.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-24
  Description: OMCI ME GEM Port Network CTP
******************************************************************************/


/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"

#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


extern hi_omci_tapi_stat_gemctppm_s gst_gempm_history[HI_OMCI_GEM_PM_MAX_NUM];
/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/

/* Traffic management pointer for upstream */
#define HI_OMCI_ME_GEMCTP_IS_TMP_UP(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR4)

/* Traffic descriptor profile pointer for upstream (OPT) */
#define HI_OMCI_ME_GEMCTP_IS_TDP_UP(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR5)

/* Priority queue pointer for downstream */
#define HI_OMCI_ME_GEMCTP_IS_PQ_DN(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR7)

/* Traffic descriptor profile pointer for downstream (OPT) */
#define HI_OMCI_ME_GEMCTP_IS_TDP_DN(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR9)

/* Encryption key ring (OPT) */
#define HI_OMCI_ME_GEMCTP_IS_ENCRY(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR10)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : __omci_me_gem_ctp_para_set
 Description : 配置GEMPORT上行优先级调度，上行CAR，以及下行优先级调度，下行CAR
 Input Parm  : hi_omci_me_gem_ctp_s *pst_gemctp
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_gem_ctp_para_set(hi_omci_me_gem_ctp_s *pst_gemctp)
{
	hi_omci_tapi_gemport_para_s st_gempara;
	hi_int32 i_ret;
	hi_uint32 ui_egr;
	hi_uint32 ui_pri;
	hi_uchar8 uc_traffic_option;

	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ONT_G_E, 0, HI_OMCI_ATTR4, &uc_traffic_option);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_TRAFFIC_PQ == uc_traffic_option) {
		st_gempara.ui_upcarid = HI_OMCI_TAPI_CARID_INVALID;
		st_gempara.ui_dncarid = HI_OMCI_TAPI_CARID_INVALID;
	} else {
		/* 获取GEMPORT上行CAR模板 */
		i_ret = hi_omci_me_traffic_desc_carid_get(pst_gemctp->us_traffic_up_ptr, &st_gempara.ui_upcarid);
		if (i_ret != HI_RET_SUCC) {
			hi_omci_systrace(HI_RET_SUCC, pst_gemctp->us_traffic_up_ptr, 0, 0, 0);
			st_gempara.ui_upcarid = HI_OMCI_TAPI_CARID_INVALID;
		}

		/* 获取GEMPORT下行CAR模板 */
		i_ret = hi_omci_me_traffic_desc_carid_get(pst_gemctp->us_traffic_dn_ptr, &st_gempara.ui_dncarid);
		if (i_ret != HI_RET_SUCC) {
			hi_omci_systrace(HI_RET_SUCC, pst_gemctp->us_traffic_dn_ptr, 0, 0, 0);
			st_gempara.ui_dncarid = HI_OMCI_TAPI_CARID_INVALID;
		}
	}

	st_gempara.ui_uppri = (hi_uint32)HI_OMCI_TAPI_PRI_INVALID;
	/* 获取队列实例对应的优先级 */
	//i_ret = hi_omci_me_pq_pri_get(pst_gemctp->us_traffic_mng_ptr, &ui_egr, &st_gempara.ui_uppri);
	//HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_me_pq_pri_get(pst_gemctp->us_pqdn_ptr, &ui_egr, &ui_pri);
	if (HI_RET_SUCC == i_ret) {
		st_gempara.ui_dnpri = ui_pri;
	} else {
		hi_omci_systrace(HI_RET_SUCC, pst_gemctp->us_pqdn_ptr, 0, 0, 0);
		st_gempara.ui_dnpri = (hi_uint32)HI_OMCI_TAPI_PRI_INVALID;
	}

	switch (pst_gemctp->uc_encrykey) {
	case HI_OMCI_GEM_NO_ENCRYTION_E:
		st_gempara.ui_decrypt_en = HI_DISABLE;
		st_gempara.ui_encrypt_en = HI_DISABLE;
		break;

	case HI_OMCI_GEM_BOTH_ENCRYTION_E:
		st_gempara.ui_decrypt_en = HI_ENABLE;
		st_gempara.ui_encrypt_en = HI_ENABLE;
		break;

	case HI_OMCI_GEM_MC_ENCRYTION_E:
		st_gempara.ui_decrypt_en = HI_ENABLE;
		st_gempara.ui_encrypt_en = HI_DISABLE;
		break;

	default:
		hi_omci_debug("[INFO]:encrypt key ring[%hhu] is invalid\n", pst_gemctp->uc_encrykey);
		break;
	}

	hi_omci_debug("[INFO]:us_portid=0x%x,us_pri=0x%x,ds_pri=0x%x,us_carid=0x%x,ds_carid=0x%x,decrypt_en=0x%x,encrypt_en=0x%x\n",
		      pst_gemctp->us_portid, st_gempara.ui_uppri, st_gempara.ui_dnpri, st_gempara.ui_upcarid, st_gempara.ui_dncarid,
		      st_gempara.ui_decrypt_en, st_gempara.ui_encrypt_en);

	st_gempara.em_type = HI_OMCI_TAPI_GENERAL_GEMPORT_E;
	i_ret = hi_omci_tapi_gemport_set(pst_gemctp->us_portid, &st_gempara);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}


/******************************************************************************
 Function    : hi_omci_me_gem_ctp_create
 Description : GEM Port Network CTP create
               操作数据库后执行
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_gem_ctp_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32   ui_ret = HI_RET_SUCC, ui_index, ui_resv = HI_OMCI_GEM_PM_MAX_NUM;
	hi_omci_me_gem_ctp_s  *pst_inst = (hi_omci_me_gem_ctp_s *)pv_data;

	/* get the entity ID and instance ID */
	hi_omci_debug("[INFO]:us_instid=0x%x\n", pst_inst->st_msghead.us_instid);

	hi_omci_debug("[INFO]:us_portid=0x%x,us_tcont_ptr=0x%x,uc_direction=0x%x,us_traffic_mng_ptr=0x%x\n",
		      pst_inst->us_portid, pst_inst->us_tcont_ptr, pst_inst->uc_direction, pst_inst->us_traffic_mng_ptr);
	hi_omci_debug("[INFO]:us_traffic_up_ptr=0x%x,uc_unicnt=0x%x,us_pqdn_ptr=0x%x,uc_encrystate=0x%x\n",
		      pst_inst->us_traffic_up_ptr, pst_inst->uc_unicnt, pst_inst->us_pqdn_ptr, pst_inst->uc_encrystate);
	hi_omci_debug("[INFO]:us_traffic_dn_ptr=0x%x,uc_encrykey=0x%x\n",
		      pst_inst->us_traffic_dn_ptr, pst_inst->uc_encrykey);

	ui_ret = __omci_me_gem_ctp_para_set(pst_inst);
	HI_OMCI_RET_CHECK(ui_ret);

	for (ui_index = 0; ui_index < HI_OMCI_GEM_PM_MAX_NUM; ui_index++) {
		if (pst_inst->us_portid == gst_gempm_history[ui_index].ui_gemport) {
			break;
		}
		if ((0xFFFFFFFF == gst_gempm_history[ui_index].ui_gemport) && (HI_OMCI_GEM_PM_MAX_NUM == ui_resv)) {
			ui_resv = ui_index;
		}
	}

	if (HI_OMCI_GEM_PM_MAX_NUM == ui_index) {
		if (HI_OMCI_GEM_PM_MAX_NUM != ui_resv) {
			gst_gempm_history[ui_resv].ui_gemport = pst_inst->us_portid;
		}
	}
	hi_omci_systrace(ui_ret, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/******************************************************************************
 Function    : hi_omci_me_gem_ctp_set
 Description : GEM Port Network CTP set
               操作数据库后执行
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_gem_ctp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32   ui_ret = HI_RET_SUCC;
	hi_omci_me_gem_ctp_s *pst_inst = (hi_omci_me_gem_ctp_s *)pv_data;
	hi_omci_me_gem_ctp_s st_gemctp;
	hi_ushort16 us_instid = pst_inst->st_msghead.us_instid;
	hi_ushort16 us_mask = pst_inst->st_msghead.us_attmask;

	HI_OS_MEMSET_S(&st_gemctp, sizeof(hi_omci_me_gem_ctp_s), 0, sizeof(hi_omci_me_gem_ctp_s));

	/* get the entity ID and instance ID */
	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_GEM_CTP_E, us_instid, &st_gemctp);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_debug("[ERRO]:ui_ret=0x%x\n", ui_ret);
		return ui_ret;
	}

	hi_omci_debug("[INFO]:us_portid=0x%x,us_tcont_ptr=0x%x,uc_direction=0x%x,us_traffic_mng_ptr=0x%x\n",
		      st_gemctp.us_portid, st_gemctp.us_tcont_ptr, st_gemctp.uc_direction, st_gemctp.us_traffic_mng_ptr);
	hi_omci_debug("[INFO]:us_traffic_up_ptr=0x%x,uc_unicnt=0x%x,us_pqdn_ptr=0x%x,uc_encrystate=0x%x\n",
		      st_gemctp.us_traffic_up_ptr, st_gemctp.uc_unicnt, st_gemctp.us_pqdn_ptr, st_gemctp.uc_encrystate);
	hi_omci_debug("[INFO]:us_traffic_dn_ptr=0x%x,uc_encrykey=0x%x\n",
		      st_gemctp.us_traffic_dn_ptr, st_gemctp.uc_encrykey);

	if (HI_OMCI_ME_GEMCTP_IS_TMP_UP(us_mask) || HI_OMCI_ME_GEMCTP_IS_TDP_UP(us_mask) ||
	    HI_OMCI_ME_GEMCTP_IS_PQ_DN(us_mask) || HI_OMCI_ME_GEMCTP_IS_TDP_DN(us_mask) ||
	    HI_OMCI_ME_GEMCTP_IS_ENCRY(us_mask)) {
		ui_ret = __omci_me_gem_ctp_para_set(pst_inst);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}

/******************************************************************************
 Function    : hi_omci_me_gem_ctp_delete
 Description : GEM Port Network CTP delete
               操作数据库前执行
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_gem_ctp_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_gem_ctp_s *pst_inst = (hi_omci_me_gem_ctp_s *)pv_data;
	hi_int32 i_ret = HI_RET_SUCC;
	hi_ushort16 us_gemportid;
	hi_ushort16 us_instid = pst_inst->st_msghead.us_instid;
	hi_uint32 ui_index;

	i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, us_instid, HI_OMCI_ATTR1, &us_gemportid);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_debug("delete gemportid = %04hu\n", us_gemportid);

	i_ret = hi_omci_tapi_gemport_del(us_gemportid);
	HI_OMCI_RET_CHECK(i_ret);

	for (ui_index = 0; ui_index < HI_OMCI_GEM_PM_MAX_NUM; ui_index++) {
		if (pst_inst->us_portid == gst_gempm_history[ui_index].ui_gemport) {
			gst_gempm_history[ui_index].ui_gemport = 0xFFFFFFFF;
			break;
		}
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
