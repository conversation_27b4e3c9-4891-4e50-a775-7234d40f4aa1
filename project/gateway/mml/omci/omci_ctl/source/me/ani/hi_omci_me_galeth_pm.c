/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_galeth_pm.c
  Version    : 初稿
  Author     :
  Creation   :
  Description: GAL Ethernet PM history data
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
static hi_uint32 gui_galethpm_history = 0;
static hi_uint32 gui_galethpm_pre_history = 0;

/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_galethpm_stat_get
 Description :
 Input Parm  : hi_void *pv_data       GALETH历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_void *pv_data       GALETH当前累计统计数据
               hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_galethpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_uint32 ui_galethpm_curr;
	hi_omci_me_galeth_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_galeth_get(&ui_galethpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减，将结构保存到数据库 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_GAL_ETH_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_interendtime++;
	st_entity.ui_discardfrm = (ui_galethpm_curr > gui_galethpm_history) ? (ui_galethpm_curr - gui_galethpm_history) : 0;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_GAL_ETH_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 将当前统计更新到历史统计 */
	gui_galethpm_pre_history = gui_galethpm_history;
	gui_galethpm_history = ui_galethpm_curr;

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_galethpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_galeth_pm_s *pst_entry = (hi_omci_me_galeth_pm_s *)pv_data;
	hi_omci_me_galeth_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;


	/*获取实例数据 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_GAL_ETH_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.ui_discardfrm = (gui_galethpm_history > gui_galethpm_pre_history) ? (gui_galethpm_history -
				  gui_galethpm_pre_history) : 0;

	/*将清零的数据保存到gemport PM实例数据中*/
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_GAL_ETH_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_galethpm_getcurrent
 Description : GAL Ethernet PM history data getcurrent
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_galethpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_galeth_pm_s *pst_entry = (hi_omci_me_galeth_pm_s *)pv_data;
	hi_uint32 ui_galethpm_curr;
	hi_omci_me_galeth_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_galeth_get(&ui_galethpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减，将结构保存到数据库 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_GAL_ETH_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.ui_discardfrm = (ui_galethpm_curr > gui_galethpm_history) ? (ui_galethpm_curr - gui_galethpm_history) : 0;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_GAL_ETH_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_galethpm_create
 Description : GAL Ethernet PM history data create
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_galethpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_galeth_pm_s *pst_entry = (hi_omci_me_galeth_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_galethpm_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	gui_galethpm_history = 0;
	gui_galethpm_pre_history = 0;

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_galethpm_stat_timer, sizeof(st_galethpm_stat_timer), 0, sizeof(st_galethpm_stat_timer));
	st_galethpm_stat_timer.ui_meid = HI_OMCI_PRO_ME_GAL_ETH_PM_E;
	st_galethpm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_galethpm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_galethpm_delete
 Description : GAL Ethernet PM history data delete
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_galethpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_galeth_pm_s *pst_entry = (hi_omci_me_galeth_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_galethpm_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 删除定时器 */
	HI_OS_MEMSET_S(&st_galethpm_stat_timer, sizeof(st_galethpm_stat_timer), 0, sizeof(st_galethpm_stat_timer));
	st_galethpm_stat_timer.ui_meid = HI_OMCI_PRO_ME_GAL_ETH_PM_E;
	st_galethpm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_galethpm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

