/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_map.c
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : OMCI映射状态机
******************************************************************************/


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"

#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*----------------------------------------------*
 * 宏定义                                       *
 *----------------------------------------------*/
#define HI_OMCI_MAP_INVALID_TCONT   256

/*----------------------------------------------*
 * 常量定义                                     *
 *----------------------------------------------*/

typedef hi_int32(* hi_omci_map_hander)(hi_omci_map_event_e em_event, hi_void *pv_data);

typedef struct {
	hi_uint32 ui_gemport;
	hi_uint32 ui_tcont;
} hi_omci_map_pri_gemport_s;

typedef struct {
	hi_omci_map_pri_gemport_s pri[HI_OMCI_8021P_PRI_NUM];
	uint32_t unmarked_frame_option;
	uint32_t default_pbit;
} hi_omci_map_pri_s;

/*----------------------------------------------*
 * 内部函数原型说明                             *
 *----------------------------------------------*/
static hi_int32 hi_omci_map_state_init(hi_omci_map_event_e em_event, hi_void *pv_data);
static hi_int32 hi_omci_map_state_pri_preview(hi_omci_map_event_e em_event, hi_void *pv_data);
static hi_int32 hi_omci_map_state_vlan_preview(hi_omci_map_event_e em_event, hi_void *pv_data);
static hi_int32 hi_omci_map_state_vlan_pri_preview(hi_omci_map_event_e em_event, hi_void *pv_data);
static hi_int32 hi_omci_map_state_pri(hi_omci_map_event_e em_event, hi_void *pv_data);
static hi_int32 hi_omci_map_state_vlan(hi_omci_map_event_e em_event, hi_void *pv_data);
static hi_int32 hi_omci_map_state_vlan_pri(hi_omci_map_event_e em_event, hi_void *pv_data);

/*----------------------------------------------*
 * 模块级变量                                   *
 *----------------------------------------------*/
hi_uint32 g_ui_port_map = HI_FALSE;
hi_list_head g_st_map_listhead;
static hi_uint32 g_ui_sysinfo_mode = 0xf;//保存HGU/SFU

static hi_omci_map_hander g_apf_hander[HI_OMCI_MAP_STATE_NUM] = {
	hi_omci_map_state_init,
	hi_omci_map_state_pri_preview,
	hi_omci_map_state_vlan_preview,
	hi_omci_map_state_vlan_pri_preview,
	hi_omci_map_state_pri,
	hi_omci_map_state_vlan,
	hi_omci_map_state_vlan_pri
};

extern hi_uint32 hi_omci_me_8021p_check_mappri(hi_omci_me_8021p_s *pst_entity);

/*****************************************************************************
 函 数 名  : hi_omci_map_init
 功能描述  : 初始化映射状态机
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_map_init(hi_uint32 ui_mode)
{
	hi_list_init_head(&g_st_map_listhead);
	g_ui_sysinfo_mode = ui_mode;
}

/*****************************************************************************
 函 数 名  : hi_omci_map_exit
 功能描述  : 去初始化映射状态机，清空队列
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_map_exit(hi_void)
{
	hi_list_head *pst_list_pos = HI_NULL;
	hi_list_head *pst_list_next = HI_NULL;
	hi_omci_map_state_s *pst_state = HI_NULL;

	hi_list_for_each_safe(pst_list_pos, pst_list_next, &g_st_map_listhead) {
		pst_state = (hi_omci_map_state_s *)hi_list_entry(pst_list_pos, hi_omci_map_state_s, st_listhead);
		if (HI_NULL != pst_state) {
			hi_omci_ani_systrace(HI_RET_SUCC, pst_state->ui_bp_id, pst_state->em_state, 0, 0);
			(void)g_apf_hander[pst_state->em_state](HI_OMCI_MAP_EVENT_RELEASE, (hi_void *)pst_state);
			hi_list_del(&pst_state->st_listhead);
			hi_os_free(pst_state);
		}
	}

	return;
}

/*****************************************************************************
 函 数 名  : hi_omci_map_start
 功能描述  : 启动映射状态机
 输入参数  : hi_uint32 ui_bp_id
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_map_start(hi_uint32 ui_bp_id)
{
	hi_list_head *pst_list_pos = HI_NULL;
	hi_list_head *pst_list_next = HI_NULL;
	hi_omci_map_state_s *pst_state = HI_NULL;

	/* 查找bp_id是否存在 */
	hi_list_for_each_safe(pst_list_pos, pst_list_next, &g_st_map_listhead) {
		pst_state = (hi_omci_map_state_s *)hi_list_entry(pst_list_pos, hi_omci_map_state_s, st_listhead);
		if (HI_NULL != pst_state && pst_state->ui_bp_id == ui_bp_id) {
			hi_omci_ani_systrace(HI_RET_ITEM_NOTEXIST, ui_bp_id, 0, 0, 0);
			return HI_RET_SUCC;//如果已经存在节点就返回成功
		}
	}

	pst_state = (hi_omci_map_state_s *)hi_os_malloc(sizeof(hi_omci_map_state_s));
	if (HI_NULL == pst_state) {
		hi_omci_ani_systrace(HI_RET_MALLOC_FAIL, ui_bp_id, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	HI_OS_MEMSET_S(pst_state, sizeof(hi_omci_map_state_s), 0, sizeof(hi_omci_map_state_s));
	pst_state->ui_bp_id = ui_bp_id;
	pst_state->em_state = HI_OMCI_MAP_STATE_INIT;

	hi_list_add_tail(&pst_state->st_listhead, &g_st_map_listhead);

	hi_omci_ani_systrace(HI_RET_SUCC, ui_bp_id, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_map_stop
 功能描述  : 停止映射状态机
 输入参数  : hi_uint32 ui_bp_id
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_map_stop(hi_uint32 ui_bp_id)
{
	hi_list_head *pst_list_pos = HI_NULL;
	hi_list_head *pst_list_next = HI_NULL;
	hi_omci_map_state_s *pst_state = HI_NULL;

	hi_list_for_each_safe(pst_list_pos, pst_list_next, &g_st_map_listhead) {
		pst_state = (hi_omci_map_state_s *)hi_list_entry(pst_list_pos, hi_omci_map_state_s, st_listhead);
		if (HI_NULL != pst_state && pst_state->ui_bp_id == ui_bp_id) {
			hi_list_del(&pst_state->st_listhead);
			hi_os_free(pst_state);
			hi_omci_ani_systrace(HI_RET_SUCC, ui_bp_id, 0, 0, 0);
			return HI_RET_SUCC;
		}
	}

	hi_omci_ani_systrace(HI_RET_FAIL, ui_bp_id, 0, 0, 0);
	return (hi_int32)HI_RET_FAIL;
}

/*****************************************************************************
 函 数 名  : hi_omci_map_port_enable
 功能描述  : 使能端口映射
 输入参数  : hi_uint32 ui_enable
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_map_port_enable(hi_uint32 ui_enable)
{
	hi_omci_ani_systrace(HI_RET_SUCC, ui_enable, 0, 0, 0);
	g_ui_port_map = ui_enable;
}

/*****************************************************************************
 函 数 名  : hi_omci_map_msg_hander
 功能描述  : 处理映射状态机消息
 输入参数  : hi_uint32 ui_bp_id
             hi_omci_map_event_e em_event
             hi_void *pv_data 只有当em_event取值HI_OMCI_MAP_EVENT_BP_TO_VF时,
             为VLAN filter指针,其它情况为空针
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_map_msg_hander(hi_uint32 ui_bp_id, hi_omci_map_event_e em_event, hi_void *pv_data)
{
	hi_list_head *pst_list_pos = HI_NULL;
	hi_list_head *pst_list_next = HI_NULL;
	hi_omci_map_state_s *pst_state = HI_NULL;
	hi_int32 i_ret;

	hi_list_for_each_safe(pst_list_pos, pst_list_next, &g_st_map_listhead) {
		pst_state = (hi_omci_map_state_s *)hi_list_entry(pst_list_pos, hi_omci_map_state_s, st_listhead);
		if (HI_NULL != pst_state && pst_state->ui_bp_id == ui_bp_id) {
			pst_state->pv_data = pv_data;
			hi_omci_systrace(HI_RET_SUCC, ui_bp_id, em_event, pst_state->em_state, 0);
			hi_omci_debug("current_state: %d event: %d\r\n", pst_state->em_state, em_event);
			i_ret = g_apf_hander[pst_state->em_state](em_event, (hi_void *)pst_state);
			hi_omci_systrace(i_ret, ui_bp_id, em_event, pst_state->em_state, 0);
			return i_ret;
		}
	}

	hi_omci_ani_systrace(HI_RET_FAIL, ui_bp_id, em_event, 0, 0);
	return (hi_int32)HI_RET_FAIL;
}

/*****************************************************************************
 函 数 名  : hi_omci_map_get_portid
 功能描述  : 根据MAC bridge Port configure data 实例ID获取端口号
 输入参数  : hi_uint32 ui_bp_id
 输出参数  : hi_uint32 *us_portid
 返 回 值  : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_get_portid(hi_uint32 ui_bp_id, hi_uint32 *us_portid)
{
	hi_uint32 ui_ret;
	hi_ushort16 us_macportinst;
	hi_ushort16 us_bridgeid;
	hi_ushort16 us_pptpuni;
	hi_uchar8 uc_tptype;

	/*兼容hgu模式下，会先下发指向pptp的MAC_PORT_CFG实体*/
	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == g_ui_sysinfo_mode) {
		*us_portid = HI_OMCI_TAPI_VEIP_E;
		return HI_RET_SUCC;
	}

	/* 获取关联的桥实例ID */
	us_macportinst = (hi_ushort16)ui_bp_id;
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_macportinst, HI_OMCI_ATTR1, &us_bridgeid);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 根据桥实例ID获取在同一桥下，关联PPTP UNI的MAC bridge port */
	uc_tptype = HI_MAC_BRI_PORT_UNI_E;
	ui_ret = hi_omci_ext_get_instid_byattr2(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR1, &us_bridgeid,
						HI_OMCI_ATTR3, &uc_tptype, &us_macportinst);
	if (ui_ret != HI_RET_SUCC) {
		return HI_RET_SUCC;
	}

	/* 获取PPTP UNI的实例ID */
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_macportinst, HI_OMCI_ATTR4, &us_pptpuni);
	HI_OMCI_RET_CHECK(ui_ret);

	*us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_pptpuni);

	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_map_get_gemport
 功能描述  : 根据MAC bridge Port configure data 实例ID获取GEMPORT和TCONT
 输入参数  : hi_uint32 ui_bp_id
 输出参数  : hi_uint32 *ui_gemport
             hi_uint32 *ui_tcont
 返 回 值  : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_get_gemport(hi_uint32 ui_bp_id, hi_uint32 *ui_gemport, hi_uint32 *ui_tcont,
					hi_uchar8 *uc_interworking)
{
	hi_omci_me_gem_iwtp_s st_gemiwtp;
	hi_omci_me_gem_ctp_s st_gemctp;
	hi_uint32 ui_ret = 0;
	hi_ushort16 us_macportinst = 0;
	hi_ushort16 us_gemiwtp_inst = 0;

	HI_OS_MEMSET_S(&st_gemiwtp, sizeof(st_gemiwtp), 0, sizeof(st_gemiwtp));
	HI_OS_MEMSET_S(&st_gemctp, sizeof(st_gemctp), 0, sizeof(st_gemctp));
	us_macportinst = (hi_ushort16)ui_bp_id;
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_macportinst, HI_OMCI_ATTR4, &us_gemiwtp_inst);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_GEM_IWTP_E, us_gemiwtp_inst, &st_gemiwtp);
	HI_OMCI_RET_CHECK(ui_ret);

	*uc_interworking = st_gemiwtp.uc_interworking;

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_GEM_CTP_E, st_gemiwtp.us_gemctp_ptr, &st_gemctp);
	HI_OMCI_RET_CHECK(ui_ret);

	*ui_gemport = st_gemctp.us_portid;
	*ui_tcont = HI_OMCI_GET_TCONT_ID(st_gemctp.us_tcont_ptr);

	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_map_get_pri
 功能描述  : 根据MAC bridge Port configure data 实例ID获取对应优先级映射的
             GEMPORT和TCONT
 输入参数  : hi_uint32 ui_bp_id
 输出参数  : hi_omci_map_pri_s *pst_pri
 返 回 值  : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_get_pri(hi_uint32 ui_bp_id, hi_omci_map_pri_s *pst_pri)
{
	hi_omci_me_8021p_s st_8021p_srvprofile;
	hi_uint32 ui_pri;
	hi_uint32 ui_ret;
	hi_ushort16 *pus_iwtp;
	hi_ushort16 us_pre_iwtp = HI_OMCI_8021P_INVALID_GEMIWTP;
	hi_ushort16 us_8021pinst;
	hi_ushort16 us_gemctp_ptr;
	hi_ushort16 us_gemportid;
	hi_ushort16 us_tcontid;

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, (hi_ushort16)ui_bp_id, HI_OMCI_ATTR4, &us_8021pinst);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_802_1P_E, us_8021pinst, &st_8021p_srvprofile);
	HI_OMCI_RET_CHECK(ui_ret);
	pst_pri->unmarked_frame_option = HI_DISABLE;
	if ((st_8021p_srvprofile.uc_frameoption == 1) && (st_8021p_srvprofile.uc_defaultmark < HI_OMCI_8021P_PRI_NUM)) {
		pst_pri->unmarked_frame_option = HI_ENABLE;
		pst_pri->default_pbit = st_8021p_srvprofile.uc_defaultmark;
	}
	for (ui_pri = 0, pus_iwtp = &st_8021p_srvprofile.us_iwtp_ptr0;
	     ui_pri < HI_OMCI_8021P_PRI_NUM;
	     ui_pri++, pus_iwtp++) {
		if ((HI_OMCI_8021P_INVALID_GEMIWTP != *pus_iwtp) && (us_pre_iwtp != *pus_iwtp)) {
			ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_IWTP_E, *pus_iwtp, HI_OMCI_ATTR1, &us_gemctp_ptr);
			HI_OMCI_RET_CHECK(ui_ret);

			ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, us_gemctp_ptr, HI_OMCI_ATTR1, &us_gemportid);
			HI_OMCI_RET_CHECK(ui_ret);

			ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, us_gemctp_ptr, HI_OMCI_ATTR2, &us_tcontid);
			HI_OMCI_RET_CHECK(ui_ret);

			pst_pri->pri[ui_pri].ui_gemport = us_gemportid;
			pst_pri->pri[ui_pri].ui_tcont = HI_OMCI_GET_TCONT_ID(us_tcontid);
		} else if (HI_OMCI_8021P_INVALID_GEMIWTP != *pus_iwtp) {
			pst_pri->pri[ui_pri].ui_gemport = pst_pri->pri[ui_pri - 1].ui_gemport;
			pst_pri->pri[ui_pri].ui_tcont = pst_pri->pri[ui_pri - 1].ui_tcont;
		} else {
			pst_pri->pri[ui_pri].ui_tcont = HI_OMCI_MAP_INVALID_TCONT;
		}

		us_pre_iwtp = *pus_iwtp;
	}

	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_set_port
  Description  : 配置端口映射
  Input Param  : hi_omci_map_state_s *pst_state
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_set_port(hi_omci_map_state_s *pst_state)
{
	hi_omci_tapi_map_s st_omci_tapi_map;
	hi_uint32 ui_ret = 0;
	hi_uint32 ui_portid = 0;
	hi_uint32 ui_gemport = 0;
	hi_uint32 ui_tcont = 0;
	hi_uchar8 uc_interworking = 0;
	hi_uchar8 uc_tptype = 0;
	hi_omci_map_pri_s st_map_pri;
	HI_OS_MEMSET_S(&st_omci_tapi_map, sizeof(st_omci_tapi_map), 0, sizeof(st_omci_tapi_map));

	ui_ret = hi_omci_map_get_portid(pst_state->ui_bp_id, &ui_portid);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, (hi_ushort16)pst_state->ui_bp_id, HI_OMCI_ATTR3,
				      &uc_tptype);
	HI_OMCI_RET_CHECK(ui_ret);
	hi_omci_debug("[INFO]:uc_tptype=0x%x\n", uc_tptype);
	if (HI_MAC_BRI_PORT_8021P_MAP_E == uc_tptype) {
		ui_ret = hi_omci_map_get_pri(pst_state->ui_bp_id, &st_map_pri);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_gemport = st_map_pri.pri[0].ui_gemport;
		ui_tcont = st_map_pri.pri[0].ui_tcont;
	} else if (HI_MAC_BRI_PORT_IWTP_E == uc_tptype) {
		ui_ret = hi_omci_map_get_gemport(pst_state->ui_bp_id, &ui_gemport, &ui_tcont, &uc_interworking);
		HI_OMCI_RET_CHECK(ui_ret);

		if (6 == uc_interworking) { //Downstream broadcast
			return HI_RET_SUCC;
		}
	} else {
		hi_omci_ani_systrace(HI_RET_FAIL, uc_tptype, 0, 0, 0);
		return (hi_int32)HI_RET_FAIL;
	}
	hi_omci_debug("[INFO]:ui_tcont=0x%x ui_gemport=0x%x\n", ui_tcont, ui_gemport);
	st_omci_tapi_map.ui_mask = HI_OMCI_TAPI_MAP_IGR;
	st_omci_tapi_map.em_port = (hi_omci_tapi_port_e)ui_portid;
	st_omci_tapi_map.ui_gemport = ui_gemport;
	st_omci_tapi_map.ui_tcont = ui_tcont;

	ui_ret = hi_omci_tapi_map_set(&st_omci_tapi_map);
	HI_OMCI_RET_CHECK(ui_ret);

	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_del_port
  Description  : 删除端口映射
  Input Param  : hi_omci_map_state_s *pst_state
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_del_port(hi_omci_map_state_s *pst_state)
{
	hi_omci_tapi_map_s st_omci_tapi_map;
	hi_uint32 ui_ret;
	hi_uint32 ui_portid;
	HI_OS_MEMSET_S(&st_omci_tapi_map, sizeof(st_omci_tapi_map), 0, sizeof(st_omci_tapi_map));

	if (g_ui_sysinfo_mode == HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E) {
		ui_portid = HI_OMCI_TAPI_VEIP_E;
	} else {
		ui_ret = hi_omci_map_get_portid(pst_state->ui_bp_id, &ui_portid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	st_omci_tapi_map.ui_mask = HI_OMCI_TAPI_MAP_IGR;
	st_omci_tapi_map.em_port = (hi_omci_tapi_port_e)ui_portid;
	if (hi_omci_tapi_map_get(&st_omci_tapi_map) == HI_RET_SUCC)
		hi_omci_tapi_map_del(&st_omci_tapi_map);

	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_set_vlan_gemiwtp
  Description  : 配置VLAN映射
                 有GEM IWTP和802.1p service profile两种方式
                 802.1p service profile方式下各优先级都指向同一GEMPORT
  Input Param  : hi_omci_map_state_s *pst_state
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_set_vlan(hi_omci_map_state_s *pst_state)
{
	hi_omci_me_vlan_filt_s *pst_vlanfilter = (hi_omci_me_vlan_filt_s *)pst_state->pv_data;
	hi_omci_tapi_map_s st_omci_tapi_map;
	hi_omci_map_pri_s st_map_pri;
	hi_uint32 ui_ret;
	hi_uint32 ui_portid;
	hi_uint32 ui_vlanid;
	hi_uint32 ui_gemport;
	hi_uint32 ui_tcont;
	hi_ushort16 us_macportinst;
	hi_uchar8 uc_tptype;
	hi_uchar8 uc_interworking;

	HI_OS_MEMSET_S(&st_omci_tapi_map, sizeof(st_omci_tapi_map), 0, sizeof(st_omci_tapi_map));

	if (g_ui_port_map == HI_TRUE) {
		ui_ret = hi_omci_map_get_portid(pst_state->ui_bp_id, &ui_portid);
		HI_OMCI_RET_CHECK(ui_ret);

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_IGR;
		st_omci_tapi_map.em_port = (hi_omci_tapi_port_e)ui_portid;
	}

	us_macportinst = (hi_ushort16)pst_state->ui_bp_id;
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_macportinst, HI_OMCI_ATTR3, &uc_tptype);
	HI_OMCI_RET_CHECK(ui_ret);

	if (HI_MAC_BRI_PORT_8021P_MAP_E == uc_tptype) {
		ui_ret = hi_omci_map_get_pri(pst_state->ui_bp_id, &st_map_pri);
		HI_OMCI_RET_CHECK(ui_ret);

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_VLAN;
		st_omci_tapi_map.ui_gemport = st_map_pri.pri[0].ui_gemport;
		st_omci_tapi_map.ui_tcont = st_map_pri.pri[0].ui_tcont;
	} else if (HI_MAC_BRI_PORT_IWTP_E == uc_tptype) {
		ui_ret = hi_omci_map_get_gemport(pst_state->ui_bp_id, &ui_gemport, &ui_tcont, &uc_interworking);
		HI_OMCI_RET_CHECK(ui_ret);

		if (6 == uc_interworking) { //Downstream broadcast
			return HI_RET_SUCC;
		}

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_VLAN;
		st_omci_tapi_map.ui_gemport = ui_gemport;
		st_omci_tapi_map.ui_tcont = ui_tcont;
	} else {
		hi_omci_ani_systrace(HI_RET_FAIL, uc_tptype, 0, 0, 0);
		return (hi_int32)HI_RET_FAIL;
	}

	/* 配置新映射之前，才对历史映射进行删除 */
	ui_ret = hi_omci_map_del_port(pst_state);
	HI_OMCI_RET_CHECK(ui_ret);
	for (ui_vlanid = 0; ui_vlanid < pst_vlanfilter->uc_entitynum; ui_vlanid++) {
		st_omci_tapi_map.ui_vlan = HI_OMCI_GET_VLAN_FROM_TCI(ntohs(pst_vlanfilter->us_vlantab[ui_vlanid]));

		ui_ret = hi_omci_tapi_map_set(&st_omci_tapi_map);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	if (0 == pst_vlanfilter->uc_entitynum && 1 == pst_vlanfilter->uc_forward_oper) { //untag 映射
		st_omci_tapi_map.ui_vlan = 4096;

		ui_ret = hi_omci_tapi_map_set(&st_omci_tapi_map);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_del_vlan
  Description  : 删除VLAN映射
  Input Param  : hi_omci_map_state_s *pst_state
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_del_vlan(hi_omci_map_state_s *pst_state)
{
	hi_omci_me_vlan_filt_s st_vlanfilter;
	hi_omci_tapi_map_s st_omci_tapi_map;
	hi_uint32 ui_ret = 0;
	hi_uint32 ui_portid = 0;
	hi_uint32 ui_vlanid = 0;
	HI_OS_MEMSET_S(&st_vlanfilter, sizeof(st_vlanfilter), 0, sizeof(st_vlanfilter));
	HI_OS_MEMSET_S(&st_omci_tapi_map, sizeof(st_omci_tapi_map), 0, sizeof(st_omci_tapi_map));

	if (HI_TRUE == g_ui_port_map) {
		ui_ret = hi_omci_map_get_portid(pst_state->ui_bp_id, &ui_portid);
		HI_OMCI_RET_CHECK(ui_ret);

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_IGR;
		st_omci_tapi_map.em_port = (hi_omci_tapi_port_e)ui_portid;
	}

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VLAN_FILT_E, (hi_ushort16)pst_state->ui_bp_id, &st_vlanfilter);
	HI_OMCI_RET_CHECK(ui_ret);

	st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_VLAN;
	for (ui_vlanid = 0; ui_vlanid < st_vlanfilter.uc_entitynum; ui_vlanid++) {
		st_omci_tapi_map.ui_vlan = HI_OMCI_GET_VLAN_FROM_TCI(ntohs(st_vlanfilter.us_vlantab[ui_vlanid]));
		hi_omci_debug("mask = 0x%08x, vlan = %010u, pri = %010u, port = %010d, gemport = %010u, tcont = %010u\n",
			      st_omci_tapi_map.ui_mask, st_omci_tapi_map.ui_vlan, st_omci_tapi_map.ui_pri, st_omci_tapi_map.em_port,
			      st_omci_tapi_map.ui_gemport, st_omci_tapi_map.ui_tcont);
		if (hi_omci_tapi_map_get(&st_omci_tapi_map) == HI_RET_SUCC) {
			ui_ret = hi_omci_tapi_map_del(&st_omci_tapi_map);
			HI_OMCI_RET_CHECK(ui_ret);
		}
	}
	if ((st_vlanfilter.uc_entitynum == 0) && (st_vlanfilter.uc_forward_oper == 1)) { //untag 映射
		st_omci_tapi_map.ui_vlan = 4096;
		if (hi_omci_tapi_map_get(&st_omci_tapi_map) == HI_RET_SUCC)
			hi_omci_tapi_map_del(&st_omci_tapi_map);
	}

	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_update_vlan
  Description  : 更新VLAN映射
  Input Param  : hi_omci_map_state_s *pst_state
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_update_vlan(hi_omci_map_state_s *pst_state)
{
	hi_omci_me_vlan_filt_s *pst_vlanfilter = (hi_omci_me_vlan_filt_s *)pst_state->pv_data;
	hi_omci_me_vlan_filt_s st_vlanfilter;
	hi_omci_tapi_map_s st_omci_tapi_map;
	hi_omci_tapi_map_s omci_tapi_map_del = {0};
	hi_omci_map_pri_s st_map_pri;
	hi_uint32 ui_ret;
	hi_uint32 ui_portid;
	hi_uint32 ui_gemport;
	hi_uint32 ui_tcont;
	hi_uint32 ui_vlanid;
	hi_uint32 ui_index;
	hi_uint32 ui_isfound;
	hi_uchar8 uc_tptype;
	hi_uchar8 uc_interworking;

	HI_OS_MEMSET_S(&st_vlanfilter, sizeof(st_vlanfilter), 0, sizeof(st_vlanfilter));
	HI_OS_MEMSET_S(&st_omci_tapi_map, sizeof(st_omci_tapi_map), 0, sizeof(st_omci_tapi_map));

	if (g_ui_port_map == HI_TRUE) {
		ui_ret = hi_omci_map_get_portid(pst_state->ui_bp_id, &ui_portid);
		HI_OMCI_RET_CHECK(ui_ret);

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_IGR;
		st_omci_tapi_map.em_port = (hi_omci_tapi_port_e)ui_portid;
		omci_tapi_map_del.ui_mask |= HI_OMCI_TAPI_MAP_IGR;
		omci_tapi_map_del.em_port = (hi_omci_tapi_port_e)ui_portid;
	}

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, (hi_ushort16)pst_state->ui_bp_id, HI_OMCI_ATTR3,
				      &uc_tptype);
	HI_OMCI_RET_CHECK(ui_ret);

	if (HI_MAC_BRI_PORT_8021P_MAP_E == uc_tptype) {
		ui_ret = hi_omci_map_get_pri(pst_state->ui_bp_id, &st_map_pri);
		HI_OMCI_RET_CHECK(ui_ret);

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_VLAN;
		st_omci_tapi_map.ui_gemport = st_map_pri.pri[0].ui_gemport;
		st_omci_tapi_map.ui_tcont = st_map_pri.pri[0].ui_tcont;
		omci_tapi_map_del.ui_mask |= HI_OMCI_TAPI_MAP_VLAN;
	} else if (HI_MAC_BRI_PORT_IWTP_E == uc_tptype) {
		ui_ret = hi_omci_map_get_gemport(pst_state->ui_bp_id, &ui_gemport, &ui_tcont, &uc_interworking);
		HI_OMCI_RET_CHECK(ui_ret);

		if (6 == uc_interworking) { //Downstream broadcast
			return HI_RET_SUCC;
		}

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_VLAN;
		st_omci_tapi_map.ui_gemport = ui_gemport;
		st_omci_tapi_map.ui_tcont = ui_tcont;
		omci_tapi_map_del.ui_mask |= HI_OMCI_TAPI_MAP_VLAN;
	} else {
		hi_omci_ani_systrace(HI_RET_FAIL, uc_tptype, 0, 0, 0);
		return (hi_int32)HI_RET_FAIL;
	}

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VLAN_FILT_E, (hi_ushort16)pst_state->ui_bp_id, &st_vlanfilter);
	HI_OMCI_RET_CHECK(ui_ret);

	/* 释放旧有的 */
	for (ui_vlanid = 0; ui_vlanid < st_vlanfilter.uc_entitynum; ui_vlanid++) {
		ui_isfound = HI_FALSE;

		for (ui_index = 0; ui_index < pst_vlanfilter->uc_entitynum; ui_index++) {
			if (st_vlanfilter.us_vlantab[ui_vlanid] == pst_vlanfilter->us_vlantab[ui_index]) {
				ui_isfound = HI_TRUE;
				break;
			}
		}

		/* VLAN已经不在新的列表内 */
		if (ui_isfound == HI_FALSE) {
			omci_tapi_map_del.ui_vlan = HI_OMCI_GET_VLAN_FROM_TCI(ntohs(st_vlanfilter.us_vlantab[ui_vlanid]));

			hi_omci_debug("delete mask = 0x%08x, vlan = %010u, pri = %010u, port = %010d, gemport = %010u, tcont = %010u\n",
				      omci_tapi_map_del.ui_mask, omci_tapi_map_del.ui_vlan, omci_tapi_map_del.ui_pri, omci_tapi_map_del.em_port,
				      omci_tapi_map_del.ui_gemport, omci_tapi_map_del.ui_tcont);
			if (hi_omci_tapi_map_get(&omci_tapi_map_del) == HI_RET_SUCC) {
				ui_ret = hi_omci_tapi_map_del(&omci_tapi_map_del);
				HI_OMCI_RET_CHECK(ui_ret);
			}
		}
	}

	for (ui_vlanid = 0; ui_vlanid < pst_vlanfilter->uc_entitynum; ui_vlanid++) {
		ui_isfound = HI_FALSE;

		for (ui_index = 0; ui_index < st_vlanfilter.uc_entitynum; ui_index++) {
			if (pst_vlanfilter->us_vlantab[ui_vlanid] == st_vlanfilter.us_vlantab[ui_index]) {
				ui_isfound = HI_TRUE;
				break;
			}
		}

		/* VLAN不在就列表中 */
		if (ui_isfound == HI_FALSE) {
			st_omci_tapi_map.ui_vlan = HI_OMCI_GET_VLAN_FROM_TCI(ntohs(pst_vlanfilter->us_vlantab[ui_vlanid]));

			hi_omci_debug("add mask = 0x%08x, vlan = %010u, pri = %010u, port = %010d, gemport = %010u, tcont = %010u\n",
				      st_omci_tapi_map.ui_mask, st_omci_tapi_map.ui_vlan, st_omci_tapi_map.ui_pri, st_omci_tapi_map.em_port,
				      st_omci_tapi_map.ui_gemport, st_omci_tapi_map.ui_tcont);

			ui_ret = hi_omci_tapi_map_set(&st_omci_tapi_map);
			HI_OMCI_RET_CHECK(ui_ret);
		}
	}

	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

static uint32_t hi_omci_check_vlan_filter_untag(uint32_t bp_id)
{
	uint8_t forward_oper;
	uint8_t untag_fwd[] = {HI_TRUE, HI_TRUE, HI_FALSE, HI_TRUE, HI_FALSE, HI_TRUE, HI_FALSE,
		HI_TRUE, HI_FALSE, HI_TRUE, HI_FALSE, HI_TRUE, HI_FALSE, HI_TRUE, HI_FALSE, HI_TRUE, HI_FALSE,
		HI_TRUE, HI_FALSE, HI_TRUE, HI_FALSE, HI_FALSE, HI_TRUE, HI_FALSE, HI_TRUE, HI_FALSE, HI_TRUE,
		HI_FALSE, HI_TRUE, HI_FALSE, HI_TRUE, HI_FALSE, HI_TRUE, HI_FALSE};

	if (hi_omci_ext_get_attr(HI_OMCI_PRO_ME_VLAN_FILT_E, (uint16_t)bp_id, HI_OMCI_ATTR2, &forward_oper) == HI_RET_SUCC) {
		if (forward_oper >= (sizeof(untag_fwd) / sizeof(uint8_t)))
			return HI_FALSE;
		return untag_fwd[forward_oper];
	}
	return HI_FALSE;
}

static void hi_omci_map_set_pri_untag(hi_omci_map_pri_s map_pri, uint32_t pri, uint32_t action, uint32_t bp_id)
{
	hi_omci_tapi_map_s omci_tapi_map = {0};
	if ((hi_omci_check_vlan_filter_untag(bp_id) != HI_TRUE) || (map_pri.unmarked_frame_option == HI_DISABLE) ||
		(map_pri.default_pbit >= HI_OMCI_8021P_PRI_NUM) || (map_pri.default_pbit != pri)) {
		return;
	}
	omci_tapi_map.ui_vlan = 0x1000;
	omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_VLAN;
	omci_tapi_map.ui_gemport = map_pri.pri[map_pri.default_pbit].ui_gemport;
	omci_tapi_map.ui_tcont = map_pri.pri[map_pri.default_pbit].ui_tcont;
	if (action == 0) {
		hi_omci_tapi_map_set(&omci_tapi_map);
		return;
	}
	if (hi_omci_tapi_map_get(&omci_tapi_map) == HI_RET_SUCC)
		hi_omci_tapi_map_del(&omci_tapi_map);
}

/*****************************************************************************
  Function     : hi_omci_map_set_pri
  Description  : 配置优先级映射
  Input Param  : hi_omci_map_state_s *pst_state
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_set_pri(hi_omci_map_state_s *pst_state)
{
	hi_omci_map_pri_s st_map_pri;
	hi_omci_tapi_map_s st_omci_tapi_map;
	hi_uint32 ui_ret;
	hi_uint32 ui_portid;
	hi_uint32 ui_pri;

	HI_OS_MEMSET_S(&st_omci_tapi_map, sizeof(st_omci_tapi_map), 0, sizeof(st_omci_tapi_map));

	if (g_ui_port_map == HI_TRUE) {
		ui_ret = hi_omci_map_get_portid(pst_state->ui_bp_id, &ui_portid);
		HI_OMCI_RET_CHECK(ui_ret);

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_IGR;
		st_omci_tapi_map.em_port = (hi_omci_tapi_port_e)ui_portid;
	}

	ui_ret = hi_omci_map_get_pri(pst_state->ui_bp_id, &st_map_pri);
	HI_OMCI_RET_CHECK(ui_ret);

	st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_PRI;

	for (ui_pri = 0; ui_pri < HI_OMCI_8021P_PRI_NUM; ui_pri++) {
		if (HI_OMCI_MAP_INVALID_TCONT == st_map_pri.pri[ui_pri].ui_tcont) {
			continue;
		}

		st_omci_tapi_map.ui_pri = ui_pri;
		st_omci_tapi_map.ui_gemport = st_map_pri.pri[ui_pri].ui_gemport;
		st_omci_tapi_map.ui_tcont = st_map_pri.pri[ui_pri].ui_tcont;

		hi_omci_debug("mask = 0x%08x, vlan = %010u, pri = %010u, port = %010d, gemport = %010u, tcont = %010u\n",
			      st_omci_tapi_map.ui_mask, st_omci_tapi_map.ui_vlan, st_omci_tapi_map.ui_pri, st_omci_tapi_map.em_port,
			      st_omci_tapi_map.ui_gemport, st_omci_tapi_map.ui_tcont);

		ui_ret = hi_omci_tapi_map_set(&st_omci_tapi_map);
		HI_OMCI_RET_CHECK(ui_ret);
		hi_omci_map_set_pri_untag(st_map_pri, ui_pri, 0, pst_state->ui_bp_id);
	}
	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

static int32_t hi_omci_map_set_8021p_untag(hi_omci_map_state_s *state)
{
	hi_omci_map_pri_s map_pri;
	uint32_t ret;
	uint32_t pri;

	ret = hi_omci_map_get_pri(state->ui_bp_id, &map_pri);
	HI_OMCI_RET_CHECK(ret);
	for (pri = 0; pri < HI_OMCI_8021P_PRI_NUM; pri++) {
		if (map_pri.pri[pri].ui_tcont == HI_OMCI_MAP_INVALID_TCONT) {
			continue;
		}
		hi_omci_map_set_pri_untag(map_pri, pri, 0, state->ui_bp_id);
	}
	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_del_pri
  Description  : 删除优先级映射
  Input Param  : hi_omci_map_state_s *pst_state
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_del_pri(hi_omci_map_state_s *pst_state)
{
	hi_omci_tapi_map_s st_omci_tapi_map;
	hi_uint32 ui_ret;
	hi_uint32 ui_portid;
	hi_uint32 ui_pri;
	hi_omci_map_pri_s map_pri;

	HI_OS_MEMSET_S(&st_omci_tapi_map, sizeof(st_omci_tapi_map), 0, sizeof(st_omci_tapi_map));

	if (g_ui_port_map == HI_TRUE) {
		ui_ret = hi_omci_map_get_portid(pst_state->ui_bp_id, &ui_portid);
		HI_OMCI_RET_CHECK(ui_ret);

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_IGR;
		st_omci_tapi_map.em_port = (hi_omci_tapi_port_e)ui_portid;
	}

	st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_PRI;
	hi_omci_map_get_pri(pst_state->ui_bp_id, &map_pri);
	for (ui_pri = 0; ui_pri < HI_OMCI_8021P_PRI_NUM; ui_pri++) {
		st_omci_tapi_map.ui_pri = ui_pri;

		hi_omci_debug("mask = 0x%08x, vlan = %010u, pri = %010u, port = %010d, gemport = %010u, tcont = %010u\n",
			      st_omci_tapi_map.ui_mask, st_omci_tapi_map.ui_vlan, st_omci_tapi_map.ui_pri, st_omci_tapi_map.em_port,
			      st_omci_tapi_map.ui_gemport, st_omci_tapi_map.ui_tcont);

		ui_ret = hi_omci_tapi_map_get(&st_omci_tapi_map);
		if (ui_ret != HI_RET_SUCC) {
			continue;
		}

		hi_omci_debug("mask = 0x%08x, vlan = %010u, pri = %010u, port = %010d, gemport = %010u, tcont = %010u\n",
			      st_omci_tapi_map.ui_mask, st_omci_tapi_map.ui_vlan, st_omci_tapi_map.ui_pri, st_omci_tapi_map.em_port,
			      st_omci_tapi_map.ui_gemport, st_omci_tapi_map.ui_tcont);
		ui_ret = hi_omci_tapi_map_del(&st_omci_tapi_map);
		HI_OMCI_RET_CHECK(ui_ret);
		hi_omci_map_set_pri_untag(map_pri, ui_pri, 1, pst_state->ui_bp_id);
	}
	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_set_vlan_pri
  Description  : 配置VLAN+优先级映射
  Input Param  : hi_omci_map_state_s *pst_state
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_set_vlan_pri(hi_omci_map_state_s *pst_state)
{
	hi_omci_me_vlan_filt_s *pst_vlanfilter = (hi_omci_me_vlan_filt_s *)pst_state->pv_data;
	hi_omci_map_pri_s st_map_pri;
	hi_omci_tapi_map_s st_omci_tapi_map;
	hi_uint32 ui_ret;
	hi_uint32 ui_portid;
	hi_uint32 ui_vlanid;
	hi_uint32 ui_pri;

	HI_OS_MEMSET_S(&st_omci_tapi_map, sizeof(st_omci_tapi_map), 0, sizeof(st_omci_tapi_map));

	if (HI_TRUE == g_ui_port_map) {
		ui_ret = hi_omci_map_get_portid(pst_state->ui_bp_id, &ui_portid);
		HI_OMCI_RET_CHECK(ui_ret);

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_IGR;
		st_omci_tapi_map.em_port = (hi_omci_tapi_port_e)ui_portid;
	}

	ui_ret = hi_omci_map_get_pri(pst_state->ui_bp_id, &st_map_pri);
	HI_OMCI_RET_CHECK(ui_ret);

	st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_VLAN | HI_OMCI_TAPI_MAP_PRI;

	for (ui_vlanid = 0; ui_vlanid < pst_vlanfilter->uc_entitynum; ui_vlanid++) {
		for (ui_pri = 0; ui_pri < HI_OMCI_8021P_PRI_NUM; ui_pri++) {
			if (HI_OMCI_MAP_INVALID_TCONT == st_map_pri.pri[ui_pri].ui_tcont) {
				continue;
			}

			st_omci_tapi_map.ui_vlan = HI_OMCI_GET_VLAN_FROM_TCI(ntohs(pst_vlanfilter->us_vlantab[ui_vlanid]));
			st_omci_tapi_map.ui_pri = ui_pri;
			st_omci_tapi_map.ui_gemport = st_map_pri.pri[ui_pri].ui_gemport;
			st_omci_tapi_map.ui_tcont = st_map_pri.pri[ui_pri].ui_tcont;

			hi_omci_debug("mask = 0x%08x, vlan = %010u, pri = %010u, port = %010d, gemport = %010u, tcont = %010u\n",
				      st_omci_tapi_map.ui_mask, st_omci_tapi_map.ui_vlan, st_omci_tapi_map.ui_pri, st_omci_tapi_map.em_port,
				      st_omci_tapi_map.ui_gemport, st_omci_tapi_map.ui_tcont);

			ui_ret = hi_omci_tapi_map_set(&st_omci_tapi_map);
			HI_OMCI_RET_CHECK(ui_ret);
			hi_omci_map_set_pri_untag(st_map_pri, ui_pri, 0, pst_state->ui_bp_id);
		}
	}
	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_del_vlan_pri
  Description  : 删除VLAN+优先级映射
  Input Param  : hi_omci_map_state_s *pst_state
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
static hi_int32 hi_omci_map_del_vlan_pri(hi_omci_map_state_s *pst_state)
{
	hi_omci_me_vlan_filt_s st_vlanfilter;
	hi_omci_tapi_map_s st_omci_tapi_map;
	hi_uint32 ui_ret;
	hi_uint32 ui_portid;
	hi_uint32 ui_vlanid;
	hi_uint32 ui_pri;
	hi_omci_map_pri_s map_pri;

	HI_OS_MEMSET_S(&st_omci_tapi_map, sizeof(st_omci_tapi_map), 0, sizeof(st_omci_tapi_map));

	if (HI_TRUE == g_ui_port_map) {
		ui_ret = hi_omci_map_get_portid(pst_state->ui_bp_id, &ui_portid);
		HI_OMCI_RET_CHECK(ui_ret);

		st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_IGR;
		st_omci_tapi_map.em_port = (hi_omci_tapi_port_e)ui_portid;
	}

	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VLAN_FILT_E, (hi_ushort16)pst_state->ui_bp_id, &st_vlanfilter);
	HI_OMCI_RET_CHECK(ui_ret);

	st_omci_tapi_map.ui_mask |= HI_OMCI_TAPI_MAP_VLAN | HI_OMCI_TAPI_MAP_PRI;
	hi_omci_map_get_pri(pst_state->ui_bp_id, &map_pri);
	for (ui_vlanid = 0; ui_vlanid < st_vlanfilter.uc_entitynum; ui_vlanid++) {
		for (ui_pri = 0; ui_pri < HI_OMCI_8021P_PRI_NUM; ui_pri++) {
			st_omci_tapi_map.ui_vlan = HI_OMCI_GET_VLAN_FROM_TCI(ntohs(st_vlanfilter.us_vlantab[ui_vlanid]));
			st_omci_tapi_map.ui_pri = ui_pri;

			hi_omci_debug("mask = 0x%08x, vlan = %010u, pri = %010u, port = %010d, gemport = %010u, tcont = %010u\n",
				      st_omci_tapi_map.ui_mask, st_omci_tapi_map.ui_vlan, st_omci_tapi_map.ui_pri, st_omci_tapi_map.em_port,
				      st_omci_tapi_map.ui_gemport, st_omci_tapi_map.ui_tcont);

			ui_ret = hi_omci_tapi_map_get(&st_omci_tapi_map);
			if (ui_ret != HI_RET_SUCC) {
				continue;
			}
			ui_ret = hi_omci_tapi_map_del(&st_omci_tapi_map);
			HI_OMCI_RET_CHECK(ui_ret);
			hi_omci_map_set_pri_untag(map_pri, ui_pri, 1, pst_state->ui_bp_id);
		}
	}
	hi_omci_ani_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_map_state_init
 功能描述  : 初始化状态
 输入参数  : hi_omci_map_event_e em_event
             hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_map_state_init(hi_omci_map_event_e em_event, hi_void *pv_data)
{
	hi_omci_map_state_s *pst_state = (hi_omci_map_state_s *)pv_data;
	hi_uint32 ui_ret;
	hi_ushort16 us_8021pinst;
	hi_omci_me_8021p_s st_entry;



	switch (em_event) {
	case HI_OMCI_MAP_EVENT_BP_TO_8021P_E:
		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, (hi_ushort16)pst_state->ui_bp_id, HI_OMCI_ATTR4,
					      &us_8021pinst);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_802_1P_E, us_8021pinst, &st_entry);
		HI_OMCI_RET_CHECK(ui_ret);

		em_event = (hi_omci_map_event_e)hi_omci_me_8021p_check_mappri(&st_entry);
		if (HI_OMCI_MAP_EVENT_8021P_TO_SAME_GEMIWTP_E == em_event) {
			pst_state->em_state = HI_OMCI_MAP_STATE_VLAN_PRI;
		} else if (HI_OMCI_MAP_EVENT_8021P_TO_GEMIWTP_E == em_event) {
			pst_state->em_state = HI_OMCI_MAP_STATE_PRI;
		} else {
			pst_state->em_state = HI_OMCI_MAP_STATE_PRI_PREVIEW;
		}

		break;

	case HI_OMCI_MAP_EVENT_BP_TO_GEMIWTP_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_VLAN_PREVIEW;

		ui_ret = hi_omci_map_set_port(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);
		break;

	default:
		break;
	}

	hi_omci_ani_systrace(HI_RET_SUCC, em_event, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_map_state_pri_preview
 功能描述  : 优先级预处理状态
 输入参数  : hi_omci_map_event_e em_event
             hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_map_state_pri_preview(hi_omci_map_event_e em_event, hi_void *pv_data)
{
	hi_omci_map_state_s *pst_state = (hi_omci_map_state_s *)pv_data;
	hi_uint32 ui_ret;
	hi_ushort16 us_tppointer = 0, us_iwtp_ptr0 = 0, us_mark = 0;

	switch (em_event) {
	case HI_OMCI_MAP_EVENT_8021P_TO_GEMIWTP_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_PRI;

		ui_ret = hi_omci_map_del_port(pst_state);
		if (ui_ret != HI_RET_SUCC) {
			/* 不一定存在端口映射，此处只报warning */
			hi_omci_debug("%s error\n", __func__);
		}

		ui_ret = hi_omci_map_set_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_MAP_EVENT_8021P_TO_SAME_GEMIWTP_E:
		hi_omci_debug("%s g_ui_port_map %u\n", __func__, g_ui_port_map);
		if (g_ui_port_map == HI_TRUE) {
			ui_ret = hi_omci_map_set_port(pst_state);
			HI_OMCI_RET_CHECK(ui_ret);
		}

		break;

	case HI_OMCI_MAP_EVENT_BP_TO_8021P_E:
	case HI_OMCI_MAP_EVENT_BP_TO_VF_E:
		if (HI_RET_SUCC == hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, (hi_ushort16)pst_state->ui_bp_id,
							HI_OMCI_ATTR4, &us_tppointer)) {
			if (HI_RET_SUCC == hi_omci_ext_get_attr(HI_OMCI_PRO_ME_802_1P_E, us_tppointer, HI_OMCI_ATTR2, &us_iwtp_ptr0)) {
				if (HI_OMCI_8021P_INVALID_GEMIWTP != us_iwtp_ptr0) {
					pst_state->em_state = HI_OMCI_MAP_STATE_VLAN;
					ui_ret = hi_omci_map_set_vlan(pst_state);
					HI_OMCI_RET_CHECK(ui_ret);
					us_mark = 1;
				}
			}
		}

		if (0 == us_mark) {
			pst_state->em_state = HI_OMCI_MAP_STATE_VLAN_PRI_PREVIEW;
		}
		break;

	case HI_OMCI_MAP_EVENT_RELEASE:
	case HI_OMCI_MAP_EVENT_BP_RELEASE_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_INIT;

		ui_ret = hi_omci_map_del_port(pst_state);
		if (ui_ret != HI_RET_SUCC) {
			/* 不一定存在端口映射，此处只报warning */
			hi_omci_debug("%s error\n", __func__);
		}

		break;

	default:
		break;
	}

	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_state_vlan_preview
  Description  : VLAN预处理状态
  Input Param  : hi_omci_map_event_e em_event
                 hi_void *pv_data
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_map_state_vlan_preview(hi_omci_map_event_e em_event, hi_void *pv_data)
{
	hi_omci_map_state_s *pst_state = (hi_omci_map_state_s *)pv_data;
	hi_uint32 ui_ret;
	hi_uchar8 uc_tptype;
	hi_uint32 ui_gemport;
	hi_uint32 ui_tcont;
	hi_uchar8 uc_interworking;

	switch (em_event) {
	case HI_OMCI_MAP_EVENT_BP_TO_VF_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_VLAN;

		if (HI_RET_SUCC == hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, (hi_ushort16)pst_state->ui_bp_id,
							HI_OMCI_ATTR3, &uc_tptype)) {
			if (HI_MAC_BRI_PORT_IWTP_E == uc_tptype) {
				/* 暂时不支持Downstream broadcast类型的gemport */
				if (HI_RET_SUCC == hi_omci_map_get_gemport(pst_state->ui_bp_id, &ui_gemport, &ui_tcont, &uc_interworking)) {
					if (6 == uc_interworking) {
						break;
					}
				}
			}
		}
		ui_ret = hi_omci_map_set_vlan(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);
		break;

	case HI_OMCI_MAP_EVENT_RELEASE:
	case HI_OMCI_MAP_EVENT_BP_RELEASE_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_INIT;

		ui_ret = hi_omci_map_del_port(pst_state);
		if (ui_ret != HI_RET_SUCC) {
			/* 不一定存在端口映射，此处只报warning */
			hi_omci_debug("%s error\n", __func__);
		}
		break;

	default:
		break;
	}

	hi_omci_ani_systrace(HI_RET_SUCC, em_event, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_state_vlan_pri_preview
  Description  : VLAN+优先级预处理
  Input Param  : hi_omci_map_event_e em_event
                 hi_void *pv_data
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_map_state_vlan_pri_preview(hi_omci_map_event_e em_event, hi_void *pv_data)
{
	hi_omci_map_state_s *pst_state = (hi_omci_map_state_s *)pv_data;
	hi_omci_me_vlan_filt_s st_vlanfilter;
	hi_uint32 ui_ret;

	switch (em_event) {
	case HI_OMCI_MAP_EVENT_8021P_TO_GEMIWTP_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_VLAN_PRI;

		ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VLAN_FILT_E, (hi_ushort16)pst_state->ui_bp_id, &st_vlanfilter);
		HI_OMCI_RET_CHECK(ui_ret);

		pst_state->pv_data = (hi_omci_me_vlan_filt_s *)&st_vlanfilter;
		if (0 == st_vlanfilter.uc_entitynum && 1 == st_vlanfilter.uc_forward_oper) { //untag映射
			pst_state->em_state = HI_OMCI_MAP_STATE_VLAN;

			ui_ret = hi_omci_map_set_vlan(pst_state);
			HI_OMCI_RET_CHECK(ui_ret);
		} else {

			ui_ret = hi_omci_map_set_vlan_pri(pst_state);
			HI_OMCI_RET_CHECK(ui_ret);
		}

		break;

	case HI_OMCI_MAP_EVENT_8021P_TO_SAME_GEMIWTP_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_VLAN;

		ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VLAN_FILT_E, (hi_ushort16)pst_state->ui_bp_id, &st_vlanfilter);
		HI_OMCI_RET_CHECK(ui_ret);

		pst_state->pv_data = (hi_omci_me_vlan_filt_s *)&st_vlanfilter;
		ui_ret = hi_omci_map_set_vlan(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_MAP_EVENT_VF_RELEASE_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_PRI_PREVIEW;
		break;
	case HI_OMCI_MAP_EVENT_BP_RELEASE_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_INIT;
		break;
	default:
		break;
	}

	hi_omci_ani_systrace(HI_RET_SUCC, em_event, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_state_pri
  Description  : 优先级映射状态
  Input Param  : hi_omci_map_event_e em_event
                 hi_void *pv_data
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_map_state_pri(hi_omci_map_event_e em_event, hi_void *pv_data)
{
	hi_omci_map_state_s *pst_state = (hi_omci_map_state_s *)pv_data;
	hi_uint32 ui_ret;

	switch (em_event) {
	case HI_OMCI_MAP_EVENT_BP_TO_VF_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_VLAN_PRI;

		ui_ret = hi_omci_map_del_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_map_set_vlan_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_MAP_EVENT_8021P_TO_SAME_GEMIWTP_E:
	case HI_OMCI_MAP_EVENT_8021P_TO_GEMIWTP_E:

		ui_ret = hi_omci_map_del_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_map_set_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;
	case HI_OMCI_MAP_EVENT_BP_TO_8021P_E:
		ui_ret = hi_omci_map_del_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);
		ui_ret = hi_omci_map_set_8021p_untag(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);
		break;

	case HI_OMCI_MAP_EVENT_RELEASE:
		pst_state->em_state = HI_OMCI_MAP_STATE_INIT;

		ui_ret = hi_omci_map_del_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;
	case HI_OMCI_MAP_EVENT_8021P_RELEASE_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_PRI_PREVIEW;

		ui_ret = hi_omci_map_del_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	default:
		break;

	}

	hi_omci_ani_systrace(HI_RET_SUCC, em_event, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_state_vlan
  Description  : VLAN映射状态
  Input Param  : hi_omci_map_event_e em_event
                 hi_void *pv_data
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_map_state_vlan(hi_omci_map_event_e em_event, hi_void *pv_data)
{
	hi_omci_map_state_s *pst_state = (hi_omci_map_state_s *)pv_data;
	hi_omci_me_vlan_filt_s st_vlanfilter;
	hi_uint32 ui_ret;
	hi_ushort16 us_macportinst;
	hi_uchar8 uc_tptype;

	switch (em_event) {
	case HI_OMCI_MAP_EVENT_BP_TO_VF_E:
		ui_ret = hi_omci_map_update_vlan(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_MAP_EVENT_8021P_TO_GEMIWTP_E:

		us_macportinst = (hi_ushort16)pst_state->ui_bp_id;
		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_macportinst, HI_OMCI_ATTR3, &uc_tptype);
		HI_OMCI_RET_CHECK(ui_ret);

		if (HI_MAC_BRI_PORT_8021P_MAP_E == uc_tptype) {
			pst_state->em_state = HI_OMCI_MAP_STATE_VLAN_PRI;

			ui_ret = hi_omci_map_del_vlan(pst_state);
			HI_OMCI_RET_CHECK(ui_ret);

			ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VLAN_FILT_E, (hi_ushort16)pst_state->ui_bp_id, &st_vlanfilter);
			HI_OMCI_RET_CHECK(ui_ret);

			pst_state->pv_data = (hi_omci_me_vlan_filt_s *)&st_vlanfilter;
			ui_ret = hi_omci_map_set_vlan_pri(pst_state);
			HI_OMCI_RET_CHECK(ui_ret);
		}

		break;

	case HI_OMCI_MAP_EVENT_RELEASE:
		pst_state->em_state = HI_OMCI_MAP_STATE_INIT;

		ui_ret = hi_omci_map_del_vlan(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;
	case HI_OMCI_MAP_EVENT_VF_RELEASE_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_VLAN_PREVIEW;

		ui_ret = hi_omci_map_del_vlan(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_MAP_EVENT_8021P_RELEASE_E:

		us_macportinst = (hi_ushort16)pst_state->ui_bp_id;
		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, us_macportinst, HI_OMCI_ATTR3, &uc_tptype);
		HI_OMCI_RET_CHECK(ui_ret);

		if (HI_MAC_BRI_PORT_8021P_MAP_E == uc_tptype) {
			pst_state->em_state = HI_OMCI_MAP_STATE_VLAN_PRI_PREVIEW;

			ui_ret = hi_omci_map_del_vlan(pst_state);
			HI_OMCI_RET_CHECK(ui_ret);
		}

		break;

	default:
		break;
	}

	hi_omci_ani_systrace(HI_RET_SUCC, em_event, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_map_state_vlan_pri
  Description  : VLAN+优先级映射状态
  Input Param  : hi_omci_map_event_e em_event
                 hi_void *pv_data
  Output Param ：无
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_map_state_vlan_pri(hi_omci_map_event_e em_event, hi_void *pv_data)
{
	hi_omci_map_state_s *pst_state = (hi_omci_map_state_s *)pv_data;
	hi_omci_me_vlan_filt_s st_vlanfilter;
	hi_uint32 ui_ret;

	switch (em_event) {
	case HI_OMCI_MAP_EVENT_8021P_TO_SAME_GEMIWTP_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_VLAN;

		ui_ret = hi_omci_map_del_vlan_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VLAN_FILT_E, (hi_ushort16)pst_state->ui_bp_id, &st_vlanfilter);
		HI_OMCI_RET_CHECK(ui_ret);

		pst_state->pv_data = (hi_omci_me_vlan_filt_s *)&st_vlanfilter;
		ui_ret = hi_omci_map_set_vlan(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_MAP_EVENT_8021P_TO_GEMIWTP_E:

		ui_ret = hi_omci_map_del_vlan_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_VLAN_FILT_E, (hi_ushort16)pst_state->ui_bp_id, &st_vlanfilter);
		HI_OMCI_RET_CHECK(ui_ret);

		pst_state->pv_data = (hi_omci_me_vlan_filt_s *)&st_vlanfilter;
		ui_ret = hi_omci_map_set_vlan_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_MAP_EVENT_BP_TO_VF_E:

		ui_ret = hi_omci_map_del_vlan_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_map_set_vlan_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_MAP_EVENT_VF_RELEASE_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_PRI;

		ui_ret = hi_omci_map_del_vlan_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		ui_ret = hi_omci_map_del_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;

	case HI_OMCI_MAP_EVENT_RELEASE:
		pst_state->em_state = HI_OMCI_MAP_STATE_INIT;

		ui_ret = hi_omci_map_del_vlan_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;
	case HI_OMCI_MAP_EVENT_8021P_RELEASE_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_VLAN_PRI_PREVIEW;

		ui_ret = hi_omci_map_del_vlan_pri(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);

		break;
	case HI_OMCI_MAP_EVENT_BP_TO_GEMIWTP_E:
		pst_state->em_state = HI_OMCI_MAP_STATE_PRI_PREVIEW;
		ui_ret = hi_omci_map_set_port(pst_state);
		HI_OMCI_RET_CHECK(ui_ret);
		break;
	default:
		break;
	}

	hi_omci_ani_systrace(HI_RET_SUCC, em_event, 0, 0, 0);
	return HI_RET_SUCC;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
