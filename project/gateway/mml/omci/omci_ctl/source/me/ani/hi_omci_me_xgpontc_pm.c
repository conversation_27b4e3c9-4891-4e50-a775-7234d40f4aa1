/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_xgpontc_pm.c
  Version    : 初稿
  Author     :
  Creation   :
  Description: XG-PON TC performance monitoring history data
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
static hi_omci_tapi_stat_xgpontcpm_s gst_xgpontcpm_history;
static hi_omci_tapi_stat_xgpontcpm_s gst_xgpontcpm_prev_history;
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_xgpontcpm_stat_get
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgpontcpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_tapi_stat_xgpontcpm_s st_tcpm_curr;
	hi_omci_me_xgpontcpm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_tc_get(&st_tcpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减，将结构保存到数据库 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_XGPON_TC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_endtime++;
	st_entity.ui_psbdhecerr = (st_tcpm_curr.ui_psbdhecerr > gst_xgpontcpm_history.ui_psbdhecerr) ?
				  (st_tcpm_curr.ui_psbdhecerr - gst_xgpontcpm_history.ui_psbdhecerr) : 0;
	st_entity.ui_xgtchecerr = (st_tcpm_curr.ui_xgtchecerr > gst_xgpontcpm_history.ui_xgtchecerr) ?
				  (st_tcpm_curr.ui_xgtchecerr - gst_xgpontcpm_history.ui_xgtchecerr) : 0;
	st_entity.ui_unknownprofile = (st_tcpm_curr.ui_unknownprofile > gst_xgpontcpm_history.ui_unknownprofile) ?
				      (st_tcpm_curr.ui_unknownprofile - gst_xgpontcpm_history.ui_unknownprofile) : 0;
	st_entity.ui_transxgem = (st_tcpm_curr.ui_transxgem > gst_xgpontcpm_history.ui_transxgem) ?
				 (st_tcpm_curr.ui_transxgem - gst_xgpontcpm_history.ui_transxgem) : 0;
	st_entity.ui_fragxgem = (st_tcpm_curr.ui_fragxgem > gst_xgpontcpm_history.ui_fragxgem) ?
				(st_tcpm_curr.ui_fragxgem - gst_xgpontcpm_history.ui_fragxgem) : 0;
	st_entity.ui_xgemheclost = (st_tcpm_curr.ui_xgemheclost > gst_xgpontcpm_history.ui_xgemheclost) ?
				   (st_tcpm_curr.ui_xgemheclost - gst_xgpontcpm_history.ui_xgemheclost) : 0;
	st_entity.ui_xgemkeyerr = (st_tcpm_curr.ui_xgemkeyerr > gst_xgpontcpm_history.ui_xgemkeyerr) ?
				  (st_tcpm_curr.ui_xgemkeyerr - gst_xgpontcpm_history.ui_xgemkeyerr) : 0;
	st_entity.ui_xgemhecerr = (st_tcpm_curr.ui_xgemhecerr > gst_xgpontcpm_history.ui_xgemhecerr) ?
				  (st_tcpm_curr.ui_xgemhecerr - gst_xgpontcpm_history.ui_xgemhecerr) : 0;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_XGPON_TC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 将当前统计更新到历史统计 */
	HI_OS_MEMCPY_S(&gst_xgpontcpm_prev_history, sizeof(gst_xgpontcpm_prev_history), &gst_xgpontcpm_history,
		       sizeof(hi_omci_tapi_stat_xgpontcpm_s));
	HI_OS_MEMCPY_S(&gst_xgpontcpm_history, sizeof(gst_xgpontcpm_history), &st_tcpm_curr,
		       sizeof(hi_omci_tapi_stat_xgpontcpm_s));

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_xgpontcpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgpontcpm_s *pst_timer = (hi_omci_me_xgpontcpm_s *)pv_data;
	hi_omci_me_xgpontcpm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->st_msghead.us_instid;;

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_XGPON_TC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.ui_psbdhecerr = (gst_xgpontcpm_history.ui_psbdhecerr > gst_xgpontcpm_prev_history.ui_psbdhecerr) ?
				  (gst_xgpontcpm_history.ui_psbdhecerr - gst_xgpontcpm_prev_history.ui_psbdhecerr) : 0;
	st_entity.ui_xgtchecerr = (gst_xgpontcpm_history.ui_xgtchecerr > gst_xgpontcpm_prev_history.ui_xgtchecerr) ?
				  (gst_xgpontcpm_history.ui_xgtchecerr - gst_xgpontcpm_prev_history.ui_xgtchecerr) : 0;
	st_entity.ui_unknownprofile = (gst_xgpontcpm_history.ui_unknownprofile > gst_xgpontcpm_prev_history.ui_unknownprofile)
				      ? (gst_xgpontcpm_history.ui_unknownprofile -
					 gst_xgpontcpm_prev_history.ui_unknownprofile) : 0;
	st_entity.ui_transxgem = (gst_xgpontcpm_history.ui_transxgem > gst_xgpontcpm_prev_history.ui_transxgem) ?
				 (gst_xgpontcpm_history.ui_transxgem - gst_xgpontcpm_prev_history.ui_transxgem) : 0;
	st_entity.ui_fragxgem = (gst_xgpontcpm_history.ui_fragxgem > gst_xgpontcpm_prev_history.ui_fragxgem) ?
				(gst_xgpontcpm_history.ui_fragxgem - gst_xgpontcpm_prev_history.ui_fragxgem) : 0;
	st_entity.ui_xgemheclost = (gst_xgpontcpm_history.ui_xgemheclost > gst_xgpontcpm_prev_history.ui_xgemheclost) ?
				   (gst_xgpontcpm_history.ui_xgemheclost - gst_xgpontcpm_prev_history.ui_xgemheclost) : 0;
	st_entity.ui_xgemkeyerr = (gst_xgpontcpm_history.ui_xgemkeyerr > gst_xgpontcpm_prev_history.ui_xgemkeyerr) ?
				  (gst_xgpontcpm_history.ui_xgemkeyerr - gst_xgpontcpm_prev_history.ui_xgemkeyerr) : 0;
	st_entity.ui_xgemhecerr = (gst_xgpontcpm_history.ui_xgemhecerr > gst_xgpontcpm_prev_history.ui_xgemhecerr) ?
				  (gst_xgpontcpm_history.ui_xgemhecerr - gst_xgpontcpm_prev_history.ui_xgemhecerr) : 0;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_XGPON_TC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_xgpontcpm_getcurrent
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgpontcpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgpontcpm_s *pst_timer = (hi_omci_me_xgpontcpm_s *)pv_data;
	hi_omci_tapi_stat_xgpontcpm_s st_tcpm_curr;
	hi_omci_me_xgpontcpm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->st_msghead.us_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_tc_get(&st_tcpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减*/
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_XGPON_TC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_endtime++;
	st_entity.ui_psbdhecerr = (st_tcpm_curr.ui_psbdhecerr > gst_xgpontcpm_history.ui_psbdhecerr) ?
				  (st_tcpm_curr.ui_psbdhecerr - gst_xgpontcpm_history.ui_psbdhecerr) : 0;
	st_entity.ui_xgtchecerr = (st_tcpm_curr.ui_xgtchecerr > gst_xgpontcpm_history.ui_xgtchecerr) ?
				  (st_tcpm_curr.ui_xgtchecerr - gst_xgpontcpm_history.ui_xgtchecerr) : 0;
	st_entity.ui_unknownprofile = (st_tcpm_curr.ui_unknownprofile > gst_xgpontcpm_history.ui_unknownprofile) ?
				      (st_tcpm_curr.ui_unknownprofile - gst_xgpontcpm_history.ui_unknownprofile) : 0;
	st_entity.ui_transxgem = (st_tcpm_curr.ui_transxgem > gst_xgpontcpm_history.ui_transxgem) ?
				 (st_tcpm_curr.ui_transxgem - gst_xgpontcpm_history.ui_transxgem) : 0;
	st_entity.ui_fragxgem = (st_tcpm_curr.ui_fragxgem > gst_xgpontcpm_history.ui_fragxgem) ?
				(st_tcpm_curr.ui_fragxgem - gst_xgpontcpm_history.ui_fragxgem) : 0;
	st_entity.ui_xgemheclost = (st_tcpm_curr.ui_xgemheclost > gst_xgpontcpm_history.ui_xgemheclost) ?
				   (st_tcpm_curr.ui_xgemheclost - gst_xgpontcpm_history.ui_xgemheclost) : 0;
	st_entity.ui_xgemkeyerr = (st_tcpm_curr.ui_xgemkeyerr > gst_xgpontcpm_history.ui_xgemkeyerr) ?
				  (st_tcpm_curr.ui_xgemkeyerr - gst_xgpontcpm_history.ui_xgemkeyerr) : 0;
	st_entity.ui_xgemhecerr = (st_tcpm_curr.ui_xgemhecerr > gst_xgpontcpm_history.ui_xgemhecerr) ?
				  (st_tcpm_curr.ui_xgemhecerr - gst_xgpontcpm_history.ui_xgemhecerr) : 0;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_XGPON_TC_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_xgpontcpm_create
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgpontcpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgpontcpm_s *pst_entry = (hi_omci_me_xgpontcpm_s *)pv_data;
	hi_omci_me_stat_timer_s st_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	HI_OS_MEMSET_S(&gst_xgpontcpm_history, sizeof(gst_xgpontcpm_history), 0, sizeof(hi_omci_tapi_stat_xgpontcpm_s));
	HI_OS_MEMSET_S(&gst_xgpontcpm_prev_history, sizeof(gst_xgpontcpm_prev_history), 0,
		       sizeof(hi_omci_tapi_stat_xgpontcpm_s));

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_stat_timer, sizeof(st_stat_timer), 0, sizeof(hi_omci_me_stat_timer_s));
	st_stat_timer.ui_meid = HI_OMCI_PRO_ME_XGPON_TC_PM_E;
	st_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_xgpontcpm_delete
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgpontcpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgpontcpm_s *pst_entry = (hi_omci_me_xgpontcpm_s *)pv_data;
	hi_omci_me_stat_timer_s st_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 删除定时器 */
	HI_OS_MEMSET_S(&st_stat_timer, sizeof(st_stat_timer), 0, sizeof(hi_omci_me_stat_timer_s));
	st_stat_timer.ui_meid = HI_OMCI_PRO_ME_XGPON_TC_PM_E;
	st_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

