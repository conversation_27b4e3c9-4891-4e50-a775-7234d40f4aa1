/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_xgpon_downmanagement_pm.c
  Version    : 初稿
  Author     :
  Creation   :
  Description: XG-PON downstream management performance monitoring history data
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
static hi_omci_tapi_stat_xgpondownmng_pm_s gst_xgpondownmng_pm_history;
static hi_omci_tapi_stat_xgpondownmng_pm_s gst_xgpondownmng_pm_prev_history;
extern hi_uint32 gui_extend_omci_cnt;
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_xgpondownmngpm_stat_get
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgpondownmngpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_tapi_stat_xgpondownmng_pm_s st_downmngpm_curr;
	hi_omci_me_xgpondownmng_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_downmng_get(&st_downmngpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减，将结构保存到数据库 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_XGPON_DOWNMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_endtime++;
	st_entity.ui_ploammicerr = (st_downmngpm_curr.ui_ploammicerr > gst_xgpondownmng_pm_history.ui_ploammicerr) ?
				   (st_downmngpm_curr.ui_ploammicerr - gst_xgpondownmng_pm_history.ui_ploammicerr) : 0;
	st_entity.ui_dsploammesgcnt = (st_downmngpm_curr.ui_dsploammesgcnt > gst_xgpondownmng_pm_history.ui_dsploammesgcnt) ?
				      (st_downmngpm_curr.ui_dsploammesgcnt -
				       gst_xgpondownmng_pm_history.ui_dsploammesgcnt) : 0;
	st_entity.ui_profilemesgrcv = (st_downmngpm_curr.ui_profilemesgrcv > gst_xgpondownmng_pm_history.ui_profilemesgrcv) ?
				      (st_downmngpm_curr.ui_profilemesgrcv -
				       gst_xgpondownmng_pm_history.ui_profilemesgrcv) : 0;
	st_entity.ui_rangingtimemesgrcv = (st_downmngpm_curr.ui_rangingtimemesgrcv >
					   gst_xgpondownmng_pm_history.ui_rangingtimemesgrcv) ? (st_downmngpm_curr.ui_rangingtimemesgrcv -
							   gst_xgpondownmng_pm_history.ui_rangingtimemesgrcv) : 0;
	st_entity.ui_deactiveonuidmesgrcv = (st_downmngpm_curr.ui_deactiveonuidmesgrcv >
					     gst_xgpondownmng_pm_history.ui_deactiveonuidmesgrcv) ? (st_downmngpm_curr.ui_deactiveonuidmesgrcv -
							     gst_xgpondownmng_pm_history.ui_deactiveonuidmesgrcv) : 0;
	st_entity.ui_disablesnmesgrcv = (st_downmngpm_curr.ui_disablesnmesgrcv >
					 gst_xgpondownmng_pm_history.ui_disablesnmesgrcv) ? (st_downmngpm_curr.ui_disablesnmesgrcv -
							 gst_xgpondownmng_pm_history.ui_disablesnmesgrcv) : 0;
	st_entity.ui_reqregmesgrcv = (st_downmngpm_curr.ui_reqregmesgrcv > gst_xgpondownmng_pm_history.ui_reqregmesgrcv) ?
				     (st_downmngpm_curr.ui_reqregmesgrcv - gst_xgpondownmng_pm_history.ui_reqregmesgrcv) :
				     0;
	st_entity.ui_assignallocidmesgrcv = (st_downmngpm_curr.ui_assignallocidmesgrcv >
					     gst_xgpondownmng_pm_history.ui_assignallocidmesgrcv) ? (st_downmngpm_curr.ui_assignallocidmesgrcv -
							     gst_xgpondownmng_pm_history.ui_assignallocidmesgrcv) : 0;
	st_entity.ui_keycontrolmesgrcv = (st_downmngpm_curr.ui_keycontrolmesgrcv >
					  gst_xgpondownmng_pm_history.ui_keycontrolmesgrcv) ? (st_downmngpm_curr.ui_keycontrolmesgrcv -
							  gst_xgpondownmng_pm_history.ui_keycontrolmesgrcv) : 0;
	st_entity.ui_sleepallowmesgrcv = (st_downmngpm_curr.ui_sleepallowmesgrcv >
					  gst_xgpondownmng_pm_history.ui_sleepallowmesgrcv) ? (st_downmngpm_curr.ui_sleepallowmesgrcv -
							  gst_xgpondownmng_pm_history.ui_sleepallowmesgrcv) : 0;
	st_entity.ui_assignonuidmesgrcv = (st_downmngpm_curr.ui_assignonuidmesgrcv >
					   gst_xgpondownmng_pm_history.ui_assignonuidmesgrcv) ? (st_downmngpm_curr.ui_assignonuidmesgrcv -
							   gst_xgpondownmng_pm_history.ui_assignonuidmesgrcv) : 0;
	st_entity.ui_omcimicerr = (st_downmngpm_curr.ui_omcimicerr > gst_xgpondownmng_pm_history.ui_omcimicerr) ?
				  (st_downmngpm_curr.ui_omcimicerr - gst_xgpondownmng_pm_history.ui_omcimicerr) : 0;

	st_entity.ui_baselineomcimesgrcv = ((st_downmngpm_curr.ui_baselineomcimesgrcv - gui_extend_omci_cnt) >
					    gst_xgpondownmng_pm_history.ui_baselineomcimesgrcv) ?
					   (st_downmngpm_curr.ui_baselineomcimesgrcv - gui_extend_omci_cnt - gst_xgpondownmng_pm_history.ui_baselineomcimesgrcv) :
					   0;
	st_entity.ui_extomcimesgrcv = gui_extend_omci_cnt ? gui_extend_omci_cnt : 0;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_XGPON_DOWNMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 将当前统计更新到历史统计 */
	gui_extend_omci_cnt = 0;
	HI_OS_MEMCPY_S(&gst_xgpondownmng_pm_prev_history, sizeof(gst_xgpondownmng_pm_prev_history),
		       &gst_xgpondownmng_pm_history, sizeof(hi_omci_tapi_stat_xgpondownmng_pm_s));
	HI_OS_MEMCPY_S(&gst_xgpondownmng_pm_history, sizeof(gst_xgpondownmng_pm_history), &st_downmngpm_curr,
		       sizeof(hi_omci_tapi_stat_xgpondownmng_pm_s));

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_xgpondownmngpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgpondownmng_pm_s *pst_timer = (hi_omci_me_xgpondownmng_pm_s *)pv_data;
	hi_omci_me_xgpondownmng_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->st_msghead.us_instid;;

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_XGPON_DOWNMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.ui_ploammicerr = (gst_xgpondownmng_pm_history.ui_ploammicerr >
				    gst_xgpondownmng_pm_prev_history.ui_ploammicerr) ? (gst_xgpondownmng_pm_history.ui_ploammicerr -
						    gst_xgpondownmng_pm_prev_history.ui_ploammicerr) : 0;
	st_entity.ui_dsploammesgcnt = (gst_xgpondownmng_pm_history.ui_dsploammesgcnt >
				       gst_xgpondownmng_pm_prev_history.ui_dsploammesgcnt) ? (gst_xgpondownmng_pm_history.ui_dsploammesgcnt -
						       gst_xgpondownmng_pm_prev_history.ui_dsploammesgcnt) : 0;
	st_entity.ui_profilemesgrcv = (gst_xgpondownmng_pm_history.ui_profilemesgrcv >
				       gst_xgpondownmng_pm_prev_history.ui_profilemesgrcv) ? (gst_xgpondownmng_pm_history.ui_profilemesgrcv -
						       gst_xgpondownmng_pm_prev_history.ui_profilemesgrcv) : 0;
	st_entity.ui_rangingtimemesgrcv = (gst_xgpondownmng_pm_history.ui_rangingtimemesgrcv >
					   gst_xgpondownmng_pm_prev_history.ui_rangingtimemesgrcv) ?
					  (gst_xgpondownmng_pm_history.ui_rangingtimemesgrcv - gst_xgpondownmng_pm_prev_history.ui_rangingtimemesgrcv) : 0;
	st_entity.ui_deactiveonuidmesgrcv = (gst_xgpondownmng_pm_history.ui_deactiveonuidmesgrcv >
					     gst_xgpondownmng_pm_prev_history.ui_deactiveonuidmesgrcv) ?
					    (gst_xgpondownmng_pm_history.ui_deactiveonuidmesgrcv - gst_xgpondownmng_pm_prev_history.ui_deactiveonuidmesgrcv) : 0;
	st_entity.ui_disablesnmesgrcv = (gst_xgpondownmng_pm_history.ui_disablesnmesgrcv >
					 gst_xgpondownmng_pm_prev_history.ui_disablesnmesgrcv) ? (gst_xgpondownmng_pm_history.ui_disablesnmesgrcv -
							 gst_xgpondownmng_pm_prev_history.ui_disablesnmesgrcv) : 0;
	st_entity.ui_reqregmesgrcv = (gst_xgpondownmng_pm_history.ui_reqregmesgrcv >
				      gst_xgpondownmng_pm_prev_history.ui_reqregmesgrcv) ? (gst_xgpondownmng_pm_history.ui_reqregmesgrcv -
						      gst_xgpondownmng_pm_prev_history.ui_reqregmesgrcv) : 0;
	st_entity.ui_assignallocidmesgrcv = (gst_xgpondownmng_pm_history.ui_assignallocidmesgrcv >
					     gst_xgpondownmng_pm_prev_history.ui_assignallocidmesgrcv) ?
					    (gst_xgpondownmng_pm_history.ui_assignallocidmesgrcv - gst_xgpondownmng_pm_prev_history.ui_assignallocidmesgrcv) : 0;
	st_entity.ui_keycontrolmesgrcv = (gst_xgpondownmng_pm_history.ui_keycontrolmesgrcv >
					  gst_xgpondownmng_pm_prev_history.ui_keycontrolmesgrcv) ?
					 (gst_xgpondownmng_pm_history.ui_keycontrolmesgrcv - gst_xgpondownmng_pm_prev_history.ui_keycontrolmesgrcv) : 0;
	st_entity.ui_sleepallowmesgrcv = (gst_xgpondownmng_pm_history.ui_sleepallowmesgrcv >
					  gst_xgpondownmng_pm_prev_history.ui_sleepallowmesgrcv) ?
					 (gst_xgpondownmng_pm_history.ui_sleepallowmesgrcv - gst_xgpondownmng_pm_prev_history.ui_sleepallowmesgrcv) : 0;
	st_entity.ui_assignonuidmesgrcv = (gst_xgpondownmng_pm_history.ui_assignonuidmesgrcv >
					   gst_xgpondownmng_pm_prev_history.ui_assignonuidmesgrcv) ?
					  (gst_xgpondownmng_pm_history.ui_assignonuidmesgrcv - gst_xgpondownmng_pm_prev_history.ui_assignonuidmesgrcv) : 0;
	st_entity.ui_omcimicerr = (gst_xgpondownmng_pm_history.ui_omcimicerr > gst_xgpondownmng_pm_prev_history.ui_omcimicerr)
				  ? (gst_xgpondownmng_pm_history.ui_omcimicerr -
				     gst_xgpondownmng_pm_prev_history.ui_omcimicerr) : 0;

	st_entity.ui_baselineomcimesgrcv = (gst_xgpondownmng_pm_history.ui_baselineomcimesgrcv >
					    gst_xgpondownmng_pm_prev_history.ui_baselineomcimesgrcv) ?
					   (gst_xgpondownmng_pm_history.ui_baselineomcimesgrcv - gst_xgpondownmng_pm_prev_history.ui_baselineomcimesgrcv) : 0;
	st_entity.ui_extomcimesgrcv = gst_xgpondownmng_pm_history.ui_extomcimesgrcv ?
				      gst_xgpondownmng_pm_history.ui_extomcimesgrcv : 0;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_XGPON_DOWNMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_xgpondownmngpm_getcurrent
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgpondownmngpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgpondownmng_pm_s *pst_timer = (hi_omci_me_xgpondownmng_pm_s *)pv_data;
	hi_omci_tapi_stat_xgpondownmng_pm_s st_downmngpm_curr;
	hi_omci_me_xgpondownmng_pm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->st_msghead.us_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_downmng_get(&st_downmngpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减*/
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_XGPON_DOWNMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	st_entity.uc_endtime++;
	st_entity.ui_ploammicerr = (st_downmngpm_curr.ui_ploammicerr > gst_xgpondownmng_pm_history.ui_ploammicerr) ?
				   (st_downmngpm_curr.ui_ploammicerr - gst_xgpondownmng_pm_history.ui_ploammicerr) : 0;
	st_entity.ui_dsploammesgcnt = (st_downmngpm_curr.ui_dsploammesgcnt > gst_xgpondownmng_pm_history.ui_dsploammesgcnt) ?
				      (st_downmngpm_curr.ui_dsploammesgcnt -
				       gst_xgpondownmng_pm_history.ui_dsploammesgcnt) : 0;
	st_entity.ui_profilemesgrcv = (st_downmngpm_curr.ui_profilemesgrcv > gst_xgpondownmng_pm_history.ui_profilemesgrcv) ?
				      (st_downmngpm_curr.ui_profilemesgrcv -
				       gst_xgpondownmng_pm_history.ui_profilemesgrcv) : 0;
	st_entity.ui_rangingtimemesgrcv = (st_downmngpm_curr.ui_rangingtimemesgrcv >
					   gst_xgpondownmng_pm_history.ui_rangingtimemesgrcv) ? (st_downmngpm_curr.ui_rangingtimemesgrcv -
							   gst_xgpondownmng_pm_history.ui_rangingtimemesgrcv) : 0;
	st_entity.ui_deactiveonuidmesgrcv = (st_downmngpm_curr.ui_deactiveonuidmesgrcv >
					     gst_xgpondownmng_pm_history.ui_deactiveonuidmesgrcv) ? (st_downmngpm_curr.ui_deactiveonuidmesgrcv -
							     gst_xgpondownmng_pm_history.ui_deactiveonuidmesgrcv) : 0;
	st_entity.ui_disablesnmesgrcv = (st_downmngpm_curr.ui_disablesnmesgrcv >
					 gst_xgpondownmng_pm_history.ui_disablesnmesgrcv) ? (st_downmngpm_curr.ui_disablesnmesgrcv -
							 gst_xgpondownmng_pm_history.ui_disablesnmesgrcv) : 0;
	st_entity.ui_reqregmesgrcv = (st_downmngpm_curr.ui_reqregmesgrcv > gst_xgpondownmng_pm_history.ui_reqregmesgrcv) ?
				     (st_downmngpm_curr.ui_reqregmesgrcv - gst_xgpondownmng_pm_history.ui_reqregmesgrcv) :
				     0;
	st_entity.ui_assignallocidmesgrcv = (st_downmngpm_curr.ui_assignallocidmesgrcv >
					     gst_xgpondownmng_pm_history.ui_assignallocidmesgrcv) ? (st_downmngpm_curr.ui_assignallocidmesgrcv -
							     gst_xgpondownmng_pm_history.ui_assignallocidmesgrcv) : 0;
	st_entity.ui_keycontrolmesgrcv = (st_downmngpm_curr.ui_keycontrolmesgrcv >
					  gst_xgpondownmng_pm_history.ui_keycontrolmesgrcv) ? (st_downmngpm_curr.ui_keycontrolmesgrcv -
							  gst_xgpondownmng_pm_history.ui_keycontrolmesgrcv) : 0;
	st_entity.ui_sleepallowmesgrcv = (st_downmngpm_curr.ui_sleepallowmesgrcv >
					  gst_xgpondownmng_pm_history.ui_sleepallowmesgrcv) ? (st_downmngpm_curr.ui_sleepallowmesgrcv -
							  gst_xgpondownmng_pm_history.ui_sleepallowmesgrcv) : 0;
	st_entity.ui_assignonuidmesgrcv = (st_downmngpm_curr.ui_assignonuidmesgrcv >
					   gst_xgpondownmng_pm_history.ui_assignonuidmesgrcv) ? (st_downmngpm_curr.ui_assignonuidmesgrcv -
							   gst_xgpondownmng_pm_history.ui_assignonuidmesgrcv) : 0;
	st_entity.ui_omcimicerr = (st_downmngpm_curr.ui_omcimicerr > gst_xgpondownmng_pm_history.ui_omcimicerr) ?
				  (st_downmngpm_curr.ui_omcimicerr - gst_xgpondownmng_pm_history.ui_omcimicerr) : 0;

	st_entity.ui_baselineomcimesgrcv = ((st_downmngpm_curr.ui_baselineomcimesgrcv - gui_extend_omci_cnt) >
					    gst_xgpondownmng_pm_history.ui_baselineomcimesgrcv) ?
					   (st_downmngpm_curr.ui_baselineomcimesgrcv - gui_extend_omci_cnt - gst_xgpondownmng_pm_history.ui_baselineomcimesgrcv) :
					   0;
	st_entity.ui_extomcimesgrcv = gui_extend_omci_cnt ? gui_extend_omci_cnt : 0;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_XGPON_DOWNMNG_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_xgpondownmngpm_create
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgpondownmngpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgpondownmng_pm_s *pst_entry = (hi_omci_me_xgpondownmng_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	HI_OS_MEMSET_S(&gst_xgpondownmng_pm_history, sizeof(gst_xgpondownmng_pm_history), 0,
		       sizeof(hi_omci_tapi_stat_xgpondownmng_pm_s));
	HI_OS_MEMSET_S(&gst_xgpondownmng_pm_prev_history, sizeof(gst_xgpondownmng_pm_prev_history), 0,
		       sizeof(hi_omci_tapi_stat_xgpondownmng_pm_s));
	gui_extend_omci_cnt = 0;

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_stat_timer, sizeof(st_stat_timer), 0, sizeof(hi_omci_me_stat_timer_s));
	st_stat_timer.ui_meid = HI_OMCI_PRO_ME_XGPON_DOWNMNG_PM_E;
	st_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_xgpondownmng_pm_delete
 Description :
 Input Parm  :
 Output Parm :
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_xgpondownmngpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_xgpondownmng_pm_s *pst_entry = (hi_omci_me_xgpondownmng_pm_s *)pv_data;
	hi_omci_me_stat_timer_s st_stat_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 删除定时器 */
	HI_OS_MEMSET_S(&st_stat_timer, sizeof(st_stat_timer), 0, sizeof(hi_omci_me_stat_timer_s));
	st_stat_timer.ui_meid = HI_OMCI_PRO_ME_XGPON_DOWNMNG_PM_E;
	st_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

