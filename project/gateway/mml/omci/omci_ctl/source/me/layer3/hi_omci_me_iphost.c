/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_iphost.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
hi_uint32 gui_omci_iphost_mask[HI_OMCI_TAPI_EXTVLAN_TABLE_NUM] = {0};
extern hi_uint32 gui_omci_voip_flag;
extern hi_omci_tapi_voip_extvlan_s g_st_extvlan_table;
/******************************************************************************
 Function    : hi_omci_me_iphost_init
 Description : ip host init
 Input Parm  : hi_uint32 ui_portid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_iphost_init(hi_uint32 ui_stacknum)
{
	hi_omci_me_iphost_cfg_s st_entry;
	hi_int32 i_ret;

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E, (hi_ushort16)ui_stacknum);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = (hi_ushort16)ui_stacknum;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E, (hi_ushort16)ui_stacknum, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_iphost_reset()
{
	hi_uint32 ui_index;
	for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
		gui_omci_iphost_mask[ui_index] = 0;
	}
	return HI_RET_SUCC;
}

hi_int32 hi_omci_iptochar(hi_uchar8 *puc_ipdigital, hi_char8 *pc_ipchar, hi_uint32 ui_ipchar_len)
{
	if ((HI_NULL == pc_ipchar) || (HI_NULL == puc_ipdigital)) {
		return (1);
	}
    HI_OS_SPRINTF_S(pc_ipchar, ui_ipchar_len, "%hhu.%hhu.%hhu.%hhu", *puc_ipdigital, *(puc_ipdigital + 1),
	            *(puc_ipdigital + 2), *(puc_ipdigital + 3));
	pc_ipchar[15] = '\0';
	return HI_RET_SUCC;
}

hi_int32 is_delete_wan(hi_uint32 ui_index, hi_ushort16 us_mask, hi_omci_me_iphost_cfg_s *pst_entity)
{
	if ((g_st_extvlan_table.st_extvlan[ui_index].ui_mode == 0) &&
	    (us_mask == 0x9f00) &&
	    (pst_entity->ui_ip == 0) &&
	    (pst_entity->ui_mask == 0) &&
	    (pst_entity->uc_ipoption == 0) &&
	    (pst_entity->ui_gateway == 0)) {
		hi_omci_debug("[INFO]:mask=0x%x\n", us_mask);
		return HI_RET_SUCC;
	}
	return HI_RET_FAIL;
}

/******************************************************************************
 Function    : hi_omci_me_set_iphost_attr
 Description : 设置iphost属性
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_void hi_omci_me_set_iphost_attr(hi_void *pv_data, hi_uint32 ui_instid)
{
	hi_omci_me_iphost_cfg_s st_iphost;
	hi_omci_tapi_voip_iphost_s *pst_iphost = (hi_omci_tapi_voip_iphost_s *)pv_data;
	hi_uint32 ui_tmp = 0;
	hi_char8 ac_dns[16];

	if (HI_RET_SUCC != hi_omci_ext_get_inst(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E, ui_instid, &st_iphost)) {
		return;
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(gui_omci_iphost_mask[ui_instid], HI_OMCI_ATTR1)) {
		pst_iphost->us_dhcpmode = st_iphost.uc_ipoption;
		pst_iphost->us_dhcpen = 1;
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(gui_omci_iphost_mask[ui_instid], HI_OMCI_ATTR4)) {
		pst_iphost->ui_extipaddr = ntohl(st_iphost.ui_ip);
		pst_iphost->ui_ipaddren = (pst_iphost->ui_extipaddr == 0) ? 0 : 1;
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(gui_omci_iphost_mask[ui_instid], HI_OMCI_ATTR5)) {
		pst_iphost->ui_ipmask = ntohl(st_iphost.ui_mask);
		pst_iphost->ui_ipmasken = (pst_iphost->ui_ipmask == 0) ? 0 : 1;
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(gui_omci_iphost_mask[ui_instid], HI_OMCI_ATTR6)) {
		pst_iphost->ui_defagateway = ntohl(st_iphost.ui_gateway);
		pst_iphost->ui_gatewayen = (pst_iphost->ui_defagateway == 0) ? 0 : 1;
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(gui_omci_iphost_mask[ui_instid], HI_OMCI_ATTR7)) {
		ui_tmp = ntohl(st_iphost.ui_pridns);
		hi_omci_iptochar((hi_uchar8 *)&ui_tmp, (hi_char8 *)pst_iphost->auc_dnsserv,
				 sizeof(pst_iphost->auc_dnsserv));
		if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(gui_omci_iphost_mask[ui_instid], HI_OMCI_ATTR8)) {
			ui_tmp = ntohl(st_iphost.ui_secdns);
			hi_omci_iptochar((hi_uchar8 *)&ui_tmp, ac_dns, sizeof(ac_dns));
			HI_OS_STRCAT_S((hi_char8 *)pst_iphost->auc_dnsserv, sizeof(pst_iphost->auc_dnsserv),
				       ",");
			HI_OS_STRCAT_S((hi_char8 *)pst_iphost->auc_dnsserv, sizeof(pst_iphost->auc_dnsserv),
				       ac_dns);
		}
		pst_iphost->ui_dnsen = (ntohl(st_iphost.ui_pridns) == 0) ? 0 : 1;
	}
}

/******************************************************************************
 Function    : hi_omci_me_iphost_set
 Description : IP host config data
               在数据库操作后处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_iphost_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_iphost_cfg_s *pst_entity = (hi_omci_me_iphost_cfg_s *)pv_data;
	hi_omci_tapi_voip_iphost_s st_iphost;
	hi_char8 ac_dns[16];
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;
	hi_int32    i_ret = HI_RET_SUCC;
	hi_uint32 ui_tmp = 0;
	hi_uint32 ui_index;

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);

	hi_omci_debug("[INFO]:ipoption=%hhu,mac=%2x:%2x:%2x:%2x:%2x:%2x,ontid=%s,ip=%x,mask =%x\n", pst_entity->uc_ipoption,
		      pst_entity->uc_mac[0], \
		      pst_entity->uc_mac[1], pst_entity->uc_mac[2], pst_entity->uc_mac[3], pst_entity->uc_mac[4], pst_entity->uc_mac[5],
		      pst_entity->uc_ontid, pst_entity->ui_ip, pst_entity->ui_mask);
	hi_omci_debug("[INFO]:gateway=%x,pridns=%x,secdns=%x,curip=%x,curmask=%x,curgateway=%x,curpridns=%x,cursecdns=%x,domain=%s,hostname=%s\n",
		      pst_entity->ui_gateway, pst_entity->ui_pridns, pst_entity->ui_secdns, pst_entity->ui_curip, pst_entity->ui_curmask,
		      pst_entity->ui_curgateway, pst_entity->ui_curpridns, \
		      pst_entity->ui_cursecdns, pst_entity->uc_domain, pst_entity->uc_hostname);
	HI_OS_MEMSET_S(&st_iphost, sizeof(st_iphost), 0, sizeof(st_iphost));

	for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
		if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == us_instid) {
			break;
		}
	}
	if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM == ui_index) {
		return HI_RET_SUCC;
	}

	if (is_delete_wan(ui_index, us_mask, pst_entity) == HI_RET_SUCC) {
		gui_omci_iphost_mask[ui_index] = 0;
		return HI_RET_SUCC;
	}

	if (0 == g_st_extvlan_table.st_extvlan[ui_index].ui_mode) {

		gui_omci_iphost_mask[ui_index] |= us_mask;
		return HI_RET_SUCC;
	} else {
		st_iphost.us_flag = (hi_ushort16)(g_st_extvlan_table.st_extvlan[ui_index].ui_mode - 1);
	}
	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR1)) {
		if ((0 == pst_entity->uc_ipoption) && (0 == pst_entity->ui_ip) && (0 == pst_entity->ui_mask) &&
		    (0 == pst_entity->ui_gateway) && (0 == pst_entity->ui_pridns) && (0 == pst_entity->ui_secdns)) {
			hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
			return HI_RET_SUCC;
		}
		if (0 != gui_omci_voip_flag) {
			st_iphost.us_dhcpmode = pst_entity->uc_ipoption;
			st_iphost.us_dhcpen = 1;
			if (st_iphost.us_dhcpmode & 0x01) {
				i_ret = hi_omci_tapi_voip_wan_set(&st_iphost);
				HI_OMCI_RET_CHECK(i_ret);
			}
		}
		HI_OMCI_BIT_SET(gui_omci_iphost_mask[ui_index], (16 - HI_OMCI_ATTR1));
		if (st_iphost.us_dhcpmode & 0x01) {
			hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
			return HI_RET_SUCC;
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR4)) {
		if (0 != gui_omci_voip_flag) {
			st_iphost.ui_extipaddr = ntohl(pst_entity->ui_ip);
			st_iphost.ui_ipaddren = (st_iphost.ui_extipaddr == 0) ? 0 : 1;
		}
		HI_OMCI_BIT_SET(gui_omci_iphost_mask[ui_index], (16 - HI_OMCI_ATTR4));
	}
	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR5)) {
		if (0 != gui_omci_voip_flag) {
			st_iphost.ui_ipmask = ntohl(pst_entity->ui_mask);
			st_iphost.ui_ipmasken = (st_iphost.ui_ipmask == 0) ? 0 : 1;
		}
		HI_OMCI_BIT_SET(gui_omci_iphost_mask[ui_index], (16 - HI_OMCI_ATTR5));
	}
	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR6)) {
		if (0 != gui_omci_voip_flag) {
			st_iphost.ui_defagateway = ntohl(pst_entity->ui_gateway);
			st_iphost.ui_gatewayen = (st_iphost.ui_defagateway == 0) ? 0 : 1;
		}
		HI_OMCI_BIT_SET(gui_omci_iphost_mask[ui_index], (16 - HI_OMCI_ATTR6));
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR7)) {
		if (0 != gui_omci_voip_flag) {
			ui_tmp = ntohl(pst_entity->ui_pridns);
			hi_omci_iptochar((hi_uchar8 *)&ui_tmp, (hi_char8 *)st_iphost.auc_dnsserv, sizeof(st_iphost.auc_dnsserv));
			if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR8)) {
				ui_tmp = ntohl(pst_entity->ui_secdns);
				hi_omci_iptochar((hi_uchar8 *)&ui_tmp, ac_dns, sizeof(ac_dns));
				HI_OS_STRCAT_S((hi_char8 *)st_iphost.auc_dnsserv, sizeof(st_iphost.auc_dnsserv), ",");
				HI_OS_STRCAT_S((hi_char8 *)st_iphost.auc_dnsserv, sizeof(st_iphost.auc_dnsserv), ac_dns);
			}
			st_iphost.ui_dnsen = (ntohl(pst_entity->ui_pridns) == 0) ? 0 : 1;
		}
		HI_OMCI_BIT_SET(gui_omci_iphost_mask[ui_index], (16 - HI_OMCI_ATTR7));
		if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR8)) {
			HI_OMCI_BIT_SET(gui_omci_iphost_mask[ui_index], (16 - HI_OMCI_ATTR8));
		}
	}
	if ((1 == st_iphost.ui_ipaddren) || (1 == st_iphost.ui_ipmasken) || (1 == st_iphost.ui_gatewayen) ||
	    (1 == st_iphost.ui_dnsen)) {
		i_ret = hi_omci_tapi_voip_wan_set(&st_iphost);
		HI_OMCI_RET_CHECK(i_ret);
	}
	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_iphost_get
 Description : IP host config data
               在数据库操作前处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_iphost_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_iphost_cfg_s *pst_entity = (hi_omci_me_iphost_cfg_s *)pv_data;
	hi_omci_me_iphost_cfg_s st_entity;
	hi_omci_tapi_voip_iphost_s st_iphost;
	hi_uint32    i_ret = HI_RET_SUCC;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;
	hi_uint32 ui_index;

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, pst_entity->st_msghead.us_attmask);
	if (0 != gui_omci_voip_flag) {

		HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);

		HI_OS_MEMSET_S(st_entity.uc_mac, HI_OMCI_TAPI_VOIP_MAC_LEN, 0, HI_OMCI_TAPI_VOIP_MAC_LEN);
		st_entity.ui_curip = 0;
		st_entity.ui_curmask = 0;
		st_entity.ui_curgateway = 0;
		st_entity.ui_curpridns = 0;
		st_entity.ui_cursecdns = 0;

		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);

		for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
			if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == us_instid) {
				break;
			}
		}
		if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM == ui_index) {
			return HI_RET_SUCC;
		}
		HI_OS_MEMSET_S(&st_iphost, sizeof(st_iphost), 0, sizeof(st_iphost));

		if (0 == g_st_extvlan_table.st_extvlan[ui_index].ui_mode) {
			return HI_RET_SUCC;
		} else {
			st_iphost.us_flag = (hi_ushort16)(g_st_extvlan_table.st_extvlan[ui_index].ui_mode - 1);
		}

		if (HI_RET_SUCC != hi_omci_tapi_voip_wan_get(&st_iphost)) {
			hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
			return HI_RET_SUCC;
		}
		HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);

		st_entity.uc_ipoption = (hi_uchar8)st_iphost.us_dhcpmode;
		HI_OS_MEMCPY_S(st_entity.uc_mac, sizeof(st_entity.uc_mac), st_iphost.uc_mac, HI_OMCI_TAPI_VOIP_MAC_LEN);
		st_entity.ui_curip = htonl(st_iphost.ui_extipaddr);
		st_entity.ui_curmask = htonl(st_iphost.ui_ipmask);
		st_entity.ui_curgateway = htonl(st_iphost.ui_defagateway);
		st_entity.ui_curpridns = htonl(st_iphost.ui_pridns);
		st_entity.ui_cursecdns = htonl(st_iphost.ui_secdns);

		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_IP_HOST_CFG_DATA_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);

	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
