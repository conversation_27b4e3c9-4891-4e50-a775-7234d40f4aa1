/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_pptp_eth_uni.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-28
  Description: OMCI manager entity Physical path termination point Ethernet UNI file.
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

extern hi_uint32 gui_pptppots_tstrslt_proc;
/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_CONFIGURE_STATE_UNKNOW   0

#define HI_OMCI_ME_PPTPETHUNI_ALARM_LOS HI_OMCI_ME_ALARM_BITMAP(0)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
typedef enum {
	HI_OMCI_ETHCFG_AUTO_AUTO_E     = 0x00,/*Auto-Sensing*/
	HI_OMCI_ETHCFG_10_FULL_E       = 0x01,/*10BaseT*/
	HI_OMCI_ETHCFG_100_FULL_E      = 0x02,/*100BaseT*/
	HI_OMCI_ETHCFG_1000_FULL_E     = 0x03,/*Gigabit Ethernet*/
	HI_OMCI_ETHCFG_10_AUTO_E       = 0x10,/*10BaseT Auto-sensing*/
	HI_OMCI_ETHCFG_10_HALF_E       = 0x11,/*10BaseT (half duplex)*/
	HI_OMCI_ETHCFG_100_HALF_E      = 0x12,/*100BaseT (half duplex)*/
	HI_OMCI_ETHCFG_1000_HALF_E     = 0x13,/*Gigabit Ethernet (half duplex)*/
	HI_OMCI_ETHCFG_1000_AUTO_E     = 0x20 /*Gigabit Ethernet Auto-sensing*/
} hi_omci_ethuni_config_e;
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/*****************************************************************************
 函 数 名  : hi_omci_me_pptpethuni_alarm_get
 功能描述  : PPTP端口状态告警检查
 输入参数  : hi_void *pv_data
             hi_uint32 ui_inlen
 输出参数  : hi_uint32 *pui_outlen
 返 回 值  : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_me_pptpethuni_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_alarm_timer_s *pst_timer = (hi_omci_me_alarm_timer_s *)pv_data;
	hi_omci_tapi_port_link_type_e em_link;
	hi_omci_tapi_line_status_s st_info;
	hi_int32 i_ret;
	hi_ushort16 us_bitmap_curr = 0;
	hi_ushort16 us_bitmap_hist;
	hi_ushort16 us_instid = (hi_omci_tapi_port_e)pst_timer->ui_instid;
	hi_ushort16 us_portid;
	hi_uchar8 uc_arc;
	hi_uchar8 uc_tst_rslt[40] = {0};

	/* 利用告警定时器延后发送pptp pots test result消息 */
	HI_OS_MEMSET_S(&st_info, sizeof(st_info), 0, sizeof(st_info));
	if (HI_ENABLE == gui_pptppots_tstrslt_proc) {
		st_info.ui_lineidx = 1;
		i_ret = hi_omci_tapi_voip_line_status_get(&st_info);
		if (HI_RET_SUCC == i_ret) {
			if (1 == st_info.ui_server_status) {
				uc_tst_rslt[1] = 1;
			}
			hi_omci_proc_sendtestresult(HI_OMCI_PRO_ME_PPTP_POTS_UNI_E, HI_OMCI_GET_INSTID_FORM_VOIP_PORTID(1),
						    (hi_void *)&uc_tst_rslt);
			gui_pptppots_tstrslt_proc = HI_DISABLE;
		}
	}
	us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_instid);

	/* 读取当前芯片link up/down状态 */
	i_ret = hi_omci_tapi_port_link_get((hi_omci_tapi_port_e)us_portid, &em_link);
	HI_OMCI_RET_CHECK(i_ret);

	if (em_link == HI_OMCI_TAPI_LINK_DOWN_E) {
		us_bitmap_curr |= HI_OMCI_ME_PPTPETHUNI_ALARM_LOS;
	}

	/* 读取数据库当前状态 */
	i_ret = hi_omci_sql_alarm_inst_get(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, us_instid, &us_bitmap_hist);
	HI_OMCI_RET_CHECK(i_ret);

	/* 如果告警状态发生变化，更新数据库的告警状态 */
	if (us_bitmap_curr != us_bitmap_hist) {
		/* 更新告警状态 */
		i_ret = hi_omci_sql_alarm_inst_set(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);

		/* 获取实例的ARC标识 */
		i_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, us_instid, HI_OMCI_ATTR12, &uc_arc);
		HI_OMCI_RET_CHECK(i_ret);

		/* 如果当前不禁止告警上报则发生告警消息 */
		if (uc_arc == HI_DISABLE) {
			i_ret = hi_omci_proc_sendalarm(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, us_instid, us_bitmap_curr);
			if (HI_RET_SUCC != i_ret) {
				hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", i_ret, __LINE__);
				return HI_RET_SUCC;
			}
		}
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_pptpethuni_get
 Description : Physical path termination point Ethernet get
               在数据库操作前处理
               实时获取Configuration ind属性状态
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_pptpethuni_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_omci_me_pptpethuni_s *pst_entity = (hi_omci_me_pptpethuni_s *)pv_data;
	hi_omci_me_pptpethuni_s st_entity;
	hi_omci_tapi_port_ethmode_e em_workmode;
	hi_omci_tapi_port_link_type_e em_linksta;
	hi_omci_tapi_port_type_e em_porttype;
	hi_ushort16 us_instid;
	hi_ushort16 us_portid;

	/* get the instance ID */
	us_instid  = pst_entity->st_msghead.us_instid;
	us_portid  = HI_OMCI_GET_PORTID_FROM_INSTID(us_instid);

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, pst_entity->st_msghead.us_attmask);

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, us_instid, &st_entity);
	if (HI_RET_SUCC != i_ret) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

	i_ret = hi_omci_tapi_port_link_get((hi_omci_tapi_port_e)us_portid, &em_linksta);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_debug("[INFO]:em_linksta=%010d\n", em_linksta);

	if (em_linksta == HI_OMCI_TAPI_LINK_DOWN_E) {
		st_entity.uc_configind = HI_OMCI_ME_CONFIGURE_STATE_UNKNOW;
	} else {
		i_ret = hi_omci_tapi_port_ethmode_get((hi_omci_tapi_port_e)us_portid, &em_workmode);
		HI_OMCI_RET_CHECK(i_ret);

		hi_omci_debug("[INFO]:em_workmode=%010d\n", em_workmode);

		switch (em_workmode) {
		case HI_OMCI_TAPI_ETH_DUP_10M_E:
			st_entity.uc_configind = HI_OMCI_ETHCFG_10_FULL_E;
			break;
		case HI_OMCI_TAPI_ETH_DUP_100M_E:
			st_entity.uc_configind = HI_OMCI_ETHCFG_100_FULL_E;
			break;
		case HI_OMCI_TAPI_ETH_DUP_1000M_E:
		case HI_OMCI_TAPI_ETH_DUP_2500M_E:
			st_entity.uc_configind = HI_OMCI_ETHCFG_1000_FULL_E;
			break;
		case HI_OMCI_TAPI_ETH_HALF_10M_E:
			st_entity.uc_configind = HI_OMCI_ETHCFG_10_HALF_E;
			break;
		case HI_OMCI_TAPI_ETH_HALF_100M_E:
			st_entity.uc_configind = HI_OMCI_ETHCFG_100_HALF_E;
			break;
		case HI_OMCI_TAPI_ETH_HALF_1000M_E:
			st_entity.uc_configind = HI_OMCI_ETHCFG_1000_HALF_E;
			break;
		default:
			hi_omci_systrace(em_workmode, 0, 0, 0, 0);
			break;
		}
	}


	i_ret = hi_omci_tapi_port_type_get((hi_omci_tapi_port_e)us_portid, &em_porttype);
	HI_OMCI_RET_CHECK(i_ret);

	if (em_porttype == HI_OMCI_TAPI_PORT_10_100_BASET_E) {
		st_entity.uc_sensetype = HI_OMCI_ME_PLUGIN_UNIT_TYPE_10100BASET;
	} else if (em_porttype == HI_OMCI_TAPI_PORT_10_100_1000_BASET_E) {
		st_entity.uc_sensetype = HI_OMCI_ME_PLUGIN_UNIT_TYPE_101001000BASET;
	} else {
		st_entity.uc_sensetype = HI_OMCI_ME_PLUGIN_UNIT_TYPE_NOLIM;
	}

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, us_instid, &st_entity);
	if (HI_RET_SUCC != i_ret) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_pptpethuni_set
 Description : Physical path termination point Ethernet set
               在数据库操作后处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_pptpethuni_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32    i_ret = HI_RET_SUCC;
	hi_omci_me_pptpethuni_s *pst_entity = (hi_omci_me_pptpethuni_s *)pv_data;
	hi_omci_tapi_port_ethmode_e em_workmode = HI_OMCI_TAPI_ETH_AUTO_NEG_E;
	hi_uint32 ui_enable = 0;
	hi_ushort16 us_instid;
	hi_ushort16 us_mask;
	hi_ushort16 us_portid;
	hi_uchar8    uc_autoconfg = 0;

	us_instid  = pst_entity->st_msghead.us_instid;
	us_mask    = pst_entity->st_msghead.us_attmask;
	us_portid  = HI_OMCI_GET_PORTID_FROM_INSTID(us_instid);

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);

	hi_omci_debug("[INFO]:exptype=%hhu,sensetype=%hhu,autoconfg=%hhu,loopback =%hhu,adminstate =%hhu\n",
		      pst_entity->uc_exptype, pst_entity->uc_sensetype, pst_entity->uc_autoconfg, pst_entity->uc_loopback,
		      pst_entity->uc_adminstate);
	hi_omci_debug("[INFO]:operstate=%hhu,configind=%hhu,maxframe=%hu,pausetime =%hu,bridgeind =%hhu\n",
		      pst_entity->uc_operstate, pst_entity->uc_configind, pst_entity->us_maxframe, pst_entity->us_pausetime,
		      pst_entity->uc_bridgeind);

	/*Get Auto detection configuration and configure to device*/
	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR3)) {
		uc_autoconfg = pst_entity->uc_autoconfg;

		/*属性值的合法性检查*/
		if (((HI_OMCI_ETHIF_CFG_GIGA_ETH_E < uc_autoconfg) && (uc_autoconfg < HI_OMCI_ETHIF_CFG_10_BASE_T_AUTOSENSING_E))
		    || ((HI_OMCI_ETHIF_CFG_GIGA_ETH_HALF_DUPLEX_E < uc_autoconfg) &&
			(uc_autoconfg < HI_OMCI_ETHIF_CFG_GIGA_ETH_AUTOSENSING_E))
		    || ((HI_OMCI_ETHIF_CFG_GIGA_ETH_AUTOSENSING_E < uc_autoconfg))) {
			hi_omci_systrace(HI_OMCI_PRO_ERR_PARA_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PARA_ERR_E;
		}

		/*调用API来将属性值设置为自协商模式，10M,100M,1000M,*/
		switch (pst_entity->uc_autoconfg) {
		case HI_OMCI_ETHCFG_AUTO_AUTO_E:
			em_workmode = HI_OMCI_TAPI_ETH_AUTO_NEG_E;
			break;
		case HI_OMCI_ETHCFG_10_FULL_E:
			em_workmode = HI_OMCI_TAPI_ETH_DUP_10M_E;
			break;
		case HI_OMCI_ETHCFG_100_FULL_E:
			em_workmode = HI_OMCI_TAPI_ETH_DUP_100M_E;
			break;
		case HI_OMCI_ETHCFG_1000_FULL_E:
			em_workmode = HI_OMCI_TAPI_ETH_DUP_1000M_E;
			break;
		case HI_OMCI_ETHCFG_10_HALF_E:
			em_workmode = HI_OMCI_TAPI_ETH_HALF_10M_E;
			break;
		case HI_OMCI_ETHCFG_100_HALF_E:
			em_workmode = HI_OMCI_TAPI_ETH_HALF_100M_E;
			break;
		case HI_OMCI_ETHCFG_1000_HALF_E:
			em_workmode = HI_OMCI_TAPI_ETH_HALF_1000M_E;
			break;
		default:
			break;
		}
		i_ret = hi_omci_tapi_port_ethmode_set((hi_omci_tapi_port_e)us_portid, em_workmode);
		HI_OMCI_RET_CHECK(i_ret);
	}

	/* Ethernet loopback configuration */
	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR4)) {
		if (HI_OMCI_ETH_LOOPBACK == pst_entity->uc_loopback) {
			i_ret = hi_omci_tapi_port_loopback_set((hi_omci_tapi_port_e)us_portid, HI_OMCI_ENABLE);
			HI_OMCI_RET_CHECK(i_ret);
		} else if (HI_OMCI_ETH_NO_LOOPBACK == pst_entity->uc_loopback) {
			i_ret = hi_omci_tapi_port_loopback_set((hi_omci_tapi_port_e)us_portid, HI_OMCI_DISABLE);
			HI_OMCI_RET_CHECK(i_ret);
		}
	}

	/*Get Adminstate and configure to device*/
	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR5)) {
		if (HI_OMCI_ADMINSTATE_LOCK < pst_entity->uc_adminstate) {
			hi_omci_systrace(HI_OMCI_PRO_ERR_PARA_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PARA_ERR_E;
		}

		if (HI_OMCI_ADMINSTATE_UNLOCK == pst_entity->uc_adminstate) {
			ui_enable = HI_OMCI_ENABLE;
		} else if (HI_OMCI_ADMINSTATE_LOCK == pst_entity->uc_adminstate) {
			ui_enable = HI_OMCI_DISABLE;
		}

		i_ret = hi_omci_tapi_port_state_set((hi_omci_tapi_port_e)us_portid, ui_enable);
		if (HI_RET_SUCC != i_ret) {
			hi_omci_debug("[INFO]:ui_ret=0x%x\n", i_ret);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}
	}

	/*Get Pause time and configure to device*/
	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR10)) {
		if (0 < pst_entity->us_pausetime) {
			ui_enable = HI_OMCI_ENABLE;
		} else {
			ui_enable = HI_OMCI_DISABLE;
		}

		i_ret = hi_omci_tapi_port_pause_set((hi_omci_tapi_port_e)us_portid,  ui_enable);
		if (HI_RET_SUCC != i_ret) {
			hi_omci_debug("[INFO]:ui_ret=0x%x\n", i_ret);
			hi_omci_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_pptpethuni_create
 Description : PPTP ethernet UNI create
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_pptpethuni_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_pptpethuni_s *pst_entry = (hi_omci_me_pptpethuni_s *)pv_data;
	hi_omci_me_alarm_timer_s st_pptpethuni_alarm_timer;
	hi_omci_tapi_port_type_e em_porttype;
	hi_int32 i_ret;
	hi_uint32 ui_mtu;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_ushort16 us_portid;

	us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_instid);

	/* 初始化端口类型 */
	i_ret = hi_omci_tapi_port_type_get((hi_omci_tapi_port_e)us_portid, &em_porttype);
	HI_OMCI_RET_CHECK(i_ret);

	if (em_porttype == HI_OMCI_TAPI_PORT_10_100_BASET_E) {
		pst_entry->uc_sensetype = HI_OMCI_ME_PLUGIN_UNIT_TYPE_10100BASET;
	} else if (em_porttype == HI_OMCI_TAPI_PORT_10_100_1000_BASET_E) {
		pst_entry->uc_sensetype = HI_OMCI_ME_PLUGIN_UNIT_TYPE_101001000BASET;
	} else {
		pst_entry->uc_sensetype = HI_OMCI_ME_PLUGIN_UNIT_TYPE_NOLIM;
	}

	/* 初始化端口最大帧长 */
	i_ret = hi_omci_tapi_port_mtu_get((hi_omci_tapi_port_e)us_portid, &ui_mtu);
	HI_OMCI_RET_CHECK(i_ret);
	pst_entry->us_maxframe = 2000;

	pst_entry->uc_bridgeind = 2;
	pst_entry->uc_exptype = HI_OMCI_ME_PLUGIN_UNIT_TYPE_101001000BASET;
	pst_entry->uc_operstate = 0;
	/* 数据保存 */
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, us_instid, pst_entry);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建告警实体 */
	i_ret = hi_omci_sql_alarm_inst_create(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建3秒定时器，上报告警 */
	HI_OS_MEMSET_S(&st_pptpethuni_alarm_timer, sizeof(st_pptpethuni_alarm_timer), 0, sizeof(st_pptpethuni_alarm_timer));
	st_pptpethuni_alarm_timer.ui_meid = HI_OMCI_PRO_ME_PPTP_ETH_UNI_E;
	st_pptpethuni_alarm_timer.ui_instid = us_instid;
	st_pptpethuni_alarm_timer.ui_timeout = 3000;
	st_pptpethuni_alarm_timer.ui_local = HI_ENABLE;
	i_ret = hi_omci_me_alarm_start(&st_pptpethuni_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_pptpethuni_delete
 Description : PPTP ethernet UNI delete
 Input Parm  : hi_void *pv_data
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_pptpethuni_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_pptpethuni_s *pst_entry = (hi_omci_me_pptpethuni_s *)pv_data;
	hi_omci_me_alarm_timer_s st_pptpethuni_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 删除告警实体 */
	i_ret = hi_omci_sql_alarm_inst_delete(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_pptpethuni_alarm_timer, sizeof(st_pptpethuni_alarm_timer), 0, sizeof(st_pptpethuni_alarm_timer));
	st_pptpethuni_alarm_timer.ui_meid = HI_OMCI_PRO_ME_PPTP_ETH_UNI_E;
	st_pptpethuni_alarm_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_alarm_stop(&st_pptpethuni_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_pptpethuni_init
 Description : PPTP ethernet UNI init
               ONU初始化根据端口号创建PPTP ETH UNI实例
 Input Parm  : hi_uint32 ui_portid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_pptpethuni_init(hi_uint32 ui_portid)
{
	hi_omci_me_pptpethuni_s st_entry;
	hi_int32 i_ret;
	hi_uint32 ui_outlen;
	hi_ushort16 us_instid;

	i_ret = hi_omci_tapi_port_pptpinstid_get(ui_portid, &us_instid);
	HI_OMCI_RET_CHECK(i_ret);
	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_PPTP_ETH_UNI_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;

	i_ret = hi_omci_me_pptpethuni_create(&st_entry, sizeof(st_entry), &ui_outlen);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_pptpethuni_exit
 Description : PPTP ethernet UNI exit
               一般用于MIB reset时
 Input Parm  : hi_uint32 ui_portid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_pptpethuni_exit(hi_uint32 ui_portid)
{
	hi_omci_me_pptpethuni_s st_entry;
	hi_int32 i_ret;
	hi_uint32 ui_outlen;
	hi_ushort16 us_instid;

	i_ret = hi_omci_tapi_port_pptpinstid_get(ui_portid, &us_instid);
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;

	i_ret = hi_omci_me_pptpethuni_delete(&st_entry, sizeof(st_entry), &ui_outlen);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
