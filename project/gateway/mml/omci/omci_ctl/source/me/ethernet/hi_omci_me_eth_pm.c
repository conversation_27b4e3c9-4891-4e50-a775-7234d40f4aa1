/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_eth_pm.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-16
  Description: Ethernet performance monitoring history data
******************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_ETHPM_ALARM_FCSERR       HI_OMCI_ME_ALARM_BITMAP(1)
#define HI_OMCI_ME_ETHPM_ALARM_EXCESS       HI_OMCI_ME_ALARM_BITMAP(2)
#define HI_OMCI_ME_ETHPM_ALARM_LATE         HI_OMCI_ME_ALARM_BITMAP(3)
#define HI_OMCI_ME_ETHPM_ALARM_FRMLONG      HI_OMCI_ME_ALARM_BITMAP(4)
#define HI_OMCI_ME_ETHPM_ALARM_RX_OVERFLOW  HI_OMCI_ME_ALARM_BITMAP(5)
#define HI_OMCI_ME_ETHPM_ALARM_TX_OVERFLOW  HI_OMCI_ME_ALARM_BITMAP(6)
#define HI_OMCI_ME_ETHPM_ALARM_SINGLE_COL   HI_OMCI_ME_ALARM_BITMAP(7)
#define HI_OMCI_ME_ETHPM_ALARM_MULTI_COL    HI_OMCI_ME_ALARM_BITMAP(8)
#define HI_OMCI_ME_ETHPM_ALARM_SQE          HI_OMCI_ME_ALARM_BITMAP(9)
#define HI_OMCI_ME_ETHPM_ALARM_TX_DEFER     HI_OMCI_ME_ALARM_BITMAP(10)
#define HI_OMCI_ME_ETHPM_ALARM_TX_MACERR    HI_OMCI_ME_ALARM_BITMAP(11)
#define HI_OMCI_ME_ETHPM_ALARM_SENSE_ERR    HI_OMCI_ME_ALARM_BITMAP(12)
#define HI_OMCI_ME_ETHPM_ALARM_ALIGN_ERR    HI_OMCI_ME_ALARM_BITMAP(13)
#define HI_OMCI_ME_ETHPM_ALARM_RX_MACERR    HI_OMCI_ME_ALARM_BITMAP(14)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_omci_tapi_stat_eth_s gst_ethpm_history[HI_OMCI_ETH_PM3_MAX_NUM];
static hi_omci_tapi_stat_eth_s gst_ethpm_prev_history[HI_OMCI_ETH_PM3_MAX_NUM];
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_ethpm_alarm_get
 Description : ethernet data统计告警检查
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethpm_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_alarm_timer_s *pst_timer = (hi_omci_me_alarm_timer_s *)pv_data;
	hi_omci_me_ethpm_s st_entity;
	hi_omci_me_threshold1_s st_threshold1;
	hi_omci_me_threshold2_s st_threshold2;
	hi_int32 i_ret = 0;
	hi_ushort16 us_bitmap_curr = 0;
	hi_ushort16 us_bitmap_hist = 0;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	HI_OS_MEMSET_S(&st_threshold1, sizeof(st_threshold1), 0, sizeof(st_threshold1));
	HI_OS_MEMSET_S(&st_threshold2, sizeof(st_threshold2), 0, sizeof(st_threshold2));
	/* 读取当前芯片端口MAC统计 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 获取各参数阈值 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_THRESHOLD_DATA1_E, st_entity.us_thresid, &st_threshold1);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_THRESHOLD_DATA2_E, st_entity.us_thresid, &st_threshold2);
	HI_OMCI_RET_CHECK(i_ret);

	/* 比较是否超出阈值，确定是告警产生，还是撤销 */
	if (st_entity.ui_fcserrors > st_threshold1.ui_thres1) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_FCSERR;
	}

	if (st_entity.ui_excesscnt > st_threshold1.ui_thres2) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_EXCESS;
	}

	if (st_entity.ui_latecnt > st_threshold1.ui_thres3) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_LATE;
	}

	if (st_entity.ui_frmlong > st_threshold1.ui_thres4) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_FRMLONG;
	}

	if (st_entity.ui_overflowrcv > st_threshold1.ui_thres5) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_RX_OVERFLOW;
	}

	if (st_entity.ui_overflowtrans > st_threshold1.ui_thres6) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_TX_OVERFLOW;
	}

	if (st_entity.ui_singlcoll > st_threshold1.ui_thres7) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_SINGLE_COL;
	}

	if (st_entity.ui_mulcoll > st_threshold2.ui_thres8) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_MULTI_COL;
	}

	if (st_entity.ui_sqe > st_threshold2.ui_thres9) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_SQE;
	}

	if (st_entity.ui_defertrans > st_threshold2.ui_thres10) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_TX_DEFER;
	}

	if (st_entity.ui_mactranserr > st_threshold2.ui_thres11) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_TX_MACERR;
	}

	if (st_entity.ui_senseerr > st_threshold2.ui_thres12) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_SENSE_ERR;
	}

	if (st_entity.ui_alignerr > st_threshold2.ui_thres13) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_ALIGN_ERR;
	}

	if (st_entity.ui_macrcverr > st_threshold2.ui_thres14) {
		us_bitmap_curr |= HI_OMCI_ME_ETHPM_ALARM_RX_MACERR;
	}

	/* 读取数据库当前告警状态 */
	i_ret = hi_omci_sql_alarm_inst_get(HI_OMCI_PRO_ME_ETH_PM_E, us_instid, &us_bitmap_hist);
	HI_OMCI_RET_CHECK(i_ret);

	/* 如果告警状态发生变化，更新数据库的告警状态 */
	if (us_bitmap_curr != us_bitmap_hist) {
		i_ret = hi_omci_sql_alarm_inst_set(HI_OMCI_PRO_ME_ETH_PM_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = hi_omci_proc_sendalarm(HI_OMCI_PRO_ME_ETH_PM_E, us_instid, us_bitmap_curr);
		HI_OMCI_RET_CHECK(i_ret);
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethpm_stat_get
 Description : 获取eth pm统计
 Input Parm  : hi_void *pv_data       ethernet port data 历史累计统计数据
               hi_uint32 ui_inlen
 Output Parm : hi_void *pv_data       ethernet port data 当前累计统计数据
               hi_uint32 *pui_outlen  输出数据长度
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_stat_timer_s *pst_timer = (hi_omci_me_stat_timer_s *)pv_data;
	hi_omci_me_ethpm_s st_entity;
	hi_omci_tapi_stat_eth_s st_ethpm_curr;
	hi_int32 i_ret = 0;
	hi_ushort16 us_instid = (hi_ushort16)pst_timer->ui_instid;
	hi_ushort16 us_portid = 0;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	HI_OS_MEMSET_S(&st_ethpm_curr, sizeof(st_ethpm_curr), 0, sizeof(st_ethpm_curr));

	us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_instid);

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_eth_get((hi_omci_tapi_port_e)us_portid, &st_ethpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	/* 与历史数据相减，将结构保存到数据库 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_OMCI_ETH_PM3_MAX_NUM > us_portid) {
		st_entity.uc_endtime++;
		st_entity.ui_fcserrors = (st_ethpm_curr.ui_fcserrors > gst_ethpm_history[us_portid].ui_fcserrors) ?
					 (st_ethpm_curr.ui_fcserrors - gst_ethpm_history[us_portid].ui_fcserrors) : 0;
		st_entity.ui_excesscnt = (st_ethpm_curr.ui_excesscnt > gst_ethpm_history[us_portid].ui_excesscnt) ?
					 (st_ethpm_curr.ui_excesscnt - gst_ethpm_history[us_portid].ui_excesscnt) : 0;
		st_entity.ui_latecnt = (st_ethpm_curr.ui_latecnt > gst_ethpm_history[us_portid].ui_latecnt) ?
				       (st_ethpm_curr.ui_latecnt - gst_ethpm_history[us_portid].ui_latecnt) : 0;
		st_entity.ui_frmlong = (st_ethpm_curr.ui_frmlong > gst_ethpm_history[us_portid].ui_frmlong) ?
				       (st_ethpm_curr.ui_frmlong - gst_ethpm_history[us_portid].ui_frmlong) : 0;
		st_entity.ui_overflowrcv = (st_ethpm_curr.ui_overflowrcv > gst_ethpm_history[us_portid].ui_overflowrcv) ?
					   (st_ethpm_curr.ui_overflowrcv - gst_ethpm_history[us_portid].ui_overflowrcv) : 0;
		st_entity.ui_overflowtrans = (st_ethpm_curr.ui_overflowtrans > gst_ethpm_history[us_portid].ui_overflowtrans) ?
					     (st_ethpm_curr.ui_overflowtrans - gst_ethpm_history[us_portid].ui_overflowtrans) : 0;
		st_entity.ui_singlcoll = (st_ethpm_curr.ui_singlcoll > gst_ethpm_history[us_portid].ui_singlcoll) ?
					 (st_ethpm_curr.ui_singlcoll - gst_ethpm_history[us_portid].ui_singlcoll) : 0;
		st_entity.ui_mulcoll = (st_ethpm_curr.ui_mulcoll > gst_ethpm_history[us_portid].ui_mulcoll) ?
				       (st_ethpm_curr.ui_mulcoll - gst_ethpm_history[us_portid].ui_mulcoll) : 0;
		st_entity.ui_sqe = (st_ethpm_curr.ui_sqe > gst_ethpm_history[us_portid].ui_sqe) ? (st_ethpm_curr.ui_sqe -
				   gst_ethpm_history[us_portid].ui_sqe) : 0;
		st_entity.ui_defertrans = (st_ethpm_curr.ui_defertrans > gst_ethpm_history[us_portid].ui_defertrans) ?
					  (st_ethpm_curr.ui_defertrans - gst_ethpm_history[us_portid].ui_defertrans) : 0;
		st_entity.ui_mactranserr = (st_ethpm_curr.ui_mactranserr > gst_ethpm_history[us_portid].ui_mactranserr) ?
					   (st_ethpm_curr.ui_mactranserr - gst_ethpm_history[us_portid].ui_mactranserr) : 0;
		st_entity.ui_senseerr = (st_ethpm_curr.ui_senseerr > gst_ethpm_history[us_portid].ui_senseerr) ?
					(st_ethpm_curr.ui_senseerr - gst_ethpm_history[us_portid].ui_senseerr) : 0;
		st_entity.ui_alignerr = (st_ethpm_curr.ui_alignerr > gst_ethpm_history[us_portid].ui_alignerr) ?
					(st_ethpm_curr.ui_alignerr - gst_ethpm_history[us_portid].ui_alignerr) : 0;
		st_entity.ui_macrcverr = (st_ethpm_curr.ui_macrcverr > gst_ethpm_history[us_portid].ui_macrcverr) ?
					 (st_ethpm_curr.ui_macrcverr - gst_ethpm_history[us_portid].ui_macrcverr) : 0;

		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETH_PM_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);

		/* 将当前统计更新到历史统计 */
		HI_OS_MEMCPY_S(&gst_ethpm_prev_history[us_portid], sizeof(gst_ethpm_prev_history[us_portid]),
			       &gst_ethpm_history[us_portid], sizeof(hi_omci_tapi_stat_eth_s));
		HI_OS_MEMCPY_S(&gst_ethpm_history[us_portid], sizeof(gst_ethpm_history[us_portid]), &st_ethpm_curr,
			       sizeof(hi_omci_tapi_stat_eth_s));
	}


	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_ethpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm_s *pst_entry = (hi_omci_me_ethpm_s *)pv_data;
	hi_omci_me_ethpm_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_ushort16 us_portid;

	/*获取ETH PM实例数据 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_instid);
	if (HI_OMCI_ETH_PM3_MAX_NUM > us_portid) {
		st_entity.ui_fcserrors = (gst_ethpm_history[us_portid].ui_fcserrors > gst_ethpm_prev_history[us_portid].ui_fcserrors)
					 ? (gst_ethpm_history[us_portid].ui_fcserrors -
					    gst_ethpm_prev_history[us_portid].ui_fcserrors) : 0;
		st_entity.ui_excesscnt = (gst_ethpm_history[us_portid].ui_excesscnt > gst_ethpm_prev_history[us_portid].ui_excesscnt)
					 ? (gst_ethpm_history[us_portid].ui_excesscnt -
					    gst_ethpm_prev_history[us_portid].ui_excesscnt) : 0;
		st_entity.ui_latecnt = (gst_ethpm_history[us_portid].ui_latecnt > gst_ethpm_prev_history[us_portid].ui_latecnt) ?
				       (gst_ethpm_history[us_portid].ui_latecnt -
					gst_ethpm_prev_history[us_portid].ui_latecnt) : 0;
		st_entity.ui_frmlong = (gst_ethpm_history[us_portid].ui_frmlong > gst_ethpm_prev_history[us_portid].ui_frmlong) ?
				       (gst_ethpm_history[us_portid].ui_frmlong -
					gst_ethpm_prev_history[us_portid].ui_frmlong) : 0;
		st_entity.ui_overflowrcv = (gst_ethpm_history[us_portid].ui_overflowrcv >
					    gst_ethpm_prev_history[us_portid].ui_overflowrcv) ? (gst_ethpm_history[us_portid].ui_overflowrcv -
							    gst_ethpm_prev_history[us_portid].ui_overflowrcv) : 0;
		st_entity.ui_overflowtrans = (gst_ethpm_history[us_portid].ui_overflowtrans >
					      gst_ethpm_prev_history[us_portid].ui_overflowtrans) ? (gst_ethpm_history[us_portid].ui_overflowtrans -
							      gst_ethpm_prev_history[us_portid].ui_overflowtrans) : 0;
		st_entity.ui_singlcoll = (gst_ethpm_history[us_portid].ui_singlcoll > gst_ethpm_prev_history[us_portid].ui_singlcoll)
					 ? (gst_ethpm_history[us_portid].ui_singlcoll -
					    gst_ethpm_prev_history[us_portid].ui_singlcoll) : 0;
		st_entity.ui_mulcoll = (gst_ethpm_history[us_portid].ui_mulcoll > gst_ethpm_prev_history[us_portid].ui_mulcoll) ?
				       (gst_ethpm_history[us_portid].ui_mulcoll -
					gst_ethpm_prev_history[us_portid].ui_mulcoll) : 0;
		st_entity.ui_sqe = (gst_ethpm_history[us_portid].ui_sqe > gst_ethpm_prev_history[us_portid].ui_sqe) ?
				   (gst_ethpm_history[us_portid].ui_sqe - gst_ethpm_prev_history[us_portid].ui_sqe) : 0;
		st_entity.ui_defertrans = (gst_ethpm_history[us_portid].ui_defertrans > gst_ethpm_prev_history[us_portid].ui_defertrans)
					  ? (gst_ethpm_history[us_portid].ui_defertrans -
					     gst_ethpm_prev_history[us_portid].ui_defertrans) : 0;
		st_entity.ui_mactranserr = (gst_ethpm_history[us_portid].ui_mactranserr >
					    gst_ethpm_prev_history[us_portid].ui_mactranserr) ? (gst_ethpm_history[us_portid].ui_mactranserr -
							    gst_ethpm_prev_history[us_portid].ui_mactranserr) : 0;
		st_entity.ui_senseerr = (gst_ethpm_history[us_portid].ui_senseerr > gst_ethpm_prev_history[us_portid].ui_senseerr) ?
					(gst_ethpm_history[us_portid].ui_senseerr -
					 gst_ethpm_prev_history[us_portid].ui_senseerr) : 0;
		st_entity.ui_alignerr = (gst_ethpm_history[us_portid].ui_alignerr > gst_ethpm_prev_history[us_portid].ui_alignerr) ?
					(gst_ethpm_history[us_portid].ui_alignerr -
					 gst_ethpm_prev_history[us_portid].ui_alignerr) : 0;
		st_entity.ui_macrcverr = (gst_ethpm_history[us_portid].ui_macrcverr > gst_ethpm_prev_history[us_portid].ui_macrcverr)
					 ? (gst_ethpm_history[us_portid].ui_macrcverr -
					    gst_ethpm_prev_history[us_portid].ui_macrcverr) : 0;

		/*将清零的数据保存到ETH PM实例数据中*/
		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETH_PM_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}


	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_ethpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm_s *pst_entry = (hi_omci_me_ethpm_s *)pv_data;
	hi_omci_me_ethpm_s st_entity;
	hi_omci_tapi_stat_eth_s st_ethpm_curr;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;
	hi_ushort16 us_portid = HI_OMCI_GET_PORTID_FROM_INSTID(us_instid);

	/*获取ETH PM实例数据 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ETH_PM_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	/* 读取芯片当前统计 */
	i_ret = hi_omci_tapi_stat_eth_get((hi_omci_tapi_port_e)us_portid, &st_ethpm_curr);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_OMCI_ETH_PM3_MAX_NUM > us_portid) {
		st_entity.ui_fcserrors = (st_ethpm_curr.ui_fcserrors > gst_ethpm_history[us_portid].ui_fcserrors) ?
					 (st_ethpm_curr.ui_fcserrors - gst_ethpm_history[us_portid].ui_fcserrors) : 0;
		st_entity.ui_excesscnt = (st_ethpm_curr.ui_excesscnt > gst_ethpm_history[us_portid].ui_excesscnt) ?
					 (st_ethpm_curr.ui_excesscnt - gst_ethpm_history[us_portid].ui_excesscnt) : 0 ;
		st_entity.ui_latecnt = (st_ethpm_curr.ui_latecnt > gst_ethpm_history[us_portid].ui_latecnt) ?
				       (st_ethpm_curr.ui_latecnt - gst_ethpm_history[us_portid].ui_latecnt) : 0 ;
		st_entity.ui_frmlong = (st_ethpm_curr.ui_frmlong > gst_ethpm_history[us_portid].ui_frmlong) ?
				       (st_ethpm_curr.ui_frmlong - gst_ethpm_history[us_portid].ui_frmlong) : 0 ;
		st_entity.ui_overflowrcv = (st_ethpm_curr.ui_overflowrcv > gst_ethpm_history[us_portid].ui_overflowrcv) ?
					   (st_ethpm_curr.ui_overflowrcv - gst_ethpm_history[us_portid].ui_overflowrcv) : 0 ;
		st_entity.ui_overflowtrans = (st_ethpm_curr.ui_overflowtrans > gst_ethpm_history[us_portid].ui_overflowtrans) ?
					     (st_ethpm_curr.ui_overflowtrans - gst_ethpm_history[us_portid].ui_overflowtrans) : 0 ;
		st_entity.ui_singlcoll = (st_ethpm_curr.ui_singlcoll > gst_ethpm_history[us_portid].ui_singlcoll) ?
					 (st_ethpm_curr.ui_singlcoll - gst_ethpm_history[us_portid].ui_singlcoll) : 0 ;
		st_entity.ui_mulcoll = (st_ethpm_curr.ui_mulcoll > gst_ethpm_history[us_portid].ui_mulcoll) ?
				       (st_ethpm_curr.ui_mulcoll - gst_ethpm_history[us_portid].ui_mulcoll) : 0 ;
		st_entity.ui_sqe = (st_ethpm_curr.ui_sqe > gst_ethpm_history[us_portid].ui_sqe) ? (st_ethpm_curr.ui_sqe -
				   gst_ethpm_history[us_portid].ui_sqe) : 0 ;
		st_entity.ui_defertrans = (st_ethpm_curr.ui_defertrans > gst_ethpm_history[us_portid].ui_defertrans) ?
					  (st_ethpm_curr.ui_defertrans - gst_ethpm_history[us_portid].ui_defertrans) : 0;
		st_entity.ui_mactranserr = (st_ethpm_curr.ui_mactranserr > gst_ethpm_history[us_portid].ui_mactranserr) ?
					   (st_ethpm_curr.ui_mactranserr - gst_ethpm_history[us_portid].ui_mactranserr) : 0 ;
		st_entity.ui_senseerr = (st_ethpm_curr.ui_senseerr > gst_ethpm_history[us_portid].ui_senseerr) ?
					(st_ethpm_curr.ui_senseerr - gst_ethpm_history[us_portid].ui_senseerr) : 0;
		st_entity.ui_alignerr = (st_ethpm_curr.ui_alignerr > gst_ethpm_history[us_portid].ui_alignerr) ?
					(st_ethpm_curr.ui_alignerr - gst_ethpm_history[us_portid].ui_alignerr) : 0;
		st_entity.ui_macrcverr = (st_ethpm_curr.ui_macrcverr > gst_ethpm_history[us_portid].ui_macrcverr) ?
					 (st_ethpm_curr.ui_macrcverr - gst_ethpm_history[us_portid].ui_macrcverr) : 0;

		/*将数据保存到ETH PM实例数据中*/
		i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ETH_PM_E, us_instid, &st_entity);
		HI_OMCI_RET_CHECK(i_ret);
	}


	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_me_ethpm_create
 Description : Ethernet performance monitoring history data create
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm_s *pst_entry = (hi_omci_me_ethpm_s *)pv_data;
	hi_omci_me_stat_timer_s st_ethpm_stat_timer;
	hi_omci_me_alarm_timer_s st_ethpm_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	HI_OS_MEMSET_S(&gst_ethpm_prev_history[0], sizeof(hi_omci_tapi_stat_eth_s) * HI_OMCI_ETH_PM3_MAX_NUM, 0,
		       sizeof(hi_omci_tapi_stat_eth_s) * HI_OMCI_ETH_PM3_MAX_NUM);
	HI_OS_MEMSET_S(&gst_ethpm_history[0], sizeof(hi_omci_tapi_stat_eth_s) * HI_OMCI_ETH_PM3_MAX_NUM, 0,
		       sizeof(hi_omci_tapi_stat_eth_s) * HI_OMCI_ETH_PM3_MAX_NUM);

	/* 创建ETH PM告警实体 */
	i_ret = hi_omci_sql_alarm_inst_create(HI_OMCI_PRO_ME_ETH_PM_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建15分钟定时器，获取统计 */
	HI_OS_MEMSET_S(&st_ethpm_stat_timer, sizeof(st_ethpm_stat_timer), 0, sizeof(st_ethpm_stat_timer));
	st_ethpm_stat_timer.ui_meid = HI_OMCI_PRO_ME_ETH_PM_E;
	st_ethpm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_start(&st_ethpm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	/* 创建15分钟定时器，上报告警 */
	HI_OS_MEMSET_S(&st_ethpm_alarm_timer, sizeof(st_ethpm_alarm_timer), 0, sizeof(st_ethpm_alarm_timer));
	st_ethpm_alarm_timer.ui_meid = HI_OMCI_PRO_ME_ETH_PM_E;
	st_ethpm_alarm_timer.ui_instid = us_instid;
	st_ethpm_alarm_timer.ui_timeout = HI_OMCI_ME_ALARM_COMM_TIMEOUT;
	i_ret = hi_omci_me_alarm_start(&st_ethpm_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_ethpm_delete
 Description : Ethernet performance monitoring history data delete
 Input Parm  : hi_void *pv_data,
               hi_uint32 ui_inlen
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_ethpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_omci_me_ethpm_s *pst_entry = (hi_omci_me_ethpm_s *)pv_data;
	hi_omci_me_stat_timer_s st_ethpm_stat_timer;
	hi_omci_me_alarm_timer_s st_ethpm_alarm_timer;
	hi_int32 i_ret;
	hi_ushort16 us_instid = pst_entry->st_msghead.us_instid;

	/* 创建ETH PM告警实体 */
	i_ret = hi_omci_sql_alarm_inst_delete(HI_OMCI_PRO_ME_ETH_PM_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_ethpm_stat_timer, sizeof(st_ethpm_stat_timer), 0, sizeof(st_ethpm_stat_timer));
	st_ethpm_stat_timer.ui_meid = HI_OMCI_PRO_ME_ETH_PM_E;
	st_ethpm_stat_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_stat_stop(&st_ethpm_stat_timer);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_ethpm_alarm_timer, sizeof(st_ethpm_alarm_timer), 0, sizeof(st_ethpm_alarm_timer));
	st_ethpm_alarm_timer.ui_meid = HI_OMCI_PRO_ME_ETH_PM_E;
	st_ethpm_alarm_timer.ui_instid = us_instid;
	i_ret = hi_omci_me_alarm_stop(&st_ethpm_alarm_timer);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

