/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_veip.c
  Version    : 初稿
  Author     :
  Creation   :
  Description: VEIP
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
hi_uint32 gui_omci_tr069_extvlan_mask = 0;
hi_uint32 gui_omci_veip_mask = 0;
extern hi_omci_tapi_voip_extvlan_s g_st_extvlan_table;
extern hi_uint32 gui_omci_voip_flag;
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

hi_int32 hi_omci_me_veip_init()
{
	hi_omci_me_veip_s st_entry;
	hi_int32 i_ret;
	hi_ushort16 us_instid = HI_OMCI_ME_VEIP_INSTID;

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_VEIP_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;
	st_entry.us_ianaasgport = 0xffff;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_VEIP_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_me_veip_set
 Description : Virtual Ethernet interface point
               在数据库操作后处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_veip_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_uint32    i_ret = HI_RET_SUCC, ui_index = 0, ui_status = 0;
	hi_omci_me_veip_s *pst_entity = (hi_omci_me_veip_s *)pv_data;
	//hi_omci_me_veip_s st_entity;
	hi_omci_me_udptcp_cfg_s st_udptcp;
	hi_omci_me_extvlantag_op_s st_extvlan;
	hi_omci_tapi_voip_iphost_s st_tapi_iphost;
	hi_omci_mac_bri_port_type_e em_type = HI_MAC_BRI_PORT_IP_E;
	hi_ushort16 us_vlanext_instid = 0;
	hi_ushort16 us_macport_instid = 0;
	hi_ushort16 us_mask = 0;
	hi_uint32   ui_tr069_status = 0;

	us_mask    = pst_entity->st_msghead.us_attmask;
	HI_OS_MEMSET_S(&st_udptcp, sizeof(st_udptcp), 0, sizeof(st_udptcp));
	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", pst_entity->st_msghead.us_instid, us_mask);

	hi_omci_debug("[INFO]:admstate=%hhu,optstate=%hhu,domainname=%s,tcpudp_ptr =%hu,ianaasgport =%hu\n",
		      pst_entity->uc_admstate, pst_entity->uc_optstate, pst_entity->uc_domainname, pst_entity->us_tcpudp_ptr,
		      pst_entity->us_ianaasgport);

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR1)) {
		if (0 != gui_omci_voip_flag) {
			i_ret = hi_omci_tapi_tr069_conf_set((hi_uint32 *)&pst_entity->uc_admstate);
			HI_OMCI_RET_CHECK(i_ret);
		} else {
			HI_OMCI_BIT_SET(gui_omci_veip_mask, (16 - HI_OMCI_ATTR1));
		}
	}

	if (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR4)) {
		hi_omci_tapi_tr069_status_get(&ui_tr069_status);
		i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, pst_entity->us_tcpudp_ptr, &st_udptcp);
		if ((0 != i_ret) && (1 == ui_tr069_status)) {
			hi_omci_debug("[INFO]:i_ret = 0x%x,uc_tr069_status = %u\n", i_ret, ui_tr069_status);
			/*删除tr069 wan*/
			i_ret = hi_omci_tapi_voip_wan_del(1);
			HI_OMCI_RET_CHECK(i_ret);
			for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
				if (g_st_extvlan_table.st_extvlan[ui_index].ui_mode == 2) {
					g_st_extvlan_table.st_extvlan[ui_index].ui_mode = 0;
					break;
				}
			}
			i_ret = hi_omci_tapi_tr069_conf_set(&ui_status);
			HI_OMCI_RET_CHECK(i_ret);
		} else if (0 != i_ret) {
			if (0 == gui_omci_voip_flag) {
				HI_OMCI_BIT_SET(gui_omci_veip_mask, (16 - HI_OMCI_ATTR4));
			}
			hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
			return HI_RET_SUCC;
		} else {
			if (0 != gui_omci_voip_flag) {
				for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
					if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == st_udptcp.us_iphost_ptr) {
						break;
					}
				}
				ui_status = 1;
				i_ret = hi_omci_tapi_tr069_conf_set(&ui_status);
				HI_OMCI_RET_CHECK(i_ret);
				/*创建voip wan*/
				i_ret = hi_omci_ext_get_instid_byattr2(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR3, &em_type, HI_OMCI_ATTR4,
								       &st_udptcp.us_iphost_ptr, &us_macport_instid);
				i_ret += hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, HI_OMCI_ATTR7, &us_macport_instid,
								       &us_vlanext_instid);
				i_ret += hi_omci_ext_get_inst(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_vlanext_instid, &st_extvlan);

				if (HI_RET_SUCC == i_ret) {
					if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM != ui_index) {
						g_st_extvlan_table.st_extvlan[ui_index].ui_mode = 2;
						HI_OS_MEMSET_S(&st_tapi_iphost, sizeof(st_tapi_iphost), 0, sizeof(st_tapi_iphost));
						hi_omci_me_set_iphost_attr(&st_tapi_iphost, g_st_extvlan_table.st_extvlan[ui_index].ui_index);
						st_tapi_iphost.us_flag = 1;
						if (0x1fff != g_st_extvlan_table.st_extvlan[ui_index].us_vlan) {
							st_tapi_iphost.ui_vlan = g_st_extvlan_table.st_extvlan[ui_index].us_vlan;
							st_tapi_iphost.ui_vlanen = 1;
						}

						st_tapi_iphost.us_8021p = g_st_extvlan_table.st_extvlan[ui_index].us_pri;
						st_tapi_iphost.us_8021pen = 1;
						i_ret = hi_omci_tapi_voip_wan_set(&st_tapi_iphost);
						HI_OMCI_RET_CHECK(i_ret);
					}
				} else {
					if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM != ui_index) {
						g_st_extvlan_table.st_extvlan[ui_index].ui_mode = 2;
					}
				}
			} else {
				for (ui_index = 0; ui_index < HI_OMCI_TAPI_EXTVLAN_TABLE_NUM; ui_index++) {
					if (g_st_extvlan_table.st_extvlan[ui_index].ui_index == st_udptcp.us_iphost_ptr) {
						break;
					}
				}

				if (HI_OMCI_TAPI_EXTVLAN_TABLE_NUM != ui_index) {
					g_st_extvlan_table.st_extvlan[ui_index].ui_mode = 2;
					i_ret = hi_omci_ext_get_instid_byattr2(HI_OMCI_PRO_ME_MAC_PORT_CFG_DATA_E, HI_OMCI_ATTR3, &em_type, HI_OMCI_ATTR4,
									       &st_udptcp.us_iphost_ptr, &us_macport_instid);
					i_ret += hi_omci_ext_get_instid_byattr(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, HI_OMCI_ATTR7, &us_macport_instid,
									       &us_vlanext_instid);
					i_ret += hi_omci_ext_get_inst(HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E, us_vlanext_instid, &st_extvlan);
					if (HI_RET_SUCC ==  i_ret) {
						gui_omci_tr069_extvlan_mask += (g_st_extvlan_table.st_extvlan[ui_index].us_pri << 16);
						gui_omci_tr069_extvlan_mask += g_st_extvlan_table.st_extvlan[ui_index].us_vlan;
					}
				}
				HI_OMCI_BIT_SET(gui_omci_veip_mask, (16 - HI_OMCI_ATTR4));
			}
		}
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
