/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_esc.c
  Version    :
  Author     : owen
  Creation   : 2016-3-15
  Description: OMCI ME Enhanced security control
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

#include "hi_timer.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_ESC_T1   (3 * 1000)  /* 3 seconds */
#define HI_OMCI_ME_ESC_T2   (1 * 1000)  /* 1 second */
#define HI_OMCI_ME_ESC_T3   (1 * 1000)  /* 1 second */

#define HI_OMCI_ME_ESC_AVC   (1 * 500)  /* 0.5 second */

/* OLT crypto capabilities */
#define HI_OMCI_ME_ESC_IS_OLT_CAP(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR1)

/* OLT random challenge table */
#define HI_OMCI_ME_ESC_IS_OLT_TAB(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR2)

/* OLT challenge status */
#define HI_OMCI_ME_ESC_IS_OLT_STA(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR3)

/* OLT authentication result table */
#define HI_OMCI_ME_ESC_IS_OLT_RET_TAB(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR7)

/* OLT authentication result table */
#define HI_OMCI_ME_ESC_IS_OLT_RET_STA(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR8)

/* Broadcast key table */
#define HI_OMCI_ME_ESC_IS_BCKEY(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR11)

#define HI_OMCI_ME_ESC_CRYPTO_AES_CMAC_128  1

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
typedef enum {
	HI_OMCI_ME_ESC_INACTIVE = 0,    /* Inactive  S0 */
	HI_OMCI_ME_ESC_OLT_CHALLENGE,   /* OLT challenge pending  S1 */
	HI_OMCI_ME_ESC_ONU_CHALLENGE,   /* ONU challenge pending  S2 */
	HI_OMCI_ME_ESC_AUTH_SUCC,       /* Authentication success  S3 */
	HI_OMCI_ME_ESC_AUTH_FAIL,       /* Authentication failure  S4 */
	HI_OMCI_ME_ESC_AUTH_ERR,        /* Authentication error  S5 */
	HI_OMCI_ME_ESC_STATE_NUM,
} hi_omci_me_esc_state_e;

typedef enum {
	/* The OLT initiates the authentication process by
	 * writing a challenge into the OLT random challenge table. */
	HI_OMCI_ME_ESC_OLT_CHALLENGE_SET = 0,

	/* it has chosen the ONU random challenge and calculated
	 * the ONU authentication result table. */
	HI_OMCI_ME_ESC_ONU_CHALLENGE_RDY,

	/* the ONU is unable to perform the operations necessary to
	 * transition to ONU challenge pending state S2 */
	HI_OMCI_ME_ESC_ONU_CHALLENGE_FAIL,

	/* If no response to the ONU.s challenge is received in the
	 * OLT authentication result table before timer T1 expires */
	HI_OMCI_ME_ESC_T1_TIMEOUT,

	/* If a response is received in the
	 * OLT authentication result table */
	HI_OMCI_ME_ESC_OLT_AUTH_RET,

	HI_OMCI_ME_ESC_NEW_MSK_SET,
	HI_OMCI_ME_ESC_T2_TIMEOUT,
	HI_OMCI_ME_ESC_T3_TIMEOUT,
} hi_omci_me_esc_msg_e;

typedef hi_int32(*__omci_me_esc_handler)(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data);

/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_int32 __omci_me_esc_s0_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data);
static hi_int32 __omci_me_esc_s1_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data);
static hi_int32 __omci_me_esc_s2_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data);
static hi_int32 __omci_me_esc_s3_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data);
static hi_int32 __omci_me_esc_s4_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data);
static hi_int32 __omci_me_esc_s5_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data);

static __omci_me_esc_handler g_fp_omci_me_esc_handler[] = {
	__omci_me_esc_s0_proc,
	__omci_me_esc_s1_proc,
	__omci_me_esc_s2_proc,
	__omci_me_esc_s3_proc,
	__omci_me_esc_s4_proc,
	__omci_me_esc_s5_proc,
};

static hi_uint32 gui_t1_id = 0;
static hi_uint32 gui_t2_id = 0;
static hi_uint32 gui_t3_id = 0;
static hi_uint32 gui_avc_id = 0;
static hi_uint32 gui_avc_inst = 0;

static hi_uint32 gui_olt_rand_tab_num;
static hi_uint32 gui_olt_rest_tab_num;

static hi_omci_me_esc_state_e gem_auth_state = HI_OMCI_ME_ESC_INACTIVE;

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/******************************************************************************
Function    : __omci_me_esc_state_move
Description : auth state move
Input Parm  : hi_omci_me_esc_state_e em_new
Output Parm : N/A

Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_void __omci_me_esc_state_move(hi_omci_me_esc_state_e em_new)
{
	hi_uchar8 uc_state = (hi_uchar8)em_new;

	hi_omci_debug("S%d => S%d\n", gem_auth_state, em_new);
	gem_auth_state = em_new;

	/* update mib */
	hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ESC_E, 0, HI_OMCI_ATTR9, &uc_state);

	//hi_omci_proc_sendavc(HI_OMCI_PRO_ME_ESC_E, 0, HI_OMCI_ATTR_BITMAP(HI_OMCI_ATTR10), &uc_state, sizeof(uc_state));

	return;
}

/******************************************************************************
 Function    : __omci_me_esc_timer_start
 Description : timer start
 Input Parm  :
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_timer_start(hi_timer_call_f pf_handler,
				   hi_uint32 *pui_tid, hi_uint32 ui_timeout)
{
	hi_int32 i_ret;

	i_ret = hi_timer_create(pf_handler, HI_NULL, pui_tid);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_timer_mod_wo_lock(*pui_tid, ui_timeout);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_timer_init
 Description : timer init
 Input Parm  : __omci_me_esc_timer_func *pf_handler,
               hi_uint32 *pui_tid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_timer_stop(hi_uint32 ui_tid)
{
	hi_int32 i_ret;

	i_ret = hi_timer_destroy_wo_lock(ui_tid);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_auth_handler
 Description : generate the ONU authentication result
 Input Parm  : hi_omci_me_esc_msg_e em_msg,
               hi_void *pv_data
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 __omci_me_esc_auth_handler(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data)
{
	hi_omci_debug(">>>gem_auth_state[%d] em_msg[%d]\n", gem_auth_state, em_msg);
	return g_fp_omci_me_esc_handler[gem_auth_state](em_msg, pv_data);/*lint !e128*/
}

/******************************************************************************
 Function    : __omci_me_esc_t1_proc
 Description : T1 timer
 Input Parm  : hi_void *pv_data
 Output Parm : N/A

 Return      : N/A

******************************************************************************/
hi_void __omci_me_esc_t1_proc(hi_void *pv_data)
{
	__omci_me_esc_auth_handler((hi_omci_me_esc_msg_e)HI_OMCI_ME_ESC_T1_TIMEOUT, HI_NULL);
	return;
}

/******************************************************************************
 Function    : __omci_me_esc_t2_proc
 Description : T2 timer
 Input Parm  : hi_void *pv_data
 Output Parm : N/A

 Return      : N/A

******************************************************************************/
hi_void __omci_me_esc_t2_proc(hi_void *pv_data)
{
	__omci_me_esc_auth_handler((hi_omci_me_esc_msg_e)HI_OMCI_ME_ESC_T2_TIMEOUT, HI_NULL);
	return;
}

/******************************************************************************
 Function    : __omci_me_esc_t3_proc
 Description : T3 timer
 Input Parm  : hi_void *pv_data
 Output Parm : N/A

 Return      : N/A

******************************************************************************/
hi_void __omci_me_esc_t3_proc(hi_void *pv_data)
{
	__omci_me_esc_auth_handler((hi_omci_me_esc_msg_e)HI_OMCI_ME_ESC_T3_TIMEOUT, HI_NULL);
	return;
}

/******************************************************************************
 Function    : __omci_me_esc_challenge_gen
 Description : generate the challenge
 Input Parm  : hi_uchar8 *puc_data,
               hi_uint32 ui_len
 Output Parm : N/A

 Return      : N/A

******************************************************************************/
hi_void __omci_me_esc_challenge_gen(hi_uchar8 *puc_data, hi_uint32 ui_len)
{
	hi_uint32 ui_index;

	for (ui_index = 0; ui_index < ui_len; ui_index++, puc_data++) {
		*puc_data = (hi_uchar8)hi_os_random();
	}

	return;
}

/******************************************************************************
 Function    : __omci_me_esc_onu_challenge_gen
 Description : generate the ONU challenge
 Input Parm  : hi_ushort16 us_instid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_onu_challenge_gen(hi_ushort16 us_instid)
{
	hi_uint32 ui_ret;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum;
	hi_uchar8 *puc_tab;
	hi_uchar8 *puc_tmp;
	hi_uchar8 uc_size;

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR5, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	uc_size = ui_tabnum * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN;
	puc_tab = hi_os_malloc(uc_size);
	if (HI_NULL == puc_tab) {
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	for (ui_index = 0, puc_tmp = puc_tab; ui_index < ui_tabnum;
	     ui_index++, puc_tmp += HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN) {
		__omci_me_esc_challenge_gen(puc_tmp, HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN);
	}

	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR5, puc_tab);
	hi_os_free(puc_tab);
	HI_OMCI_RET_CHECK(ui_ret);

	/* AVC [ONU random challenge table complete] */
	ui_ret = hi_omci_proc_sendavc(HI_OMCI_PRO_ME_ESC_E, us_instid,
				      HI_OMCI_ATTR_BITMAP(HI_OMCI_ATTR5), &uc_size, sizeof(uc_size));
	HI_OMCI_RET_CHECK(ui_ret);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_authret_gen
 Description : generate the ONU authentication result
               The value of the ONU authentication result table is equal to
               SelectedHashFunction (PSK, (ONU_selected_crypto capabilities |
                    OLT_random_challenge_table | ONU_random_challenge_table |
                    0x0000 0000 0000 0000)
 Input Parm  : hi_omci_me_esc_s *pst_inst
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_authret_gen(hi_omci_me_esc_s *pst_inst)
{
	hi_uint32 ui_ret;
	hi_omci_tapi_aes_cmac_s st_aescmac;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum;
	hi_uchar8 *puc_data;
	hi_uchar8 *puc_tab;
	hi_uchar8 *puc_tab_temp;
	hi_ushort16 us_instid = pst_inst->st_msghead.us_instid;
	hi_uchar8 uc_onu_cap;
	hi_uchar8 uc_size;

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR4, &uc_onu_cap);
	HI_OMCI_RET_CHECK(ui_ret);

	/* ONU_selected_crypto capabilities | */
	HI_OS_MEMSET_S(st_aescmac.auc_data, sizeof(st_aescmac.auc_data), 0, sizeof(st_aescmac.auc_data));
	puc_data = st_aescmac.auc_data;
	*puc_data = uc_onu_cap;
	puc_data++;

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR2, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	/* prepare for the OLT random challenge table memary */
	puc_tab = hi_os_malloc(ui_tabnum * HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN);
	if (HI_NULL == puc_tab) {
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	/* get the OLT random challenge table */
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR2, puc_tab);
	HI_OMCI_RET_EXT_CHECK(ui_ret, hi_os_free(puc_tab););

	/* ONU_selected_crypto capabilities | OLT_random_challenge_table */
	for (ui_index = 0, puc_tab_temp = puc_tab + 1; ui_index < gui_olt_rand_tab_num; ui_index++) {
		HI_OS_MEMCPY_S(puc_data, sizeof(st_aescmac.auc_data) - (puc_data - st_aescmac.auc_data) * sizeof(hi_uchar8),
			       puc_tab_temp, (HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1));
		puc_data += HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1;
		puc_tab_temp += HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN;
	}

	/* release the OLT random challenge table memary */
	hi_os_free(puc_tab);

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR5, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	/* prepare for the ONU random challenge table memary */
	puc_tab = hi_os_malloc(ui_tabnum * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN);
	if (HI_NULL == puc_tab) {
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	/* get the ONU random challenge table */
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR5, puc_tab);
	HI_OMCI_RET_EXT_CHECK(ui_ret, hi_os_free(puc_tab););

	/* ONU_selected_crypto capabilities | OLT_random_challenge_table | ONU_random_challenge_table */
	HI_OS_MEMCPY_S(puc_data, sizeof(st_aescmac.auc_data) - (puc_data - st_aescmac.auc_data) * sizeof(hi_uchar8), puc_tab,
		       (ui_tabnum * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN));

	/* release the ONU random challenge table memary */
	hi_os_free(puc_tab);

	st_aescmac.ui_len = (gui_olt_rand_tab_num * (HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1))
			    + (ui_tabnum * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN) + sizeof(uc_onu_cap) + 8;

	ui_ret = hi_omci_tapi_aes_cmac(&st_aescmac);
	HI_OMCI_RET_CHECK(ui_ret);

	hi_omci_debug(">>>auc_result[%x][%x][%x][%x][%x][%x][%x][%x] [%x][%x][%x][%x][%x][%x][%x][%x]\n",
		      st_aescmac.auc_result[0], st_aescmac.auc_result[1], st_aescmac.auc_result[2], st_aescmac.auc_result[3],
		      st_aescmac.auc_result[4], st_aescmac.auc_result[5], st_aescmac.auc_result[6], st_aescmac.auc_result[7],
		      st_aescmac.auc_result[8], st_aescmac.auc_result[9], st_aescmac.auc_result[10], st_aescmac.auc_result[11],
		      st_aescmac.auc_result[12], st_aescmac.auc_result[13], st_aescmac.auc_result[14], st_aescmac.auc_result[15]);
	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR6, st_aescmac.auc_result);
	HI_OMCI_RET_CHECK(ui_ret);

	/* AVC [ONU authentication result table complete] */
	uc_size = HI_OMCI_ONU_AUTH_RET_TAB_ITME_LEN;
	ui_ret = hi_omci_proc_sendavc(HI_OMCI_PRO_ME_ESC_E, us_instid,
				      HI_OMCI_ATTR_BITMAP(HI_OMCI_ATTR6), &uc_size, sizeof(uc_size));
	HI_OMCI_RET_CHECK(ui_ret);

	hi_omci_misc_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_oltret_validate
 Description : validates the OLT authentication result
               The OLT response is equal to
               SelectedHashFunction (PSK, (ONU_selected_crypto capabilities |
                    ONU_random_challenge_table | OLT_random_challenge_table |
                    ONU_serial_number).
 Input Parm  : hi_uchar8 *puc_data,
               hi_uint32 ui_len
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_oltret_validate(hi_omci_me_esc_s *pst_inst)
{
	hi_uint32 ui_ret;
	hi_omci_tapi_aes_cmac_s st_aescmac;
	hi_omci_tapi_sn_loid_s st_psk;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum, ui_tabnum_onu;
	hi_uchar8 *puc_data;
	hi_uchar8 *puc_tab_onu;
	hi_uchar8 *puc_olt_tab;
	hi_uchar8 *puc_olt_tab_temp;
	hi_ushort16 us_instid = pst_inst->st_msghead.us_instid;
	hi_uchar8 uc_onu_cap;

	ui_ret = hi_omci_tapi_sn_loid_get(&st_psk);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR2, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR4, &uc_onu_cap);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR5, &ui_tabnum_onu);
	HI_OMCI_RET_CHECK(ui_ret);

	/* prepare for the ONU random challenge table memary */
	puc_tab_onu = hi_os_malloc(ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN);
	if (HI_NULL == puc_tab_onu) {
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	/* get the ONU random challenge table */
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR5, puc_tab_onu);
	HI_OMCI_RET_EXT_CHECK(ui_ret, hi_os_free(puc_tab_onu););

	/* prepare for the OLT random challenge table memary */
	puc_olt_tab = hi_os_malloc(ui_tabnum * HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN);
	if (HI_NULL == puc_olt_tab) {
		hi_os_free(puc_tab_onu);
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	/* get the OLT random challenge table */
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR2, puc_olt_tab);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(puc_tab_onu);
		hi_os_free(puc_olt_tab);
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	HI_OS_MEMSET_S(st_aescmac.auc_data, sizeof(st_aescmac.auc_data), 0, sizeof(st_aescmac.auc_data));
	puc_data = st_aescmac.auc_data;

	puc_data[0] = uc_onu_cap;
	puc_data++;

	HI_OS_MEMCPY_S(puc_data, sizeof(st_aescmac.auc_data) - (puc_data - st_aescmac.auc_data) * sizeof(hi_uchar8),
		       puc_tab_onu, (ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN));
	puc_data += (ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN);
	hi_os_free(puc_tab_onu);

	for (ui_index = 0, puc_olt_tab_temp = puc_olt_tab + 1; ui_index < gui_olt_rand_tab_num; ui_index++) {
		HI_OS_MEMCPY_S(puc_data, sizeof(st_aescmac.auc_data) - (puc_data - st_aescmac.auc_data) * sizeof(hi_uchar8),
			       puc_olt_tab_temp, (HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1));
		puc_data += HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1;
		puc_olt_tab_temp += HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN;
	}

	hi_os_free(puc_olt_tab);

	hi_omci_debug("st_psk.auc_sn: %s \n", st_psk.auc_sn);
	HI_OS_MEMCPY_S(puc_data, sizeof(st_aescmac.auc_data) - (puc_data - st_aescmac.auc_data) * sizeof(hi_uchar8),
		       st_psk.auc_sn, sizeof(st_psk.auc_sn));

	st_aescmac.ui_len = (gui_olt_rand_tab_num * (HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1))
			    + (ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN) + sizeof(uc_onu_cap) + sizeof(st_psk.auc_sn);
	hi_omci_debug("len: %x \n", st_aescmac.ui_len);

	ui_ret = hi_omci_tapi_aes_cmac(&st_aescmac);
	HI_OMCI_RET_CHECK(ui_ret);

	/* get the OLT authentication result table */
	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR7, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	puc_olt_tab = hi_os_malloc(ui_tabnum * HI_OMCI_OLT_AUTH_RET_TAB_ITME_LEN);
	if (HI_NULL == puc_olt_tab) {
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR7, puc_olt_tab);
	HI_OMCI_RET_EXT_CHECK(ui_ret, hi_os_free(puc_olt_tab););

	/* compare */
	hi_omci_debug("st_aescmac.auc_result: %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x\n", st_aescmac.auc_result[0]
		      , st_aescmac.auc_result[1], st_aescmac.auc_result[2], st_aescmac.auc_result[3], st_aescmac.auc_result[4]
		      , st_aescmac.auc_result[5], st_aescmac.auc_result[6], st_aescmac.auc_result[7], st_aescmac.auc_result[8]
		      , st_aescmac.auc_result[9], st_aescmac.auc_result[10], st_aescmac.auc_result[11], st_aescmac.auc_result[12]
		      , st_aescmac.auc_result[13], st_aescmac.auc_result[14], st_aescmac.auc_result[15]);
	hi_omci_debug("puc_olt_tab: %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x\n", *(puc_olt_tab + 1)
		      , *(puc_olt_tab + 2), *(puc_olt_tab + 3), *(puc_olt_tab + 4), *(puc_olt_tab + 5)
		      , *(puc_olt_tab + 6), *(puc_olt_tab + 7), *(puc_olt_tab + 8), *(puc_olt_tab + 9)
		      , *(puc_olt_tab + 10), *(puc_olt_tab + 11), *(puc_olt_tab + 12), *(puc_olt_tab + 13)
		      , *(puc_olt_tab + 14), *(puc_olt_tab + 15), *(puc_olt_tab + 16));
	if (0 != hi_os_memcmp(st_aescmac.auc_result, (hi_void *)(puc_olt_tab + 1),
			      sizeof(st_aescmac.auc_result))) {
		hi_omci_debug("validates the OLT fail.\n");
		//HI_PRINT_MEM(sizeof(st_aescmac.auc_result), st_aescmac.auc_result);
		//HI_PRINT_MEM((ui_tabnum * HI_OMCI_OLT_AUTH_RET_TAB_ITME_LEN), puc_olt_tab);
		//hi_os_free(puc_olt_tab);
		//hi_omci_misc_systrace( HI_RET_FAIL, 0, 0, 0, 0);
		//return HI_RET_FAIL;
	}
	hi_omci_debug("validates the OLT succ.\n");
	hi_os_free(puc_olt_tab);
	hi_omci_misc_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_msk_set
 Description : The master session key is defined as:
                    SelectedHashFunction (PSK, (OLT random_challenge | ONU random challenge)).
 Input Parm  : hi_uchar8 *puc_data,
               hi_uint32 ui_len
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_msk_set(hi_uint32 ui_inst)
{
	hi_uint32 ui_ret;
	hi_omci_tapi_aes_cmac_s st_aescmac;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum, ui_tabnum_onu;
	hi_uchar8 *puc_tab_onu;
	hi_uchar8 *puc_data;
	hi_uchar8 *puc_olt_tab;
	hi_uchar8 *puc_olt_tab_temp;
	hi_ushort16 us_instid = ui_inst;
	hi_omci_debug("us_instid: %hu \n", us_instid);

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR2, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR5, &ui_tabnum_onu);
	HI_OMCI_RET_CHECK(ui_ret);

	/* prepare for the ONU random challenge table memary */
	puc_tab_onu = hi_os_malloc(ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN);
	if (HI_NULL == puc_tab_onu) {
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	/* get the ONU random challenge table */
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR5, puc_tab_onu);
	HI_OMCI_RET_EXT_CHECK(ui_ret, hi_os_free(puc_tab_onu););

	puc_olt_tab = hi_os_malloc(ui_tabnum * HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN);
	if (HI_NULL == puc_olt_tab) {
		hi_os_free(puc_tab_onu);
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR2, puc_olt_tab);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(puc_tab_onu);
		hi_os_free(puc_olt_tab);
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}
	/* set the msk */
	hi_os_memset(st_aescmac.auc_data, 0, sizeof(st_aescmac.auc_data));
	puc_data = st_aescmac.auc_data;

	for (ui_index = 0, puc_olt_tab_temp = puc_olt_tab + 1; ui_index < gui_olt_rand_tab_num; ui_index++) {
		hi_os_memcpy(puc_data, puc_olt_tab_temp, (HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1));
		puc_data += HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1;
		puc_olt_tab_temp += HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN;
	}

	hi_os_free(puc_olt_tab);

	hi_os_memcpy(puc_data, puc_tab_onu, (ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN));
	hi_os_free(puc_tab_onu);

	st_aescmac.ui_len = (gui_olt_rand_tab_num * (HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1)) +
			    (ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN);

	ui_ret = hi_omci_tapi_aes_cmac(&st_aescmac);
	HI_OMCI_RET_CHECK(ui_ret);
	hi_omci_debug("st_aescmac.auc_result: %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x\n", st_aescmac.auc_result[0]
		      , st_aescmac.auc_result[1], st_aescmac.auc_result[2], st_aescmac.auc_result[3], st_aescmac.auc_result[4]
		      , st_aescmac.auc_result[5], st_aescmac.auc_result[6], st_aescmac.auc_result[7], st_aescmac.auc_result[8]
		      , st_aescmac.auc_result[9], st_aescmac.auc_result[10], st_aescmac.auc_result[11], st_aescmac.auc_result[12]
		      , st_aescmac.auc_result[13], st_aescmac.auc_result[14], st_aescmac.auc_result[15]);
	ui_ret = hi_omci_tapi_msk_set(st_aescmac.auc_result);
	HI_OMCI_RET_CHECK(ui_ret);

	hi_omci_misc_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_msk_name_set
 Description : The master session key name is defined as:
                    SelectedHashFunction (PSK, (ONU random challenge | OLT random
                        challenge | 0x 3141 5926 5358 9793 3141 5926 5358 9793)).
 Input Parm  : hi_uchar8 *puc_data,
               hi_uint32 ui_len
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_msk_name_set(hi_omci_me_esc_s *pst_inst)
{
	hi_uint32 ui_ret;
	hi_omci_tapi_aes_cmac_s st_aescmac;
	hi_uint32 ui_index;
	hi_uint32 ui_tabnum, ui_tabnum_onu;
	hi_uchar8 *puc_tab_onu;
	hi_uchar8 *puc_data;
	hi_uchar8 *puc_olt_tab;
	hi_uchar8 *puc_olt_tab_temp;
	hi_uchar8 auc_challenge[] = {0x31, 0x41, 0x59, 0x26, 0x53, 0x58, 0x97, 0x93,
				     0x31, 0x41, 0x59, 0x26, 0x53, 0x58, 0x97, 0x93
				    };
	hi_ushort16 us_instid = pst_inst->st_msghead.us_instid;

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR2, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR5, &ui_tabnum_onu);
	HI_OMCI_RET_CHECK(ui_ret);

	/* prepare for the ONU random challenge table memary */
	puc_tab_onu = hi_os_malloc(ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN);
	if (HI_NULL == puc_tab_onu) {
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	/* get the ONU random challenge table */
	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR5, puc_tab_onu);
	HI_OMCI_RET_EXT_CHECK(ui_ret, hi_os_free(puc_tab_onu););

	puc_olt_tab = hi_os_malloc(ui_tabnum * HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN);
	if (HI_NULL == puc_olt_tab) {
		hi_os_free(puc_tab_onu);
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR2, puc_olt_tab);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_free(puc_tab_onu);
		hi_os_free(puc_olt_tab);
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	/* set the mib with session key name */
	HI_OS_MEMSET_S(st_aescmac.auc_data, sizeof(st_aescmac.auc_data), 0, sizeof(st_aescmac.auc_data));
	puc_data = st_aescmac.auc_data;

	HI_OS_MEMCPY_S(puc_data, sizeof(st_aescmac.auc_data) - (puc_data - st_aescmac.auc_data) * sizeof(hi_uchar8),
		       puc_tab_onu, (ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN));
	puc_data += (ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN);
	hi_os_free(puc_tab_onu);

	for (ui_index = 0, puc_olt_tab_temp = puc_olt_tab + 1; ui_index < gui_olt_rand_tab_num; ui_index++) {
		HI_OS_MEMCPY_S(puc_data, sizeof(st_aescmac.auc_data) - (puc_data - st_aescmac.auc_data) * sizeof(hi_uchar8),
			       puc_olt_tab_temp, (HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1));
		puc_data += HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1;
		puc_olt_tab_temp += HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN;
	}

	hi_os_free(puc_olt_tab);

	HI_OS_MEMCPY_S(puc_data, sizeof(st_aescmac.auc_data) - (puc_data - st_aescmac.auc_data) * sizeof(hi_uchar8),
		       auc_challenge, sizeof(auc_challenge));

	st_aescmac.ui_len = (gui_olt_rand_tab_num * (HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN - 1))
			    + (ui_tabnum_onu * HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN) + sizeof(auc_challenge);

	ui_ret = hi_omci_tapi_aes_cmac(&st_aescmac);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR10, st_aescmac.auc_result);
	HI_OMCI_RET_CHECK(ui_ret);

	hi_omci_misc_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_avc_proc
 Description : AVC [ONU authentication status]
 Input Parm  : hi_void *pv_data
 Output Parm : N/A
 Return      : N/A
******************************************************************************/
hi_void __omci_me_esc_avc_proc(hi_void *pv_data)
{
	hi_uchar8 uc_state;
	uc_state = gem_auth_state;

	/* flush msk*/
	__omci_me_esc_msk_set(gui_avc_inst);
	/* send avc*/
	hi_omci_proc_sendavc(HI_OMCI_PRO_ME_ESC_E, 0, HI_OMCI_ATTR_BITMAP(HI_OMCI_ATTR9), &uc_state, sizeof(uc_state));
	/* stop avc timer */
	__omci_me_esc_timer_stop(gui_avc_id);
	return;
}

/******************************************************************************
 Function    : __omci_me_esc_s0_proc
 Description : s0 state processor
 Input Parm  : hi_omci_me_esc_msg_e em_msg,
               hi_void *pv_data
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_s0_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data)
{
	hi_int32 i_ret;
	hi_omci_me_esc_s *pst_inst = (hi_omci_me_esc_s *)pv_data;
	hi_ushort16 us_instid = pst_inst->st_msghead.us_instid;

	if (HI_OMCI_ME_ESC_OLT_CHALLENGE_SET != em_msg) {
		return HI_RET_SUCC;
	}

	__omci_me_esc_state_move(HI_OMCI_ME_ESC_OLT_CHALLENGE);

	/* generate the ONU random challenge */
	i_ret = __omci_me_esc_onu_challenge_gen(us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	/* calculated the ONU authentication result table */
	i_ret = __omci_me_esc_authret_gen(pst_inst);
	HI_OMCI_RET_EXT_CHECK(i_ret, __omci_me_esc_state_move(HI_OMCI_ME_ESC_AUTH_ERR);
			      __omci_me_esc_timer_start(__omci_me_esc_t3_proc, &gui_t3_id, HI_OMCI_ME_ESC_T3););

	/* start T1 timer */
	i_ret = __omci_me_esc_timer_start(__omci_me_esc_t1_proc, &gui_t1_id, HI_OMCI_ME_ESC_T1);
	HI_OMCI_RET_CHECK(i_ret);

	__omci_me_esc_state_move(HI_OMCI_ME_ESC_ONU_CHALLENGE);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_s1_proc
 Description : s1 state processor
 Input Parm  : hi_omci_me_esc_msg_e em_msg,
               hi_void *pv_data
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_s1_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data)
{
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_s2_proc
 Description : s2 state processor
 Input Parm  : hi_omci_me_esc_msg_e em_msg,
               hi_void *pv_data
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_s2_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data)
{
	hi_int32 i_ret;
	hi_omci_me_esc_s *pst_inst = (hi_omci_me_esc_s *)pv_data;
	hi_omci_debug(">>>em_msg=%d\n", em_msg);
	switch (em_msg) {
	case HI_OMCI_ME_ESC_OLT_AUTH_RET:

		i_ret = __omci_me_esc_timer_stop(gui_t1_id);
		HI_OMCI_RET_CHECK(i_ret);

		i_ret = __omci_me_esc_oltret_validate(pst_inst);
		HI_OMCI_RET_EXT_CHECK(i_ret, __omci_me_esc_state_move(HI_OMCI_ME_ESC_AUTH_FAIL);
				      __omci_me_esc_timer_start(__omci_me_esc_t2_proc, &gui_t2_id, HI_OMCI_ME_ESC_T2););

		/* The ONU must set a valid value for the master session key name attribute
		 * before it enters authentication success state S3 */
		i_ret = __omci_me_esc_msk_name_set(pst_inst);
		HI_OMCI_RET_CHECK(i_ret);

		/* The OLT will typically read the master session key name attribute
		 * when it receives the AVC indicating that the ONU authentication status attribute has changed to
		 * state S3 to ensure that the ONU is synchronized and the new key is ready to be utilized within the
		 * TC layer PLOAM function. */
		__omci_me_esc_state_move(HI_OMCI_ME_ESC_AUTH_SUCC);

		/* AVC [ONU authentication status] */
		__omci_me_esc_timer_start(__omci_me_esc_avc_proc, &gui_avc_id, HI_OMCI_ME_ESC_AVC);
		gui_avc_inst = pst_inst->st_msghead.us_instid;
		//uc_state = gem_auth_state;
		//i_ret = hi_omci_proc_sendavc(HI_OMCI_PRO_ME_ESC_E, 0,
		//HI_OMCI_ATTR_BITMAP(HI_OMCI_ATTR10), &uc_state, sizeof(uc_state));
		//HI_OMCI_RET_CHECK(i_ret);
		//i_ret = __omci_me_esc_msk_set(pst_inst);
		//HI_OMCI_RET_CHECK(i_ret);

		break;

	case HI_OMCI_ME_ESC_T1_TIMEOUT:
		hi_omci_debug(">>>kkk state=%d\n", gem_auth_state);
		__omci_me_esc_state_move(HI_OMCI_ME_ESC_AUTH_ERR);
		__omci_me_esc_timer_start(__omci_me_esc_t3_proc, &gui_t3_id, HI_OMCI_ME_ESC_T3);
		break;

	default:
		break;
	}

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_s3_proc
 Description : s3 state processor
               ONU supplies master session key to TC layer. Is PSK changed ?
 Input Parm  : hi_omci_me_esc_msg_e em_msg,
               hi_void *pv_data
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_s3_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data)
{
	if (HI_OMCI_ME_ESC_NEW_MSK_SET != em_msg) {
		return HI_RET_SUCC;
	}

	__omci_me_esc_state_move(HI_OMCI_ME_ESC_INACTIVE);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_s4_proc
 Description : s4 state processor
 Input Parm  : hi_omci_me_esc_msg_e em_msg,
               hi_void *pv_data
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_s4_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data)
{
	if (HI_OMCI_ME_ESC_T2_TIMEOUT != em_msg) {
		return HI_RET_SUCC;
	}

	__omci_me_esc_timer_stop(gui_t2_id);
	__omci_me_esc_state_move(HI_OMCI_ME_ESC_INACTIVE);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_s5_proc
 Description : s5 state processor
 Input Parm  : hi_omci_me_esc_msg_e em_msg,
               hi_void *pv_data
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_s5_proc(hi_omci_me_esc_msg_e em_msg, hi_void *pv_data)
{
	if (HI_OMCI_ME_ESC_T3_TIMEOUT != em_msg) {
		return HI_RET_SUCC;
	}

	__omci_me_esc_timer_stop(gui_t3_id);
	__omci_me_esc_state_move(HI_OMCI_ME_ESC_INACTIVE);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_olt_tab_set
 Description : OLT random challenge table and OLT authentication result table
               setting
 Input Parm  : hi_uchar8 *puc_item,
               hi_ushort16 us_instid,
               hi_ushort16 us_attr,
               hi_uint32 ui_tablen
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_olt_tab_set(hi_uchar8 *puc_item,
				   hi_ushort16 us_instid, hi_ushort16 us_attr,
				   hi_uint32 ui_tablen, hi_uint32 *pui_tabindex)
{
	hi_uint32 ui_ret;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_size;
	hi_uint32 ui_tabindex;
	hi_uchar8 *puc_tab;
	hi_uchar8 *puc_tabitem;

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, us_attr, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_size = ui_tabnum * ui_tablen;
	puc_tab = (hi_void *)hi_os_malloc(ui_size);
	if (HI_NULL == puc_tab) {
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, ui_size, ui_tabnum, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	/* The first byte is the entry index, and the remaining 16 bytes
	 * are the content of the entry.*/
	ui_tabindex = puc_item[0];

	if (ui_tabindex == 0) {
		/* The OLT can clear the table with a set operation to index 0. */
		HI_OS_MEMSET_S(puc_tab, ui_size, 0, ui_size);
	} else if (ui_tabindex > ui_tabnum || ui_tabindex == 0) {
		/* the setting is out of range. */
		hi_os_free(puc_tab);
		hi_omci_misc_systrace(HI_RET_DEVFULL, ui_tabindex, ui_tabnum, 0, 0);
		return HI_RET_DEVFULL;
	} else {
		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, us_attr, puc_tab);
		if (ui_ret != HI_RET_SUCC) {
			hi_os_free(puc_tab);
			hi_omci_misc_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		/* set the table item */
		puc_tabitem = puc_tab + (ui_tabindex - 1) * ui_tablen;
		HI_OS_MEMCPY_S(puc_tabitem, ui_tablen, puc_item, ui_tablen);
	}

	/* save the table item */
	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, us_attr, puc_tab);
	hi_os_free(puc_tab);
	HI_OMCI_RET_CHECK(ui_ret);

	*pui_tabindex = ui_tabindex;

	hi_omci_misc_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_olt_tab_clr
 Description : OLT random challenge table and OLT authentication result table
               clearing
 Input Parm  : hi_ushort16 us_instid,
               hi_ushort16 us_attr,
               hi_uint32 ui_tablen
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_olt_tab_clr(hi_ushort16 us_instid,
				   hi_ushort16 us_attr, hi_uint32 ui_tablen)
{
	hi_uint32 ui_ret;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_size;
	hi_uchar8 *puc_tab;

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, us_attr, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);

	ui_size = ui_tabnum * ui_tablen;
	puc_tab = (hi_void *)hi_os_malloc(ui_size);
	if (HI_NULL == puc_tab) {
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, ui_size, ui_tabnum, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	HI_OS_MEMSET_S(puc_tab, ui_size, 0, ui_size);

	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, us_attr, puc_tab);
	hi_os_free(puc_tab);
	HI_OMCI_RET_CHECK(ui_ret);

	hi_omci_misc_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_me_esc_bckey_tab_set
 Description : OLT random challenge table and OLT authentication result table
               setting
 Input Parm  : hi_omci_me_bc_key_tab_s *pst_bckey,
               hi_ushort16 us_instid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_me_esc_bckey_tab_set(hi_omci_me_bc_key_tab_s *pst_bckey, hi_ushort16 us_instid)
{
	hi_omci_me_bc_key_tab_s *pst_tab;
	hi_omci_me_bc_key_tab_s *pst_item;
	hi_uint32 ui_ret;
	hi_uint32 ui_tabnum;
	hi_uint32 ui_size;

	ui_ret = hi_omci_ext_get_tabnum(HI_OMCI_PRO_ME_ESC_E, HI_OMCI_ATTR11, &ui_tabnum);
	HI_OMCI_RET_CHECK(ui_ret);
	hi_omci_debug("[INFO]:ui_tabnum =0x%x\n", ui_tabnum);
	ui_size = ui_tabnum * sizeof(hi_omci_me_bc_key_tab_s);
	pst_tab = (hi_void *)hi_os_malloc(ui_size);
	if (HI_NULL == pst_tab) {
		hi_omci_misc_systrace(HI_RET_MALLOC_FAIL, ui_size, ui_tabnum, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}
	hi_omci_debug("[INFO]:pst_bckey->st_rowctl.ctrl =0x%x\n", pst_bckey->st_rowctl.ctrl);
	hi_omci_debug("[INFO]:pst_bckey->st_rowid.keyindex =0x%x\n", pst_bckey->st_rowid.keyindex);
	if (HI_OMCI_ME_BCKEY_CLRALL == pst_bckey->st_rowctl.ctrl) {
		HI_OS_MEMSET_S(pst_tab, ui_size, 0, ui_size);

		ui_ret = hi_omci_tapi_mckey_del(0);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_os_free(pst_tab);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		ui_ret = hi_omci_tapi_mckey_del(1);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_os_free(pst_tab);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}
	} else if (pst_bckey->st_rowid.keyindex > ui_tabnum
		   || pst_bckey->st_rowid.keyindex == 0) {
		hi_os_free(pst_tab);
		hi_omci_misc_systrace(HI_RET_DEVFULL, pst_bckey->st_rowid.keyindex, ui_tabnum, 0, 0);
		return HI_RET_DEVFULL;
	} else if (HI_OMCI_ME_BCKEY_SET == pst_bckey->st_rowctl.ctrl) {
		//pst_item = pst_tab + (pst_bckey->st_rowid.keyindex - 1) * sizeof(hi_omci_me_bc_key_tab_s);
		pst_item = pst_tab + (pst_bckey->st_rowid.keyindex - 1);
		HI_OS_MEMCPY_S(pst_item, sizeof(hi_omci_me_bc_key_tab_s), pst_bckey, sizeof(hi_omci_me_bc_key_tab_s));

		/* They always read back as 0 */
		pst_item->st_rowctl.ctrl = 0;
		hi_omci_debug("pst_bckey->auc_key: %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x\n", pst_bckey->auc_key[0]
			      , pst_bckey->auc_key[1], pst_bckey->auc_key[2], pst_bckey->auc_key[3], pst_bckey->auc_key[4]
			      , pst_bckey->auc_key[5], pst_bckey->auc_key[6], pst_bckey->auc_key[7], pst_bckey->auc_key[8]
			      , pst_bckey->auc_key[9], pst_bckey->auc_key[10], pst_bckey->auc_key[11], pst_bckey->auc_key[12]
			      , pst_bckey->auc_key[13], pst_bckey->auc_key[14], pst_bckey->auc_key[15]);
		ui_ret = hi_omci_tapi_mckey_set(pst_bckey->st_rowid.keyindex - 1, HI_OMCI_KEY_LEN, pst_bckey->auc_key);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_os_free(pst_tab);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}
	} else if (HI_OMCI_ME_BCKEY_CLR == pst_bckey->st_rowctl.ctrl) {
		//pst_item = pst_tab + (pst_bckey->st_rowid.keyindex - 1) * sizeof(hi_omci_me_bc_key_tab_s);
		pst_item = pst_tab + (pst_bckey->st_rowid.keyindex - 1);
		HI_OS_MEMSET_S(pst_item, sizeof(hi_omci_me_bc_key_tab_s), 0, sizeof(hi_omci_me_bc_key_tab_s));

		ui_ret = hi_omci_tapi_mckey_del(0);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_os_free(pst_tab);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}

		ui_ret = hi_omci_tapi_mckey_del(1);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);
			hi_os_free(pst_tab);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}
	}

	/* save the table item */
	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR11, pst_tab);
	hi_os_free(pst_tab);
	HI_OMCI_RET_CHECK(ui_ret);

	hi_omci_misc_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_me_esc_set
 Description : Enhanced security control set
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_me_esc_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_esc_s *pst_inst = (hi_omci_me_esc_s *)pv_data;
	hi_uint32 ui_ret;
	hi_ushort16 us_instid = pst_inst->st_msghead.us_instid;
	hi_ushort16 us_mask = pst_inst->st_msghead.us_attmask;
	hi_uchar8 uc_crypto_cap;

	if (HI_OMCI_ME_ESC_IS_OLT_CAP(us_mask)) {
		/* ONU selected crypto capabilities. Its value specifies one of the bit
		 * positions that has the value 1 in the OLT crypto capabilities attribute.
		 * support AES-CMAC-128 only. */
		if (pst_inst->auc_olt_cap[15] & HI_OMCI_ME_ESC_CRYPTO_AES_CMAC_128) {
			uc_crypto_cap = HI_OMCI_ME_ESC_CRYPTO_AES_CMAC_128;
			ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_ESC_E, us_instid, HI_OMCI_ATTR4, &uc_crypto_cap);
			HI_OMCI_RET_CHECK(ui_ret);
		}
	}

	if (HI_OMCI_ME_ESC_IS_OLT_TAB(us_mask)) {
		ui_ret = __omci_me_esc_olt_tab_set(pst_inst->auc_olt_tab, us_instid,
						   HI_OMCI_ATTR2, HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN, &gui_olt_rand_tab_num);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	/* It controls and reports the status of the OLT crypto capabilities and the OLT
	 * random challenge table attributes. As a status report, the value false indicates
	 * that the values represented in those MEs are not complete. The value true
	 * indicates that they are complete. This attribute behaves as follows:
	 * If the OLT writes to either the OLT crypto capabilities or the OLT
	 * random challenge table, then the OLT challenge status attribute
	 * becomes false.
	 * If the OLT challenge status attribute is false and the OLT sets the
	 * OLT challenge status attribute to true, the ONU begins processing the
	 * contents of the OLT crypto capabilities and OLT random challenge
	 * table using the selected cryptographic hash algorithm. */
	if (HI_OMCI_ME_ESC_IS_OLT_STA(us_mask) && HI_TRUE == pst_inst->uc_olt_status) {
		ui_ret = __omci_me_esc_auth_handler(HI_OMCI_ME_ESC_OLT_CHALLENGE_SET, pst_inst);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	if (HI_OMCI_ME_ESC_IS_OLT_RET_TAB(us_mask)) {
		ui_ret = __omci_me_esc_olt_tab_set(pst_inst->auc_olt_auth_ret_tab, us_instid,
						   HI_OMCI_ATTR7, HI_OMCI_OLT_AUTH_RET_TAB_ITME_LEN, &gui_olt_rest_tab_num);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	/* As a status report, the
	 * value false indicates that the value represented in the OLT authentication
	 * result table is not complete. The value true indicates that it is complete. This
	 * attribute behaves as follows:
	 * If the OLT writes to the OLT authentication result table, then OLT
	 * result status becomes false.
	 * If the OLT result status is false and the OLT sets the OLT result status
	 * to true, the ONU begins processing the contents of the OLT
	 * authentication result table using the specified algorithm. */
	if (HI_OMCI_ME_ESC_IS_OLT_RET_STA(us_mask)) {
		ui_ret = __omci_me_esc_auth_handler(HI_OMCI_ME_ESC_OLT_AUTH_RET, pst_inst);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	if (HI_OMCI_ME_ESC_IS_BCKEY(us_mask)) {
		ui_ret = __omci_me_esc_bckey_tab_set(&pst_inst->st_bckey, us_instid);
		HI_OMCI_RET_CHECK(ui_ret);
	}

	hi_omci_misc_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_esc_init
 Description : Enhanced security control init
 Input Parm  :
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_esc_init()
{
	hi_omci_me_esc_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = 0;

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_ESC_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	st_entity.st_msghead.us_instid = us_instid;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_ESC_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = __omci_me_esc_olt_tab_clr(us_instid,
					  HI_OMCI_ATTR2, HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = __omci_me_esc_olt_tab_clr(us_instid,
					  HI_OMCI_ATTR7, HI_OMCI_OLT_AUTH_RET_TAB_ITME_LEN);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_esc_exit
 Description : Enhanced security control exit
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_esc_exit()
{
	hi_omci_me_esc_s st_entity;
	hi_uint32 ui_ret;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	ui_ret = __omci_me_esc_auth_handler(HI_OMCI_ME_ESC_NEW_MSK_SET, &st_entity);
	HI_OMCI_RET_CHECK(ui_ret);

	return HI_RET_SUCC;
}
