/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_large_string.c
  Version    : 初稿
  Author     :
  Creation   :
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_me_large_string_set
 Description : Large string set
               在数据库操作后处理
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_large_string_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_large_string_s *pst_entity = (hi_omci_me_large_string_s *)pv_data;
	hi_omci_me_sip_agentdata_s st_agent;
	hi_omci_me_udptcp_cfg_s st_udptcp;
	hi_omci_tapi_voip_server_s st_server;
	hi_omci_me_large_string_s st_string;
	hi_omci_me_network_addr_s st_network;
	hi_omci_me_tr069_mng_server_s st_tr069_server;
	hi_omci_tapi_tr069_server_s st_tr069_tapi;
	hi_ushort16 us_mask = pst_entity->st_msghead.us_attmask;
	hi_ushort16 us_instid = pst_entity->st_msghead.us_instid;
	hi_char8  uc_ptr[HI_OMCI_TAPI_VOIP_STR_LEN];
	hi_uint32 ui_instnum = 0;
	hi_uint32 aui_instid[4] = {0};
	hi_uint32 ui_index = 0;
	hi_uint32 ui_index1 = 0;
	hi_uint32 ui_reg_flag = 0;
	hi_char8 *puc_server = HI_NULL;
	hi_uint32    i_ret = HI_RET_SUCC;
	hi_char8 *savePtr = HI_NULL;
	hi_omci_me_auth_secur_method_s st_auth;
	memset(&st_auth, 0, sizeof(st_auth));
	memset(&st_udptcp, 0, sizeof(st_udptcp));

	hi_omci_debug("[INFO]:us_instid=0x%x,mask=0x%x\n", us_instid, us_mask);
	hi_omci_debug("[INFO]:parts=%s\n", pst_entity->uc_part[0]);

	if ((HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR2)) ||
	    (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR3))) {
		/*Voip Branch*/
		if (HI_RET_SUCC == hi_omci_ext_get_install(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, aui_instid, 4, &ui_instnum)) {
			for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
				HI_OS_MEMSET_S(&st_agent, sizeof(st_agent), 0, sizeof(st_agent));

				if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_AGENT_CFG_DATA_E, (hi_ushort16)aui_instid[ui_index],
									&st_agent)) {
					HI_OS_MEMSET_S(&st_network, sizeof(st_network), 0, sizeof(st_network));
					if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_NETWORK_ADDR_E, st_agent.us_sip_registrar, &st_network)) {
						if (st_network.us_addr_ptr == us_instid) {
							ui_reg_flag = 1;
						}
					}
					if ((st_agent.us_psa_pointer == us_instid) || (st_agent.us_opa_pointer == us_instid) || (1 == ui_reg_flag)) {
						i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LARGE_STR_E, us_instid, &st_string);
						HI_OMCI_RET_CHECK(i_ret);
						i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TCP_UDP_CFG_DATA_E, st_agent.us_tu_pointer, &st_udptcp);
						if (HI_RET_SUCC == i_ret) {
							st_server.us_protocol = st_udptcp.uc_protocol;
						} else {
							st_server.us_protocol = 0xFFFF;
						}
						HI_OS_MEMSET_S(&st_server, sizeof(st_server), 0, sizeof(st_server));
						HI_OS_MEMSET_S(uc_ptr, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);

						if (st_string.uc_largestring > 5) {
							st_string.uc_largestring = 5;
						}
						for (ui_index1 = 0; ui_index1 < st_string.uc_largestring; ui_index1++) {
							HI_OS_STRCAT_S(uc_ptr, sizeof(uc_ptr), (hi_char8 *)st_string.uc_part[ui_index1]);
						}
						puc_server = hi_os_strtok_r(uc_ptr, ":", &savePtr);
						if (HI_NULL != puc_server) {
							HI_OS_STRCAT_S((hi_char8 *)st_server.uc_server, sizeof(st_server.uc_server), (hi_char8 *)puc_server);
							puc_server = hi_os_strtok_r(HI_NULL, ":", &savePtr);
							if (HI_NULL != puc_server) {
								HI_OS_SSCANF_S(puc_server, "%u", &st_server.us_portid);
							}
						}

						if (st_agent.us_psa_pointer == us_instid) {
							i_ret = hi_omci_tapi_voip_proxyserver_set(&st_server);
							HI_OMCI_RET_CHECK(i_ret);

							/*如没配置reg服务器信息，则和proxy服务器信息一致*/
							i_ret = hi_omci_tapi_voip_regserver_set(&st_server);
							HI_OMCI_RET_CHECK(i_ret);
						} else if (st_agent.us_opa_pointer == us_instid) {
							i_ret = hi_omci_tapi_voip_outbserver_set(&st_server);
							HI_OMCI_RET_CHECK(i_ret);
						} else if (1 == ui_reg_flag) {
							i_ret = hi_omci_tapi_voip_regserver_set(&st_server);
							HI_OMCI_RET_CHECK(i_ret);
							ui_reg_flag = 0;
						}
					}
				}
			}
		}

		/*Tr069 Branch*/
		if (HI_RET_SUCC == hi_omci_ext_get_install(HI_OMCI_PRO_ME_TR069_MANAGEMENT_SERVER_E, aui_instid, 4, &ui_instnum)) {
			for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
				HI_OS_MEMSET_S(&st_tr069_server, sizeof(st_tr069_server), 0, sizeof(st_tr069_server));
				if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_TR069_MANAGEMENT_SERVER_E, (hi_ushort16)aui_instid[ui_index],
									&st_tr069_server)) {
					HI_OS_MEMSET_S(&st_network, sizeof(st_network), 0, sizeof(st_network));
					if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_NETWORK_ADDR_E, st_tr069_server.us_acsaddr, &st_network)) {
						if ((us_instid == st_network.us_addr_ptr) && (st_tr069_server.uc_adminstate == 0)) {
							HI_OS_MEMSET_S(&st_tr069_tapi, sizeof(st_tr069_tapi), 0, sizeof(st_tr069_tapi));
							i_ret = hi_omci_tapi_tr069_server_get(&st_tr069_tapi);
							HI_OMCI_RET_CHECK(i_ret);
							hi_omci_debug("[INFO]:tr069.adminstate=%hhu acsaddr=%s acsaddr_entity=%s\n", st_tr069_server.uc_adminstate,
								      st_tr069_tapi.uc_acsaddr, pst_entity->uc_part[0]);
							HI_OS_MEMSET_S(st_tr069_tapi.uc_acsaddr, HI_OMCI_TAPI_TR069_ACS_LEN, 0, HI_OMCI_TAPI_TR069_ACS_LEN);
							HI_OS_STRCAT_S((hi_char8 *)st_tr069_tapi.uc_acsaddr, sizeof(st_tr069_tapi.uc_acsaddr),
								       (hi_char8 *)pst_entity->uc_part[0]);

							if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_ATH_SECR_METHOD_E, st_network.us_secur_ptr, &st_auth)) {
								HI_OS_MEMCPY_S(st_tr069_tapi.uc_auth_username, sizeof(st_tr069_tapi.uc_auth_username), st_auth.uc_username1,
									       sizeof(st_auth.uc_username1));
								HI_OS_STRCAT_S((hi_char8 *)st_tr069_tapi.uc_auth_username, sizeof(st_tr069_tapi.uc_auth_username),
									       (hi_char8 *)st_auth.uc_username2);
								HI_OS_MEMCPY_S(st_tr069_tapi.uc_auth_password, sizeof(st_tr069_tapi.uc_auth_password), st_auth.uc_password,
									       sizeof(st_auth.uc_password));
							}

							i_ret = hi_omci_tapi_tr069_server_set(&st_tr069_tapi);
							HI_OMCI_RET_CHECK(i_ret);
						}
					}
				}
			}
		}
	}

	hi_omci_me_sip_userdata_s st_entity;
	hi_omci_tapi_voip_auth_s st_tapiauth = { 0 };
	hi_ushort16 us_pptp_instid = 0;

	if ((HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR2)) &&
	    (HI_OMCI_TRUE == HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR1))) {
		if (HI_RET_SUCC == hi_omci_ext_get_install(HI_OMCI_PRO_ME_SIP_USER_DATA_E, aui_instid, 4, &ui_instnum)) {
			for (ui_index = 0; ui_index < ui_instnum; ui_index++) {
				memset_s(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
				if (HI_RET_SUCC == hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SIP_USER_DATA_E,
									(hi_ushort16)aui_instid[ui_index], &st_entity)) {
					if (st_entity.us_up_aor != us_instid) {
						continue;
					}
					us_pptp_instid = st_entity.us_pptp_pointer;
					if (0xffff == us_pptp_instid) {
						us_pptp_instid = HI_OMCI_GET_INSTID_FORM_VOIP_PORTID(1);
					}

					memset_s(&st_tapiauth, sizeof(st_tapiauth), 0, sizeof(st_tapiauth));
					st_tapiauth.ui_lineidx = HI_OMCI_GET_VOIP_PORTID_FROM_INSTID(us_pptp_instid);

					hi_omci_tapi_voip_sipauth_get(&st_tapiauth);
					memset_s(&st_tapiauth.uc_number, HI_OMCI_TAPI_VOIP_STR_LEN, 0, HI_OMCI_TAPI_VOIP_STR_LEN);
					strcat_s((hi_char8 *)st_tapiauth.uc_number, sizeof(st_tapiauth.uc_number),
						 (hi_char8 *)pst_entity->uc_part[0]);
					hi_omci_debug("[INFO]:uc_number=%s\n", st_tapiauth.uc_number);
					hi_omci_tapi_voip_sipauth_set(&st_tapiauth);

				}
			}
		}
	}

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

