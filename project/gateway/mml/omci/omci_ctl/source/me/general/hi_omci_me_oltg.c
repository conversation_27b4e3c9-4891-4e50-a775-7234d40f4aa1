/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_oltg.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-11-1
  Description: OMCI ME OLT-G
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/


hi_int32 hi_omci_me_oltg_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_olt_g_s *pst_inst = (hi_omci_me_olt_g_s *)pv_data;
	hi_ushort16 us_mask = pst_inst->st_msghead.us_attmask;

#if 0
	{
		hi_uint32 i;

		printf("\r\n[%s %d]us_mask = 0x%x", __FUNCTION__, __LINE__, us_mask);
		printf("\r\n[%s %d]sfc = 0x%x", __FUNCTION__, __LINE__, pst_inst->st_time.gemseqnum);
		printf("\r\n[%s %d]time: ", __FUNCTION__, __LINE__);
		for (i = 0; i < 10; i++) {
			printf("%02x ", pst_inst->st_time.uc_tstampn[i]);
		}
	}
#endif

	if (HI_OMCI_CHECK_ATTR(us_mask, HI_OMCI_ATTR4)) {
		(void)hi_omci_tapi_time_set((hi_uchar8 *) & (pst_inst->st_time));
	}

	return HI_RET_SUCC;
}
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_oltg_init
 Description : OLT-G init
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_oltg_init()
{
	hi_omci_me_olt_g_s st_entity;
	hi_int32 i_ret;
	hi_ushort16 us_instid = 0;

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_OLT_G_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	st_entity.st_msghead.us_instid = us_instid;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_OLT_G_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}
