/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Hisilicon optical info
 * Author: hsan
 * Create: 2024-01-12
 * History: 2024-01-12 coding
 */

#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

int32_t hi_omci_me_onu_optical_info_init()
{
	struct hi_omci_me_optical_info entity = {0};
	int32_t ret;
	uint16_t instid = 0;

	ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_CUC_OPT_INFOMATION_E, instid);
	if (ret != HI_RET_SUCC) {
		hi_omci_debug("[INFO]:ret=0x%x, %s line =%d\n", ret, __func__, __LINE__);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	entity.st_msghead.us_instid = instid;
	ret = hi_omci_tapi_optical_info_get(&entity.opt_info);
	if (ret != HI_RET_SUCC) {
		hi_omci_debug("[INFO]:ret=0x%x, %s line =%d\n", ret, __func__, __LINE__);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_CUC_OPT_INFOMATION_E, instid, &entity);
	if (ret != HI_RET_SUCC) {
		hi_omci_debug("[INFO]:ret=0x%x, %s line =%d\n", ret, __func__, __LINE__);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	return HI_RET_SUCC;
}
