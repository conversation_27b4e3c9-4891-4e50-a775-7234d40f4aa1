/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_onu_capability.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-31
  Description: CTC ONU capcbility
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"

static int32_t hi_omci_me_onu_operator_capability_init(uint16_t meid, hi_omci_tapi_sysinfo_s *sysinfo)
{
	hi_omci_me_onu_capability_s entity = {0};
	int32_t ret;
	uint16_t instid = 0;

	ret = hi_omci_ext_create_inst(meid, instid);
	if (ret != HI_RET_SUCC) {
		hi_omci_debug("[INFO]:ret=0x%x, %s line =%d\n", ret, __func__, __LINE__);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	entity.st_msghead.us_instid = instid;
	entity.uc_type = (uint8_t)sysinfo->ui_product_mode;

	if (meid == HI_OMCI_PRO_ME_CTC_ONU_CAPABILITY_E) {
		entity.auc_operatorid[0] = 'C';
		entity.auc_operatorid[1] = 'T';
		entity.auc_operatorid[2] = 'C';
	} else {
		entity.auc_operatorid[0] = 'C';
		entity.auc_operatorid[1] = 'U';
		entity.auc_operatorid[2] = 'C';
	}
	entity.uc_txpower_ctrl = HI_OMCI_ME_ONU_CAPABILITY_TXPOWER_UNITE_E;
	entity.uc_version = 0x3;

	ret = hi_omci_ext_set_inst(meid, instid, &entity);
	if (ret != HI_RET_SUCC) {
		hi_omci_debug("[INFO]:ret=0x%x, %s line =%d\n", ret, __func__, __LINE__);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	return HI_RET_SUCC;
}

int32_t hi_omci_me_onu_capability_init()
{
	hi_omci_tapi_sysinfo_s sysinfo = {0};
	int32_t ret;

	ret = hi_omci_tapi_sysinfo_get(&sysinfo);
	if (ret != HI_RET_SUCC) {
		hi_omci_debug("[INFO]:ret=0x%x, %s line=%d\n", ret, __func__, __LINE__);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	ret = hi_omci_me_onu_operator_capability_init(HI_OMCI_PRO_ME_CTC_ONU_CAPABILITY_E, &sysinfo);
	if (ret != HI_RET_SUCC) {
		hi_omci_debug("[INFO]:ret=0x%x, %s line=%d\n", ret, __func__, __LINE__);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	ret = hi_omci_me_onu_operator_capability_init(HI_OMCI_PRO_ME_CUC_ONU_CAPABILITY_E, &sysinfo);
	if (ret != HI_RET_SUCC) {
		hi_omci_debug("[INFO]:ret=0x%x, %s line=%d\n", ret, __func__, __LINE__);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	return HI_RET_SUCC;
}
