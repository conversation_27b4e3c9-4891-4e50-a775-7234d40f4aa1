/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_loid_auth.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-31
  Description: LOID authentication
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
#include "hi_ipc.h"
#include "hi_notifier.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_loid_auth_init
 Description : LOID authentication init
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_loid_auth_init()
{
	hi_omci_me_loid_auth_s st_entity;
	hi_omci_tapi_sn_loid_s st_sn_loid;
	hi_omci_tapi_sysinfo_s sysinfo = { 0 };
	hi_int32 i_ret;
	hi_ushort16 us_instid = 0;//单实例，ME-ID=0

	/*需要从tapi层获取文件存放的loid的值*/
	i_ret = hi_omci_tapi_sn_loid_get(&st_sn_loid);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_LOID_AUTH_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	st_entity.st_msghead.us_instid = us_instid;

	i_ret = hi_omci_tapi_sysinfo_get(&sysinfo);
	HI_OMCI_RET_CHECK(i_ret);
	if (strcmp((char *)sysinfo.auc_operator, "CU") == 0) {
		st_entity.uc_operaid[0] = 'C';
		st_entity.uc_operaid[1] = 'U';
		st_entity.uc_operaid[2] = 'C';
	} else {
		st_entity.uc_operaid[0] = 'C';
		st_entity.uc_operaid[1] = 'T';
		st_entity.uc_operaid[2] = 'C';
	}

	HI_OS_MEMCPY_S(st_entity.uc_loid, sizeof(st_entity.uc_loid), st_sn_loid.auc_loid, sizeof(st_entity.uc_loid));
	HI_OS_MEMCPY_S(st_entity.uc_password, sizeof(st_entity.uc_password), st_sn_loid.auc_lopwd,
		       sizeof(st_entity.uc_password));

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_LOID_AUTH_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_loid_auth_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_int32 i_ret;
	hi_uint32 ui_state;
	hi_omci_me_loid_auth_s *pst_entity = (hi_omci_me_loid_auth_s *)pv_data;
	hi_omci_notifier_data_s st_notifier_data = { 0 };
	hi_omci_tapi_sysinfo_s st_info = { 0 };

	hi_omci_debug("authstatus : %hhu \n", pst_entity->uc_authstatus);
	if (pst_entity->uc_authstatus > HI_OMCI_ME_LOID_DUP_LOID) {
		hi_omci_systrace(HI_OMCI_PRO_ERR_PARA_ERR_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	if (0 != hi_os_strcasecmp((hi_char8 *)st_info.auc_operator, "CMCC")) {
		st_notifier_data.em_type = HI_OMCI_NOTIFY_LOID_REGSTA_E;
		st_notifier_data.ui_data = pst_entity->uc_authstatus;
#ifdef CONFIG_PLATFORM_OPENWRT
        HI_IPC_CALL("hi_sml_owal_led_gpon_proc", &st_notifier_data);
#else
		i_ret = hi_notifier_call(HI_OMCI_NOTIFIY_NAME, &st_notifier_data);
		HI_OMCI_RET_CHECK(i_ret);
#endif
		ui_state = (pst_entity->uc_authstatus <= HI_OMCI_ME_LOID_AUTH_SUCC ? HI_FALSE : pst_entity->uc_authstatus);
		i_ret = HI_IPC_CALL("hi_pon_set_fail_regstate", &ui_state);
		if (HI_RET_SUCC != i_ret) {
			return i_ret;
		}
	}
	return HI_RET_SUCC;
}

hi_int32 hi_omci_me_loid_auth_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_int32 i_ret;
	hi_ushort16 us_instid = 0;
	hi_omci_me_loid_auth_s st_entity;
	hi_omci_tapi_sn_loid_s st_sn_loid;

	i_ret = hi_omci_tapi_sn_loid_get(&st_sn_loid);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LOID_AUTH_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMCPY_S(st_entity.uc_loid, sizeof(st_entity.uc_loid), st_sn_loid.auc_loid, sizeof(st_entity.uc_loid));
	HI_OS_MEMCPY_S(st_entity.uc_password, sizeof(st_entity.uc_password), st_sn_loid.auc_lopwd,
		       sizeof(st_entity.uc_password));

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_LOID_AUTH_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	return HI_RET_SUCC;
}
