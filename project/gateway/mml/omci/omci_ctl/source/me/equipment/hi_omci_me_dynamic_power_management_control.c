/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_dynamic_power_management_control.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-12-23
  Description: OMCI ME dynamic power management control
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_ME_POWERMNG_IS_MNG_MODE(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR2)
#define HI_OMCI_ME_POWERMNG_IS_MAX_SLEEP(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR5)
#define HI_OMCI_ME_POWERMNG_IS_MIN_AWARE(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR6)
#define HI_OMCI_ME_POWERMNG_IS_MIN_ACTHELD(mask) HI_OMCI_CHECK_ATTR(mask, HI_OMCI_ATTR7)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_powermng_set
 Description : dynamic power management control set 配置数据库后处理
 Input Parm  : hi_void*pv_data, hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_powermng_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
	hi_omci_me_powermng_s *pst_inst = (hi_omci_me_powermng_s *)pv_data;
	hi_omci_tapi_pmu_attr_s st_attr;
	hi_int32 i_ret;
	hi_ushort16 us_mask = pst_inst->st_msghead.us_attmask;

	i_ret = hi_omci_tapi_pmu_attr_get(&st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_OMCI_ME_POWERMNG_IS_MNG_MODE(us_mask)) {
		st_attr.ui_reductmode = pst_inst->uc_reductmode;
	}

	if (HI_OMCI_ME_POWERMNG_IS_MAX_SLEEP(us_mask)) {
		st_attr.ui_maxsleep = pst_inst->ui_maxsleep;
	}

	if (HI_OMCI_ME_POWERMNG_IS_MIN_AWARE(us_mask)) {
		st_attr.ui_minaware = pst_inst->ui_minaware;
	}

	if (HI_OMCI_ME_POWERMNG_IS_MIN_ACTHELD(us_mask)) {
		st_attr.ui_minactive = pst_inst->us_minactive;
	}

	i_ret = hi_omci_tapi_pmu_attr_set(&st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_equ_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_powermng_init
 Description : dynamic power management control初始化，创建MIB实例
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_powermng_init()
{
	hi_omci_me_powermng_s st_entry;
	hi_omci_tapi_pmu_attr_s st_attr;
	hi_int32 i_ret;
	hi_ushort16 us_instid = 0;

	/* 不一定有节能实体功能
	 * 如果不支持节能，此处返回错误，不创建节能实体
	 */
	i_ret = hi_omci_tapi_pmu_attr_get(&st_attr);
	if (HI_RET_SUCC != i_ret) {
		hi_omci_equ_systrace(i_ret, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_DYNAMIC_POWER_MNG_CONTROL_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	st_entry.st_msghead.us_instid = us_instid;
	st_entry.uc_reductcap = (hi_uchar8)st_attr.ui_reductcap;
	st_entry.uc_reductmode = (hi_uchar8)st_attr.ui_reductmode;
	st_entry.us_itransinit = (hi_ushort16)st_attr.ui_itransinit;
	st_entry.us_itxinit = (hi_ushort16)st_attr.ui_itxinit;
	st_entry.ui_maxsleep = st_attr.ui_maxsleep;
	st_entry.ui_minaware = st_attr.ui_minaware;
	st_entry.us_minactive = (hi_ushort16)st_attr.ui_minactive;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_DYNAMIC_POWER_MNG_CONTROL_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_equ_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

