/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_cardholder.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-25
  Description: cardholder
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

hi_int32 hi_omci_me_cardholder_init(hi_uint32 ui_isup, hi_uint32 ui_index, hi_uint32 ui_ishgu)
{
	hi_omci_me_cardholder_s st_entry;
	hi_omci_tapi_sysinfo_s st_info;
	hi_int32 i_ret = 0;
	hi_uint32 ui_unit_type = 0;
	hi_uint32 ui_portnum = 0;
	hi_ushort16 us_instid = 0;

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	if (HI_TRUE == ui_isup) {
		us_instid = (0x0100 | (1 << 7));
		ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_GPON24881244;
		ui_portnum = 1;
	} else if (HI_TRUE == ui_ishgu) {
		us_instid = (0x0100 | 6);
		ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_VEIP;
		ui_portnum = 1;
	} else {
		us_instid = (0x0100 | (ui_index & 0xff));//ui_index 0:伪槽位  1-254:自定义
		if (2 == ui_index) {
			ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_POTS;
		} else if (0 == ui_index) {
			if (0  == st_info.ui_fe_num) {
				hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
				return HI_RET_SUCC;
			}
			ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_10100BASET;
			ui_portnum = st_info.ui_fe_num;
		} else if (1 == ui_index) {
			if (0  == st_info.ui_ge_num) {
				hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
				return HI_RET_SUCC;
			}
			ui_unit_type = HI_OMCI_ME_PLUGIN_UNIT_TYPE_101001000BASET;
			ui_portnum = st_info.ui_ge_num;
		} else {
			hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
			return HI_RET_SUCC;
		}
	}

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_CARD_HOLDER_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entry, sizeof(st_entry), 0, sizeof(st_entry));
	st_entry.st_msghead.us_instid = us_instid;
	st_entry.uc_acttype = (hi_uchar8)ui_unit_type;
	st_entry.uc_exptype = (hi_uchar8)ui_unit_type;
	st_entry.uc_expportcnt = (hi_uchar8)ui_portnum;
	HI_OS_MEMCPY_S(st_entry.uc_expequipid, sizeof(st_entry.uc_expequipid), st_info.auc_equid,
		       sizeof(st_entry.uc_expequipid));
	HI_OS_MEMCPY_S(st_entry.uc_actequipid, sizeof(st_entry.uc_actequipid), st_info.auc_equid,
		       sizeof(st_entry.uc_actequipid));

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_CARD_HOLDER_E, us_instid, &st_entry);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

