/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_software_image.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-31
  Description: OMCI manager entity software image file.
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"
#include "hi_omci_me_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
#include "hi_cfm_api.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define UPGRADE_STATE_FILE    HI_CONF_WORK_DIR"/upgrade_state.txt"
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_softwareimage_init
 Description : software image init
               支持双备份，默认image0为主区
 Input Parm  : hi_uint32 ui_index 指示image编号
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_softwareimage_init(hi_uint32 ui_index)
{
	hi_omci_me_software_image_msg_s st_entity;
	hi_omci_tapi_sysinfo_s st_info;
	hi_int32 i_ret;
	char cmd_buff[256];
	hi_char8 auc_chr_version[14] = {0};
	hi_char8 *puc_chr = HI_NULL;
	hi_uint32 ui_active_temp = 0;
	hi_uint32 ui_commited_temp = 0;
	hi_uint32 ui_version_id_temp = 0;
	HI_FILE_S *pf_file;
	int i_upgrade = access(UPGRADE_STATE_FILE, F_OK);
	HI_OS_MEMSET_S(cmd_buff, sizeof(cmd_buff), 0, sizeof(cmd_buff));

	/* 创建upgrade state file,保存升级状态和历史版本号 */
	if (0 != i_upgrade) {
		HI_OS_MEMSET_S(cmd_buff, sizeof(cmd_buff), 0, sizeof(cmd_buff));
		HI_OS_SPRINTF_S(cmd_buff, sizeof(cmd_buff), "touch %s", UPGRADE_STATE_FILE);
		system(cmd_buff);
		HI_OS_MEMSET_S(cmd_buff, sizeof(cmd_buff), 0, sizeof(cmd_buff));
		HI_OS_SPRINTF_S(cmd_buff, sizeof(cmd_buff), "echo active=0 >> %s", UPGRADE_STATE_FILE);
		system(cmd_buff);
		HI_OS_MEMSET_S(cmd_buff, sizeof(cmd_buff), 0, sizeof(cmd_buff));
		HI_OS_SPRINTF_S(cmd_buff, sizeof(cmd_buff), "echo commited=0 >> %s", UPGRADE_STATE_FILE);
		system(cmd_buff);
		HI_OS_MEMSET_S(cmd_buff, sizeof(cmd_buff), 0, sizeof(cmd_buff));
		HI_OS_SPRINTF_S(cmd_buff, sizeof(cmd_buff), "echo version_id=0 >> %s", UPGRADE_STATE_FILE);
		system(cmd_buff);
		HI_OS_MEMSET_S(cmd_buff, sizeof(cmd_buff), 0, sizeof(cmd_buff));
		HI_OS_SPRINTF_S(cmd_buff, sizeof(cmd_buff), "echo sw_version= >> %s", UPGRADE_STATE_FILE);
		system(cmd_buff);
	}

	/* ME-ID 1st byte：ONU(0) or a cardholder (1..254)
	 * ME-ID 2nd byte：software image ME instances(0、1)*/
	hi_ushort16 us_instid = (hi_ushort16)ui_index;

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	i_ret = hi_omci_ext_create_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));

	/* 基于升级文件对me进行赋值 */
	pf_file = hi_os_fopen(UPGRADE_STATE_FILE, "r+");
	if (HI_NULL == pf_file) {
		hi_os_printf("failt to open :%s\n", UPGRADE_STATE_FILE);
		return HI_RET_FAIL;
	}

	while (HI_NULL != hi_os_fgets(cmd_buff, sizeof(cmd_buff), pf_file)) {
		cmd_buff[hi_os_strlen(cmd_buff) - 1] = '\0';
		if (HI_NULL != hi_os_strstr(cmd_buff, "active=")) {
			puc_chr = hi_os_strchr(cmd_buff, '=');
			if (HI_NULL != puc_chr) {
				ui_active_temp = atoi(puc_chr + 1);
				st_entity.st_image.uc_active = (ui_index == ui_active_temp ? HI_TRUE : HI_FALSE);
			}
		}

		if (HI_NULL != hi_os_strstr(cmd_buff, "commited=")) {
			puc_chr = hi_os_strchr(cmd_buff, '=');
			if (HI_NULL != puc_chr) {
				ui_commited_temp = atoi(puc_chr + 1);
				st_entity.st_image.uc_commited = (ui_index == ui_commited_temp ? HI_TRUE : HI_FALSE);
			}
		}

		if (HI_NULL != hi_os_strstr(cmd_buff, "version_id=")) {
			puc_chr = hi_os_strchr(cmd_buff, '=');
			if (HI_NULL != puc_chr) {
				ui_version_id_temp = atoi(puc_chr + 1);

				if (ui_index == ui_version_id_temp) {
					HI_OS_MEMCPY_S(st_entity.st_image.uc_version, sizeof(st_entity.st_image.uc_version), st_info.auc_soft_version,
						       sizeof(st_entity.st_image.uc_version));
				}
			}
		}
		/* 初始化sw_version=为空，需要特殊处理 */
		if (HI_NULL != hi_os_strstr(cmd_buff, "sw_version=")) {
			puc_chr = hi_os_strchr(cmd_buff, '=');
			if (HI_NULL != puc_chr) {
				if (0 == hi_os_strcmp(cmd_buff, "sw_version=")) {
					HI_OS_MEMCPY_S(st_entity.st_image.uc_version, sizeof(st_entity.st_image.uc_version), st_info.auc_soft_version,
						       sizeof(st_entity.st_image.uc_version));
				} else if (ui_index != ui_version_id_temp) {
					/* sw_version超过13字节，需要做截断处理 */
					if (strlen(puc_chr + 1) > 13) {
						HI_OS_MEMCPY_S(auc_chr_version, sizeof(auc_chr_version), (puc_chr + 1), sizeof(auc_chr_version));
						auc_chr_version[13] = '\0';
						HI_OS_SPRINTF_S(st_entity.st_image.uc_version, sizeof(st_entity.st_image.uc_version), "%s", auc_chr_version);
					} else {
						HI_OS_SPRINTF_S(st_entity.st_image.uc_version, sizeof(st_entity.st_image.uc_version), "%s", (puc_chr + 1));
					}
				}
			}
		}
	}
	hi_os_fclose(pf_file);
	st_entity.st_image.uc_valid = HI_TRUE;

	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_me_softwareimage_get
 Description : Software Image gwt
               在数据库操作前处理
               实时获取主备分区版本号信息
 Input Parm  : hi_void*pv_data
               hi_uint32 ui_in
 Output Parm : hi_uint32 *pui_outlen
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_me_softwareimage_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_int32  i_ret = 0;
	hi_omci_tapi_sysinfo_s st_info;
	hi_omci_me_software_image_msg_s st_entity;
	HI_FILE_S *pf_file;
	hi_ushort16 us_instid = 0;
	char cmd_buff[256];
	hi_char8 auc_chr_version[14] = {0};
	hi_char8 *puc_chr = HI_NULL;

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	/* 基于升级文件对me进行赋值 */
	pf_file = hi_os_fopen(UPGRADE_STATE_FILE, "r+");
	if (HI_NULL == pf_file) {
		hi_os_printf("failt to open :%s\n", UPGRADE_STATE_FILE);
		hi_omci_systrace(HI_RET_FAIL, 0, 0, 0, 0);
		return HI_RET_FAIL;
	}

	while (HI_NULL != hi_os_fgets(cmd_buff, sizeof(cmd_buff), pf_file)) {
		cmd_buff[hi_os_strlen(cmd_buff) - 1] = '\0';
		if (HI_NULL != hi_os_strstr(cmd_buff, "active=")) {
			puc_chr = hi_os_strchr(cmd_buff, '=');
			if (HI_NULL != puc_chr) {
				us_instid = atoi(puc_chr + 1); /* 主分区实体号 */
			}
		}
		if (HI_NULL != hi_os_strstr(cmd_buff, "sw_version=")) {
			puc_chr = hi_os_strchr(cmd_buff, '=');
			if (HI_NULL != puc_chr) {
				HI_OS_MEMCPY_S(auc_chr_version, sizeof(auc_chr_version), (puc_chr + 1), sizeof(auc_chr_version));
			}
		}
	}
	hi_os_fclose(pf_file);

	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMCPY_S(st_entity.st_image.uc_version, sizeof(st_entity.st_image.uc_version),
		       st_info.auc_soft_version, sizeof(st_entity.st_image.uc_version));
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);

	us_instid = (us_instid == 1) ? 0 : 1; /* 备分区实体号 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMCPY_S(st_entity.st_image.uc_version, sizeof(st_entity.st_image.uc_version),
		       auc_chr_version, sizeof(st_entity.st_image.uc_version));
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
