include $(HI_EXT_CONFIG)
#===============================================================================
# export sub dir
#===============================================================================
HI_SUB_DIR :=
HI_SUB_DIR += source/me/ani
HI_SUB_DIR += source/me/equipment
HI_SUB_DIR += source/me/layer2
HI_SUB_DIR += source/me/layer3
HI_SUB_DIR += source/me/ethernet
HI_SUB_DIR += source/me/general
HI_SUB_DIR += source/me/voip
HI_SUB_DIR += source/me/alarm
HI_SUB_DIR += source/me/stat
HI_SUB_DIR += source/me/miscellaneous
HI_SUB_DIR += source/init
HI_SUB_DIR += source
#===============================================================================
# export lib
#===============================================================================
HI_LOC_LIB +=-lhi_basic -lhi_timer -lhi_notifier -lsqlite3  -lhi_omci_tapi -lhi_omci_sql
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/../omci_sql/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/../omci_tapi/include
HI_LOC_U_INCLUDE += -I$(HI_HISI_LINUX_DIR)/net/pon/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/basic/include/os
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/util/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/timer/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/notifier/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/ipc/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/ioreactor/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/sysinfo/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/board/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/pon/api/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/pon/sml/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/network/common/include
HI_LOC_U_INCLUDE += -I$(HI_OPENSRC_DIR)/sqlite3/sqlite-autoconf-3081101
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/upgrade/include
HI_LOC_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/cfm/cfm_lib

#
#===============================================================================
# target
#===============================================================================
TARGET 		= libhi_omci_ctl.so
TARGET_TYPE	= so

include $(HI_HISI_GW_APP_SCRIPT_DIR)/app.mk
