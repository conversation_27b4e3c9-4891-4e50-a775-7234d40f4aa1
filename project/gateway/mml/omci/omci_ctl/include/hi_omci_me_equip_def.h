/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : omci_me_equip_def.h
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2011_09_29

******************************************************************************/
#ifndef __OMCI_ME_EQUIP_DEF_H__
#define __OMCI_ME_EQUIP_DEF_H__

#include "hi_omci_config.h"
#include "hi_omci_tapi_optical.h"

/* ----------------cardholder   HI_OMCI_ME_CARD_HOLDER_E--------------- */
#define HI_OMCI_ME_PLUGIN_UNIT_TYPE_NOLIM           0
#define HI_OMCI_ME_PLUGIN_UNIT_TYPE_10100BASET      24
#define HI_OMCI_ME_PLUGIN_UNIT_TYPE_POTS      32
#define HI_OMCI_ME_PLUGIN_UNIT_TYPE_101001000BASET  47
#define HI_OMCI_ME_PLUGIN_UNIT_TYPE_VEIP            48
#define HI_OMCI_ME_PLUGIN_UNIT_TYPE_GPON24881244    248

#define HI_OMCI_EQUIPMENT_ID_LEN       20 /*Equipment ID length ,protocol*/
#pragma pack(1)
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_acttype;                                      /*actual plug-in unit type*/
	hi_uchar8   uc_exptype;                                      /*expected plug-in unit type*/
	hi_uchar8   uc_expportcnt;                                   /*expected Port number*/
	hi_uchar8   uc_expequipid[HI_OMCI_EQUIPMENT_ID_LEN];         /*expected Equipment ID*/
	hi_uchar8   uc_actequipid[HI_OMCI_EQUIPMENT_ID_LEN];         /*actual Equipment ID*/
	hi_uchar8   uc_protect_ptr;                                  /*protection profile pointer*/
	hi_uchar8   uc_protectswitch;                                /*invoke protection switch*/
} hi_omci_me_cardholder_s;

/* ----------------circuit pack   HI_OMCI_ME_CIRCUIT_PACK_E--------------- */

/*Cirucit Pack*/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_type;             /*Type*/
	hi_uchar8   uc_portnum;          /*Number of ports*/
	hi_uchar8   uc_sn[8];            /*Serial number*/
	hi_uchar8   uc_version[14];      /*Version*/
	hi_uint32   ui_vendorid;         /*Vendor id*/
	hi_uchar8   uc_adminstate;       /* Administrative state*/
	hi_uchar8   uc_operstate;        /*Operational state*/
	hi_uchar8   uc_bridge_ip;        /*Bridged or IP ind*/
	hi_uchar8   uc_equipid[20];      /*Equipment id*/
	hi_uchar8   uc_cardcfg;          /* Card configuration*/
	hi_uchar8   uc_tcontnum;         /* total T-CONT buffer number associated with the circuit pack*/
	hi_uchar8   uc_pqnum;            /* total PQ number associated with the circuit pack*/
	hi_uchar8   uc_tsnum;            /* total traffic scheduler number associated with the circuit pack*/
	hi_uint32   ui_powershed;        /* power shed override*/
} hi_omci_me_circuitpack_s;


/* ----------------software image   HI_OMCI_ME_SW_IMAGE_E--------------- */
typedef struct {
	hi_char8    uc_version[14];
	hi_uchar8   uc_commited;      /*Is committed*/
	hi_uchar8   uc_active;        /*Is active*/
	hi_uchar8   uc_valid;         /*Is valid*/
} hi_omci_me_software_image_s;
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_omci_me_software_image_s st_image;
} hi_omci_me_software_image_msg_s;

/* ----------------onu-g     HI_OMCI_ME_ONU_G_E--------------- */
typedef enum {
	HI_OMCI_ME_ONUG_REG_INIT = 0,
	HI_OMCI_ME_ONUG_AUTH_SUCC,
	HI_OMCI_ME_ONUG_LOID_ERR,
	HI_OMCI_ME_ONUG_PWD_ERR,
	HI_OMCI_ME_ONUG_DUP_LOID,
} hi_omci_me_onu_g_credentials_status;

typedef struct {
	hi_uchar8   uc_vendorid[4];       /*Vendor id*/
	hi_uchar8   uc_version[14];       /*Version*/
	hi_uchar8   uc_sn[8];             /*Serial Number*/
	hi_uchar8   uc_trafficflag;       /*Traffic management option*/
	hi_uchar8   uc_deprecated;        /*Deprecated*/
	hi_uchar8   uc_batterybackup;     /*Battery backup*/
	hi_uchar8   uc_adminstate;        /*Administrative state*/
	hi_uchar8   uc_operstate;         /*Operational state*/
	hi_uchar8   uc_onusurvival;       /*ONU survival time*/
	hi_uchar8   uc_loid[24];          /*Logical ONU ID（OPT）*/
	hi_uchar8   uc_passwd[12];        /*Logical password（OPT）*/
	hi_uchar8   uc_status;            /*Credentials status（OPT）*/
	hi_ushort16 tc_layer_option;      /*Extended TC-layer（OPT） */
} hi_omci_me_onu_g_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_omci_me_onu_g_s st_onug;
} hi_omci_me_onu_g_msg_s;

/* ----------------onu2-g     HI_OMCI_ME_ONU2_G_E--------------- */

/* G.984.4 2008 (2/08) */
#define HI_OMCI_ME_ONU2G_OMCC_984_4             0x84

/* G.984.4 2008 amd 1 (06/09) */
#define HI_OMCI_ME_ONU2G_OMCC_984_4_AMD1        0x85

/* G.984.4 2009 amd 2 (2009). Baseline message set only, without the extended message set option */
#define HI_OMCI_ME_ONU2G_OMCC_984_4_AMD2        0x86

/* G.984.4 2009 amd 2 (2009). Extended message set option, in addition to the baseline message set. */
#define HI_OMCI_ME_ONU2G_OMCC_984_4_AMD2_EXT    0x96

/* G.988 (2010). Baseline message set only, without the extended message set option */
#define HI_OMCI_ME_ONU2_OMCC_988                0xa0

/* G.988 amd 1 (2011). Baseline message set only */
#define HI_OMCI_ME_ONU2_OMCC_988_AMD1           0xa1

/* G.988 (2010). Baseline and extended message set */
#define HI_OMCI_ME_ONU2_OMCC_988_EXT            0xb0

/* G.988 amd 1 (2011). Baseline and extended message set */
#define HI_OMCI_ME_ONU2_OMCC_988_AMD1_EXT       0xb1

/* 1:MP map-filtering */
#define HI_OMCI_ME_ONU2_SERVICE_MODEL_1_MP_MAP_FILTER       (1 << 4)

/* N:MP bridge-map-filtering */
#define HI_OMCI_ME_ONU2_SERVICE_MODEL_N_MP_BR_MAP_FILTER    (1 << 6)

#define HI_OMCI_ME_ONU2_SEC_MODE_AES128           0x1
#define HI_OMCI_ME_ONU2_SEC_MODE_AES256           0x2
#define HI_OMCI_ME_ONU2_SEC_MODE_SM4              0x3

#define HI_GPON_KEY_MODE_AES128                   0
#define HI_GPON_KEY_MODE_AES256                   1
#define HI_GPON_KEY_MODE_SM4                      2

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_equipid[20]; /*Equipment id*/
	hi_uchar8   uc_omccversion; /*OMCC version*/
	hi_ushort16 us_vendorcode;  /*Vendor product code*/
	hi_uchar8   uc_secflag;     /*Security capability*/
	hi_uchar8   uc_secmode;     /*Security mode*/
	hi_ushort16 us_pqnum;       /*Total priority queue number*/
	hi_uchar8   uc_trafficnum;  /*Total traffic scheduler number*/
	hi_uchar8   uc_deprecated;  /*Deprecated*/
	hi_ushort16 us_gemportnum;  /*Total GEM port-ID number*/
	hi_uint32   ui_sysuptime;   /*SysUpTime*/
	hi_ushort16 us_concap;      /*Connectivity capability*/
	hi_uchar8   uc_curmode;     /*Current connectivity mode*/
	hi_ushort16 us_qoscfg;      /*QoS configuration flexibility*/
	hi_ushort16 us_pqscale;     /*Priority queue scale factor*/
} hi_omci_me_onu2_g_s;

/**************ONU data    HI_OMCI_ME_ONU_DATA_E*************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_mibsync;           /*MIB data sync*/
} hi_omci_me_onudata_s;

/*-------------ONU power shedding    HI_OMCI_ME_ONU_POWER_SHEDDING_E----------*/

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16 us_timerreset;  /*Restore power timer reset interval*/
	hi_ushort16 us_data;        /*Data class shedding interval*/
	hi_ushort16 us_voice;       /*Voice class shedding interval*/
	hi_ushort16 us_videooverlay;/*Video overlay class shedding interval*/
	hi_ushort16 us_videoreturn; /*Video return class shedding interval*/
	hi_ushort16 us_dsl;         /*DSL class shedding interval*/
	hi_ushort16 us_atm;         /*ATM class shedding interval*/
	hi_ushort16 us_ces;         /*CES class shedding interval*/
	hi_ushort16 us_frame;       /*Frame class shedding interval*/
	hi_ushort16 us_sdhsonet;    /*Sdh-sonet class shedding interval*/
	hi_ushort16 us_status;      /*Shedding status*/
} hi_omci_me_onu_power_s;

/*--------------Port mapping package   HI_OMCI_ME_PORT_MAP_PACKAGE_E-----------------*/
typedef struct {
	hi_uchar8   uc_phyport;
	hi_ushort16 us_equiptype[12];
} hi_omci_combinedport_s;

#define HI_OMCI_MAX_COMBINEDPORT_NUM 1

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_maxports;
	hi_ushort16 us_portlist1;
	hi_ushort16 us_portlist2;
	hi_ushort16 us_portlist3;
	hi_ushort16 us_portlist4;
	hi_ushort16 us_portlist5;
	hi_ushort16 us_portlist6;
	hi_ushort16 us_portlist7;
	hi_ushort16 us_portlist8;
	hi_omci_combinedport_s uc_combinedport[HI_OMCI_MAX_COMBINEDPORT_NUM];
} hi_omci_me_portmappkg_s;

/********Equipment extension package   HI_OMCI_ME_EQM_EXT_PACKAGE_E*********/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16 us_envsense;      /*Environmental sense*/
	hi_ushort16 us_contactoutput; /*Contact closure output*/
} hi_omci_me_equippkg_s;

/**********Protection data    HI_OMCI_ME_PROTECTION_DATA_E**************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16 us_working_ptr;     /*Working ANI-G pointer*/
	hi_ushort16 us_protect_ptr;     /*Protection ANI-G pointer*/
	hi_uchar8   uc_protecttype;
	hi_uchar8   uc_reverind;
	hi_ushort16 us_restoretime;
	hi_ushort16 us_switchtime;
} hi_omci_me_protectdata_s;

/************Equipment protection profile  HI_OMCI_ME_EQM_PROTECTION_PROFILE_E**************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_protectslot[2];
	hi_uchar8 uc_workingslot[8];
	hi_uchar8 uc_protectstatus[2];
	hi_uchar8 uc_reverind;
	hi_uchar8 uc_restoretime;
} hi_omci_me_equipprotect_s;

/**************ONU remote debug    HI_OMCI_ME_ONU_REMOTE_DEBUG_E***************/
#define HI_OMCI_ME_RMT_CMD_LEN 25
#define HI_OMCI_ME_RMT_DBG_REPLY_LEN 30  /*result length*/

typedef struct {
	hi_omci_me_msg_head_s st_msghead;                    /* Instance ID */
	hi_uchar8   uc_cmdfmt;                               /* Command Format,  0x0--ASCII, 0x1--free format. */
	hi_uchar8   uc_cmd[HI_OMCI_ME_RMT_CMD_LEN];          /* Command */
	hi_uchar8   uc_reply[HI_OMCI_ME_RMT_DBG_REPLY_LEN];  /* Reply */
} hi_omci_me_remote_debug_s;

/*****************LOID authentication*******************/
typedef enum {
	HI_OMCI_ME_LOID_AUTH_INIT = 0,
	HI_OMCI_ME_LOID_AUTH_SUCC,
	HI_OMCI_ME_LOID_LOID_ERR,
	HI_OMCI_ME_LOID_PWD_ERR,
	HI_OMCI_ME_LOID_DUP_LOID,
} hi_omci_me_loid_auth_status;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;                   /* Instance ID */
	hi_uchar8   uc_operaid[4];                          /* Operator ID. */
	hi_uchar8   uc_loid[24];                            /* LOID */
	hi_uchar8   uc_password[12];                        /* password */
	hi_uchar8   uc_authstatus;                          /*Authentication status*/
} hi_omci_me_loid_auth_s;

/***************ONU-E*****************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uint32 ui_vendorid;
	hi_uchar8 uc_version[14];
	hi_uchar8 uc_sn[8];
} hi_omci_me_onu_e_s;

/***********ONU dynamic power management control**********/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_reductcap; /*Power reduction management capability*/
	hi_uchar8   uc_reductmode;
	hi_ushort16 us_itransinit;
	hi_ushort16 us_itxinit;
	hi_uint32   ui_maxsleep; /*Maximum sleep interval*/
	hi_uint32   ui_minaware; /*Minimum aware interval*/
	hi_ushort16 us_minactive; /*Minimum active held interval*/
} hi_omci_me_powermng_s;

/************ONU image mode    HI_OMCI_ME_ONU_IMAGEMODE_E*************/
typedef struct {
	hi_uchar8 uc_uppqnum;
	hi_uchar8 uc_uppqmap;
	hi_uchar8 uc_dnpqnum;
	hi_uchar8 uc_dnpqmap;
} omci_cos_queue_map_capa_s;

typedef struct {
	hi_uchar8 us_upcos0: 3;
	hi_uchar8 us_upcos1: 3;
	hi_uchar8 us_upcos2: 3;
	hi_uchar8 us_upcos3: 3;
	hi_uchar8 us_upcos4: 3;
	hi_uchar8 us_upcos5: 3;
	hi_uchar8 us_upcos6: 3;
	hi_uchar8 us_upcos7: 3;

	hi_uchar8 us_dncos0: 3;
	hi_uchar8 us_dncos1: 3;
	hi_uchar8 us_dncos2: 3;
	hi_uchar8 us_dncos3: 3;
	hi_uchar8 us_dncos4: 3;
	hi_uchar8 us_dncos5: 3;
	hi_uchar8 us_dncos6: 3;
	hi_uchar8 us_dncos7: 3;
} omci_cos_queue_map_attr_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_flowmappingmode;     /* Flow Mapping Mode*/
	hi_uchar8   uc_trafficmanage;        /* Traffic management option*/
	hi_uchar8   uc_flowcar;         /* Flow CAR*/
	hi_uchar8   uc_transparent;             /* Ont transparent*/
	hi_ushort16 us_macnum;          /* Current Mac Learning Number*/
	hi_uint32   ui_macagetime;      /* MAC Age Time */
	hi_uchar8   uc_timesync ;       /* Time sync from olt identify/Big entity ability */
	hi_uint32   ui_timehigh ;       /* OLT time high 32bits */
	hi_uint32   ui_timelow ;         /* OLT time low 32bits */
	hi_ushort16 us_signallevel;       /* Tx Optical signal level*/
	hi_ushort16 us_laserbias;       /* Laser Bias Current*/
	hi_uchar8   uc_temperature;     /* Temperature*/
	hi_ushort16 us_voltage;         /* voltage*/
	omci_cos_queue_map_capa_s st_d1q2pqcapa;
	omci_cos_queue_map_attr_s st_d1q2pqmap;
	hi_uchar8 uc_batterymode;
} hi_omci_me_extended_ont_ability_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_opt_txpower_option;
	hi_uchar8 uc_flow_trans;
	hi_uchar8 uc_mc_acl;
	hi_uchar8 uc_sw_protect;
	hi_uchar8 uc_big_entity;
	hi_uchar8 uc_mem_occu;
	hi_uchar8 uc_cpu_occu;
	hi_uchar8 uc_cpu_temp;
} hi_omci_me_extended_ont2_ability_s;

/***********HW extended ONT ability************/
/* 用于ont image实体的属性3的枚举，指示ont是按照gem port traffic descriptor实体下发的值来做car，
   还是按照flow来做car，此情景主要用于一个gemport可对应多条流的情况;
   默认应该是1，按照gemport trfc dscrptr做car，与协议一致 */
typedef enum {
	HI_OMCI_CAR_MODE_GEMPORT_CAR               = 1,
	HI_OMCI_CAR_MODE_FLOW_CAR                   = 2,
	HI_OMCI_CAR_MODE_BUTT,
} hi_omci_car_mode_e;

typedef enum {
	HI_OMCI_FLOW_MAPPING_MODE_VLAN_E = 1,
	HI_OMCI_FLOW_MAPPING_MODE_PRI_E,
	HI_OMCI_FLOW_MAPPING_MODE_VLAN_PRI_E,
	HI_OMCI_FLOW_MAPPING_MODE_PORT_E,
	HI_OMCI_FLOW_MAPPING_MODE_PORT_VLAN_E,
	HI_OMCI_FLOW_MAPPING_MODE_PORT_PRI_E,
	HI_OMCI_FLOW_MAPPING_MODE_PORT_VLAN_PRI_E,
	HI_OMCI_FLOW_MAPPING_MODE_E1T1_E,
	HI_OMCI_FLOW_MAPPING_MODE_IPTOS_E
} hi_omci_flow_mapping_mode_e;

/***********************  CTC ONU capability ************************/
typedef enum {
	HI_OMCI_ME_ONU_CAPABILITY_TXPOWER_IGNORE_E = 0x0,
	HI_OMCI_ME_ONU_CAPABILITY_TXPOWER_UNITE_E,
	HI_OMCI_ME_ONU_CAPABILITY_TXPOWER_SPEC_E
} hi_omci_me_onu_capability_txpower_e;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 auc_operatorid[4];
	hi_uchar8 uc_version;
	hi_uchar8 uc_type;
	hi_uchar8 uc_txpower_ctrl;
} hi_omci_me_onu_capability_s;

typedef  struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_cpu_usage;
	hi_uchar8   uc_memory_usage;
	hi_uchar8   auc_onu_type[14];
	hi_uint32     ui_rate;
	hi_uchar8    uc_controycode;
	hi_uint32    ui_coconfigfilecap;
	hi_uint32    ui_coconfigfilecap2;
	hi_uint32    ui_loopDetectTime1;
	hi_uint32    ui_loopDetectTime2;
	hi_uchar8   auc_password[10];
	hi_uint32    ui_holdo5_statetime;
	hi_uint32    ui_holdo6_statetime;
	hi_uchar8   uc_option60_en;
	hi_uchar8   uc_option60_controlLevel;
	hi_ushort16  us_option60_string;
	hi_uchar8   uc_restore_factory;
} hi_omci_me_onu3_g_s;

struct hi_omci_me_optical_info {
	hi_omci_me_msg_head_s st_msghead;
	struct hi_omci_tapi_optical_info opt_info;
};

#pragma pack()

#endif /* __OMCI_ME_EQUIP_DEF_H__ */
