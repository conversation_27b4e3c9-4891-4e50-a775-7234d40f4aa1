/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : omci_me_voip_def.h
  版 本 号   : 初稿
  作    者   :
  生成日期   :

******************************************************************************/
#ifndef __OMCI_ME_VOIP_DEF_H__
#define __OMCI_ME_VOIP_DEF_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#pragma pack(1)

#define HI_OMCI_BIT_GET(reg,bit)                ((0 ==  ((reg) & (1 << (bit))))  ? 0:1)
#define HI_OMCI_BIT_SET(reg,bit)                ((reg) |= (1 << (bit)))
/************************Physical path termination point POTS UNI*******************************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_admini_state;   /*Administrative state*/
	hi_ushort16 us_itp;             /*Interworking TP pointer（OPT）*/
	hi_uchar8   uc_arc;             /*ARC（OPT）*/
	hi_uchar8   uc_arc_interval;   /*ARC interval（OPT）*/
	hi_uchar8   uc_impedance;      /*Impedance（OPT）*/
	hi_uchar8   uc_tras_path;      /*Transmission path（OPT）*/
	hi_uchar8   uc_rx_gain;        /*Rx gain（OPT）*/
	hi_uchar8   uc_tx_gain;        /*Tx gain（OPT）*/
	hi_uchar8   uc_oper_state;    /*Operational state（OPT）*/
	hi_uchar8   uc_hook_state;    /*Hook state（OPT）*/
	hi_ushort16 us_holdover_time; /*POTS holdover time（OPT）*/
} __attribute__((packed))hi_omci_me_pptp_pots_uni_s;

/************************SIP user data*******************************/
#define HI_OMCI_VOIP_STP_LEN (25)
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16    us_sa_pointer;
	hi_ushort16    us_up_aor;
	hi_uchar8       uc_sd_name[HI_OMCI_VOIP_STP_LEN];
	hi_ushort16    us_username;
	hi_ushort16    us_vss_uri;
	hi_uint32        ui_vse_time;
	hi_ushort16    us_ndp_pointer;
	hi_ushort16    us_asp_pointer;
	hi_ushort16    us_fc_pointer;
	hi_ushort16    us_pptp_pointer;
	hi_uchar8       uc_release_timer;
	hi_uchar8       uc_roh_timer;
} __attribute__((packed))hi_omci_me_sip_userdata_s;
/************************SIP agent config data*******************************/
typedef struct {
	hi_ushort16    us_src;
	hi_uchar8       uc_tone;
	hi_ushort16    us_message;
} __attribute__((packed))omci_sip_response_s;
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16          us_psa_pointer;
	hi_ushort16          us_opa_pointer;
	hi_uint32              ui_ps_dns;
	hi_uint32              ui_ss_dns;
	hi_ushort16          us_tu_pointer;
	hi_uint32              ui_sre_time;
	hi_uint32              ui_srhs_time;
	hi_ushort16          us_hp_uri;
	hi_uchar8             uc_sip_status;
	hi_ushort16          us_sip_registrar;
	hi_uint32              ui_softswitch;
	omci_sip_response_s st_sr_table;
	hi_uchar8             us_sot_control;
	hi_uchar8             uc_su_format;
	hi_ushort16          us_rsa_pointer;
} __attribute__((packed))hi_omci_me_sip_agentdata_s;
/************************VoIP voice CTP*******************************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16          us_up_pointer;
	hi_ushort16          us_pptp_pointer;
	hi_uchar8             uc_vmp_pointer[HI_OMCI_VOIP_STP_LEN];
	hi_ushort16          us_signal_code;
} __attribute__((packed))hi_omci_me_voip_voice_ctp_s;
/************************VoIP config data*******************************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8                   uc_as_proto;
	hi_uchar8                   uc_proto_used;
	hi_uint32                    ui_avc_methods;
	hi_uchar8                   uc_method_used;
	hi_ushort16                us_confaddr_point;
	hi_uchar8                   uc_conf_state;
	hi_uchar8                   uc_retrieve_prof;
	hi_uchar8                   uc_prof_vers[HI_OMCI_VOIP_STP_LEN];
} __attribute__((packed))hi_omci_me_config_data_s;

/************************Network dial plan table*******************************/
typedef struct {
	hi_uchar8                   uc_diaplan_id;
	hi_uchar8                   uc_action;
	hi_uchar8                   uc_diaplan_token[28];
} __attribute__((packed))omci_dialplan_s;
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16                us_diaplan_num;
	hi_ushort16                us_diaplan_max;
	hi_ushort16                us_cridial_timeout;
	hi_ushort16                us_pardial_timeout;
	hi_uchar8                   uc_diaplan_form;
	omci_dialplan_s         st_diaplantable;
} __attribute__((packed))hi_omci_me_dial_plan_s;
/************************RTP profile data*******************************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16                   us_loacl_portmin;
	hi_ushort16                   us_loacl_portmax;
	hi_uchar8                      uc_dscpmark;
	hi_uchar8                      uc_piggevents;
	hi_uchar8                      uc_toneevents;
	hi_uchar8                      uc_dtmfevents;
	hi_uchar8                      uc_casevents;
} __attribute__((packed))hi_omci_me_rtp_s;
/************************VoIP media profile*******************************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8                   uc_fax_mode;
	hi_uchar8                   uc_vsp_pointer[2];
	hi_uchar8                   uc_codec_selec1;
	hi_uchar8                   uc_packperi_selec1;
	hi_uchar8                   uc_silen_suppre1;
	hi_uchar8                   uc_codec_selec2;
	hi_uchar8                   uc_packperi_selec2;
	hi_uchar8                   uc_silen_suppre2;
	hi_uchar8                   uc_codec_selec3;
	hi_uchar8                   uc_packperi_selec3;
	hi_uchar8                   uc_silen_suppre3;
	hi_uchar8                   uc_codec_selec4;
	hi_uchar8                   uc_packperi_selec4;
	hi_uchar8                   uc_silen_suppre4;
	hi_uchar8                   uc_oob_dtmf;
	hi_uchar8                   uc_rtp_pointer[2];
} __attribute__((packed))hi_omci_me_media_s;
/************************VoIP line status*******************************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8                   uc_codec_used[2];
	hi_uchar8                   uc_server_status;
	hi_uchar8                   uc_session_type;
	hi_uchar8                   uc_call1_peri[2];
	hi_uchar8                   uc_call2_peri[2];
	hi_uchar8                   uc_call1_addr[25];
	hi_uchar8                   uc_call2_addr[25];
	hi_uchar8                   uc_line_status;
	hi_uchar8                   uc_emergency_call_status;
} __attribute__((packed))hi_omci_me_line_status_s;

/************************RTP PM history data*******************************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8                   uc_interval;
	hi_uchar8                   uc_threshold[2];
	hi_uint32                   ui_rtp_error;
	hi_uint32                   ui_pack_loss;
	hi_uint32                   ui_max_jitter;
	hi_uint32                   ui_max_rtcp;
	hi_uint32                   ui_buff_under;
	hi_uint32                   ui_buff_over;
} __attribute__((packed))hi_omci_me_rtp_pm_s;

/************************SIP AGENT PM history data*******************************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8                   uc_interval;
	hi_uchar8                   uc_threshold[2];
	hi_uint32                   ui_transactions;
	hi_uint32                   ui_rx_invite_reqs;
	hi_uint32                   ui_rx_invite_retrans;
	hi_uint32                   ui_rx_noninvite_reqs;
	hi_uint32                   ui_rx_noninvite_retrans;
	hi_uint32                   ui_rx_response;
	hi_uint32                   ui_rx_response_retrans;
	hi_uint32                   ui_tx_invite_reqs;
	hi_uint32                   ui_tx_invite_retrans;
	hi_uint32                   ui_tx_noninvite_reqs;
	hi_uint32                   ui_tx_noninvite_retrans;
	hi_uint32                   ui_tx_response;
	hi_uint32                   ui_tx_response_retrans;
} __attribute__((packed))hi_omci_me_sipagent_pm_s;

/************************SIP CALLINIT PM history data*******************************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8                   uc_interval;
	hi_uchar8                   uc_threshold[2];
	hi_uint32                   ui_failed_connect_cour;
	hi_uint32                   ui_failed_valid_cour;
	hi_uint32                   ui_timeout_cour;
	hi_uint32                   ui_failure_recv_cour;
	hi_uint32                   ui_failed_auth_cour;
} __attribute__((packed))hi_omci_me_sip_callinit_pm_s;

/************************Call control PM history data*******************************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8                   uc_interval;
	hi_uchar8                   uc_threshold[2];
	hi_uint32                   ui_callsetup_fail;
	hi_uint32                   ui_callsetup_timer;
	hi_uint32                   ui_callterm_fail;
	hi_uint32                   ui_analogport_releases;
	hi_uint32                   ui_analogport_offhooktimer;
} __attribute__((packed))hi_omci_me_call_control_pm_s;
#pragma pack()

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __OMCI_ME_EQUIP_DEF_H__ */

