/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_me_api.h
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2012_08_22

******************************************************************************/
#ifndef __HI_OMCI_ME_API_H__
#define __HI_OMCI_ME_API_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

typedef hi_int32(* hi_omci_me_callback)(hi_void);

typedef struct {
	hi_omci_me_callback create;         /* ME创建回调函数 */
	hi_omci_me_callback modify;         /* ME修改回调函数 */
	hi_omci_me_callback reg_proc;       /* ME处理注册回调函数 */
	hi_omci_me_callback release_proc;   /* ME处理注销回调函数 */
} hi_omci_reg_callback;

extern hi_uint32 hi_omci_mib_recv_msg(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_dbg_set_upgradefile(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

//pptp eth uni
extern hi_int32 hi_omci_me_pptpethuni_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptpethuni_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptpethuni_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethpm_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethpm3_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm3_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm3_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm3_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm3_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethpm3_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_veip_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
//layer2
extern hi_uint32 hi_omci_me_8021p_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_exvlantag_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_exvlantag_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_exvlantag_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_maccfg_create_after(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_maccfg_del_before(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_mac_serv_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_mac_serv_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_mac_serv_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_vlan_filt_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_vlan_filt_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_vlan_filt_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_macportcfg_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_macportcfg_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_macportcfg_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_macportcfg_get_portid(hi_ushort16 us_instid, hi_uchar8 *puc_portid);

extern hi_uint32 hi_omci_me_multi_oper_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multi_oper_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multi_oper_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_multisubs_cfg_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multisubs_cfg_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multisubs_cfg_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_macport_table_create(hi_ushort16 us_instid);
extern hi_uint32 hi_omci_me_macport_table_delete(hi_ushort16 us_instid);
extern hi_int32 hi_omci_me_macport_table_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_macportfilt_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_macportfilt_create(hi_ushort16 us_instid);
extern hi_uint32 hi_omci_me_macportfilt_delete(hi_ushort16 us_instid);

extern hi_int32 hi_omci_me_bridgepm_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_bridgepm_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_bridgepm_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_bridgepm_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_brportpm_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_brportpm_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_brportpm_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_brportpm_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_brportpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethextpm_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethextpm_64b_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_64b_stat_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_64b_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_64b_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_64b_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethextpm_64b_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethfrmpm_up_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_up_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_up_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_up_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_up_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_ethfrmpm_down_alarm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_down_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_down_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_down_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_ethfrmpm_down_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
//ANI
extern hi_int32 hi_omci_me_tcont_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_multiwtp_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multiwtp_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_multiwtp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_anig_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_anig_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_anig_test(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_anig_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_fecpm_alarm_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_fecpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_fecpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_fecpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_fecpm_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_fecpm_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_uint32 hi_omci_me_gem_ctp_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_gem_ctp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_gem_ctp_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_gem_iwtp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_traffic_desc_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_traffic_desc_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_traffic_desc_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_traffic_desc_carid_get(hi_uint32 ui_instid, hi_uint32 *pui_carid);
extern hi_int32 hi_omci_me_traffic_desc_init();
extern hi_int32 hi_omci_me_traffic_desc_exit();

extern hi_int32 hi_omci_me_pq_pri_get(hi_ushort16 us_instid, hi_uint32 *ui_egr, hi_uint32 *pui_pri);
extern hi_uint32 hi_omci_me_pq_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_gemctppm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_gemctppm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_gemctppm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_gemctppm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_gemctppm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_galethpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_galethpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_galethpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_galethpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_galethpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

//equip
extern hi_uint32 hi_omci_me_softimage_activate(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_softimage_get_bef(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_softimage_commit(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_softwareimage_init(hi_uint32 ui_index);
extern hi_int32 hi_omci_me_softwareimage_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_me_rmtdbg_set_after(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_onudata_reset(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_iphost_reset();
extern hi_int32 hi_omci_me_powermng_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_loid_auth_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_loid_auth_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_onug_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_onug_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_onu2g_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

//layer3

/* 二次开发ME接口 */
extern hi_int32 hi_omci_me_reg_service(hi_uint32 ui_meid, hi_omci_proc_msg_type_e em_msg_type,
				       hi_uint32 ui_before, HI_FUNCCALLBACK_EXT pf_callback, hi_char8 *pc_funcname);
extern hi_int32 hi_omci_me_release_service(hi_uint32 ui_meid, hi_omci_proc_msg_type_e em_msg_type);
extern hi_int32 hi_omci_me_reg_alarm(hi_uint32 ui_meid, HI_FUNCCALLBACK_EXT callback);
extern hi_int32 hi_omci_me_release_alarm(hi_uint32 ui_meid);
extern hi_int32 hi_omci_me_reg_stat(hi_uint32 ui_meid, HI_FUNCCALLBACK_EXT callback);
extern hi_int32 hi_omci_me_release_stat(hi_uint32 ui_meid);

//voip
extern hi_int32 hi_omci_me_pptp_pots_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptp_pots_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_pptp_pots_test(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_cfg_data_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_cfg_data_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_iptochar(hi_uchar8 *puc_ipdigital, hi_char8 *pc_ipchar, hi_uint32 ui_ipchar_len);
extern hi_void hi_omci_me_set_iphost_attr(hi_void *pv_data, hi_uint32 ui_instid);
extern hi_int32 hi_omci_me_iphost_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_iphost_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_dial_plan_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_dial_plan_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_dial_plan_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_media_profile_create(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_media_profile_delete(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_media_profile_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_user_data_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_rtp_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_rtp_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agent_data_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agent_data_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_voice_ctp_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_line_status_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_rtppm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_rtppm_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_rtppm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_rtppm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_large_string_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agent_data_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agent_data_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_tr069_management_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agentpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agentpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agentpm_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agentpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_callinitpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_callinitpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_callinitpm_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_callinitpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_call_control_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_call_control_pm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_call_control_stat_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_call_control_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_user_data_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_user_data_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
#if 0
extern hi_int32 hi_omci_me_sip_user_data_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_sip_agent_data_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_voice_ctp_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_voice_ctp_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_voip_voice_ctp_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_auth_security_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_auth_security_get(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
#endif
extern hi_int32 hi_omci_me_auth_security_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_xgpontcpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpontcpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpontcpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpontcpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpontcpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_xgpondownmngpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpondownmngpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpondownmngpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpondownmngpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgpondownmngpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_xgponupmngpm_stat_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgponupmngpm_get(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgponupmngpm_getcurrent(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgponupmngpm_create(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_xgponupmngpm_delete(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OMCI_ME_API_H__ */
