/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : omci_me_l2_def.h
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2011_09_27
******************************************************************************/
#ifndef __OMCI_ME_L2_DEF_H__
#define __OMCI_ME_L2_DEF_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


//#include "hi_drv_common.h"


/**************802.1p mapper service profile  HI_OMCI_ME_802_1P_E**************/
#define HI_OMCI_DSCP_2_P_LEN          24
#define HI_OMCI_8021P_PRI_NUM         8
#define HI_OMCI_8021P_INVALID_GEMIWTP 0xffff

#define HI_OMCI_8021P_PBIT_FROM_DSCP    0
#define HI_OMCI_8021P_PBIT_FROM_DEFAULT 1

#pragma pack(1)

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16 us_tp_ptr;
	hi_ushort16 us_iwtp_ptr0;
	hi_ushort16 us_iwtp_ptr1;
	hi_ushort16 us_iwtp_ptr2;
	hi_ushort16 us_iwtp_ptr3;
	hi_ushort16 us_iwtp_ptr4;
	hi_ushort16 us_iwtp_ptr5;
	hi_ushort16 us_iwtp_ptr6;
	hi_ushort16 us_iwtp_ptr7;
	hi_uchar8   uc_frameoption;       /* how the ONT should handle Untagged Ethernet frames
                                              received across the associated Ethernet interface*/
	hi_uchar8   uc_dscp2p[HI_OMCI_DSCP_2_P_LEN]; /* in conjunction with the Unmarked Frame Option attribute*/
	hi_uchar8   uc_defaultmark;       /* in conjunction with the Unmarked Frame Option attribute*/
	hi_uchar8   uc_tptype;            /* 0x0 if the mapper is used for Bridging-mapping. 1-PPTP ETH UNI,2-IP Host Service*/
} hi_omci_me_8021p_s;

/****************MAC bridge configuration data  HI_OMCI_ME_MAC_CFG_DATA_E**************/
#define HI_OMCI_DESIGNATED_ROOT_LEN 8
#define HI_OMCI_DEFAULT_BRIDGE_PRI  0x8000     /* default bridge priority */
#define HI_OMCI_DEFAULT_ROOT_PATH_COST  0   /* default root path cost */
#define HI_OMCI_DEFAULT_ROOT_PORT_NUM  0    /* default root path cost */
#define HI_OMCI_MAC_DEFAULT_HELLO 0x0800      /* default Hello time */
#define HI_OMCI_MAC_DEFAULT_DELAY 0x1e00      /* default Foward delay */

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_macaddr[HI_MAC_LEN];   /* the MAC address used by the bdg*/
	hi_ushort16 us_pri;           /* the priority of the bridge*/
	hi_uchar8   uc_designatedroot[HI_OMCI_DESIGNATED_ROOT_LEN];  /*the bridge identifier for the root of the spanning tree*/
	hi_uint32   ui_rootpathcost;  /* the cost of the best path to the root as seen from the bridge*/
	hi_uchar8   uc_bdgportcount;  /* the number of existing ports controlled by this bridge*/
	hi_ushort16 us_rootportnum;   /* the port number that has the lowest cost from the bridge to the root bridge*/
	hi_ushort16 us_hellotime;     /* the time interval (in 256ths of a second) between hello packets*/
	hi_ushort16 us_forwarddelay;  /* the time (in 256ths of a second) that the bridge on
                                     the Ethernet card in the ONT retains a packet before forwarding it*/
} hi_omci_me_mac_cfg_data_s;

/*************MAC bridge port configuration data  HI_OMCI_ME_MAC_PORT_CFG_DATA_E************/

#define HI_OMCI_DIRECTION_UPSTREAM            0
#define HI_OMCI_DIRECTION_DOWNSTREAM       1

/* TPType definition in MAC Bridge Port Configuration Data*/
typedef enum {
	HI_MAC_BRI_PORT_UNI_E        = 0x01, /*Physical path termination point Ethernet UNI   */
	HI_MAC_BRI_PORT_ATMTP_E      = 0x02, /*Interworking VCC termination point             */
	HI_MAC_BRI_PORT_8021P_MAP_E  = 0x03, /*802.1p mapper service profile                  */
	HI_MAC_BRI_PORT_IP_E         = 0x04, /*IP host config data                            */
	HI_MAC_BRI_PORT_IWTP_E       = 0x05, /*GEM interworking termination point             */
	HI_MAC_BRI_PORT_MIWTP_E      = 0x06, /*Multicast GEM interworking termination point   */
	HI_MAC_BRI_PORT_PPTP_xDSL_E  = 0x07, /*Physical path termination point xDSL UNI part 1*/
	HI_MAC_BRI_PORT_PPTP_vDSL_E  = 0x08, /*Physical path termination point VDSL UNI       */
	HI_MAC_BRI_PORT_ETHTP_E      = 0x09, /*Ethernet flow termination point                */
	HI_MAC_BRI_PORT_PPTP_80211_E = 0x0A, /*Physical path termination point 802.11 UNI     */
	HI_MAC_BRI_PORT_VEIP_E       = 0x0B, /*Virtual Ethernet interface point               */

	HI_MAC_BRI_PORT_MAX_E        = 0xFF,
} hi_omci_mac_bri_port_type_e;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16 us_bdgid_ptr;                    /* identifies the MAC bridge controlling the port*/
	hi_uchar8   uc_portnum;                    /*This attribute is the bridge port number. It must be unique.*/
	hi_uchar8   uc_tptype;                     /* identifies the type of the TP associated with this MAC bridge port*/
	hi_ushort16 us_tp_ptr;                     /* points to the tp associated with this MAC bdg port*/
	hi_ushort16 uc_pri;                        /* the priority of the port*/
	hi_ushort16 us_pathcost;                   /* the cost contribution of the port to the path cost towards the spanning tree root bridge*/
	hi_uchar8   uc_spanind;                    /* whether or not STP LAN topology change detection is enabled at this port*/
	hi_uchar8   uc_deprecated1;                /* Deprecated */
	hi_uchar8   uc_deprecated2;                /* Deprecated */
	hi_uchar8   uc_portmac[HI_MAC_LEN];   /* Port MAC Address*/
	hi_ushort16 us_out_ptr;                    /* This attribute points to a GEM traffic descriptor that limits the traffic rate leaving the MAC bridge */
	hi_ushort16 us_in_ptr;                     /* This attribute points to a GEM traffic descriptor that limits the traffic rate entering the MAC bridge */
	hi_uchar8 uc_macdepth;                   /*This attribute specifies the maximum number of MAC addresses to be learned by this MAC bridge port*/
} hi_omci_me_mac_port_cfg_s;


typedef enum {
	HI_OMCI_ONT_MAPPING_MODE_MIN_E = 0,
	HI_OMCI_ONT_MAPPING_MODE_VLAN_E = 1,
	HI_OMCI_ONT_MAPPING_MODE_PRIORITY_E,
	HI_OMCI_ONT_MAPPING_MODE_VLAN_AND_PRIORITY_E,
	HI_OMCI_ONT_MAPPING_MODE_PORT_E,
	HI_OMCI_ONT_MAPPING_MODE_PORT_AND_VLAN_E,
	HI_OMCI_ONT_MAPPING_MODE_PORT_AND_PRIORITY_E,
	HI_OMCI_ONT_MAPPING_MODE_PORT_AND_VLAN_AND_PRIORITY_E,
	HI_OMCI_ONT_MAPPING_MODE_E1_T1_E,
	HI_OMCI_ONT_MAPPING_MODE_IPTOS_E, /*即DSCP映射方式*/
	HI_OMCI_ONT_MAPPING_MODE_AUTO_ADAPTER_E = 0xdb, /*当映射方式为0xdb时，表明当前为自适应方式*/
	HI_OMCI_ONT_MAPPING_MODE_MAX_E = 255
} hi_omci_ont_mapping_mode_e;


/***********MAC bridge performance monitoring history data HI_OMCI_ME_MAC_PM_E**********/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_endtime;              /* Interval End Time */
	hi_ushort16 us_thresid;              /* Threshold DataB-PON id */
	hi_uint32   ui_discardcnt;          /* BridgeLearningEntry DiscardCount */
} hi_omci_me_macpm_s;

/**********MAC bridge port performance monitoring history data  HI_OMCI_ME_MAC_PORT_PM_E********/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_interendtime;      /* Interval End Time */
	hi_ushort16 us_thresholdid;       /* Threshold DataB-PON id */
	hi_uint32   ui_forwardcnt;        /* ForwardedFrame Counter */
	hi_uint32   ui_delaydiscardcnt;   /* DelayExceededDiscardCounter */
	hi_uint32   ui_mtudiscardcnt;     /* MTUExceededDiscardCounter */
	hi_uint32   ui_rcvcnt;            /* ReceivedFrame Counter */
	hi_uint32   ui_discardcnt;        /* ReceivedAndDiscardedCounter */
} __attribute__((packed)) hi_omci_me_macportpm_s ;

/**************MAC bridge service profile HI_OMCI_ME_MAC_SERVICE_E*****************/
#define HI_OMCI_MAC_SRVPROFILE_AGE_DEF  300

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_spantreeind;     /* Spanning tree ind*/
	hi_uchar8   uc_learnind;        /* Learning ind*/
	hi_uchar8   uc_portbridging;    /* Port bridging ind*/
	hi_ushort16 us_pri;             /* Priority*/
	hi_ushort16 us_maxage;          /* Max age*/
	hi_ushort16 us_hellotime;       /* Hello time*/
	hi_ushort16 us_forwarddelay;    /* Forward delay*/
	hi_uchar8   uc_unknownmac;      /* Unknown MAC address discard, treament of unknown D-MAC,TRUE-discard, False-Foward*/
	hi_uchar8   uc_learndepth;      /* the mac learning depth*/
	hi_uint32   ui_dynfilter;       /* Dynamic filtering ageing time*/
} hi_omci_me_mac_service_s;


/*****************MAC bridge port filter preassign table HI_OMCI_ME_MAC_PORT_FILT_PRE_E***************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_ipv4multi; /*IPv4 multicast filtering*/
	hi_uchar8 uc_ipv6multi; /*IPv6 multicast filtering*/
	hi_uchar8 uc_ipv4broad; /*IPv4 broadcast filtering*/
	hi_uchar8 uc_rarp;      /*RARP filtering*/
	hi_uchar8 uc_ipx;       /*IPX filtering*/
	hi_uchar8 uc_netbeui;   /*NetBEUI filtering*/
	hi_uchar8 uc_appletalk; /*AppleTalk filtering*/
	hi_uchar8 uc_bridgeinfo;/*Bridge management information filtering*/
	hi_uchar8 uc_arp;       /*ARP filtering*/
	hi_uchar8 uc_pppoe;     /*PPPoE broadcast filtering*/
} hi_omci_me_mac_port_filt_prea_s;

/*************MAC bridge port bridge design HI_OMCI_ME_MAC_PORT_DESIGN_E**************/
typedef struct {
	hi_omci_me_msg_head_s   st_msghead;
	hi_uchar8 uc_designroot[24];
	hi_uchar8 uc_portstate;
} hi_omci_me_mac_port_design_s;

/*************MAC bridge port bridge table data HI_OMCI_ME_MAC_PORT_TAB_DATA_E**************/
#define HI_OMCI_PORT_TAB_PACKET_FILTER 1
#define HI_OMCI_PORT_TAB_PACKET_FORWARD 0
#define HI_OMCI_PORT_TAB_MAC_STATIC 0
#define HI_OMCI_PORT_TAB_MAC_DYNAMICAL 1
typedef struct {
	hi_ushort16 us_info;   /*Information (2 bytes)*/
	hi_uchar8   uc_macaddr[6];/*MAC address (6 bytes)*/
} omci_me_bridge_table_s;

typedef struct {
	hi_ushort16 us_info;   /*Information (2 bytes)*/
	hi_ushort16 us_vlan;
	hi_uchar8   uc_macaddr[6];/*MAC address (6 bytes)*/
} omci_me_ext_bridge_table_s;

typedef struct {
	hi_omci_me_msg_head_s      st_msghead;
	omci_me_bridge_table_s     st_bridge;
} hi_omci_me_mac_port_table_s;

/******************MAC bridge port filter table data HI_OMCI_ME_MAC_PORT_FILT_DATA_E**********************/
#define HI_OMCI_ME_MAC_PORT_FILT_TBL_SIZE      255
/* 一次Set动作最多可以设置的表项数量 */
#define HI_OMCI_ME_MAC_PORT_FILT_TBL_SET_MAX_ENTRY     3

typedef struct {
	hi_uchar8   uc_entrynum;     /* an index in this attribute list */
	hi_uchar8   uc_filter;       /* Filter byte */
	hi_uchar8   uc_macadd[HI_MAC_LEN];   /* MAC address */
} hi_omci_me_filt_entry_s;

typedef struct {
	hi_omci_me_msg_head_s    st_msghead;
	hi_omci_me_filt_entry_s  st_filtertbl;
} hi_omci_me_mac_port_filt_s;

/*********************Multicast operations profile HI_OMCI_ME_MULTI_OPER_PROFILE_E********************/
#define HI_OMCI_IGMP_CTRL_TAB 4
#define HI_OMCI_MULITI_IPV6_LEADING_LEN 12

typedef enum {
	HI_OMCI_IGMPV1 = 1,
	HI_OMCI_IGMPV2 = 2,
	HI_OMCI_IGMPV3 = 3,
	HI_OMCI_MLDV1  = 16,
	HI_OMCI_MLDV2  = 17,
} hi_omci_igmp_version_e;

typedef enum {
	HI_IGMP_SNOOP_ONLY                    = 0,
	HI_IGMP_SNOOP_WITH_PROXY       = 1,
	HI_IGMP_PROXY                              = 2,
	HI_IGMP_ENABLE_CTRL                   = 5,
} HI_IGMP_PROXY_MODE_E;

typedef enum {
	HI_OMCI_IGMP_TAG_TRANSPARENT = 0,
	HI_OMCI_IGMP_TAG_ADD,
	HI_OMCI_IGMP_TAG_TRANSLATION_TCI,
	HI_OMCI_IGMP_TAG_TRANSLATION_VLAN,
} hi_omci_igmp_Tag_control_e;

typedef enum {
	HI_OMCI_MC_DS_TRANSPARENT = 0,
	HI_OMCI_MC_DS_DEL,
	HI_OMCI_MC_DS_ADD,
	HI_OMCI_MC_DS_TRANSLATION_TCI,
	HI_OMCI_MC_DS_TRANSLATION_VLAN,
	HI_OMCI_MC_DS_ADD_SUBS,
	HI_OMCI_MC_DS_TRANSLATION_TCI_SUBS,
	HI_OMCI_MC_DS_TRANSLATION_VLAN_SUBS,
} hi_omci_mc_downstream_tag_e;

typedef enum {
	HI_OMCI_MULTI_OPER_TAB_SET = 1,
	HI_OMCI_MULTI_OPER_TAB_DEL,
	HI_OMCI_MULTI_OPER_TAB_CLR,
} hi_omci_multi_oper_tab_opt_e;

typedef union {
	struct {
		hi_ushort16 row_key: 10;
		hi_ushort16 test: 1;
		hi_ushort16 row_partid: 3;
		hi_ushort16 set_ctrl: 2;
	} bits;

	struct {
		hi_ushort16 index: 14;
		hi_ushort16 set_ctrl: 2;
	} ibits;

	hi_ushort16 us_value;
} hi_omci_multi_tab_ctrl_s;

typedef struct {
	hi_omci_multi_tab_ctrl_s st_tablectrl;
	union {
		struct {
			hi_ushort16 us_gemport;   /*GEM port-ID*/
			hi_ushort16 us_vlan;      /*VLAN ID*/
			hi_uint32   ui_sip;       /*Source IP address*/
			hi_uint32   ui_dip_start; /*Destination IP address of the start of the multicast range*/
			hi_uint32   ui_dip_end;   /*Destination IP address of the end of the multicast range*/
			hi_uint32   ui_imputedbandwidth;/*Imputed group bandwidth. Expressed in bytes per second*/
			hi_ushort16 us_resv;      /*Reserved*/
		} part0;
		struct {
			hi_uchar8 auc_ipv6_src_leading[HI_OMCI_MULITI_IPV6_LEADING_LEN]; /* Leading bytes of IPv6 source address */
			hi_ushort16 us_preview_len;         /* Preview length */
			hi_ushort16 us_preview_time;        /* Preview repeat time */
			hi_ushort16 us_preview_cnt;         /* Preview repeat count */
			hi_ushort16 us_preview_rst;         /* Preview reset */
			hi_ushort16 us_recv;
		} part1;
		struct {
			hi_uchar8 auc_ipv6_dst_leading[HI_OMCI_MULITI_IPV6_LEADING_LEN]; /* Leading bytes of IPv6 destination address */
			hi_uchar8 auc_recv[10];
		} part2;
	} st_row;
} hi_omci_ctrl_table_s;

typedef struct {
	hi_ushort16 us_vlan;     /*VLAN ID*/
	hi_uint32   ui_sip;      /*Source IP address*/
	hi_uint32   ui_multi_dip;/*Multicast destination IP address*/
} hi_omci_lost_grp_s;

typedef struct {
	hi_uchar8 uc_ctrltype;
	hi_ushort16 us_tci;
} hi_omci_multi_tci_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_version ;               /* the version of IGMP to be supported */
	hi_uchar8   uc_function;               /* IGMP function*/
	hi_uchar8   uc_imleave;                /* whether or not enables the immediate leave function */
	hi_ushort16 us_uptci;                  /* Upstream IGMP TCI */
	hi_uchar8   uc_uptagctrl;              /* Upstream IGMP tag control */
	hi_uint32   ui_rate;                   /* maximum rate of upstream IGMP traffic */
	hi_omci_ctrl_table_s   st_dynctrl;     /* dynamic multicast group address ranges */
	hi_omci_ctrl_table_s   st_stactrl;     /* static multicast group address ranges */
	hi_omci_lost_grp_s     st_lostgrp;     /* a list of groups from the dynamic access control list table */
	hi_uchar8   uc_robustness;             /* possible packet loss in the network */
	hi_uint32   ui_ipaddress;              /* IP address to be used by a proxy querier */
	hi_uint32   ui_queryintvl;             /* interval between general queries in seconds */
	hi_uint32 ui_maxrsptime;             /* max response time added by the proxy into general query messages directed to UNIs */
	hi_uint32 ui_lastqueryintvl;         /* max response time inserted into group-specific queries sent to UNIs in response to group leave messages */
	hi_uchar8   uc_unauthbehavi;           /*Unauthorized join request behaviour*/
	hi_omci_multi_tci_s st_tci;            /* Downstream IGMP and multicast TCI */
} hi_omci_me_multi_oper_profile_s;

/************************VLAN Tagging Filter Data HI_OMCI_ME_VLAN_FILT_E***************************/
/*vlan Tagging Filter Table */
#define HI_OMCI_VLAN_FILTER_TAB            12

/* get the vlan ID and PRI from TCI */
#define HI_OMCI_GET_VLAN_FROM_TCI(us_TCI)   ((hi_ushort16)(us_TCI) & 0xFFF)
#define HI_OMCI_GET_PRI_FROM_TCI(us_TCI)   ((hi_ushort16)(us_TCI >> 13 ) & 0x7)

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16 us_vlantab[HI_OMCI_VLAN_FILTER_TAB];
	hi_uchar8   uc_forward_oper;
	hi_uchar8   uc_entitynum;       /*the number of entries in the VLAN Filter
                                      Table that are valid*/
} hi_omci_me_vlan_filt_s;

/*********VLAN Tagging Operation Configuration Data  HI_OMCI_ME_VLAN_TAG_E************/
/* OMCI default TCI, vlan is 1, priority is 1 */
#define HI_OMCI_DEFAULT_TCI                0x0001

/* OMCI default VLAN, vlan is 1 */
#define HI_OMCI_DEFAULT_VLAN               0x1

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_usopermode;
	hi_ushort16 us_ustci;
	hi_uchar8   uc_dsopermode;
	hi_uchar8   uc_assoctype;
	hi_ushort16 us_assocme_ptr;
} hi_omci_me_vlantag_op_s;

/* The default Upstream VLAN tagging operation mode*/
#define HI_OMCI_UPSTREAM_VLAN_TRANSPRANT_MODE       0x00
#define HI_OMCI_UPSTREAM_VLAN_RETAG_MODE                 0x01
#define HI_OMCI_UPSTREAM_VLAN_ADD_TAG_MODE             0x02
#define HI_OMCI_DOWNSTREAM_VLAN_TRANSPRANT_MODE 0x00
#define HI_OMCI_DOWNSTREAM_VLAN_REMOVETAG_MODE   0x01

typedef enum {
	HI_OMCI_VLAN_TAG_OPER_TYPE_DEFAULT_E = 0,
	HI_OMCI_VLAN_TAG_OPER_TYPE_IP_HOST_E,
	HI_OMCI_VLAN_TAG_OPER_TYPE_8021P_E,
	HI_OMCI_VLAN_TAG_OPER_TYPE_MAC_BR_PORT_E,
	HI_OMCI_VLAN_TAG_OPER_TYPE_PPTP_xDSL_UNI_E,
	HI_OMCI_VLAN_TAG_OPER_TYPE_GEM_IWTP_E,
	HI_OMCI_VLAN_TAG_OPER_TYPE_MULTI_GEM_IWTP_E,
	HI_OMCI_VLAN_TAG_OPER_TYPE_PPTP_MoCA_UNI_E,
	HI_OMCI_VLAN_TAG_OPER_TYPE_PPTP_80211_UNI_E,
	HI_OMCI_VLAN_TAG_OPER_TYPE_ETH_FLOW_TP_E,
	HI_OMCI_VLAN_TAG_OPER_TYPE_PPTP_ETH_UNI_E,

	HI_OMCI_VLAN_TAG_OPER_TYPE_MAX_E    = 255,
} EN_OMCI_ME_VLAN_TAG;

/****************Extended VLAN tagging operation configuration data  HI_OMCI_ME_EXTEND_VLAN_TAG_E**************/
/*Filter Ethertype*/
#define HI_OMCI_ETHERTYPE_NOFILTER            0     /*Do not filter on Ethertype.*/
#define HI_OMCI_ETHERTYPE_FILTER_IPoE         1     /*Ethertype = 0x0800 (filter IPoE frames)*/
#define HI_OMCI_ETHERTYPE_FILTER_PPPoE        2     /*Ethertype = 0x8863 or 0x8864 (filter PPPoE frames)*/
#define HI_OMCI_ETHERTYPE_FILTER_ARP          3     /*Ethertype = 0x0806 (filter ARP frames)*/
#define HI_OMCI_ETHERTYPE_FILTER_IPV6         4     /*Ethertype = 0x86DD (filter IPv6 IPoE frames)*/
#define HI_OMCI_ETHERTYPE_FILTER_USERDEFINED  15    /*Ethertype = 0x0806 (filter ARP frames)*/

#define HI_OMCI_FRM_ETHTYPE_NOFILTER        0
#define HI_OMCI_FRM_ETHTYPE_IPOE            0x0800
#define HI_OMCI_FRM_ETHTYPE_PPPOE           0x88638864
#define HI_OMCI_FRM_ETHTYPE_ARP             0x0806
#define HI_OMCI_FRM_ETHTYPE_IPV6            0x86DD

/* Treatment tags to remove field */
#define HI_OMCI_TREAT_REMOVE_0_TAG             0
#define HI_OMCI_TREAT_REMOVE_1_TAG             1
#define HI_OMCI_TREAT_REMOVE_2_TAG             2
#define HI_OMCI_TREAT_DISCARD                  3

#define HI_OMCI_FILETER_PRIORITY_NO            8    /* not filter */
#define HI_OMCI_FILTER_NO_TAG                  15   /*没有OUTER TAG */
#define HI_OMCI_FILTER_DEFAULT_TAG             14   /* Default Priority Tag rule */
#define HI_OMCI_FILTER_NO_TPID                 0

#define HI_OMCI_TREATMENT_TAG_NO_ADD                15      /*不添加OUTER TAG*/

/* Add an outer tag, and copy the outer priority from the inner priority of the received frame */
#define HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_INNER    8

/* Add an outer tag, and derive P bits from the DSCP field of the incoming frame */
#define HI_OMCI_TREATMENT_TAG_ADD_PRI_COPY_DSCP     10

/* Copy the outer VID from the inner VID of the received frame */
#define HI_OMCI_TREATMENT_TAG_ADD_VLAN_COPY_INNER   4096

/* Copy the outer VID from the outer VID of the received frame */
#define HI_OMCI_TREATMENT_TAG_ADD_VLAN_COPY_OUTER   4097

/* Copy TPID (and DEI, if present) from inner tag of received frame */
#define HI_OMCI_TREATMENT_TAG_ADD_TPID_CPOY_INNER   0

/* Copy TPID (and DEI, if present) from outer tag of received frame */
#define HI_OMCI_TREATMENT_TAG_ADD_TPID_CPOY_OUTER   1

/* Set TPID = output TPID attribute value, copy DEI bit from inner tag of received frame */
#define HI_OMCI_TREATMENT_TAG_ADD_TPID_DEI_INNER    2

/* Set TPID = output TPID, copy DEI from outer tag of received frame */
#define HI_OMCI_TREATMENT_TAG_ADD_TPID_DEI_OUTER    3

/* Set TPID = 0x8100 */
#define HI_OMCI_TREATMENT_TAG_ADD_TPID_8100         4

#define HI_OMCI_TREATMENT_TAG_ADD_TPID_RESV         5

/* Set TPID = output TPID, DEI = 0 */
#define HI_OMCI_TREATMENT_TAG_ADD_TPID_DEI_0        6

/* Set TPID = output TPID, DEI = 1 */
#define HI_OMCI_TREATMENT_TAG_ADD_TPID_DEI_1        7

#define HI_OMCI_FILETER_NO                     8    /* not filter */

#define HI_OMCI_VLANID_MAX                     4094

/* 无效的vlan */
#define HI_OMCI_INVALID_VLAN                   4096

#define HI_OMCI_EXTEND_VLAN_TAG_ENTRY_SIZE     16
#define HI_OMCI_EXTEND_HWVLAN_TAG_ENTRY_SIZE   18
#define HI_OMCI_EXTEND_VLAN_TAG_TABLE          16
#define HI_OMCI_DOWNSTREAM_MODE_TRANSPARENT    1
#define HI_OMCI_EXVLAN_8_BYTE                  8
#define HI_OMCI_HWEXVLAN_LAST_2_BYTE           2

typedef enum {
	HI_OMCI_ASSOCIATE_TYPE_MAC_BRG_PORT_CFG_DATA = 0, //MAC bridge port configuration data
	HI_OMCI_ASSOCIATE_TYPE_8021P_MAPPER_SRV_PROFILE,  //802.1p mapper service profile
	HI_OMCI_ASSOCIATE_TYPE_PPTP_ETH_UNI,              //Physical path termination point Ethernet UNI
	HI_OMCI_ASSOCIATE_TYPE_IP_HOST_CFG_DATA,          //IP host config data
	HI_OMCI_ASSOCIATE_TYPE_PPTP_XDSL_UNI,             //Physical path termination point xDSL UNI
	HI_OMCI_ASSOCIATE_TYPE_GEM_IWTP,                  //GEM interworking termination point
	HI_OMCI_ASSOCIATE_TYPE_MULTI_GEM_IWTP,            //Multicast GEM interworking termination point
	HI_OMCI_ASSOCIATE_TYPE_PPTP_MOCA_UNI,             //Physical path termination point MoCA UNI
	HI_OMCI_ASSOCIATE_TYPE_PPTP_80211_UNI,            //Physical path termination point 802.11 UNI
	HI_OMCI_ASSOCIATE_TYPE_ETH_FLOW_TERMIN_POINT = 9, //Ethernet flow termination point
	HI_OMCI_ASSOCIATE_TYPE_VEIP                       //Virtual Ethernet interface point
} HI_OMCI_ME_ASSOCIATE_TYPE_E;

#define HI_OMCI_EXVLAN_FILTER_DEFAULT   0
#define HI_OMCI_EXVLAN_FILTER_IGNORE    1
#define HI_OMCI_EXVLAN_FILTER_NONE      2
#define HI_OMCI_EXVLAN_FILTER_PRI       3

/*
 * bit  4       3       2~0
 *      tpid    vlan    pri
 */
#define HI_OMCI_EXVLAN_FILTER_CODING(tpid, vlan, pri) \
	((((tpid) & 0x1) << 4) | (((vlan) & 0x1) << 3) | ((pri) & 0x7))

typedef enum {
	HI_OMCI_EXVLAN_FILTER_DEFAULT_E         = HI_OMCI_EXVLAN_FILTER_CODING(HI_FALSE, HI_FALSE, HI_OMCI_EXVLAN_FILTER_DEFAULT),
	HI_OMCI_EXVLAN_FILTER_IGNORE_E          = HI_OMCI_EXVLAN_FILTER_CODING(HI_FALSE, HI_FALSE, HI_OMCI_EXVLAN_FILTER_IGNORE),
	HI_OMCI_EXVLAN_FILTER_NONE_E            = HI_OMCI_EXVLAN_FILTER_CODING(HI_FALSE, HI_FALSE, HI_OMCI_EXVLAN_FILTER_NONE),
	HI_OMCI_EXVLAN_FILTER_VLAN_E            = HI_OMCI_EXVLAN_FILTER_CODING(HI_FALSE, HI_TRUE, HI_OMCI_EXVLAN_FILTER_NONE),
	HI_OMCI_EXVLAN_FILTER_PRI_E             = HI_OMCI_EXVLAN_FILTER_CODING(HI_FALSE, HI_FALSE, HI_OMCI_EXVLAN_FILTER_PRI),
	HI_OMCI_EXVLAN_FILTER_TPID_E            = HI_OMCI_EXVLAN_FILTER_CODING(HI_TRUE, HI_FALSE, HI_OMCI_EXVLAN_FILTER_NONE),
	HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E        = HI_OMCI_EXVLAN_FILTER_CODING(HI_FALSE, HI_TRUE, HI_OMCI_EXVLAN_FILTER_PRI),
	HI_OMCI_EXVLAN_FILTER_VLAN_TPID_E       = HI_OMCI_EXVLAN_FILTER_CODING(HI_TRUE, HI_TRUE, HI_OMCI_EXVLAN_FILTER_NONE),
	HI_OMCI_EXVLAN_FILTER_PRI_TPID_E        = HI_OMCI_EXVLAN_FILTER_CODING(HI_TRUE, HI_FALSE, HI_OMCI_EXVLAN_FILTER_PRI),
	HI_OMCI_EXVLAN_FILTER_VLAN_PRI_TPID_E   = HI_OMCI_EXVLAN_FILTER_CODING(HI_TRUE, HI_TRUE, HI_OMCI_EXVLAN_FILTER_PRI),
	HI_OMCI_EXVLAN_FILTER_INVALID_E         = 0xffff
} hi_omci_me_exvlan_filter_type_e;

/*
 * TPID/VLAN/PRI被指定赋值的，置为有效
 * bit  6       5       4       3~2     1~0
 *      tpid    pri     vlan    add     remove
 */
#define HI_OMCI_EXVLAN_ACTION_CODING(tpid, pri, vlan, add, remove) \
	((((tpid) & 0x1) << 6) | (((pri) & 0x1) << 5) | (((vlan) & 0x1) << 4) | (((add) & 0x3) << 2) | ((remove) & 0x3))

#define HI_OMCI_EXVLAN_ACT_TRANSPARENT() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_FALSE, HI_FALSE, \
				     HI_FALSE, HI_FALSE, HI_OMCI_TREAT_REMOVE_0_TAG)

#define HI_OMCI_EXVLAN_ACT_DISCARD() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_FALSE, HI_FALSE, \
				     HI_FALSE, HI_FALSE, HI_OMCI_TREAT_DISCARD)

#define HI_OMCI_EXVLAN_ACT_RMV_1TAG() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_FALSE, HI_FALSE, \
				     HI_FALSE, HI_FALSE, HI_OMCI_TREAT_REMOVE_1_TAG)

#define HI_OMCI_EXVLAN_ACT_RMV_2TAG() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_FALSE, HI_FALSE, \
				     HI_FALSE, HI_FALSE, HI_OMCI_TREAT_REMOVE_2_TAG)

#define HI_OMCI_EXVLAN_ACT_ADD_1TAG() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_TRUE, HI_TRUE, \
				     HI_TRUE, HI_TRUE, HI_OMCI_TREAT_REMOVE_0_TAG)

#define HI_OMCI_EXVLAN_ACT_ADD_1TAG_VLAN_PRI() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_FALSE, HI_TRUE, \
				     HI_TRUE, HI_TRUE, HI_OMCI_TREAT_REMOVE_0_TAG)

#define HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_TRUE, HI_FALSE, \
				     HI_TRUE, HI_TRUE, HI_OMCI_TREAT_REMOVE_0_TAG)

#define HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI_TPID() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_FALSE, HI_FALSE, \
				     HI_TRUE, HI_TRUE, HI_OMCI_TREAT_REMOVE_0_TAG)

#define HI_OMCI_EXVLAN_ACT_MOD_1TAG() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_TRUE, HI_TRUE, \
				     HI_TRUE, HI_TRUE, HI_OMCI_TREAT_REMOVE_1_TAG)

#define HI_OMCI_EXVLAN_ACT_MOD_0TAG_VLAN() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_FALSE, HI_FALSE, \
				     HI_TRUE, HI_TRUE, HI_OMCI_TREAT_REMOVE_0_TAG)

#define HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_FALSE, HI_FALSE, \
				     HI_TRUE, HI_TRUE, HI_OMCI_TREAT_REMOVE_1_TAG)

#define HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_FALSE, HI_TRUE, \
				     HI_FALSE, HI_TRUE, HI_OMCI_TREAT_REMOVE_1_TAG)

#define HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_TPID() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_TRUE, HI_TRUE, \
				     HI_FALSE, HI_TRUE, HI_OMCI_TREAT_REMOVE_1_TAG)

#define HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_PRI() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_FALSE, HI_TRUE, \
				     HI_TRUE, HI_TRUE, HI_OMCI_TREAT_REMOVE_1_TAG)

#define HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_TPID() \
	HI_OMCI_EXVLAN_ACTION_CODING(HI_TRUE, HI_FALSE, \
				     HI_TRUE, HI_TRUE, HI_OMCI_TREAT_REMOVE_1_TAG)

typedef enum {
	HI_OMCI_EXVLAN_ACT_TRANSPARENT_E        = HI_OMCI_EXVLAN_ACT_TRANSPARENT(),               /* 透传 */
	HI_OMCI_EXVLAN_ACT_DISCARD_E            = HI_OMCI_EXVLAN_ACT_DISCARD(),                   /* 丢弃 */
	HI_OMCI_EXVLAN_ACT_MOD_0TAG_VLAN_E      = HI_OMCI_EXVLAN_ACT_MOD_0TAG_VLAN(),             /* 切换外层TAG，VLAN修改 */
	HI_OMCI_EXVLAN_ACT_RMV_1TAG_E           = HI_OMCI_EXVLAN_ACT_RMV_1TAG(),                  /* 删除一层TAG */
	HI_OMCI_EXVLAN_ACT_RMV_2TAG_E           = HI_OMCI_EXVLAN_ACT_RMV_2TAG(),                  /* 删除两层TAG */
	HI_OMCI_EXVLAN_ACT_ADD_1TAG_E           = HI_OMCI_EXVLAN_ACT_ADD_1TAG(),                  /* 添加一层TAG，VLAN/PRI/TPID都被指定 */
	HI_OMCI_EXVLAN_ACT_ADD_1TAG_VLAN_PRI_E  = HI_OMCI_EXVLAN_ACT_ADD_1TAG_VLAN_PRI(),         /* 添加一层TAG，VLAN/PRI都被指定 */
	HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI_E       = HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI(),              /* 添加一层TAG，VLAN/TPID被指定，PRI拷贝内层TAG */
	HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI_TPID_E  = HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI_TPID(),         /* 添加一层TAG，VLAN被指定，PRI/TPID拷贝内层TAG */
	HI_OMCI_EXVLAN_ACT_MOD_1TAG_E           = HI_OMCI_EXVLAN_ACT_MOD_1TAG(),                  /* 切换外层TAG，VLAN/PRI/TPID都修改 */
	HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_E      = HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN(),             /* 切换外层TAG，VLAN修改 */
	HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_E       = HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI(),              /* 切换外层TAG，PRI修改 */
	HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_TPID_E  = HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_TPID(),         /* 切换外层TAG，PRI/TPID修改 */
	HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_PRI_E  = HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_PRI(),         /* 切换外层TAG，VLAN/PRI修改 */
	HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_TPID_E = HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_TPID(),        /* 切换外层TAG，VLAN/TPID修改 */
	HI_OMCI_EXVLAN_ACT_INVALID_E            = 0xffff
} hi_omci_me_exvlan_action_type_e;

#define HI_OMCI_EXVLAN_OPT_CODING(action, ethtype, outfilter, infilter) \
	((((action) & 0xff) << 24) | (((ethtype) & 0xff) << 16) | (((outfilter) & 0xff) << 8) | ((infilter) & 0xff))

#define HI_OMCI_EXVLAN_OPT_TRANSPARENT() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_TRANSPARENT_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_DEFAULT_E)

#define HI_OMCI_EXVLAN_OPT_UNTAG_TRANSPARENT() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_TRANSPARENT_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E)

#define HI_OMCI_EXVLAN_OPT_UNTAG_DISC() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_DISCARD_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E)

#define HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E)
#define HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_VLAN_PRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_VLAN_PRI_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E)

#define HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_VLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI_TPID_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E)

#define HI_OMCI_EXVLAN_OPT_UNTAG_ETHTYPE_ADD_TAG() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_E, \
				  HI_TRUE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E)


#define HI_OMCI_EXVLAN_OPT_1TAG_TRANSPARENT() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_TRANSPARENT_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_MOD_SVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_TPID_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_TPID_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_MOD_CVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E)


#define HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_CVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_E, \
				  HI_TRUE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_CVLAN_CPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E)

#define HI_OMCI_EXVLAN_OPT_1STAG_MOD_SVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_TPID_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CPRI_MOD_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_PRI_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_E, \
				  HI_TRUE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_E, \
				  HI_TRUE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CPRI_ETHTYPE_MOD_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_E, \
				  HI_TRUE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_PRI_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_PRI_E, \
				  HI_TRUE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SVLAN_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_PRI_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SVLAN_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_PRI_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SVLAN_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_PRI_E, \
				  HI_TRUE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_E, \
				  HI_TRUE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E)

#define HI_OMCI_EXVLAN_OPT_0TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_0TAG_VLAN_E, \
				  HI_TRUE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_PRI_E, \
				  HI_TRUE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E)


#define HI_OMCI_EXVLAN_OPT_1TAG_ADD_TAG_VLAN_PRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_VLAN_PRI_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_NONE_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_ADD_TAG_VLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI_TPID_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_NONE_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_ADD_CVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI_TPID_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ADD_SVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI_TPID_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ADD_SVLAN_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_VLAN_PRI_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ADD_SVLAN_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_VLAN_PRI_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E)
#if 0
#define HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ADD_SVLAN_SPRI() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI_TPID_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_PRI_E)
#endif
#define HI_OMCI_EXVLAN_OPT_1TAG_ADD_SVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_PRI_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_DEFAULT_ADD_SVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_ADD_1TAG_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_DEFAULT_E)

#define HI_OMCI_EXVLAN_OPT_1TAG_DEFAULT_DISC() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_DISCARD_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_IGNORE_E, \
				  HI_OMCI_EXVLAN_FILTER_DEFAULT_E)

#define HI_OMCI_EXVLAN_OPT_2TAG_TRANSPARENT() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_TRANSPARENT_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E, \
				  HI_OMCI_EXVLAN_FILTER_NONE_E)

#define HI_OMCI_EXVLAN_OPT_2TAG_MOD_SVLAN() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_MOD_1TAG_VLAN_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_VLAN_E, \
				  HI_OMCI_EXVLAN_FILTER_NONE_E)

#define HI_OMCI_EXVLAN_OPT_2TAG_DEAFULT_DISC() \
	HI_OMCI_EXVLAN_OPT_CODING(HI_OMCI_EXVLAN_ACT_DISCARD_E, \
				  HI_FALSE, \
				  HI_OMCI_EXVLAN_FILTER_DEFAULT_E, \
				  HI_OMCI_EXVLAN_FILTER_DEFAULT_E)

typedef enum {
	HI_OMCI_EXVLAN_OPT_TRANSPARENT_E                        = HI_OMCI_EXVLAN_OPT_TRANSPARENT(),
	HI_OMCI_EXVLAN_OPT_UNTAG_TRANSPARENT_E                  = HI_OMCI_EXVLAN_OPT_UNTAG_TRANSPARENT(),
	HI_OMCI_EXVLAN_OPT_UNTAG_DISC_E                         = HI_OMCI_EXVLAN_OPT_UNTAG_DISC(),
	HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_E                      = HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG(),
	HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_VLAN_PRI_E             = HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_VLAN_PRI(),
	HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_VLAN_E                 = HI_OMCI_EXVLAN_OPT_UNTAG_ADD_TAG_VLAN(),
	HI_OMCI_EXVLAN_OPT_UNTAG_ETHTYPE_ADD_TAG_E              = HI_OMCI_EXVLAN_OPT_UNTAG_ETHTYPE_ADD_TAG(),
	HI_OMCI_EXVLAN_OPT_1TAG_TRANSPARENT_E                   = HI_OMCI_EXVLAN_OPT_1TAG_TRANSPARENT(),
	HI_OMCI_EXVLAN_OPT_1TAG_MOD_SVLAN_E                     = HI_OMCI_EXVLAN_OPT_1TAG_MOD_SVLAN(),
	HI_OMCI_EXVLAN_OPT_1TAG_MOD_CVLAN_E                     = HI_OMCI_EXVLAN_OPT_1TAG_MOD_CVLAN(),
	HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_CVLAN_E             = HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_CVLAN(),
	HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_CVLAN_CPRI_E        = HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_CVLAN_CPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_E  = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN(),
	HI_OMCI_EXVLAN_OPT_1STAG_MOD_SVLAN_E                    = HI_OMCI_EXVLAN_OPT_1STAG_MOD_SVLAN(),

	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SPRI_E                = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_CPRI_MOD_SPRI_E                 = HI_OMCI_EXVLAN_OPT_1TAG_CPRI_MOD_SPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_SPRI_E              = HI_OMCI_EXVLAN_OPT_1TAG_ETHTYPE_MOD_SPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SPRI_E           = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SPRI_E        = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_CPRI_ETHTYPE_MOD_SPRI_E         = HI_OMCI_EXVLAN_OPT_1TAG_CPRI_ETHTYPE_MOD_SPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SPRI_E   = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SPRI(),

	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SVLAN_SPRI_E          = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_MOD_SVLAN_SPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SVLAN_SPRI_E     = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_MOD_SVLAN_SPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SVLAN_SPRI_E  = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ETHTYPE_MOD_SVLAN_SPRI(),
	HI_OMCI_EXVLAN_OPT_0TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_E  = HI_OMCI_EXVLAN_OPT_0TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN(),
	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_SPRI_E = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ETHTYPE_MOD_SVLAN_SPRI(),


	HI_OMCI_EXVLAN_OPT_1TAG_ADD_TAG_VLAN_PRI_E              = HI_OMCI_EXVLAN_OPT_1TAG_ADD_TAG_VLAN_PRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_ADD_TAG_VLAN_E                  = HI_OMCI_EXVLAN_OPT_1TAG_ADD_TAG_VLAN(),
	HI_OMCI_EXVLAN_OPT_1TAG_ADD_CVLAN_E                     = HI_OMCI_EXVLAN_OPT_1TAG_ADD_CVLAN(),
	HI_OMCI_EXVLAN_OPT_1TAG_ADD_SVLAN_E                     = HI_OMCI_EXVLAN_OPT_1TAG_ADD_SVLAN(),
	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ADD_SVLAN_E          = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ADD_SVLAN(),
	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ADD_SVLAN_SPRI_E     = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_CPRI_ADD_SVLAN_SPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ADD_SVLAN_SPRI_E          = HI_OMCI_EXVLAN_OPT_1TAG_CVLAN_ADD_SVLAN_SPRI(),
	HI_OMCI_EXVLAN_OPT_1TAG_DEFAULT_ADD_SVLAN_E             = HI_OMCI_EXVLAN_OPT_1TAG_DEFAULT_ADD_SVLAN(),
	HI_OMCI_EXVLAN_OPT_1TAG_DEFAULT_DISC_E                  = HI_OMCI_EXVLAN_OPT_1TAG_DEFAULT_DISC(),
	HI_OMCI_EXVLAN_OPT_2TAG_TRANSPARENT_E                   = HI_OMCI_EXVLAN_OPT_2TAG_TRANSPARENT(),
	HI_OMCI_EXVLAN_OPT_2TAG_MOD_SVLAN_E                     = HI_OMCI_EXVLAN_OPT_2TAG_MOD_SVLAN(),
	HI_OMCI_EXVLAN_OPT_2TAG_DEAFULT_DISC_E                  = HI_OMCI_EXVLAN_OPT_2TAG_DEAFULT_DISC(),
	HI_OMCI_EXVLAN_OPT_INVALID_E                            = 0xffffffff
} hi_omci_me_exvlan_opt_type_e;

/*Filter field*/
#define HI_OMCI_EXVLAN_GET_FILTER_OUT_PRI(pucUpVlanTbl)  (((omci_extvlantag_s*)pucUpVlanTbl)->ui_filt_outer_pri   )
#define HI_OMCI_EXVLAN_GET_FILTER_OUT_VID(pucUpVlanTbl)  (((omci_extvlantag_s*)pucUpVlanTbl)->ui_filt_outer_vid   )
#define HI_OMCI_EXVLAN_GET_FILTER_OUT_TPID(pucUpVlanTbl) (((omci_extvlantag_s*)pucUpVlanTbl)->ui_filt_outer_tpid  )
#define HI_OMCI_EXVLAN_GET_FILTER_INN_PRI(pucUpVlanTbl)  (((omci_extvlantag_s*)pucUpVlanTbl)->ui_filt_in_pri   )
#define HI_OMCI_EXVLAN_GET_FILTER_INN_VID(pucUpVlanTbl)  (((omci_extvlantag_s*)pucUpVlanTbl)->ui_filt_in_vid   )
#define HI_OMCI_EXVLAN_GET_FILTER_INN_TPID(pucUpVlanTbl) (((omci_extvlantag_s*)pucUpVlanTbl)->ui_filt_in_tpid  )
#define HI_OMCI_EXVLAN_GET_FILTER_ETHTYPE(pucUpVlanTbl)  (((omci_extvlantag_s*)pucUpVlanTbl)->ui_filt_ethertype  )

#define HI_OMCI_EXVLAN_GET_TREAT_TAG_REMOVE(pucUpVlanTbl)      (((omci_extvlantag_s*)pucUpVlanTbl)->ui_treat_tag_remove     )
#define HI_OMCI_EXVLAN_GET_TREAT_OUT_PRI(pucUpVlanTbl)  (((omci_extvlantag_s*)pucUpVlanTbl)->ui_treat_outer_pri )
#define HI_OMCI_EXVLAN_GET_TREAT_OUT_VID(pucUpVlanTbl)  (((omci_extvlantag_s*)pucUpVlanTbl)->ui_treat_outer_vid )
#define HI_OMCI_EXVLAN_GET_TREAT_OUT_TPID(pucUpVlanTbl) (((omci_extvlantag_s*)pucUpVlanTbl)->ui_treat_outer_tpid)
#define HI_OMCI_EXVLAN_GET_TREAT_INN_PRI(pucUpVlanTbl)  (((omci_extvlantag_s*)pucUpVlanTbl)->ui_treat_inner_pri )
#define HI_OMCI_EXVLAN_GET_TREAT_INN_VID(pucUpVlanTbl)  (((omci_extvlantag_s*)pucUpVlanTbl)->ui_treat_inner_vid )
#define HI_OMCI_EXVLAN_GET_TREAT_INN_TPID(pucUpVlanTbl) (((omci_extvlantag_s*)pucUpVlanTbl)->ui_treat_inner_tpid)

typedef struct {
	hi_uint32    ui_reserved1: 12        ; //Padding
	hi_uint32    ui_filt_outer_tpid: 3   ; //Filter outer TPID/DE
	hi_uint32    ui_filt_outer_vid: 13   ; //Filter outer VID
	hi_uint32    ui_filt_outer_pri: 4    ; //Filter outer priority

	hi_uint32    ui_filt_ethertype: 4    ; //Filter Ethertype
	hi_uint32    ui_reserved2: 8         ; //Padding
	hi_uint32    ui_filt_in_tpid: 3      ; //Filter inner TPID/DE
	hi_uint32    ui_filt_in_vid: 13      ; //Filter inner VID
	hi_uint32    ui_filt_in_pri: 4       ; //Filter inner priority

	hi_uint32    ui_treat_outer_tpid: 3  ; //Treatment outer TPID/DE
	hi_uint32    ui_treat_outer_vid: 13  ; //Treatment outer VID
	hi_uint32    ui_treat_outer_pri: 4   ; //Treatment outer priority
	hi_uint32    ui_reserved3: 10        ; //Padding
	hi_uint32    ui_treat_tag_remove: 2  ; //Treatment tags to remove

	hi_uint32    ui_treat_inner_tpid: 3  ; //Filter inner TPID/DE
	hi_uint32    ui_treat_inner_vid: 13  ; //Filter inner VID
	hi_uint32    ui_treat_inner_pri: 4   ; //Treatment inner priority
	hi_uint32    ui_reserved4: 12        ; //Padding
} omci_extvlantag_s;

typedef struct {
	hi_uint32    ui_reserved1: 12        ; //Padding
	hi_uint32    ui_filt_outer_tpid: 3   ; //Filter outer TPID/DE
	hi_uint32    ui_filt_outer_vid: 13   ; //Filter outer VID
	hi_uint32    ui_filt_outer_pri: 4    ; //Filter outer priority

	hi_uint32    ui_filt_ethertype: 4    ; //Filter Ethertype
	hi_uint32    ui_reserved2: 8         ; //Padding
	hi_uint32    ui_filt_in_tpid: 3      ; //Filter inner TPID/DE
	hi_uint32    ui_filt_in_vid: 13      ; //Filter inner VID
	hi_uint32    ui_filt_in_pri: 4       ; //Filter inner priority

	hi_uint32    ui_treat_outer_tpid: 3  ; //Treatment outer TPID/DE
	hi_uint32    ui_treat_outer_vid: 13  ; //Treatment outer VID
	hi_uint32    ui_treat_outer_pri: 4   ; //Treatment outer priority
	hi_uint32    ui_reserved3: 10        ; //Padding
	hi_uint32    ui_treat_tag_remove: 2  ; //Treatment tags to remove

	hi_uint32    ui_treat_inner_tpid: 3  ; //Filter inner TPID/DE
	hi_uint32    ui_treat_inner_vid: 13  ; //Filter inner VID
	hi_uint32    ui_treat_inner_pri: 4   ; //Treatment inner priority
	hi_uint32    ui_reserved4: 12        ; //Padding

	hi_ushort16  us_ethtype             ;  //User-defined Ethtype vlaue
} omci_hwextvlantag_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8    uc_type;
	hi_ushort16  us_rcvvlanmax;
	hi_ushort16  us_intpid;
	hi_ushort16  us_outtpid;
	hi_uchar8    uc_dnmode;
	omci_extvlantag_s  st_vlantagtable;
	hi_ushort16  us_assocme_ptr;
	hi_uchar8    uc_dscp2pbit[24];       /*DSCP to P bit mapping*/
} hi_omci_me_extvlantag_op_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16  us_rcvvlanmax;
	omci_hwextvlantag_s  st_hwvlantagtable;
} hi_omci_me_hwextvlantag_op_s;

/********************Multicast subscriber config info***************************/
typedef enum {
	HI_OMCI_ME_MULTISUBS_PKGTAB_SET = 1,
	HI_OMCI_ME_MULTISUBS_PKGTAB_DEL,
	HI_OMCI_ME_MULTISUBS_PKGTAB_CLR,
} hi_omci_me_multisubs_pkgtab_opt_e;

typedef union {
	struct {
		hi_ushort16 rowkey: 10;
		hi_ushort16 resv: 4;
		hi_ushort16 opt: 2;
	} bits;
	hi_ushort16 us_value;
} hi_omci_me_multisubs_pkgtab_tabctrl;

typedef struct {
	hi_omci_me_multisubs_pkgtab_tabctrl st_tabctrl;
	hi_ushort16 us_vid;
	hi_ushort16 us_maxgroup;
	hi_uint32 ui_maxbandwidth;
	hi_ushort16 us_multi_oper_ptr;
	hi_uchar8 auc_resv[8];
} hi_omci_me_multisubs_pkgtab_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8    uc_metype;             /*indicates the type of the ME implicitly linked*/
	hi_ushort16  us_profile_ptr;        /*points to an instance of the multicast operations profil*/
	hi_ushort16  us_maxgrp;             /*maximum number of dynamic multicast groups*/
	hi_uint32    ui_maxbw;              /*Max multicast bandwidth*/
	hi_uchar8    uc_bwenfor;            /*  Bandwidth enforcement*/
	hi_omci_me_multisubs_pkgtab_s st_pkgtab;    /* Multicast service package table */
} hi_omci_me_multisubs_cfg_s;

/**********Dot1X port extension package HI_OMCI_ME_DOT1X_PORT_EXT_PACKAGE_E************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_dot1x;         /*Dot1x enable*/
	hi_uchar8   uc_actreg;        /*Action register*/
	hi_uchar8   uc_authenpae;     /*Authenticator PAE state*/
	hi_uchar8   uc_backendauthen; /*Backend authentication state*/
	hi_uchar8   uc_adminctrl;     /*Admin controlled directions*/
	hi_uchar8   uc_operctrl;      /*Operational controlled directions*/
	hi_uchar8   uc_authenctrl;    /*Authenticator controlled port status*/
	hi_ushort16 us_quiet;         /*Quiet period*/
	hi_ushort16 us_servertimeout; /*Server timeout period*/
	hi_ushort16 us_reauthen;      /*Re-authentication period*/
	hi_uchar8   uc_reauthenenable; /*Re-authentication enabled*/
	hi_uchar8   uc_keyenable;     /*Key transmission enabled*/
} hi_omci_me_dot1xport_s;

/*******Dot1X configuration profile HI_OMCI_ME_DOT1X_CFG_PROFILE_E*******/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16 us_circuitid;        /*Circuit ID prefix*/
	hi_uchar8   uc_fallback;         /*Fallback policy*/
	hi_ushort16 us_authserver1;      /*Auth server 1*/
	hi_uchar8   uc_sharedsecret1[25];/*Shared secret auth1*/
	hi_ushort16 us_authserver2;      /*Auth server 2*/
	hi_uchar8   uc_sharedsecret2[25];/*Shared secret auth2*/
	hi_ushort16 us_authserver3;      /*Auth server 3*/
	hi_uchar8   uc_sharedsecret3[25];/*Shared secret auth3*/
	hi_uint32   ui_oltproxy;         /*OLT proxy address*/
} hi_omci_me_dot1xprofile_s;

/**********Dot1X performance monitoring history data HI_OMCI_ME_DOT1X_PMH_DATA_E**********/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_endtime;        /*Interval end time*/
	hi_ushort16 us_thresid;      /*Threshold data 1/2 id*/
	hi_uint32 ui_eapolrcv;       /*EAPOL frames received*/
	hi_uint32 ui_eapoltrans;     /*EAPOL frames transmitted*/
	hi_uint32 ui_eapolstartrcv;  /*EAPOL start frames received*/
	hi_uint32 ui_eapollogoffrcv; /*EAPOL logoff frames received*/
	hi_uint32 ui_invalideapolrcv;/*Invalid EAPOL frames received*/
	hi_uint32 ui_eapresprcv;     /*EAP resp/id frames received*/
	hi_uint32 ui_eaprsprcv;      /*EAP response frames received*/
	hi_uint32 ui_eapinittrans;   /*EAP initial request frames transmitted*/
	hi_uint32 ui_eapreqtrans;    /*EAP request frames transmitted*/
	hi_uint32 ui_eaperrrcv;      /*EAP length error frames received*/
	hi_uint32 ui_eapsucc;        /*EAP success frames generated autonomously*/
	hi_uint32 ui_eapfail;        /*EAP failure frames generated autonomously*/

} hi_omci_me_dot1xpm_s;

/******************Radius performance monitoring history data HI_OMCI_ME_RADIUS_PMH_DATA_E*******************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8   uc_endtime;
	hi_ushort16 us_thresid;
	hi_uint32 ui_accreqstrans; /*Access-request packets transmitted*/
	hi_uint32 ui_accreqretrans;/*Access-request retransmission count*/
	hi_uint32 ui_accchallrcv;  /*Access-challenge packets received*/
	hi_uint32 ui_accacceptrcv; /*Access-accept packets received*/
	hi_uint32 ui_accrejectrcv; /*Access-reject packets received*/
	hi_uint32 ui_invalidrcv;   /*Invalid radius packets received*/
} hi_omci_me_radiuspm_s;

/***********Dot1 rate limiter  HI_OMCI_ME_DOT1_RATE_LIMITER_E***************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16 us_parentme_ptr;//Parent ME pointer
	hi_uchar8   uc_tptype;  //TP type
	hi_ushort16 us_upucrate_ptr;//Upstream unicast flood rate pointer
	hi_ushort16 us_upbcrate_ptr;//Upstream broadcast rate pointer
	hi_ushort16 us_upmcrate_ptr;//Upstream multicast payload rate pointer
} hi_omci_me_dot1rate_s;

/************Dot1ag maintenance domain HI_OMCI_ME_DOT1AG_MAINT_DOMAIN_E**************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_mdlevel;
	hi_uchar8 uc_mdnamefmt;
	hi_uchar8 uc_mdname1[25];
	hi_uchar8 uc_mdname2[25];
	hi_uchar8 uc_mhfcreat;
	hi_uchar8 uc_senderid;
} hi_omci_me_dot1agdomain_s;

/************Dot1ag maintenance association  HI_OMCI_ME_DOT1AG_MAINT_ASSOCIATION_E*************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16 us_md_ptr;
	hi_uchar8   uc_manamefmt;
	hi_uchar8   uc_maname1[25];
	hi_uchar8   uc_maname2[25];
	hi_uchar8   uc_ccminterval;
	hi_ushort16 us_vlan[12];
	hi_uchar8   uc_mhfcreat;
	hi_uchar8   uc_senderid;
} hi_omci_me_dot1agasso_s;

/*************Dot1ag default MD level  HI_OMCI_ME_DOT1AG_DEF_MD_LV_E**************/
typedef struct {
	hi_ushort16 us_primaryvlan;/*Primary VLAN ID*/
	hi_uchar8   uc_tablectrl;  /*Table control*/
	hi_uchar8   uc_status;     /*Status*/
	hi_uchar8   uc_level;      /*Level*/
	hi_uchar8   uc_mhfcreat;   /*MHF creation*/
	hi_uchar8   uc_senderid;   /*Sender ID permission*/
	hi_ushort16 us_vlanlist[11];/*Associated VLANs list*/
} omci_defaultmdlevel_s;

#define MD_LEVEL_NUM 10

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_layer2type;    /*Layer 2 type*/
	hi_uchar8 uc_catlevel;      /*Catchall level*/
	hi_uchar8 uc_catmhfcreation;/*Catchall MHF creation*/
	hi_uchar8 uc_catsenderid;   /*Catchall sender ID permission*/
	omci_defaultmdlevel_s st_mdlevel[MD_LEVEL_NUM];/*Default MD level table*/
} hi_omci_me_dot1agmd_level_s;

/****************Dot1ag MEP  HI_OMCI_ME_DOT1AG_MEP_E***************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_ushort16 us_entity_ptr; /*Layer 2 entity pointer*/
	hi_uchar8   uc_type;          /*Layer 2 type*/
	hi_ushort16 us_ma_ptr;     /*MA pointer*/
	hi_ushort16 us_mepid;         /*MEP ID*/
	hi_uchar8   uc_mepctrl;       /*MEP control*/
	hi_ushort16 us_primvlan;      /*Primary VLAN*/
	hi_uchar8   uc_adminstate;    /*Administrative state*/
	hi_uchar8   uc_priority;      /*CCM and LTM priority*/
	hi_uchar8   uc_egrid[8];      /*Egress identifier*/
	hi_ushort16 us_peermepid[12]; /*Peer MEP IDs*/
	hi_uchar8   uc_aiscontrol;    /*ETH AIS control*/
	hi_uchar8   uc_faultalarm;    /*Fault alarm threshold*/
	hi_ushort16 us_declartime;    /*Alarm declaration soak time*/
	hi_ushort16 us_cleartime;     /*Alarm clear soak time*/
} hi_omci_me_dot1agmep_s;

/****************Dot1ag MEP status******************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_macaddr[6];         //MEP MAC address
	hi_uchar8 uc_faultstate;         //Fault notification generator state
	hi_uchar8 uc_highpridefect;      //Highest priority defect observed
	hi_uchar8 uc_curdefect;          //Current defects
	hi_uchar8 uc_lasterr[128];       //Last received errored CCM table
	hi_uchar8 uc_lastxcon[128];      //Last received xcon CCM table
	hi_uint32 ui_outofseqcnt;        //Out of sequence CCMs count
	hi_uint32 ui_ccmtrans;           //CCMs transmitted count
	hi_uint32 ui_unexpltr;           //Unexpected LTRs count
	hi_uint32 ui_lbrtrans;           //LBRs transmitted count
	hi_uint32 ui_nextloopback;       //Next loopback transaction identifier
	hi_uint32 ui_nexttrace;          //Next link trace transaction identifier
} hi_omci_me_dot1agmep_status_s;

/**************Dot1ag MEP CCM database**************/
#define SENDER_ID_LEN 8
typedef struct {
	hi_ushort16 us_rmepid;
	hi_uchar8   uc_rmepstate;
	hi_uint32   ui_failedtime;
	hi_uchar8   uc_macaddr[6];
	hi_uchar8   uc_rdi;
	hi_uchar8   uc_portstatus;
	hi_uchar8   uc_interstatus;
	hi_uchar8   uc_senderid[SENDER_ID_LEN];
} rmep_database_table_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	rmep_database_table_s st_db1;  //RMEP 1 database table
	rmep_database_table_s st_db2;  //RMEP 2 database table
	rmep_database_table_s st_db3;  //RMEP 3 database table
	rmep_database_table_s st_db4;  //RMEP 4 database table
	rmep_database_table_s st_db5;  //RMEP 5 database table
	rmep_database_table_s st_db6;  //RMEP 6 database table
	rmep_database_table_s st_db7;  //RMEP 7 database table
	rmep_database_table_s st_db8;  //RMEP 8 database table
	rmep_database_table_s st_db9;  //RMEP 9 database table
	rmep_database_table_s st_db10; //RMEP 10 database table
	rmep_database_table_s st_db11; //RMEP 11 database table
	rmep_database_table_s st_db12; //RMEP 12 database table
} hi_omci_me_dot1agccm_s;

/*************Dot1ag CFM stack**************/
typedef struct {
	hi_ushort16 us_portid;
	hi_uchar8   uc_level;
	hi_uchar8   uc_direction;
	hi_ushort16 us_vlanid;
	hi_ushort16 us_md_ptr;
	hi_ushort16 us_ma_ptr;
	hi_ushort16 us_mepid;
	hi_uchar8   uc_macaddr[6];
} mp_status_table_s;

typedef struct {
	hi_ushort16 us_vlanid;
	hi_ushort16 us_portid;
	hi_uchar8   uc_detectederr;
} cfg_err_list_table_s;


typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_layer2type;
	mp_status_table_s  st_mpstatus[1];   //MP status table
	cfg_err_list_table_s st_configerr[1];//Configuration error list table
} hi_omci_me_dot1agcfm_s;

/***********Dot1ag chassis-management info**************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_chassidlen;          //Chassis ID length
	hi_uchar8 uc_chassidsubtype;      //Chassis ID subtype
	hi_uchar8 uc_chassidpart1[25];    //Chassis ID part 1
	hi_uchar8 uc_chassidpart2[25];    //Chassis ID part 2
	hi_uchar8 uc_domainlen;           //Management address domain length
	hi_uchar8 uc_domain1[25];         //Management address domain 1
	hi_uchar8 uc_domain2[25];         //Management address domain 2
	hi_uchar8 uc_mngaddrlen;          //Management address length
	hi_uchar8 uc_mngaddr1[25];        //Management address 1
	hi_uchar8 uc_mngaddr2[25];        //Management address 2
} hi_omci_me_dot1agchassis_s;

/***********Multicast subscriber monitor************/
typedef struct {
	hi_ushort16 us_vlanid;   //VLAN ID
	hi_uint32 ui_sip;        //Source IP address
	hi_uint32 ui_dip;        //Multicast destination IP address
	hi_uint32 ui_bestbandwidth;//Best efforts actual bandwidth estimate, bytes per second
	hi_uint32 ui_cip;        //Client (set-top box) IP address
	hi_uint32 ui_jointime;   //Time since the most recent join of this client to the IP channel,
	hi_ushort16 us_resv;     //Reserved
} active_grp_list_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_metype;//ME type
	hi_uint32 ui_curbandwidth;//Current multicast bandwidth
	hi_uint32 ui_msgcnt;//Join messages counter
	hi_uint32 ui_bwexceedcnt;//Bandwidth exceeded counter
	active_grp_list_s st_actgrplist;//Active group list table
} hi_omci_me_multisubs_monitor_s;

/**************Ethernet frame performance monitoring history data upstream*****************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_endtime;
	hi_ushort16 us_thresid;
	hi_uint32 ui_dropevents;
	hi_uint32 ui_octets;
	hi_uint32 ui_packets;
	hi_uint32 ui_bc;
	hi_uint32 ui_mc;
	hi_uint32 ui_crcerrored;
	hi_uint32 ui_undersize;
	hi_uint32 ui_oversize;
	hi_uint32 ui_pkt64;
	hi_uint32 ui_pkt65to127;
	hi_uint32 ui_pkt128to255;
	hi_uint32 ui_pkt256to511;
	hi_uint32 ui_pkt512to1023;
	hi_uint32 ui_pkt1024to1518;
} hi_omci_me_ethpm_up_s;

/**************Ethernet frame performance monitoring history data downstream*****************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_endtime;
	hi_ushort16 us_thresid;
	hi_uint32 ui_dropevents;
	hi_uint32 ui_octets;
	hi_uint32 ui_packets;
	hi_uint32 ui_bc;
	hi_uint32 ui_mc;
	hi_uint32 ui_crcerrored;
	hi_uint32 ui_undersize;
	hi_uint32 ui_oversize;
	hi_uint32 ui_pkt64;
	hi_uint32 ui_pkt65to127;
	hi_uint32 ui_pkt128to255;
	hi_uint32 ui_pkt256to511;
	hi_uint32 ui_pkt512to1023;
	hi_uint32 ui_pkt1024to1518;
} hi_omci_me_ethpm_dn_s;

/*************Ethernet frame extended PM**************/
typedef struct {
	hi_ushort16 us_thresid;
	hi_ushort16 us_class;
	hi_ushort16 us_inst;
	hi_ushort16 us_accumula_disable;
	hi_ushort16 us_tca_disable;
	hi_ushort16 us_ctlfields;
	hi_ushort16 us_tci;
	hi_ushort16 us_reserved;
} omci_ctrlblock_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_endtime;
	omci_ctrlblock_s st_ctrlblock;
	hi_uint32 ui_dropevents;
	hi_uint32 ui_octets;
	hi_uint32 ui_frames;
	hi_uint32 ui_bc;
	hi_uint32 ui_mc;
	hi_uint32 ui_crcerrored;
	hi_uint32 ui_undersize;
	hi_uint32 ui_oversize;
	hi_uint32 ui_frm64;
	hi_uint32 ui_frm65to127;
	hi_uint32 ui_frm128to255;
	hi_uint32 ui_frm256to511;
	hi_uint32 ui_frm512to1023;
	hi_uint32 ui_frm1024to1518;
} hi_omci_me_eth_ext_pm_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_endtime;
	omci_ctrlblock_s st_ctrlblock;
	hi_ulong64 ui_dropevents;
	hi_ulong64 ui_octets;
	hi_ulong64 ui_frames;
	hi_ulong64 ui_bc;
	hi_ulong64 ui_mc;
	hi_ulong64 ui_crcerrored;
	hi_ulong64 ui_undersize;
	hi_ulong64 ui_oversize;
	hi_ulong64 ui_frm64;
	hi_ulong64 ui_frm65to127;
	hi_ulong64 ui_frm128to255;
	hi_ulong64 ui_frm256to511;
	hi_ulong64 ui_frm512to1023;
	hi_ulong64 ui_frm1024to1518;
} hi_omci_me_eth_ext_pm_64b_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_endtime;
	hi_ushort16 us_thresid;
	hi_uint32 ui_lrn_disc;      /* Bridge learning entry discard count */
} hi_omci_me_br_pm_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 uc_endtime;
	hi_ushort16 us_thresid;
	hi_uint32 ui_fwd;           /* Forwarded frame counter */
	hi_uint32 ui_dly_disc;      /* Delay exceeded discard counter */
	hi_uint32 ui_mtu_disc;      /* MTU exceeded discard counter */
	hi_uint32 ui_rcv;           /* Received frame counter */
	hi_uint32 ui_rcv_disc;      /* Received and discarded counter */
} hi_omci_me_br_port_pm_s;

typedef struct {
	hi_uchar8   uc_filterintpid;
	hi_uchar8   uc_filterouttpid;
	hi_ushort16 us_filterinvlan;
	hi_ushort16 us_filteroutvlan;
	hi_uchar8   uc_filterinpri;
	hi_uchar8   uc_filteroutpri;
	hi_uint32   ui_filterethtype;
	hi_uchar8   uc_treatintpid;
	hi_uchar8   uc_treatouttpid;
	hi_ushort16 us_treatinvlan;
	hi_ushort16 us_treatoutvlan;
	hi_uchar8   uc_treatinpri;
	hi_uchar8   uc_treatoutpri;
} hi_omci_vlan_opt_rule_s;


#pragma pack()

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __OMCI_ME_L2_DEF_H__ */
