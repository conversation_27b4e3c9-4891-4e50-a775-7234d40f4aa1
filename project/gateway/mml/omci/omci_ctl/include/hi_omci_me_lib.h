/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_me_lib.h
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-16
  Description: ME内部公共函数头文件
******************************************************************************/

#ifndef __HI_OMCI_ME_LIB_H__
#define __HI_OMCI_ME_LIB_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#include "hi_omci_me_map.h"

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_ME_ALARM_BITMAP(index) (1 << (15 - (index)))

#define HI_OMCI_ME_ALARM_COMM_TIMEOUT (15 * 60 * 1000 + 1000)    /* 15分钟，后延1秒 */


/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef struct {
	hi_uint32 ui_meid;
	hi_uint32 ui_instid;
} hi_omci_me_stat_timer_s;

typedef struct {
	hi_uint32 ui_meid;
	hi_uint32 ui_instid;
	hi_uint32 ui_local;     /* 1表示定时器是随ONT本地实体创建的;0表示是随OLT下发实体创建的 */
	hi_uint32 ui_timeout;
} hi_omci_me_alarm_timer_s;


/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
extern hi_int32 hi_omci_me_init();
extern hi_int32 hi_omci_me_exit();
extern hi_int32 hi_omci_me_reset();
extern hi_int32 hi_omci_me_deactive_exit();

extern hi_int32 hi_omci_me_pptpethuni_init(hi_uint32 ui_portid);
extern hi_int32 hi_omci_me_pptpethuni_exit(hi_uint32 ui_portid);
extern hi_int32 hi_omci_me_veip_init();
extern  hi_int32 hi_omci_me_tr069_management_init();
extern hi_int32 hi_omci_me_ip_host_init();
extern hi_int32 hi_omci_ext_ont_ability_init();
extern hi_int32 hi_omci_me_unig_init(hi_uint32 ui_portid);

extern hi_int32 hi_omci_me_tcont_init(hi_uint32 ui_tcontid);
extern hi_int32 hi_omci_me_anig_init();
extern hi_int32 hi_omci_me_pq_init(hi_uint32 ui_direction, hi_uint32 ui_tcont, hi_uint32 portid, hi_uint32 pri);

extern hi_int32 hi_omci_me_rmtdbg_init();
extern hi_int32 hi_omci_me_onudata_init();
extern hi_int32 hi_omci_me_oltg_init();
extern hi_int32 hi_omci_me_onug_init();
extern hi_int32 hi_omci_me_onu2g_init();
extern hi_int32 hi_omci_me_cardholder_init(hi_uint32 ui_isup, hi_uint32 ui_index, hi_uint32 ui_ishgu);
extern hi_int32 hi_omci_me_circuitpack_init(hi_uint32 ui_isup, hi_uint32 ui_index, hi_uint32 ui_ishgu);
extern hi_int32 hi_omci_me_onu_capability_init();
extern hi_int32 hi_omci_me_loid_auth_init();
extern hi_int32 hi_omci_me_powermng_init();
int32_t hi_omci_me_onu_optical_info_init();

extern hi_int32 hi_omci_me_stat_start(hi_omci_me_stat_timer_s *pst_timer);
extern hi_int32 hi_omci_me_stat_stop(hi_omci_me_stat_timer_s *pst_timer);
extern hi_int32 hi_omci_me_stat_init();
extern hi_int32 hi_omci_me_stat_exit();

extern hi_int32 hi_omci_me_alarm_msg_send(hi_uint32 ui_meid, hi_uint32 ui_instid, hi_uint32 ui_bitmap);
extern hi_int32 hi_omci_me_alarm_start(hi_omci_me_alarm_timer_s *pst_timer);
extern hi_int32 hi_omci_me_alarm_stop(hi_omci_me_alarm_timer_s *pst_timer);
extern hi_int32 hi_omci_me_alarm_init();
extern hi_int32 hi_omci_me_alarm_exit(hi_uint32 flag);
extern hi_int32 hi_omci_me_local_alarm_restart(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_void hi_omci_set_mibreset_flag(hi_uint32 flag);

extern hi_int32 hi_omci_me_pptp_pots_init(hi_uint32 ui_pots);
extern hi_int32 hi_omci_me_voip_line_status_init(hi_uint32 ui_pots);
extern hi_int32 hi_omci_me_iphost_init(hi_uint32 ui_stacknum);
extern hi_int32 hi_omci_me_voip_cfg_data_init();
extern hi_int32 hi_omci_voip_msg();
hi_void hi_omci_voip_msg_eixt();
extern hi_int32 hi_omci_me_esc_init();
extern hi_uint32 hi_omci_me_esc_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);
extern hi_int32 hi_omci_me_oltg_set(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen);

extern hi_int32 hi_omci_me_gemctppm_init();
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_ME_LIB_H__*/
