/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_me_misc.h
  Version     : 初稿
  Author      : owen
  Creation    : 2016-3-15
  Description : Miscellaneous services data define
******************************************************************************/

#ifndef __HI_OMCI_ME_MISC_H__
#define __HI_OMCI_ME_MISC_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_OLT_CAP_LEN                  16
#define HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN   17
#define HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN   16
#define HI_OMCI_ONU_AUTH_RET_TAB_ITME_LEN    16
#define HI_OMCI_OLT_AUTH_RET_TAB_ITME_LEN    17
#define HI_OMCI_MSK_LEN                      16
#define HI_OMCI_KEY_LEN                      16

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/

typedef enum {
	HI_OMCI_ME_BCKEY_SET = 0,
	HI_OMCI_ME_BCKEY_CLR,
	HI_OMCI_ME_BCKEY_CLRALL,
} hi_omci_me_bckey_action_e;

#pragma pack(1)

typedef struct {
	struct {
		hi_uchar8 ctrl: 2;      /* determine the attribute.s behaviour under the set action */
		hi_uchar8 resv: 2;
		hi_uchar8 fraglen: 4;   /* specify the length of the fragment */
	} st_rowctl;

	struct {
		hi_uchar8 fragnum: 4;   /* the key fragment number */
		hi_uchar8 resv: 2;
		hi_uchar8 keyindex: 2;  /* key index */
	} st_rowid;

	hi_uchar8 auc_key[HI_OMCI_KEY_LEN];
} hi_omci_me_bc_key_tab_s;

typedef struct {
	hi_omci_me_msg_head_s st_msghead;
	hi_uchar8 auc_olt_cap[HI_OMCI_OLT_CAP_LEN];                         /* OLT crypto capabilities */
	hi_uchar8 auc_olt_tab[HI_OMCI_OLT_CHALLENGE_TAB_ITME_LEN];          /* OLT random challenge table */
	hi_uchar8 uc_olt_status;                                            /* OLT challenge status */
	hi_uchar8 uc_onu_cap;                                               /* ONU selected crypto capabilities */
	hi_uchar8 auc_onu_tab[HI_OMCI_ONU_CHALLENGE_TAB_ITME_LEN];          /* ONU random challenge table */
	hi_uchar8 auc_onu_auth_ret_tab[HI_OMCI_ONU_AUTH_RET_TAB_ITME_LEN];  /* ONU authentication result table */
	hi_uchar8 auc_olt_auth_ret_tab[HI_OMCI_OLT_AUTH_RET_TAB_ITME_LEN];  /* OLT authentication result table */
	hi_uchar8 uc_olt_ret_status;                                        /* OLT result status */
	hi_uchar8 uc_onu_auth_state;                                        /* ONU authentication state */
	hi_uchar8 auc_msk[HI_OMCI_MSK_LEN];                                 /* Master session key name */
	hi_omci_me_bc_key_tab_s st_bckey;                                   /* Broadcast key table */
	hi_ushort16 us_keylen;                                              /* Effective key length */
} hi_omci_me_esc_s;

#pragma pack()

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_me_esc_exit
 Description : Enhanced security control exit
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_me_esc_exit();

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_ME_MISC_H__*/
