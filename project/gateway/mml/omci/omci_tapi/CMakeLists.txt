include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

# 组件名称
set(USERAPP_NAME hi_omci_tapi)

# 组件类型
set(USERAPP_TARGET_TYPE so)

# 依赖的源文件
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source OMCI_TAPI_SOURCE_DIR)
set(USERAPP_PRIVATE_SRC
    ${OMCI_TAPI_SOURCE_DIR}
)

# 依赖的头文件
set(USERAPP_PRIVATE_INC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${HGW_BASIC_DIR}/include
    ${HGW_BASIC_DIR}/include/os
    ${HGW_FWK_DIR}/include
    ${HGW_FWK_DIR}/ioreactor/include
    ${HGW_FWK_DIR}/util/include
    ${HGW_FWK_DIR}/ipc/ipc_lib
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_CML_DIR}/odlapi/include
    ${HGW_CML_DIR}/odl/include
    ${HGW_SERVICE_DIR}/pon/sml/include
    ${HGW_SERVICE_DIR}/network/emu/u_space/include
    ${HGW_SERVICE_DIR}/network/easymesh/include
    ${HGW_SERVICE_DIR}/dms/encrypt/include
    ${HGW_SERVICE_DIR}/dms/sysinfo/include
    ${HGW_SERVICE_DIR}/dms/upgrade/include
    ${HGW_SERVICE_DIR}/dms/board/include
    ${HGW_SERVICE_DIR}/network/common/include
    ${HGW_SERVICE_DIR}/network/qos/u_space/include
)

set(USERAPP_PRIVATE_LIB
    hi_ipc hi_odlapi
    hi_basic hi_util hi_owal_ssf
)

# 自定义宏
set(USERAPP_PRIVATE_DEFINE)

# 自定义编译选项
set(USERAPP_PRIVATE_COMPILE)

build_app_feature()
