/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_sysinfo.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-25
  Description: 获取系统信息相关接口
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
//#include "hi_omci_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_sysinfo.h"
#include "hi_board.h"
#include "hi_ipc.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
hi_omci_tapi_sysinfo_s gst_sysinfo;
static hi_uint32 gui_is_init = HI_TRUE;
#ifdef CONFIG_PLATFORM_OPENWRT
static hi_omci_tapi_sn_loid_s gst_sn_loid = {0};
#endif

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_sysinfo_get
 Description : 获取系统信息
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_sysinfo_get(hi_omci_tapi_sysinfo_s *pst_info)
{
	hi_int32 i_ret;
	hi_board_info_s st_info;
	hi_sysinfo_data_s st_sysinfo;

	if (HI_TRUE == gui_is_init) {
		i_ret = HI_IPC_CALL("hi_board_info_get", &st_info);
		HI_OMCI_RET_CHECK(i_ret);
		i_ret = HI_IPC_CALL("hi_sysinfo_data_get", &st_sysinfo);
		HI_OMCI_RET_CHECK(i_ret);

		HI_OS_MEMCPY_S((void *)&gst_sysinfo.ui_vendor_id, sizeof(gst_sysinfo.ui_vendor_id), st_sysinfo.ac_vendor_id,
			       sizeof(gst_sysinfo.ui_vendor_id));
		HI_OS_MEMCPY_S(gst_sysinfo.auc_version, sizeof(gst_sysinfo.auc_version), st_sysinfo.ac_hw_version,
			       sizeof(gst_sysinfo.auc_version));
		HI_OS_MEMCPY_S(gst_sysinfo.auc_soft_version, sizeof(gst_sysinfo.auc_soft_version), st_sysinfo.ac_sw_version,
			       sizeof(gst_sysinfo.auc_soft_version));

		HI_OS_MEMCPY_S(gst_sysinfo.auc_equid, sizeof(gst_sysinfo.auc_equid), st_sysinfo.ac_equid,
			       sizeof(gst_sysinfo.auc_equid));
		HI_OS_MEMCPY_S(gst_sysinfo.auc_operator, sizeof(gst_sysinfo.auc_operator), st_sysinfo.ac_operator,
			       sizeof(gst_sysinfo.auc_operator));
		gst_sysinfo.ui_product_code = st_sysinfo.ui_product_code;
		gst_sysinfo.omic_ext = st_sysinfo.omci_ext;
		gst_sysinfo.ui_port_num = st_info.ui_eth_num;
		gst_sysinfo.ui_ge_num = st_info.ui_ge_num;
		gst_sysinfo.ui_fe_num = st_info.ui_fe_num;
		gst_sysinfo.ui_pots_num = st_info.ui_voice_num;

		if (0 == hi_os_strcasecmp(st_sysinfo.ac_mode, "SFU")) {
			gst_sysinfo.ui_product_mode = HI_OMCI_ME_ONU_CAPABILITY_TYPE_SFU_E;
		} else if (0 == hi_os_strcasecmp(st_sysinfo.ac_mode, "HGU")) {
			gst_sysinfo.ui_product_mode = HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E;
		} else if (0 == hi_os_strcasecmp(st_sysinfo.ac_mode, "SBU")) {
			gst_sysinfo.ui_product_mode = HI_OMCI_ME_ONU_CAPABILITY_TYPE_SBU_E;
		} else {
			gst_sysinfo.ui_product_mode = HI_OMCI_ME_ONU_CAPABILITY_TYPE_RESV_E;
		}

		gui_is_init = HI_FALSE;
	}

	HI_OS_MEMCPY_S(pst_info, sizeof(*pst_info), &gst_sysinfo, sizeof(gst_sysinfo));

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_sn_loid_get
 Description : 获取系统信息
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_sn_loid_get(hi_omci_tapi_sn_loid_s *pst_info)
{
	hi_int32 i_ret;
	hi_sysinfo_data_s st_sysinfo;

	i_ret = HI_IPC_CALL("hi_sysinfo_data_get", &st_sysinfo);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMCPY_S(pst_info->auc_mac, sizeof(pst_info->auc_mac), st_sysinfo.auc_gw_mac, sizeof(pst_info->auc_mac));
	HI_OS_MEMCPY_S(pst_info->auc_sn, sizeof(pst_info->auc_sn), st_sysinfo.st_auid.auc_sn, sizeof(pst_info->auc_sn));
	HI_OS_MEMCPY_S(pst_info->auc_psk, sizeof(pst_info->auc_psk), st_sysinfo.ac_psk, sizeof(pst_info->auc_psk));
	HI_OS_MEMCPY_S(pst_info->auc_regid, sizeof(pst_info->auc_regid), st_sysinfo.ac_reg_id, sizeof(pst_info->auc_regid));

#ifndef CONFIG_PLATFORM_OPENWRT
	HI_OS_MEMCPY_S(pst_info->auc_loid, sizeof(pst_info->auc_loid), st_sysinfo.ac_loid, sizeof(pst_info->auc_loid));
	HI_OS_MEMCPY_S(pst_info->auc_lopwd, sizeof(pst_info->auc_lopwd), st_sysinfo.ac_lopwd, sizeof(pst_info->auc_lopwd));
#else
	(void)memcpy_s(pst_info->auc_loid, sizeof(pst_info->auc_loid),
		gst_sn_loid.auc_loid, sizeof(gst_sn_loid.auc_loid));
	(void)memcpy_s(pst_info->auc_lopwd, sizeof(pst_info->auc_lopwd),
		gst_sn_loid.auc_lopwd, sizeof(gst_sn_loid.auc_lopwd));
#endif

	return HI_RET_SUCC;
}
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
#ifdef CONFIG_PLATFORM_OPENWRT
HI_DEF_IPC(hi_omci_set_loid, hi_omci_tapi_sn_loid_s *, loid)
{
	(void)memcpy_s(gst_sn_loid.auc_loid, sizeof(gst_sn_loid.auc_loid),
		loid->auc_loid, sizeof(loid->auc_loid));
	(void)memcpy_s(gst_sn_loid.auc_lopwd, sizeof(gst_sn_loid.auc_lopwd),
		loid->auc_lopwd, sizeof(loid->auc_lopwd));
	return HI_RET_SUCC;
}
#endif

/*用于库文件加载*/
hi_int32 hi_omci_tapi_init()
{
	return HI_RET_SUCC;
}

hi_void hi_omci_tapi_exit()
{
	return;
}
