/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_mc.c
  Version    : 初稿
  Author     : owen
  Creation   : 2014-1-18
  Description: OMCI Multicast TAPI
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
//#include "hi_omci_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_sml_mc.h"
#include "hi_ipc.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_mc_attr_set
 Description : 配置组播处理属性
 Input Parm  : hi_omci_tapi_mc_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_attr_set(hi_omci_tapi_mc_attr_s *pst_attr)
{
	hi_sml_mc_attr_s st_attr = {0};
	hi_int32 i_ret;

	i_ret = HI_IPC_CALL("hi_sml_mc_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	switch (pst_attr->em_version) {
	case HI_OMCI_TAPI_MC_IGMPV1_E:
	case HI_OMCI_TAPI_MC_IGMPV2_E:
		st_attr.em_mode = HI_SML_MC_IGMPV2_E;
		break;

	case HI_OMCI_TAPI_MC_IGMPV3_E:
		st_attr.em_mode = HI_SML_MC_IGMPV3_E;
		break;

	case HI_OMCI_TAPI_MC_MLDV1_E:
		st_attr.em_mode = HI_SML_MC_MLDV1_E;
		break;

	case HI_OMCI_TAPI_MC_MLDV2_E:
		st_attr.em_mode = HI_SML_MC_MLDV2_E;
		break;

	default:
		hi_omci_tapi_systrace(HI_RET_INVALID_PARA, pst_attr->em_version, 0, 0, 0);
		return HI_RET_INVALID_PARA;
	}

	st_attr.ui_fast_leave_en = pst_attr->ui_imme_leave_en;
	st_attr.ui_snooping_range = HI_TRUE;

	i_ret = HI_IPC_CALL("hi_sml_mc_attr_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_attr.em_mode,
			      st_attr.ui_fast_leave_en, st_attr.ui_snooping_range, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_attr_get
 Description : 获取组播配置属性
 Input Parm  : hi_omci_tapi_mc_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_attr_get(hi_omci_tapi_mc_attr_s *pst_attr)
{
	hi_sml_mc_attr_s st_attr = {0};
	hi_int32 i_ret;

	i_ret = HI_IPC_CALL("hi_sml_mc_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	switch (st_attr.em_mode) {
	case HI_SML_MC_IGMPV2_E:
		pst_attr->em_version = HI_OMCI_TAPI_MC_IGMPV2_E;
		break;

	case HI_SML_MC_IGMPV3_E:
		pst_attr->em_version = HI_OMCI_TAPI_MC_IGMPV2_E;
		break;

	case HI_SML_MC_MLDV1_E:
		pst_attr->em_version = HI_OMCI_TAPI_MC_MLDV1_E;
		break;

	case HI_SML_MC_MLDV2_E:
		pst_attr->em_version = HI_OMCI_TAPI_MC_MLDV2_E;
		break;

	default:
		pst_attr->em_version = HI_OMCI_TAPI_MC_IGMPV2_E;
		break;
	}

	pst_attr->ui_imme_leave_en = st_attr.ui_fast_leave_en;
	pst_attr->em_func = HI_OMCI_TAPI_MC_IGMP_SNOOPING;

	hi_omci_tapi_systrace(HI_RET_SUCC, pst_attr->em_version,
			      pst_attr->ui_imme_leave_en, pst_attr->em_func, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_igmp_attr_set
 Description : 配置IGMP报文处理属性
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_omci_tapi_mc_igmp_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_igmp_attr_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_mc_igmp_attr_s *pst_attr)
{
	hi_sml_mc_attr_s st_attr = {0};
	hi_int32 i_ret;

	i_ret = HI_IPC_CALL("hi_sml_mc_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	switch (pst_attr->em_tag_act) {
	case HI_OMCI_TAPI_MC_IGMP_TRANSPARENT:
		st_attr.st_port_attr[em_port].em_igmp_upstream_mode = HI_SML_MC_IGMP_UPSTREAM_TRANSPARENT_E;
		break;

	case HI_OMCI_TAPI_MC_IGMP_TAG_ADD:
		st_attr.st_port_attr[em_port].em_igmp_upstream_mode = HI_SML_MC_IGMP_UPSTREAM_TAG_E;
		break;

	case HI_OMCI_TAPI_MC_IGMP_TAG_MOD_TCI:
	case HI_OMCI_TAPI_MC_IGMP_TAG_MOD_VLAN:
		st_attr.st_port_attr[em_port].em_igmp_upstream_mode = HI_SML_MC_IGMP_UPSTREAM_TRANSLATION_E;
		break;

	default:
		hi_omci_tapi_systrace(HI_RET_INVALID_PARA, pst_attr->em_tag_act, 0, 0, 0);
		return HI_RET_INVALID_PARA;
	}

	st_attr.st_port_attr[em_port].ui_igmp_upstream_vlan = pst_attr->ui_vlan;
	st_attr.st_port_attr[em_port].ui_igmp_upstream_pri = pst_attr->ui_pri;
	st_attr.st_port_attr[em_port].ui_conn = pst_attr->ui_gemportid;

	i_ret = HI_IPC_CALL("hi_sml_mc_attr_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port,
			      st_attr.st_port_attr[em_port].em_igmp_upstream_mode,
			      st_attr.st_port_attr[em_port].ui_igmp_upstream_vlan, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_igmp_attr_get
 Description : 获取IGMP报文配置属性
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_omci_tapi_mc_igmp_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_igmp_attr_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_mc_igmp_attr_s *pst_attr)
{
	hi_sml_mc_attr_s st_attr = {0};
	hi_int32 i_ret;

	i_ret = HI_IPC_CALL("hi_sml_mc_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	switch (st_attr.st_port_attr[em_port].em_igmp_upstream_mode) {
	case HI_SML_MC_IGMP_UPSTREAM_TRANSPARENT_E:
		pst_attr->em_tag_act = HI_OMCI_TAPI_MC_IGMP_TRANSPARENT;
		break;

	case HI_SML_MC_IGMP_UPSTREAM_TAG_E:
		pst_attr->em_tag_act = HI_OMCI_TAPI_MC_IGMP_TAG_ADD;
		break;

	case HI_SML_MC_IGMP_UPSTREAM_TRANSLATION_E:
		pst_attr->em_tag_act = HI_OMCI_TAPI_MC_IGMP_TAG_MOD_VLAN;
		break;

	default:
		hi_omci_tapi_systrace(HI_RET_FAIL, pst_attr->em_tag_act, 0, 0, 0);
		return (hi_int32)HI_RET_FAIL;
	}

	pst_attr->ui_vlan = st_attr.st_port_attr[em_port].ui_igmp_upstream_vlan;
	pst_attr->ui_pri = st_attr.st_port_attr[em_port].ui_igmp_upstream_pri;
	pst_attr->ui_gemportid = st_attr.st_port_attr[em_port].ui_conn;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port,
			      pst_attr->em_tag_act, pst_attr->ui_vlan, pst_attr->ui_pri);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_filter_set
 Description : 配置基于GEMPORT的组播白名单过滤
 Input Parm  : hi_uint32 ui_gemportid,
               hi_omci_tapi_mc_ip_filter_s *pst_filter
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_filter_set(hi_uint32 ui_gemportid, hi_omci_tapi_mc_ip_addr_s *pst_filter)
{
	hi_sml_mc_filter_s st_filter = {0};
	hi_int32 i_ret;

	st_filter.ui_conn = ui_gemportid;
	HI_OS_MEMCPY_S(&st_filter.start_ip, sizeof(st_filter.start_ip), &pst_filter->start_ip, sizeof(st_filter.start_ip));
	HI_OS_MEMCPY_S(&st_filter.end_ip, sizeof(st_filter.end_ip), &pst_filter->end_ip, sizeof(st_filter.end_ip));
	i_ret = HI_IPC_CALL("hi_sml_mc_filter_set", &st_filter);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_filter_del
 Description : 删除组播过滤规则
 Input Parm  : hi_uint32 ui_gemportid,
               hi_omci_tapi_mc_ip_filter_s *pst_filter
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_filter_del(hi_uint32 ui_gemportid, hi_omci_tapi_mc_ip_addr_s *pst_filter)
{
	hi_sml_mc_filter_s st_filter = {0};
	hi_int32 i_ret;

	st_filter.ui_conn = ui_gemportid;
	HI_OS_MEMCPY_S(&st_filter.start_ip, sizeof(st_filter.start_ip), &pst_filter->start_ip, sizeof(st_filter.start_ip));
	HI_OS_MEMCPY_S(&st_filter.end_ip, sizeof(st_filter.end_ip), &pst_filter->end_ip, sizeof(st_filter.end_ip));
	i_ret = HI_IPC_CALL("hi_sml_mc_filter_del", &st_filter);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_table_set
 Description : 配置组播转发表项
 Input Parm  : hi_omci_tapi_mc_table_list_s *pst_list
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_table_set(hi_omci_tapi_mc_table_list_s *pst_list)
{
	hi_sml_mc_vlan_s st_vlan = {0};
	hi_sml_mc_range_s st_range = {0};
	hi_int32 i_ret;

	st_vlan.em_port = (hi_sml_port_e)pst_list->em_port;
	st_vlan.ui_mc_vlan = pst_list->ui_mcvlan;
	st_vlan.ui_user_vlan = pst_list->ui_usrvlan;
	i_ret = HI_IPC_CALL("hi_sml_mc_vlan_add", &st_vlan);
	HI_OMCI_RET_CHECK(i_ret);

	st_range.em_port = (hi_sml_port_e)pst_list->em_port;
	HI_OS_MEMCPY_S(&st_range.start_ip, sizeof(st_range.start_ip), &pst_list->st_ipaddr.start_ip, sizeof(st_range.start_ip));
	HI_OS_MEMCPY_S(&st_range.end_ip, sizeof(st_range.end_ip), &pst_list->st_ipaddr.end_ip, sizeof(st_range.end_ip));
	i_ret = HI_IPC_CALL("hi_sml_mc_range_set", &st_range);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_table_del
 Description : 删除组播转发表项
 Input Parm  : hi_omci_tapi_mc_table_list_s *pst_list
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_table_del(hi_omci_tapi_mc_table_list_s *pst_list)
{
	hi_sml_mc_vlan_s st_vlan = {0};
	hi_sml_mc_range_s st_range = {0};
	hi_int32 i_ret;

	st_vlan.em_port = (hi_sml_port_e)pst_list->em_port;
	st_vlan.ui_mc_vlan = pst_list->ui_mcvlan;
	st_vlan.ui_user_vlan = pst_list->ui_usrvlan;
	i_ret = HI_IPC_CALL("hi_sml_mc_vlan_del", &st_vlan);
	HI_OMCI_RET_CHECK(i_ret);

	st_range.em_port = (hi_sml_port_e)pst_list->em_port;
	HI_OS_MEMCPY_S(&st_range.start_ip, sizeof(st_range.start_ip), &pst_list->st_ipaddr.start_ip, sizeof(st_range.start_ip));
	HI_OS_MEMCPY_S(&st_range.end_ip, sizeof(st_range.end_ip), &pst_list->st_ipaddr.end_ip, sizeof(st_range.end_ip));
	i_ret = HI_IPC_CALL("hi_sml_mc_range_del", &st_range);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_table_set
 Description : 配置组播VLAN
 Input Parm  : hi_omci_tapi_mc_table_list_s *pst_list
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_vlan_set(hi_omci_tapi_mc_vlan_s *pst_vlan)
{
	hi_sml_mc_vlan_s st_vlan = {0};
	hi_int32 i_ret;

	st_vlan.em_port = (hi_sml_port_e)pst_vlan->em_port;
	st_vlan.ui_mc_vlan = pst_vlan->ui_mcvlan;
	st_vlan.ui_user_vlan = pst_vlan->ui_usrvlan;
	i_ret = HI_IPC_CALL("hi_sml_mc_vlan_add", &st_vlan);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_table_del
 Description : 删除组播VLAN
 Input Parm  : hi_omci_tapi_mc_table_list_s *pst_list
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_vlan_del(hi_omci_tapi_mc_vlan_s *pst_vlan)
{
	hi_sml_mc_vlan_s st_vlan = {0};
	hi_int32 i_ret;

	st_vlan.em_port = (hi_sml_port_e)pst_vlan->em_port;
	st_vlan.ui_mc_vlan = pst_vlan->ui_mcvlan;
	i_ret = HI_IPC_CALL("hi_sml_mc_vlan_del", &st_vlan);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_vlan_clr
 Description : 清空端口的组播VLAN
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_vlan_clr(hi_omci_tapi_port_e em_port)
{
	hi_int32 i_ret;
	hi_sml_port_e ui_port;

	ui_port = em_port;

	i_ret = HI_IPC_CALL("hi_sml_mc_vlan_clr", &ui_port);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_group_num_set
 Description : 配置最大组播组数目
 Input Parm  : hi_omci_tapi_port_e em_port, 用户端口
               hi_uint32 ui_group_num   最大组播组数目，0表示不限制
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_group_num_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_group_num)
{
	hi_sml_mc_attr_s st_attr = {0};
	hi_int32 i_ret;

	i_ret = HI_IPC_CALL("hi_sml_mc_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	st_attr.st_port_attr[em_port].ui_max_group = ui_group_num;
	i_ret = HI_IPC_CALL("hi_sml_mc_attr_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_tagact_set
 Description : 配置下行组播数据TAG操作
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_omci_tapi_mc_tagact_e em_tagact,
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_tagact_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_mc_tagact_e em_tagact)
{
	hi_sml_mc_attr_s st_attr = {0};
	hi_int32 i_ret;

	i_ret = HI_IPC_CALL("hi_sml_mc_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	switch (em_tagact) {
	case HI_OMCI_TAPI_MC_TRANSPARENT:
		st_attr.st_port_attr[em_port].em_mcvlan_opt = HI_SML_MCVLAN_TRANSPARENT_E;
		break;

	case HI_OMCI_TAPI_MC_DEL:
		st_attr.st_port_attr[em_port].em_mcvlan_opt = HI_SML_MCVLAN_DEL_E;
		break;

	case HI_OMCI_TAPI_MC_ADD:
	case HI_OMCI_TAPI_MC_ADD_SUBS:
		break;

	case HI_OMCI_TAPI_MC_TRANSLATION_TCI:
	case HI_OMCI_TAPI_MC_TRANSLATION_VLAN:
	case HI_OMCI_TAPI_MC_TRANSLATION_TCI_SUBS:
	case HI_OMCI_TAPI_MC_TRANSLATION_VLAN_SUBS:
		st_attr.st_port_attr[em_port].em_mcvlan_opt = HI_SML_MCVLAN_TRANSLATION_E;
		break;

	default:
		hi_omci_tapi_systrace(HI_RET_INVALID_PARA, em_tagact, 0, 0, 0);
		return HI_RET_INVALID_PARA;
	}

	i_ret = HI_IPC_CALL("hi_sml_mc_attr_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_mc_uprate_set
 Description : 配置组播上行速率限制
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_uint32 ui_rate,
               hi_uint32 ui_carid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_mc_uprate_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_rate, hi_uint32 ui_carid)
{
	hi_int32 i_ret;
	hi_sml_mc_uprate_s st_attr = {0};

	st_attr.em_port = (hi_sml_port_e)em_port;
	st_attr.ui_rate = ui_rate;
	st_attr.ui_carid = ui_carid;

	i_ret = HI_IPC_CALL("hi_sml_mc_uprate_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

