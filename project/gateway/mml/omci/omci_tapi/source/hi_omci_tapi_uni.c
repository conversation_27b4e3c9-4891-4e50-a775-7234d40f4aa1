/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_uni.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-24
  Description: OMCI_TAPI UNI端口配置
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
//#include "hi_omci_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_sml_uni.h"
#include "hi_sml_qos.h"
#include "hi_ipc.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_port_type_get
 Description : 获取端口类型
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_port_type_e *pem_type
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_type_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_port_type_e *pem_type)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	*pem_type = (hi_omci_tapi_port_type_e)st_attr.em_type;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_state_set
 Description : 配置端口使能、禁止
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_enable
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_state_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_enable)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	st_attr.ui_enable = ui_enable;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_enable, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_state_get
 Description : 获取端口使能、禁止状态
 Input Parm  : hi_omci_tapi_port_e em_port,
 Output Parm : hi_uint32 *pui_enable
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_state_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_enable)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	*pui_enable = st_attr.ui_enable;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_ethmode_set
 Description : 设置以太端口工作模式
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_port_ethmode_e em_mode
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_ethmode_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_port_ethmode_e em_mode)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	st_attr.em_mode = (hi_sml_port_ethmode_e)em_mode;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, em_mode, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_ethmode_get
 Description : 获取以太端口当前的工作模式
               如果端口工作模式配置为自适应，此时获取的是协商后的工作模式
               如果端口工作模式配置为强制速率双工，如果对端不能适应，则双方使用最低
               工作模式。因此函数获取的工作模式，不一定与hi_omci_tapi_port_ethmode_set
               配置的一致。
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_port_ethmode_e em_mode
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_ethmode_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_port_ethmode_e *pem_mode)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	*pem_mode = (hi_omci_tapi_port_ethmode_e)st_attr.em_work_state;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_link_get
 Description : 获取端口LINK状态
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : hi_omci_tapi_port_link_type_e *pem_link
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_link_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_port_link_type_e *pem_link)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	*pem_link = (hi_omci_tapi_port_link_type_e)st_attr.em_link;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_loopback_set
 Description : 配置端口环回
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_uint32 ui_enable
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_loopback_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_enable)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	st_attr.ui_loopback_en = ui_enable;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_loopback_get
 Description : 获取端口环回状态
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : hi_uint32 *pui_enable
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_loopback_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_enable)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	*pui_enable = st_attr.ui_loopback_en;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_pause_set
 Description : 配置端口流控
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_uint32 ui_enable
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_pause_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_enable)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	st_attr.ui_pause_en = ui_enable;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_enable, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_pause_get
 Description : 获取端口流控是否使能
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : hi_uint32 *pui_enable
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_pause_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_enable)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	*pui_enable = st_attr.ui_pause_en;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_incar_set
 Description : 配置以太端口流量抑制
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_carid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_incar_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_cir, hi_uint32 ui_cbs, hi_uint32 ui_pir,
				     hi_uint32 ui_pbs)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	st_attr.st_car.ui_cir = ui_cir;
	st_attr.st_car.ui_cbs = ui_cbs;
	st_attr.st_car.ui_pir = ui_pir;
	st_attr.st_car.ui_pbs = ui_pbs;

	i_ret = HI_IPC_CALL("hi_sml_port_attr_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_outcar_set
 Description : 配置以太端口流量整形
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_carid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_outcar_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_cir)
{
	hi_sml_port_attr_s st_attr = {0};
	hi_int32 i_ret;

	st_attr.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_get", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	st_attr.ui_shaping = ui_cir;
	i_ret = HI_IPC_CALL("hi_sml_port_attr_set", &st_attr);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_cir, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_mtu_set
 Description : 配置端口最大允许帧长
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_uint32 ui_mtu
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_mtu_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_mtu)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_mtu, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_port_mtu_set
 Description : 配置端口最大允许帧长
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : hi_uint32 *pui_mtu
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_mtu_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_mtu)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	//return hi_sml_port_mtu_get(em_port, pui_mtu);
	return HI_RET_SUCC;
}

/******************************************************************************
Function    : hi_omci_tapi_port_pptpinstid_get
Description :根据端口类型获取pptp实例编号
Input Parm  : hi_omci_tapi_port_e em_port
Output Parm : hi_uint32 *pui_mtu
Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_pptpinstid_get(hi_omci_tapi_port_e em_port, hi_ushort16 *pus_instid)
{
	hi_int32 i_ret;
	hi_omci_tapi_port_type_e em_porttype;

	i_ret = hi_omci_tapi_port_type_get(HI_OMCI_TAPI_ETH0_E, &em_porttype);
	HI_OMCI_RET_CHECK(i_ret);

	if (em_porttype == HI_OMCI_TAPI_PORT_10_100_BASET_E) {
		*pus_instid = em_port + 1;
	} else if (em_porttype == HI_OMCI_TAPI_PORT_10_100_1000_BASET_E) {
		*pus_instid = em_port + 1 + 0x100;
	} else {
		hi_omci_tapi_systrace(HI_RET_FAIL, em_port, *pus_instid, 0, 0);
		return (hi_int32)HI_RET_FAIL;
	}

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, *pus_instid, 0, 0);
	return HI_RET_SUCC;
}
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

