/* *****************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_voip.c
  Version    : 初稿
  Author     :
  Creation   :
  Description: OMCI_TAPI VOIP端口配置
***************************************************************************** */

/* ****************************************************************************
 * INCLUDE                                    *
 * *************************************************************************** */
#include "hi_omci_tapi_lib.h"
#include "igdCmModulePub.h"
#include "hi_ipc.h"
/* ****************************************************************************
 * LOCAL_DEFINE                               *
 * *************************************************************************** */
static hi_uchar8 guc_tr069wan_status = 0;
static hi_uint32 gui_omci_voip_config_method = HI_OMCI_VOIP_CONFIG_METHOD_ONU_DEFAULT;
/* ****************************************************************************
 * LOCAL_TYPEDEF                              *
 * *************************************************************************** */
/* ****************************************************************************
 * LOCAL_VARIABLE                             *
 * *************************************************************************** */
/* ****************************************************************************
 * LOCAL_FUNCTION                             *
 * *************************************************************************** */
/* ****************************************************************************
 * PUBLIC_FUNCTION                            *
 * *************************************************************************** */

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_udptcp_set
 Description : 配置voip udptcp属性
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_udptcp_set(hi_omci_tapi_voip_udptcp_cfg_s *pst_udptcp)
{
	IgdVoiceAdvancedAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucLineIndex = (hi_uchar8)pst_udptcp->us_lineidx;
	st_data.ulSipLocalPort = pst_udptcp->us_portid;
	st_data.ucSipDscpMark = (hi_uchar8)pst_udptcp->us_tosfield;
	st_data.ulBitmap |= VOICE_ADV_ATTR_MASK_BIT4_SIP_LOCAL_PORT | VOICE_ADV_ATTR_MASK_BIT2_SIP_DSCP_MARK;
	i_ret = igdCmConfSet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ulSipLocalPort, st_data.ucSipDscpMark, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_proxyserver_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_proxyserver_set(hi_omci_tapi_voip_server_s *pst_server)
{
	IgdVoiceSipBasicAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	HI_OS_MEMCPY_S(st_data.aucProxyServer, sizeof(st_data.aucProxyServer), pst_server->uc_server,
		       HI_OMCI_TAPI_VOIP_STR_LEN);
	if (0 != pst_server->us_portid) {
		st_data.ulProxyServerPort = pst_server->us_portid;
		st_data.ulBitmap |= SIP_BASIC_ATTR_MASK_BIT1_PROXY_SERVER_PORT;
	}
	switch (pst_server->us_protocol) {
	case 6:
		st_data.ucProxyServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_TCP;
		break;
	case 17:
		st_data.ucProxyServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_UDP;
		break;
	case 56:
		st_data.ucProxyServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_TLS;
		break;
	case 132:
		st_data.ucProxyServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_SCTP;
		break;
	default:
		st_data.ucProxyServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_UDP;
		break;
	}
	if (0xFFFF == pst_server->us_protocol) {
		st_data.ulBitmap |= SIP_BASIC_ATTR_MASK_BIT0_PROXY_SERVER;
	} else {
		st_data.ulBitmap |= SIP_BASIC_ATTR_MASK_BIT0_PROXY_SERVER | SIP_BASIC_ATTR_MASK_BIT2_PROXY_SERVER_TRANSPORT;
	}
	i_ret = igdCmConfSet(IGD_VOICE_SIP_BASIC_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ulProxyServerPort, st_data.ucProxyServerTransport, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_outbserver_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_outbserver_set(hi_omci_tapi_voip_server_s *pst_server)
{
	IgdVoiceSipBasicAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	HI_OS_MEMCPY_S(st_data.aucOutBoundProxyServer, sizeof(st_data.aucOutBoundProxyServer), pst_server->uc_server,
		       HI_OMCI_TAPI_VOIP_STR_LEN);
	if (0 != pst_server->us_portid) {
		st_data.ulOutBoundProxyServerPort = pst_server->us_portid;
		st_data.ulBitmap |= SIP_BASIC_ATTR_MASK_BIT7_OUTBOUND_PROXY_SERVER_PORT;
	}
	switch (pst_server->us_protocol) {
	case 6:
		st_data.ucOutBoundProxyServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_TCP;
		break;
	case 17:
		st_data.ucOutBoundProxyServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_UDP;
		break;
	case 56:
		st_data.ucOutBoundProxyServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_TLS;
		break;
	case 132:
		st_data.ucOutBoundProxyServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_SCTP;
		break;
	default:
		st_data.ucOutBoundProxyServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_UDP;
		break;
	}
	if (0xFFFF == pst_server->us_protocol) {
		st_data.ulBitmap |= SIP_BASIC_ATTR_MASK_BIT6_OUTBOUND_PROXY_SERVER;
	} else {
		st_data.ulBitmap |=
			SIP_BASIC_ATTR_MASK_BIT6_OUTBOUND_PROXY_SERVER | SIP_BASIC_ATTR_MASK_BIT8_OUTBOUND_PROXY_SERVER_TRANSPORT;
	}
	i_ret = igdCmConfSet(IGD_VOICE_SIP_BASIC_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ucOutBoundProxyServerTransport, st_data.ulOutBoundProxyServerPort, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_regserver_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_regserver_set(hi_omci_tapi_voip_server_s *pst_server)
{
	IgdVoiceSipBasicAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	HI_OS_MEMCPY_S(st_data.aucRegistrarServer, sizeof(st_data.aucRegistrarServer), pst_server->uc_server,
		       HI_OMCI_TAPI_VOIP_STR_LEN);
	if (0 != pst_server->us_portid) {
		st_data.ulRegistrarServerPort = pst_server->us_portid;
		st_data.ulBitmap |= SIP_BASIC_ATTR_MASK_BIT4_REG_SERVER_PORT;
	}
	switch (pst_server->us_protocol) {
	case 6:
		st_data.ucRegistrarServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_TCP;
		break;
	case 17:
		st_data.ucRegistrarServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_UDP;
		break;
	case 56:
		st_data.ucRegistrarServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_TLS;
		break;
	case 132:
		st_data.ucRegistrarServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_SCTP;
		break;
	default:
		st_data.ucRegistrarServerTransport = SIP_SERVER_TRANSPORT_PROTOCOL_UDP;
		break;
	}
	if (0xFFFF == pst_server->us_protocol) {
		st_data.ulBitmap |= SIP_BASIC_ATTR_MASK_BIT3_REG_SERVER;
	} else {
		st_data.ulBitmap |= SIP_BASIC_ATTR_MASK_BIT3_REG_SERVER | SIP_BASIC_ATTR_MASK_BIT5_REG_SERVER_TRANSPORT;
	}
	i_ret = igdCmConfSet(IGD_VOICE_SIP_BASIC_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ulRegistrarServerPort, st_data.ucRegistrarServerTransport, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_agent_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_agent_set(hi_omci_tapi_voip_server_s *pst_server)
{
	IgdVoiceSipBasicAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	HI_OS_MEMCPY_S(st_data.aucUserAgentDomain, sizeof(st_data.aucUserAgentDomain), pst_server->uc_server,
		       HI_OMCI_TAPI_VOIP_STR_LEN);
	st_data.ulUserAgentPort = pst_server->us_portid;
	switch (pst_server->us_portid) {
	case 6:
		st_data.ucUserAgentTransport = SIP_SERVER_TRANSPORT_PROTOCOL_TCP;
		break;
	case 17:
		st_data.ucUserAgentTransport = SIP_SERVER_TRANSPORT_PROTOCOL_UDP;
		break;
	case 56:
		st_data.ucUserAgentTransport = SIP_SERVER_TRANSPORT_PROTOCOL_TLS;
		break;
	case 132:
		st_data.ucUserAgentTransport = SIP_SERVER_TRANSPORT_PROTOCOL_SCTP;
		break;
	default:
		st_data.ucUserAgentTransport = SIP_SERVER_TRANSPORT_PROTOCOL_UDP;
		break;
	}
	st_data.ulBitmap |= SIP_BASIC_ATTR_MASK_BIT18_USER_AGENT_DOMAIN | SIP_BASIC_ATTR_MASK_BIT19_USER_AGENT_PORT |
			    SIP_BASIC_ATTR_MASK_BIT20_USER_AGENT_TRANSPORT;
	i_ret = igdCmConfSet(IGD_VOICE_SIP_BASIC_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ulUserAgentPort, st_data.ucUserAgentTransport, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_regtime_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_regtime_set(hi_omci_tapi_voip_time_s *pst_server)
{
	IgdVoiceAdvancedAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucLineIndex = (hi_uchar8)pst_server->ui_lineidx;
	st_data.ulRegisterExpireTime = pst_server->us_reg_time;
	st_data.ulRegisterRetryInterval = pst_server->us_rereg_time;

	st_data.ulBitmap |= VOICE_ADV_ATTR_MASK_BIT5_REG_EXPIRE_TIME | VOICE_ADV_ATTR_MASK_BIT6_REG_RETRY_INTERVAL;
	i_ret = igdCmConfSet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ulRegisterExpireTime, st_data.ulRegisterRetryInterval, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_regtime_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_regtime_get(hi_omci_tapi_voip_time_s *pst_server)
{
	IgdVoiceAdvancedAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucLineIndex = (hi_uchar8)pst_server->ui_lineidx;
	i_ret = igdCmConfGet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	pst_server->us_reg_time = (hi_ushort16)st_data.ulRegisterExpireTime;
	pst_server->us_rereg_time = (hi_ushort16)st_data.ulRegisterRetryInterval;

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ulRegisterExpireTime, st_data.ulRegisterRetryInterval, 0, 0);
	return HI_RET_SUCC;
}

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_offhooktime_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_offhooktime_set(hi_uint32 *pui_offhook_time)
{
	IgdVoiceGeneralAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucOffHookStartDigitTime = (hi_uchar8) * pui_offhook_time;

	st_data.ulBitmap |= VOICE_GENTRAL_ATTR_MASK_BIT4_OFF_HOOK_TIME;
	i_ret = igdCmConfSet(IGD_VOICE_GENERAL_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ucOffHookStartDigitTime, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_offhooktime_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_offhooktime_get(hi_uint32 *pui_offhook_time)
{
	IgdVoiceGeneralAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	i_ret = igdCmConfGet(IGD_VOICE_GENERAL_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	*pui_offhook_time = st_data.ucOffHookStartDigitTime;

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ucOffHookStartDigitTime, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_sipauth_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_sipauth_set(hi_omci_tapi_voip_auth_s *pst_auth)
{
	IgdVoiceSipUserAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ulBitmap |= SIP_USER_ATTR_MASK_BIT1_AUTH_USERNAME | SIP_USER_ATTR_MASK_BIT2_AUTH_PASSWORD |
			    SIP_USER_ATTR_MASK_BIT3_NUMBER_URL;
	st_data.ucLineNum = (hi_uchar8)pst_auth->ui_lineidx;
	HI_OS_MEMCPY_S(st_data.aucAuthUsername, sizeof(st_data.aucAuthUsername), pst_auth->uc_username,
		       HI_OMCI_TAPI_VOIP_STR_LEN);
	HI_OS_MEMCPY_S(st_data.aucAuthPassword, sizeof(st_data.aucAuthPassword), pst_auth->uc_password,
		       HI_OMCI_TAPI_VOIP_STR_LEN);
	HI_OS_MEMCPY_S(st_data.aucNumberUrl, sizeof(st_data.aucNumberUrl), pst_auth->uc_number, HI_OMCI_TAPI_VOIP_STR_LEN);
	i_ret = igdCmConfSet(IGD_VOICE_SIP_USER_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_sipauth_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_sipauth_get(hi_omci_tapi_voip_auth_s *pst_auth)
{
	IgdVoiceSipUserAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucLineNum = (hi_uchar8)pst_auth->ui_lineidx;
	i_ret = igdCmConfGet(IGD_VOICE_SIP_USER_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMCPY_S(pst_auth->uc_username, sizeof(pst_auth->uc_username), st_data.aucAuthUsername,
		       HI_OMCI_TAPI_VOIP_STR_LEN);
	HI_OS_MEMCPY_S(pst_auth->uc_password, sizeof(pst_auth->uc_password), st_data.aucAuthPassword,
		       HI_OMCI_TAPI_VOIP_STR_LEN);
	HI_OS_MEMCPY_S(pst_auth->uc_number, sizeof(pst_auth->uc_number), st_data.aucNumberUrl, HI_OMCI_TAPI_VOIP_STR_LEN);
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_gain_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_gain_set(hi_omci_tapi_voip_gain_s *pst_gain)
{
	IgdVoiceProcessingAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ulBitmap |= VOICE_PROCESSING_ATTR_MASK_BIT2_TRANSMIT_GAIN | VOICE_PROCESSING_ATTR_MASK_BIT3_RECIEVE_GAIN;
	st_data.ucLineIndex = (hi_uchar8)pst_gain->ui_lineidx;
	st_data.lReceiveGain = pst_gain->ui_rxgain;
	st_data.lTransmitGain = pst_gain->ui_txgain;
	i_ret = igdCmConfSet(IGD_VOICE_PROCESSING_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_gain_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_gain_get(hi_omci_tapi_voip_gain_s *pst_gain)
{
	IgdVoiceProcessingAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucLineIndex = (hi_uchar8)pst_gain->ui_lineidx;
	i_ret = igdCmConfSet(IGD_VOICE_PROCESSING_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	pst_gain->ui_rxgain = st_data.lReceiveGain;
	pst_gain->ui_txgain = st_data.lTransmitGain;
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_hookstatus_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_hookstatus_get(hi_omci_tapi_voip_hookstatus_s *pst_status)
{
	IgdVoiceUserRegInfoTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucLineNum = (hi_uchar8)pst_status->ui_lineidx;
	i_ret = igdCmConfGet(IGD_VOICE_USER_REG_INFO_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	pst_status->ui_status = st_data.ucHookStatus;
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_type_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_type_set(hi_uint32 *pui_type)
{
	IgdVoiceGeneralAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ulBitmap |= VOICE_GENTRAL_ATTR_MASK_BIT0_SERVER_TYPE;
	switch (*pui_type) {
	case 1:
		st_data.ucVoiceServerType = VOICE_SERVER_TYPE_IMS_SIP;
		break;
	case 2:
		st_data.ucVoiceServerType = VOICE_SERVER_TYPE_H248;
		break;
	default:
		st_data.ucVoiceServerType = VOICE_SERVER_TYPE_SOFT_SWITCHING;
		break;
	}
	i_ret = igdCmConfSet(IGD_VOICE_GENERAL_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_type_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_type_get(hi_uint32 *pui_type)
{
	IgdVoiceGeneralAttrConfTab st_data;
	hi_int32 i_ret;
	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));

	i_ret = igdCmConfGet(IGD_VOICE_GENERAL_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	switch (st_data.ucVoiceServerType) {
	case VOICE_SERVER_TYPE_IMS_SIP:
		*pui_type = 1;
		break;
	case VOICE_SERVER_TYPE_H248:
		*pui_type = 2;
		break;
	default:
		*pui_type = 0;
		break;
	}
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_wan_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
static hi_uint32 hi_omci_tapi_voip_get_wan(IgdWanConnectionAttrConfTab *pst_attr, hi_uint32 *ui_listnum,
		hi_uint32 *ui_index, hi_ushort16 wan_flag)
{
	hi_uint32 i_ret = HI_RET_SUCC;
	IgdWanConnectionIndexAttrTab *pst_waninfo = HI_NULL;
	hi_uint32 ui_service_type = 0;

	if (0 == wan_flag) {
		ui_service_type = WAN_CONNECTION_INDEX_SERVICE_VOIP;
	} else {
		ui_service_type = WAN_CONNECTION_INDEX_SERVICE_TR069;
	}

	HI_OS_MEMSET_S(pst_attr, sizeof(*pst_attr), 0, sizeof(*pst_attr));
	igdCmConfGetEntryNum(IGD_WAN_CONNECTION_ATTR_TAB, ui_listnum);
	if (*ui_listnum == 0) {
		return HI_RET_SUCC;
	}

	pst_waninfo = (IgdWanConnectionIndexAttrTab *)malloc(*ui_listnum * sizeof(IgdWanConnectionIndexAttrTab));
	if (HI_NULL == pst_waninfo) {
		return HI_RET_FAIL;
	}
	HI_OS_MEMSET_S(pst_waninfo, *ui_listnum * sizeof(IgdWanConnectionIndexAttrTab), 0,
		       *ui_listnum * sizeof(IgdWanConnectionIndexAttrTab));

	i_ret = igdCmConfGetAllEntry(IGD_WAN_CONNECTION_INDEX_ATTR_TAB, (unsigned char *)pst_waninfo,
				     *ui_listnum * sizeof(IgdWanConnectionIndexAttrTab));
	if (HI_RET_SUCC != i_ret) {
		free(pst_waninfo);
		hi_omci_debug("[INFO]:i_ret =0x%x\n", i_ret);
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	for (*ui_index = 0; *ui_index < *ui_listnum; (*ui_index)++) {
		if (ui_service_type & pst_waninfo[*ui_index].ucServiceList) {
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
			pst_attr->ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;
			pst_attr->ucGlobalIndex = pst_waninfo[*ui_index].ucGlobalIndex;
			pst_attr->ucConDevIndex = pst_waninfo[*ui_index].ucConDevIndex;
			pst_attr->ucXPConIndex = pst_waninfo[*ui_index].ucXPConIndex;
			HI_OS_STRCPY_S((hi_char8 *)pst_attr->aucWanName, sizeof(pst_attr->aucWanName),
				       (hi_char8 *)pst_waninfo[*ui_index].aucWanName);
			i_ret = igdCmConfGet(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)pst_attr, sizeof(*pst_attr));
			if (HI_RET_SUCC != i_ret) {
				free(pst_waninfo);
				hi_omci_debug("[INFO]:i_ret =0x%x\n", i_ret);
				hi_omci_systrace(i_ret, 0, 0, 0, 0);
				return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
			}
			break;
		}
	}
	free(pst_waninfo);
	return HI_RET_SUCC;
}

static hi_void hi_omci_tapi_voip_set_wan_attr(IgdWanConnectionAttrConfTab *pst_attr,
		hi_omci_tapi_voip_iphost_s *pst_iphost)
{
	IgdWanConnectionAttrConfTab st_attr;

	HI_OS_MEMCPY_S(&st_attr, sizeof(st_attr), pst_attr, sizeof(st_attr));

	if (pst_iphost->us_dhcpen) {
		/* 目前不支持ping/traceroute相应，默认打开ip stack */
		if (pst_iphost->us_dhcpmode & 0x01) {
			pst_attr->ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_DHCP;
		} else {
			pst_attr->ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_STATIC;
		}
		if (pst_attr->ucAddressingType != st_attr.ucAddressingType) {
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT8_ADDRESSING_TYPE;
		}
	}

	if (pst_iphost->ui_vlanen) {
		pst_attr->ulVlanIdMark = pst_iphost->ui_vlan;
		pst_attr->ucVlanMode = WAN_CONNECTION_VLAN_OVERRIDE;
		if (pst_attr->ulVlanIdMark != st_attr.ulVlanIdMark || pst_attr->ucVlanMode != st_attr.ucVlanMode) {
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT1_VLAN_MODE | WAN_CONNECTION_ATTR_MASK_BIT5_VLANID_MARK;
		}
	}

	if (pst_iphost->us_8021pen) {
		if (0xf != (hi_uchar8)pst_iphost->us_8021p) {
			pst_attr->uc8021pMark = (hi_uchar8)pst_iphost->us_8021p;
			pst_attr->uc8021pMarkEnable = WAN_8021P_MARK_ENABLE;
		} else {
			pst_attr->uc8021pMark = 0;
			pst_attr->uc8021pMarkEnable = WAN_8021P_MARK_DISABLE;
		}

		if (pst_attr->uc8021pMark != st_attr.uc8021pMark) {
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT2_8021P_MARK;
		}
		if (pst_attr->uc8021pMarkEnable != st_attr.uc8021pMarkEnable) {
			pst_attr->ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_BIT29_8021P_MARK_ENABLE;
		}
	}

	if (pst_iphost->ui_ipaddren) {
		HI_OS_MEMCPY_S(pst_attr->aucExternalIPAddress, sizeof(pst_attr->aucExternalIPAddress), &pst_iphost->ui_extipaddr,
			       CM_IP_ADDR_LEN);
		pst_attr->ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_STATIC;

		if ((hi_os_memcmp(pst_attr->aucExternalIPAddress, st_attr.aucExternalIPAddress,
				  sizeof(pst_attr->aucExternalIPAddress)) != 0)
		    || pst_attr->ucAddressingType != st_attr.ucAddressingType) {
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT25_EXTERNAL_IP_ADDR;
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT8_ADDRESSING_TYPE;
		}
	}

	if (pst_iphost->ui_ipmasken) {
		HI_OS_MEMCPY_S(pst_attr->aucSubnetMask, sizeof(pst_attr->aucSubnetMask), &pst_iphost->ui_ipmask, CM_IP_ADDR_LEN);
		pst_attr->ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_STATIC;

		if ((hi_os_memcmp(pst_attr->aucSubnetMask, st_attr.aucSubnetMask, sizeof(pst_attr->aucSubnetMask)) != 0)
		    || pst_attr->ucAddressingType != st_attr.ucAddressingType) {
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT23_SUBNET_MASK;
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT8_ADDRESSING_TYPE;
		}
	}

	if (pst_iphost->ui_gatewayen) {
		HI_OS_MEMCPY_S(pst_attr->aucDefaultGateway, sizeof(pst_attr->aucDefaultGateway), &pst_iphost->ui_defagateway,
			       CM_IP_ADDR_LEN);
		pst_attr->ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_STATIC;

		if ((hi_os_memcmp(pst_attr->aucDefaultGateway, st_attr.aucDefaultGateway, sizeof(pst_attr->aucDefaultGateway)) != 0)
		    || pst_attr->ucAddressingType != st_attr.ucAddressingType) {
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT24_DEFAULT_GATEWAY;
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT8_ADDRESSING_TYPE;
		}
	}

	if (pst_iphost->ui_dnsen) {
		HI_OS_MEMCPY_S(pst_attr->aucDNSServers, sizeof(pst_attr->aucDNSServers), pst_iphost->auc_dnsserv,
			       WAN_CONNECTION_DNS_SERVERS_LEN);
		pst_attr->ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_STATIC;

		if ((hi_os_memcmp(pst_attr->aucDNSServers, st_attr.aucDNSServers, sizeof(pst_attr->aucDNSServers)) != 0)
		    || pst_attr->ucAddressingType != st_attr.ucAddressingType) {
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT30_DNS_SERVERS;
			pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_BIT8_ADDRESSING_TYPE;
		}
	}
	return;
}

static hi_uint32 hi_omci_tapi_voip_add_wan(IgdWanConnectionAttrConfTab *pst_attr,
		hi_omci_tapi_voip_iphost_s *pst_iphost)
{
	hi_uint32 i_ret = HI_RET_SUCC;

	pst_attr->ucAddressingType = WAN_CONNECTION_ADDRESSING_TYPE_STATIC;
	hi_omci_tapi_voip_set_wan_attr(pst_attr, pst_iphost);

	pst_attr->ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
	pst_attr->ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;
	pst_attr->ucOptEnable = 1;
	pst_attr->ucVlanMode = WAN_CONNECTION_VLAN_OVERRIDE;
	pst_attr->ulVlanIdMark = pst_iphost->ui_vlan;
	pst_attr->ucEnable = WAN_CONNECTION_XP_CONNECTION_ENABLE;
	pst_attr->ucConnectionType = WAN_CONNECTION_TYPE_IP_ROUTED;
	if (0 == pst_iphost->us_flag) {
		pst_attr->ucServiceList = WAN_CONNECTION_SERVICE_VOIP;
	} else {
		pst_attr->ucServiceList = WAN_CONNECTION_SERVICE_TR069;
	}
	pst_attr->ulMaxMTUSize = 1492;
	pst_attr->ucDNSEnabled = WAN_CONNECTION_DNS_ENABLE;
	pst_attr->ucIPMode = WAN_CONNECTION_IP_MODE_V4;

	i_ret = igdCmConfAdd(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)pst_attr, sizeof(*pst_attr));
	if (HI_RET_SUCC != i_ret) {
		hi_omci_debug("[INFO]:i_ret =0x%x\n", i_ret);
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}
	return HI_RET_SUCC;
}

static hi_uint32 hi_omci_tapi_voip_set_wan(IgdWanConnectionAttrConfTab *pst_attr,
		hi_omci_tapi_voip_iphost_s *pst_iphost)
{
	hi_uint32 i_ret = HI_RET_SUCC;

	pst_attr->ulBitmap = 0;
	pst_attr->ulBitmap1 = 0;
	hi_omci_tapi_voip_set_wan_attr(pst_attr, pst_iphost);

	if ((0 != pst_attr->ulBitmap) || (0 != pst_attr->ulBitmap1)) {
		i_ret = igdCmConfSet(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)pst_attr, sizeof(*pst_attr));
		if (HI_RET_SUCC != i_ret) {
			hi_omci_debug("[INFO]:i_ret =0x%x\n", i_ret);
			hi_omci_systrace(i_ret, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
		}
	}
	return HI_RET_SUCC;
}

hi_uint32 hi_omci_tapi_voip_wan_set(hi_omci_tapi_voip_iphost_s *pst_iphost)
{
	IgdWanConnectionAttrConfTab st_attr;
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_listnum = 0, ui_index = 0;

	i_ret = hi_omci_tapi_voip_get_wan(&st_attr, &ui_listnum, &ui_index, pst_iphost->us_flag);
	if (i_ret != HI_RET_SUCC) {
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

	if ((ui_index == ui_listnum) || (0 == ui_listnum)) {
		/* voip wan不存在，创建wan。*/
		if (pst_iphost->ui_vlanen) {
			i_ret = hi_omci_tapi_voip_add_wan(&st_attr, pst_iphost);
			if (i_ret != HI_RET_SUCC) {
				hi_omci_systrace(i_ret, 0, 0, 0, 0);
				return i_ret;
			}
		} else {
			return HI_RET_SUCC;
		}
	} else {
		i_ret = hi_omci_tapi_voip_set_wan(&st_attr, pst_iphost);
		if (i_ret != HI_RET_SUCC) {
			hi_omci_systrace(i_ret, 0, 0, 0, 0);
			return i_ret;
		}
	}
	if (0 != pst_iphost->us_flag) {
		guc_tr069wan_status = 1;
	}
	return HI_RET_SUCC;
}

hi_uint32 __omci_chartoip(hi_char8 *pszIp)
{
	hi_uint32 dwIp = 0;

	if (pszIp == NULL || strlen(pszIp) == 0) {
		return dwIp;
	}

	dwIp = inet_addr(pszIp);

	return (dwIp);
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_wan_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_uint32 hi_omci_tapi_voip_wan_get(hi_omci_tapi_voip_iphost_s *pst_iphost)
{
	IgdWanConnectionAttrConfTab st_attr;
	IgdWanConnectionStateInfoTab st_state;
	IgdWanConnectionIndexAttrTab *pst_waninfo = NULL;
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_listnum = 0, ui_index;
	hi_uint32 ui_service_type = 0;

	if (0 == pst_iphost->us_flag) {
		ui_service_type = WAN_CONNECTION_INDEX_SERVICE_VOIP;
	} else {
		ui_service_type = WAN_CONNECTION_INDEX_SERVICE_TR069;
	}

	HI_OS_MEMSET_S(&st_attr, sizeof(st_attr), 0, sizeof(st_attr));
	HI_OS_MEMSET_S(&st_state, sizeof(st_state), 0, sizeof(st_state));
	igdCmConfGetEntryNum(IGD_WAN_CONNECTION_ATTR_TAB, &ui_listnum);
	pst_waninfo = hi_os_malloc(ui_listnum * sizeof(IgdWanConnectionIndexAttrTab));
	if (pst_waninfo == NULL) {
		return HI_RET_MALLOC_FAIL;
	}
	HI_OS_MEMSET_S(pst_waninfo, ui_listnum * sizeof(IgdWanConnectionIndexAttrTab),
		       0, ui_listnum * sizeof(IgdWanConnectionIndexAttrTab));
	i_ret = igdCmConfGetAllEntry(IGD_WAN_CONNECTION_INDEX_ATTR_TAB, (unsigned char *)pst_waninfo,
				     ui_listnum * sizeof(IgdWanConnectionIndexAttrTab));
	if (HI_RET_SUCC != i_ret) {
		hi_os_free(pst_waninfo);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}
	for (ui_index = 0; ui_index < ui_listnum; ui_index++) {
		if (ui_service_type & pst_waninfo[ui_index].ucServiceList) {
			st_attr.ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
			st_attr.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;
			st_attr.ucGlobalIndex = pst_waninfo[ui_index].ucGlobalIndex;
			st_attr.ucConDevIndex = pst_waninfo[ui_index].ucConDevIndex;
			st_attr.ucXPConIndex = pst_waninfo[ui_index].ucXPConIndex;
			HI_OS_STRCPY_S((hi_char8 *)st_attr.aucWanName, sizeof(st_attr.aucWanName),
				       (hi_char8 *)pst_waninfo[ui_index].aucWanName);
			i_ret = igdCmConfGet(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)&st_attr, sizeof(st_attr));
			if (HI_RET_SUCC != i_ret) {
				hi_os_free(pst_waninfo);
				return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
			}
			break;
		}
	}

	if (ui_index == ui_listnum) {
		hi_os_free(pst_waninfo);
		return HI_RET_FAIL;
	}

	if (st_attr.ucAddressingType == WAN_CONNECTION_ADDRESSING_TYPE_DHCP) {
		pst_iphost->us_dhcpmode |= 0x1;
	}

	st_state.ulBitmap |= WAN_CONNECTION_STATE_MASK_ALL;
	st_state.ucGlobalIndex = pst_waninfo[ui_index].ucGlobalIndex;
	HI_OS_STRCPY_S((hi_char8 *)st_state.aucWanName, sizeof(st_state.aucWanName),
		       (hi_char8 *)pst_waninfo[ui_index].aucWanName);
	i_ret = igdCmConfGet(IGD_WAN_CONNECTION_STATE_TAB, (unsigned char *)&st_state, sizeof(st_state));
	if (HI_RET_SUCC != i_ret) {
		hi_os_free(pst_waninfo);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}
	HI_OS_MEMCPY_S(pst_iphost->uc_mac, sizeof(pst_iphost->uc_mac), st_attr.aucMACAddress, CM_MAC_ADDR_LEN);
	pst_iphost->ui_ipmask = __omci_chartoip((hi_char8 *)st_state.aucIPv4Mask);
	pst_iphost->ui_defagateway = __omci_chartoip((hi_char8 *)st_state.aucIPv4Gateway);
	pst_iphost->ui_extipaddr = __omci_chartoip((hi_char8 *)st_state.aucIPv4Address);
	pst_iphost->ui_pridns = __omci_chartoip((hi_char8 *)st_state.aucIPv4DnsPrimary);
	pst_iphost->ui_secdns = __omci_chartoip((hi_char8 *)st_state.aucIPv4DnsBackup);
	HI_OS_MEMCPY_S(pst_iphost->auc_dnsserv, sizeof(pst_iphost->auc_dnsserv), st_attr.aucDNSServers,
		       WAN_CONNECTION_DNS_SERVERS_LEN);
	hi_os_free(pst_waninfo);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_wan_del
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_uint32 hi_omci_tapi_voip_wan_del(hi_uint32 ui_flag)
{
	IgdWanConnectionAttrConfTab st_attr;
	IgdWanConnectionIndexAttrTab *pst_waninfo = NULL;
	hi_uint32 i_ret = HI_RET_SUCC;
	hi_uint32 ui_listnum = 0, ui_index;
	hi_uint32 ui_service_type = 0;
	if (0 == ui_flag) {
		ui_service_type = WAN_CONNECTION_INDEX_SERVICE_VOIP;
	} else {
		ui_service_type = WAN_CONNECTION_INDEX_SERVICE_TR069;
		guc_tr069wan_status = 0;
	}

	HI_OS_MEMSET_S(&st_attr, sizeof(st_attr), 0, sizeof(st_attr));
	igdCmConfGetEntryNum(IGD_WAN_CONNECTION_ATTR_TAB, &ui_listnum);
	pst_waninfo = hi_os_malloc(ui_listnum * sizeof(IgdWanConnectionIndexAttrTab));
	if (pst_waninfo == NULL) {
		return HI_RET_MALLOC_FAIL;
	}
	HI_OS_MEMSET_S(pst_waninfo, ui_listnum * sizeof(IgdWanConnectionIndexAttrTab),
		       0, ui_listnum * sizeof(IgdWanConnectionIndexAttrTab));
	i_ret = igdCmConfGetAllEntry(IGD_WAN_CONNECTION_INDEX_ATTR_TAB, (unsigned char *)pst_waninfo,
				     ui_listnum * sizeof(IgdWanConnectionIndexAttrTab));
	if (HI_RET_SUCC != i_ret) {
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", i_ret, __LINE__);
		hi_omci_systrace(i_ret, 0, 0, 0, 0);
		hi_os_free(pst_waninfo);
		return HI_RET_SUCC;
	}
	for (ui_index = 0; ui_index < ui_listnum; ui_index++) {
		if (ui_service_type & pst_waninfo[ui_index].ucServiceList) {
			st_attr.ulBitmap |= WAN_CONNECTION_ATTR_MASK_ALL;
			st_attr.ulBitmap1 |= WAN_CONNECTION_ATTR_MASK1_ALL;

			st_attr.ucGlobalIndex = pst_waninfo[ui_index].ucGlobalIndex;
			st_attr.ucConDevIndex = pst_waninfo[ui_index].ucConDevIndex;
			st_attr.ucXPConIndex = pst_waninfo[ui_index].ucXPConIndex;
			HI_OS_STRCPY_S((hi_char8 *)st_attr.aucWanName, sizeof(st_attr.aucWanName),
				       (hi_char8 *)pst_waninfo[ui_index].aucWanName);
			i_ret = igdCmConfGet(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)&st_attr, sizeof(st_attr));
			if (HI_RET_SUCC != i_ret) {
				hi_os_free(pst_waninfo);
				return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
			}
			break;
		}
	}

	if (ui_index == ui_listnum) {
		hi_os_free(pst_waninfo);
		return HI_RET_SUCC;
	}
	i_ret = igdCmConfDel(IGD_WAN_CONNECTION_ATTR_TAB, (unsigned char *)&st_attr, sizeof(st_attr));
	if (HI_RET_SUCC != i_ret) {
		hi_os_free(pst_waninfo);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}
	hi_os_free(pst_waninfo);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_rtp_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_rtp_set(hi_omci_tapi_voip_rtp_s *pst_rtp)
{
	IgdVoiceRTPServiceAttrConfTab st_data;
	IgdVoiceAdvancedAttrConfTab st_attr;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	HI_OS_MEMSET_S(&st_attr, sizeof(st_attr), 0, sizeof(st_attr));

	st_data.ucDscpMark = (hi_uchar8)pst_rtp->ui_dscp;
	st_data.ulBitmap |= RTP_SERVICE_ATTR_MASK_BIT1_DSCP_MARK;
	i_ret = igdCmConfSet(IGD_VOICE_RTP_SERVICE_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	st_attr.ucLineIndex = (hi_uchar8)pst_rtp->ui_lineidx;
	st_attr.ulRtpPortStart = pst_rtp->ui_portstart;
	st_attr.ulRtpPortEnd = pst_rtp->ui_portend;
	st_attr.ucRtpDscpMark = (hi_uchar8)pst_rtp->ui_dscp;
	st_attr.ulBitmap |= VOICE_ADV_ATTR_MASK_BIT3_RTP_DSCP_MARK | VOICE_ADV_ATTR_MASK_BIT8_RTP_PORT_START |
			    VOICE_ADV_ATTR_MASK_BIT9_RTP_PORT_END;
	i_ret = igdCmConfSet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&st_attr, sizeof(st_attr));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ucDscpMark, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_rtp_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_silence_set(hi_omci_tapi_voip_silence_s *pst_silence)
{
	IgdVoiceAdvancedAttrConfTab st_attr;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_attr, sizeof(st_attr), 0, sizeof(st_attr));
	st_attr.ucLineIndex = (hi_uchar8)pst_silence->ui_lineidx;
	st_attr.ucVadCngEnable = (hi_uchar8)pst_silence->ui_silence_en;
	st_attr.ulBitmap |= VOICE_ADV_ATTR_MASK_BIT14_VAD_CNG_ENABLE;
	i_ret = igdCmConfSet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&st_attr, sizeof(st_attr));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_attr.ucVadCngEnable, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_rtp_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_rtp_get(hi_omci_tapi_voip_rtp_s *pst_rtp)
{
	IgdVoiceRTPServiceAttrConfTab st_data;
	IgdVoiceAdvancedAttrConfTab st_attr;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	HI_OS_MEMSET_S(&st_attr, sizeof(st_attr), 0, sizeof(st_attr));
	i_ret = igdCmConfGet(IGD_VOICE_RTP_SERVICE_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	pst_rtp->ui_dscp = st_data.ucDscpMark;

	st_attr.ucLineIndex = (hi_uchar8)pst_rtp->ui_lineidx;
	i_ret = igdCmConfGet(IGD_VOICE_ADVANCED_ATTR_TAB, (unsigned char *)&st_attr, sizeof(st_attr));
	HI_OMCI_RET_CHECK(i_ret);
	pst_rtp->ui_portstart = st_attr.ulRtpPortStart;
	pst_rtp->ui_portend = st_attr.ulRtpPortEnd;

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ucDscpMark, st_attr.ulRtpPortStart, st_attr.ulRtpPortEnd, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_fax_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_faxmode_set(hi_uint32 *pui_mode)
{
	IgdVoiceFaxAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucFaxMode = (hi_uchar8) * pui_mode;

	st_data.ulBitmap |= VOICE_FAX_ATTR_MASK_BIT0_FAX_MODE;
	i_ret = igdCmConfSet(IGD_VOICE_FAX_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ucFaxMode, 0, 0, 0);
	return HI_RET_SUCC;
}

static hi_int32 CheckCodecListPriority(IgdVoiceLineCodecAttrConfTab *inputVoiceInfo)
{
	IgdVoiceCapbilityInfoTab capbilityInfo = {0};
	IgdVoiceLineCodecAttrConfTab stPara, stPara1 = {0};
	IgdVoiceLineCodecAttrConfTab *inPara = inputVoiceInfo;

	hi_int32 totalNum = 0;
	hi_int32 ret;
	// 先找当前EntryID的那条记录
	stPara1.ucEntryID = inPara->ucEntryID;
	ret = igdCmConfGet(IGD_VOICE_LINE_CODEC_ATTR_TAB, (unsigned char *)&stPara1, sizeof(stPara));
	HI_OMCI_RET_CHECK(ret);

	if (inPara->ucCodec == stPara1.ucCodec) {
		inPara->ulBitmap = VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_ALL;
		ret = igdCmConfSet(IGD_VOICE_LINE_CODEC_ATTR_TAB, (unsigned char *)inPara, sizeof(*inPara));
		return ret;
	}

	// 获取优先级编码配置数量
	ret = igdCmConfGet(IGD_VOICE_CAPBILITY_TAB, (unsigned char *)&capbilityInfo, sizeof(capbilityInfo));
	totalNum = capbilityInfo.ucSupportLineCodecNum;
	if (totalNum <= 0) {
		hi_omci_debug("totalNum=%d.\n", totalNum);
		return HI_RET_FAIL;
	}
	hi_omci_debug("totalNum=%d\n", totalNum);

	for (int i = 1; i <= totalNum; i++) {
		if (inPara->ucEntryID == i) {
			continue;
		}

		stPara.ucEntryID = i;
		ret = igdCmConfGet(IGD_VOICE_LINE_CODEC_ATTR_TAB, (unsigned char *)&stPara, sizeof(stPara));
		hi_omci_debug("Priority=%hhu,EntryID=%hhu,Codec=%hhu,PacketizationPeriod=%s\n", \
			      stPara.ucPriority, stPara.ucEntryID, stPara.ucCodec, stPara.aucPacketizationPeriod);
		if (stPara.ucCodec == inPara->ucCodec) {
			hi_omci_debug("ucPriority is repeat.\n");
			// 处理重复逻辑,重复的那条记录修改为当前记录以前的值。
			(void)memcpy_s(&stPara, sizeof(stPara), &stPara1, sizeof(stPara1));
			stPara.ucEntryID = i;
			stPara.ucPriority = i;
			stPara.ulBitmap = VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_ALL;
			ret = igdCmConfSet(IGD_VOICE_LINE_CODEC_ATTR_TAB, (unsigned char *)&stPara, sizeof(stPara));
			HI_OMCI_RET_CHECK(ret);
			break;
		}
	}
	// 按照要求修改对应EntryID的优先级
	inPara->ulBitmap = VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_ALL;
	ret = igdCmConfSet(IGD_VOICE_LINE_CODEC_ATTR_TAB, (unsigned char *)inPara, sizeof(*inPara));
	HI_OMCI_RET_CHECK(ret);
	return HI_RET_SUCC;
}

static hi_void hi_omci_tapi_voip_codec_priority_reorder(void)
{
	IgdVoiceCapbilityInfoTab capbilityInfo = {0};
	IgdVoiceLineCodecAttrConfTab stPara;
	IgdVoiceLineCodecAttrConfTab *pstPara = &stPara;
	hi_int32 totalNum = 0;

	// 获取优先级编码配置数量
	igdCmConfGet(IGD_VOICE_CAPBILITY_TAB, (unsigned char *)&capbilityInfo, sizeof(capbilityInfo));
	totalNum = capbilityInfo.ucSupportLineCodecNum;
	if (totalNum <= 0) {
		hi_omci_debug("totalNum=%d.\n", totalNum);
		return;
	}
	hi_omci_debug("totalNum=%d\n", totalNum);

	for (int i = 1; i <= totalNum; i++) {
		pstPara->ucEntryID = i;
		igdCmConfGet(IGD_VOICE_LINE_CODEC_ATTR_TAB, (unsigned char *)pstPara, sizeof(stPara));
		stPara.ucPriority = i;
		stPara.ulBitmap = VOICE_LINE_CODEC_CAPBILITY_ATTR_MASK_BIT1_PRIORITY;
		igdCmConfSet(IGD_VOICE_LINE_CODEC_ATTR_TAB, (unsigned char *)pstPara, sizeof(stPara));
	}
}

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_codec_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_codec_set(hi_omci_tapi_voip_codec_s *pst_codec)
{
	IgdVoiceLineCodecAttrConfTab st_data;
	hi_int32 i_ret;

	hi_omci_tapi_voip_codec_priority_reorder();

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucPriority = pst_codec->uc_pri;
	st_data.ucEntryID = pst_codec->uc_entryid;
	st_data.ucEnable = 0;
	switch (pst_codec->uc_codec) {
	case 0:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_G711M;
		st_data.ucEnable = 1;
		break;
	case 3:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_GSM_FR;
		break;
	case 4:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_G723_1;
		break;
	case 5:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_DVI4_8000;
		break;
	case 6:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_DVI4_16000;
		break;
	case 7:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_LPC;
		break;
	case 8:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_G711A;
		st_data.ucEnable = 1;
		break;
	case 9:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_G722;
		st_data.ucEnable = 1;
		break;
	case 10:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_L16_2C;
		break;
	case 11:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_L16_1C;
		break;
	case 12:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_QCELP;
		break;
	case 13:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_CN;
		break;
	case 14:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_MPA;
		break;
	case 15:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_G728;
		break;
	case 16:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_DVI4_11025;
		break;
	case 17:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_DVI4_22050;
		break;
	case 18:
		st_data.ucCodec = VOICE_LINE_CODEC_TYPE_G729;
		st_data.ucEnable = 1;
		break;
	default:
		break;
	}
	HI_OS_SPRINTF_S((hi_char8 *)st_data.aucPacketizationPeriod, sizeof(st_data.aucPacketizationPeriod), "%u",
			pst_codec->uc_packperi);
	if (0 == pst_codec->uc_packperi) {
		st_data.ucEnable = 0;
	}

	i_ret = CheckCodecListPriority(&st_data);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ucEntryID, st_data.ucCodec, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_codec_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_codec_get(hi_omci_tapi_voip_codec_s *pst_codec)
{
	IgdVoiceLineCodecAttrConfTab st_data;
	hi_int32 i_ret;
	hi_uint32 ui_str = 0;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucEntryID = pst_codec->uc_entryid;
	i_ret = igdCmConfGet(IGD_VOICE_LINE_CODEC_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	pst_codec->uc_enable = st_data.ucEnable;
	pst_codec->uc_pri = st_data.ucPriority;
	switch (st_data.ucCodec) {
	case VOICE_LINE_CODEC_TYPE_G711M:
		pst_codec->uc_codec = 0;
		break;
	case VOICE_LINE_CODEC_TYPE_GSM_FR:
		pst_codec->uc_codec = 3;
		break;
	case VOICE_LINE_CODEC_TYPE_G723_1:
		pst_codec->uc_codec = 4;
		break;
	case VOICE_LINE_CODEC_TYPE_DVI4_8000:
		pst_codec->uc_codec = 5;
		break;
	case VOICE_LINE_CODEC_TYPE_DVI4_16000:
		pst_codec->uc_codec = 6;
		break;
	case VOICE_LINE_CODEC_TYPE_LPC:
		pst_codec->uc_codec = 7;
		break;
	case VOICE_LINE_CODEC_TYPE_G711A:
		pst_codec->uc_codec = 8;
		break;
	case VOICE_LINE_CODEC_TYPE_G722:
		pst_codec->uc_codec = 9;
		break;
	case VOICE_LINE_CODEC_TYPE_L16_2C:
		pst_codec->uc_codec = 10;
		break;
	case VOICE_LINE_CODEC_TYPE_L16_1C:
		pst_codec->uc_codec = 11;
		break;
	case VOICE_LINE_CODEC_TYPE_QCELP:
		pst_codec->uc_codec = 12;
		break;
	case VOICE_LINE_CODEC_TYPE_CN:
		pst_codec->uc_codec = 13;
		break;
	case VOICE_LINE_CODEC_TYPE_MPA:
		pst_codec->uc_codec = 14;
		break;
	case VOICE_LINE_CODEC_TYPE_G728:
		pst_codec->uc_codec = 15;
		break;
	case VOICE_LINE_CODEC_TYPE_DVI4_11025:
		pst_codec->uc_codec = 16;
		break;
	case VOICE_LINE_CODEC_TYPE_DVI4_22050:
		pst_codec->uc_codec = 17;
		break;
	case VOICE_LINE_CODEC_TYPE_G729:
		pst_codec->uc_codec = 18;
		break;
	default:
		break;
	}
	hi_os_atoi((hi_char8 *)st_data.aucPacketizationPeriod, (hi_int32 *)(&ui_str));
	pst_codec->uc_packperi = (hi_uchar8)ui_str;
	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ucEntryID, st_data.ucCodec, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_digitmap_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_digitmap_set(hi_omci_tapi_voip_digitmap_s *pst_map)
{
	// IgdVoiceGeneralAttrConfTab st_data;
	// hi_int32 i_ret;
#if 0
	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	HI_OS_MEMCPY_S(st_data.aucDigitMap, sizeof(st_data.aucDigitMap), pst_map->auc_digitmap, HI_OMCI_TAPI_VOIP_DIGITMAP_LEN);

	st_data.ulBitmap |= VOICE_GENTRAL_ATTR_MASK_BIT12_DIGIT_MAP;
	i_ret = igdCmConfSet(IGD_VOICE_GENERAL_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
#endif
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_line_status_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_line_status_get(hi_omci_tapi_line_status_s *pst_status)
{
	IgdVoiceLineRtpStatsAttrTab st_data;
	IgdVoiceUserRegInfoTab st_attr;
	IgdVoiceLineCallStatsTab st_linecall;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	HI_OS_MEMSET_S(&st_attr, sizeof(st_attr), 0, sizeof(st_attr));
	HI_OS_MEMSET_S(&st_linecall, sizeof(st_linecall), 0, sizeof(st_linecall));

	i_ret =
		igdCmConfGet(IGD_VOICE_LINE_RTP_STATS_ATTR_TAB, (unsigned char *)&st_data, sizeof(IgdVoiceLineRtpStatsAttrTab));
	HI_OMCI_RET_CHECK(i_ret);

	switch (st_data.ulCodec) {
	case VOICE_LINE_CODEC_G711M:
		pst_status->ui_codec_used = 0;
		break;
	case VOICE_LINE_CODEC_G723_53K:
	case VOICE_LINE_CODEC_G723_63K:
		pst_status->ui_codec_used = 4;
		break;
	case VOICE_LINE_CODEC_G711A:
		pst_status->ui_codec_used = 8;
		break;
	case VOICE_LINE_CODEC_G722:
		pst_status->ui_codec_used = 9;
		break;
	case VOICE_LINE_CODEC_G729:
		pst_status->ui_codec_used = 18;
		break;
	default:
		pst_status->ui_codec_used = 8;
		break;
	}

	st_attr.ucLineNum = (hi_uchar8)pst_status->ui_lineidx;
	i_ret = igdCmConfGet(IGD_VOICE_USER_REG_INFO_TAB, (unsigned char *)&st_attr, sizeof(st_attr));
	HI_OMCI_RET_CHECK(i_ret);

	if (USER_REG_SATUS_REG_FAILED != st_attr.ucRegStatus) {
		switch (st_attr.ucRegStatus) {
		case USER_REG_SATUS_CONFIG_NOT_FINISHED:
			pst_status->ui_server_status = HI_OMCI_TAPI_VOIP_PORT_NOT_CONFIGURED;
			break;
		case USER_REG_SATUS_REGISTERING:
			pst_status->ui_server_status = HI_OMCI_TAPI_VOIP_INITIAL;
			break;
		case USER_REG_SATUS_REG_SUCCESS:
			pst_status->ui_server_status = HI_OMCI_TAPI_VOIP_REGISTERED;
			break;
		case USER_REG_SATUS_CONFIG_DISABLE:
			pst_status->ui_server_status = HI_OMCI_TAPI_VOIP_CONFIG_DISABLE_BY_SWITCH;
			break;
		default:
			pst_status->ui_server_status = HI_OMCI_TAPI_VOIP_INITIAL;
			break;
		}
	} else {
		switch (st_attr.ucRegErrInfo) {
		case USER_REG_ERR_REG_AUTH_FAILED:
			pst_status->ui_server_status = HI_OMCI_TAPI_VOIP_FAIL_REGIST_AUTHENTICATION;
			break;
		case USER_REG_ERR_REG_TIMEOUT:
			pst_status->ui_server_status = HI_OMCI_TAPI_VOIP_FAIL_REGIST_TIMEOUT;
			break;
		case USER_REG_ERR_SERVER_ERROR_RESPONSE:
			pst_status->ui_server_status = HI_OMCI_TAPI_VOIP_FAIL_REGIST_SERVER_CODE;
			break;
		default:
			pst_status->ui_server_status = HI_OMCI_TAPI_VOIP_FAIL_REGIST_AUTHENTICATION;
			break;
		}
	}

	if (st_attr.ucHookStatus == HOOK_STATUS_USING) {
		pst_status->ui_server_status = HI_OMCI_TAPI_VOIP_INSESSION;
	}

	i_ret = igdCmConfGet(IGD_VOICE_LINE_CALL_STATS_ATTR_TAB, (unsigned char *)&st_linecall, sizeof(st_linecall));
	HI_OMCI_RET_CHECK(i_ret);

	pst_status->ui_session_type = st_linecall.ulSessionType;
	pst_status->ui_line_state = st_linecall.ulVoipLineState;

	hi_omci_tapi_systrace(HI_RET_SUCC, pst_status->ui_server_status, st_data.ulCodec, pst_status->ui_session_type, 0);

	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_adminstate_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_adminstate_set(hi_omci_tapi_voip_adminstate_s *pst_state)
{
	IgdVoiceSipUserAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucLineNum = (hi_uchar8)pst_state->ui_lineidx;
	switch (pst_state->ui_state) {
	case HI_OMCI_TAPI_VOIP_ADMIN_UNLOCK:
		st_data.ucUserEnable = SIP_USER_ENABLE;
		break;
	case HI_OMCI_TAPI_VOIP_ADMIN_LOCK:
	case HI_OMCI_TAPI_VOIP_ADMIN_SHUTDOWN:
		st_data.ucUserEnable = SIP_USER_DISABLE;
		break;
	default:
		st_data.ucUserEnable = SIP_USER_ENABLE;
		break;
	}
	st_data.ulBitmap |= SIP_USER_ATTR_MASK_BIT0_USER_ENABLE;
	i_ret = igdCmConfSet(IGD_VOICE_SIP_USER_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ucUserEnable, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_adminstate_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_adminstate_get(hi_omci_tapi_voip_adminstate_s *pst_state)
{
	IgdVoiceSipUserAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	st_data.ucLineNum = (hi_uchar8)pst_state->ui_lineidx;
	i_ret = igdCmConfGet(IGD_VOICE_SIP_USER_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	switch (st_data.ucUserEnable) {
	case SIP_USER_ENABLE:
		pst_state->ui_state = 0;
		break;
	case SIP_USER_DISABLE:
		pst_state->ui_state = 1;
		break;
	default:
		pst_state->ui_state = 0;
		break;
	}

	hi_omci_tapi_systrace(HI_RET_SUCC, pst_state->ui_state, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_conf_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_conf_set(hi_uint32 *pui_en)
{
	IgdOmciAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	if (1 == *pui_en) {
		st_data.ulOmciVoip = 1;
	} else {
		st_data.ulOmciVoip = 0;
	}
	st_data.ulBitmap |= DEV_OMCI_ATTR_MASK_BIT0_VOIP;
	i_ret = igdCmConfSet(IGD_OMCI_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ulOmciVoip, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_conf_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_conf_get(hi_uint32 *pui_en)
{
	IgdOmciAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));

	i_ret = igdCmConfGet(IGD_OMCI_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	*pui_en = st_data.ulOmciVoip;

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ulOmciVoip, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_tr069_conf_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_tr069_conf_set(hi_uint32 *pui_en)
{
	IgdOmciAttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	if (1 == *pui_en) {
		st_data.ulOmciTr069 = 1;
	} else {
		st_data.ulOmciTr069 = 0;
	}
	st_data.ulBitmap |= DEV_OMCI_ATTR_MASK_BIT1_TR069;
	i_ret = igdCmConfSet(IGD_OMCI_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, st_data.ulOmciTr069, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_tr069_status_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_tr069_status_get(hi_uint32 *pui_en)
{
	*pui_en = (hi_uint32)guc_tr069wan_status;
	hi_omci_tapi_systrace(HI_RET_SUCC, guc_tr069wan_status, 0, 0, 0);
	return HI_RET_SUCC;
}
/* *****************************************************************************
 Function    : hi_omci_tapi_voip_rtppm_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_rtppm_get(hi_omci_tapi_voip_rtppm_s *pst_state)
{
	IgdVoiceLineRtpStatsAttrTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	i_ret = igdCmConfGet(IGD_VOICE_LINE_RTP_STATS_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	pst_state->ui_rtp_errors = st_data.ulRtpErrors;
	pst_state->ui_packet_loss = st_data.ulPacketLoss;
	pst_state->ui_maxi_jitter = st_data.ulMaxiJitter;
	pst_state->ui_maxi_betw_rtcp = st_data.ulMaxiBetwRtcp;
	pst_state->ui_buffer_under = st_data.ulBufferUnder;
	pst_state->ui_buffer_over = st_data.ulBufferOver;

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_sip_agentpm_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_sip_agentpm_get(hi_omci_tapi_voip_sip_agentpm_s *pst_state)
{
	IgdVoiceSipUsageStatsTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	i_ret = igdCmConfGet(IGD_VOICE_SIP_USAGE_STATS_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	pst_state->ui_initiated_cnt = st_data.initiated_cnt;
	pst_state->ui_rx_invite_cnt = st_data.recv_invite_cnt;
	pst_state->ui_rx_reinvite_cnt = st_data.recv_re_invite_cnt;
	pst_state->ui_rx_noinvite_cnt = st_data.recv_noinvite_cnt;
	pst_state->ui_rx_renoinvite_cnt = st_data.recv_re_noinvite_cnt;
	pst_state->ui_rx_response_cnt = st_data.recv_response_cnt;
	pst_state->ui_rx_reresponse_cnt = st_data.recv_re_response_cnt;
	pst_state->ui_tx_invite_cnt = st_data.send_invite_cnt;
	pst_state->ui_tx_reinvite_cnt = st_data.send_re_invite_cnt;
	pst_state->ui_tx_noinvite_cnt = st_data.send_noinvite_cnt;
	pst_state->ui_tx_renoinvite_cnt = st_data.send_re_noinvite_cnt;
	pst_state->ui_tx_response_cnt = st_data.send_response_cnt;
	pst_state->ui_tx_reresponse_cnt = st_data.send_re_response_cnt;

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_sip_callinitpm_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_sip_callinitpm_get(hi_omci_tapi_voip_sip_callinitpm_s *pst_state)
{
	IgdVoiceSipUsageStatsTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	i_ret = igdCmConfGet(IGD_VOICE_SIP_USAGE_STATS_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	pst_state->ui_connect_fail_cnt = st_data.connect_fail_cnt;
	pst_state->ui_check_fail_cnt = st_data.check_fail_cnt;
	pst_state->ui_timeout_cnt = st_data.timeout_cnt;
	pst_state->ui_recv_err_code_cnt = st_data.recv_err_code_cnt;
	pst_state->ui_authentication_fail_cnt = st_data.authentication_fail_cnt;

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_callctrlpm_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_callctrlpm_get(hi_omci_tapi_voip_callctrlpm_s *pst_state)
{
	IgdVoiceLineRtpStatsAttrTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	i_ret = igdCmConfGet(IGD_VOICE_LINE_RTP_STATS_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);
	pst_state->ui_callsetup_fail = st_data.ulCallSetupFailures;
	pst_state->ui_callsetup_timer = st_data.ulLongestDurationSingleCallSetup;
	pst_state->ui_callterm_fail = st_data.ulTerminatedCalls;
	pst_state->ui_analogport_releases = st_data.ulTimesPortRelease;
	pst_state->ui_analogport_offhooktimer = st_data.ulLongestPeriodOffHookDetected;

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_emergency_get
 Description : 获取语音紧急状态
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_emergency_get(hi_uchar8 *status)
{
	IgdVoiceGeneralAttrConfTab data;
	hi_int32 ret;
	HI_OS_MEMSET_S(&data, sizeof(data), 0, sizeof(data));

	ret = igdCmConfGet(IGD_VOICE_GENERAL_ATTR_TAB, (unsigned char *)&data, sizeof(data));
	HI_OMCI_RET_CHECK(ret);
	*status = 0;
	if (data.ulEmergencyMapEnable == VOICE_EMERGENCY_MAP_ENABLE) {
		*status = 1;
	}
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_cnfig_method_set
 Description : 设置语音配置方式，默认TR069
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_cnfig_method_set(hi_uint32 ui_config_method)
{
	gui_omci_voip_config_method = ui_config_method;
	return HI_RET_SUCC;
}

/* *****************************************************************************
 Function    : hi_omci_tapi_voip_cnfig_method_get
 Description : 获取语音配置方式
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
***************************************************************************** */
hi_int32 hi_omci_tapi_voip_cnfig_method_get(void)
{
	return gui_omci_voip_config_method;
}

/* ****************************************************************************
 * INIT_EXIT                                  *
 * *************************************************************************** */
