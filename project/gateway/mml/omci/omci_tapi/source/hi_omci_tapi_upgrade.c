/******************************************************************************

                  Copyright (C), 2012-2013, HSA

 ******************************************************************************
  Filename   : hi_omci_tapi_upgrade.c
  Version    : 初稿
  Author     : chenshibin 00184653
  Creation   : 2015-1-27
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
//#include "hi_omci_lib.h"
#include "hi_ipc.h"
#include "hi_omci_tapi_lib.h"
#include "hi_upgrade.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
static hi_uint32 g_omci_tapi_section;
/******************************************************************************
 Function    : hi_omci_tapi_upgrade_start
 Description :
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_tapi_upgrade_start(hi_uint32 ui_section, hi_upgrade_sharding_header_s *pst_sharding_header)
{
	hi_upgrade_sharding_block_start_s st_start;
	hi_uint32 ui_flag;

	HI_OMCI_RET_CHECK(HI_IPC_CALL("hi_upgrade_flag_get", &ui_flag));
	if (HI_UPGRADE_FLAG_BACKUP_E == ui_flag) {
		st_start.em_flag = HI_UPGRADE_FLAG_MAIN_E;
	} else {
		st_start.em_flag = HI_UPGRADE_FLAG_BACKUP_E;
	}

	st_start.ui_section = ui_section;
	st_start.ui_len = pst_sharding_header->ui_pkg_total_len;
	HI_OS_MEMCPY_S(&st_start.st_sharding_header, sizeof(st_start.st_sharding_header), pst_sharding_header,
		       sizeof(hi_upgrade_sharding_header_s));
	g_omci_tapi_section = ui_section;

	HI_OMCI_RET_CHECK(HI_IPC_CALL("hi_upgrade_sharding_start", &st_start));

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_upgrade_stop
 Description :
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_tapi_upgrade_stop(hi_uint32 ui_krnlcrc, hi_uint32 ui_rtfscrc, hi_uint32 ui_pkgcrc)
{
	//hi_upgrade_block_stop_s st_stop;
	hi_uint32 ui_flag;
#if 0
	st_stop.ui_section = g_omci_tapi_section;
	st_stop.st_crc.ui_krnl_crc = ui_krnlcrc;
	st_stop.st_crc.ui_rtfs_crc = ui_rtfscrc;
	st_stop.st_crc.ui_pkg_crc = ui_pkgcrc;
	HI_OMCI_RET_CHECK(hi_upgrade_block_stop(&st_stop));
#endif
	HI_OMCI_RET_CHECK(HI_IPC_CALL("hi_upgrade_flag_get", &ui_flag));
	if (HI_UPGRADE_FLAG_BACKUP_E == ui_flag) {
		ui_flag = HI_UPGRADE_FLAG_MAIN_E;
	} else {
		ui_flag = HI_UPGRADE_FLAG_BACKUP_E;
	}

	HI_OMCI_RET_CHECK(HI_IPC_CALL("hi_upgrade_flag_set", &ui_flag));

	hi_os_printf("\r\n%d section will loading!!\r\n\n", ui_flag);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_upgrade_write
 Description :
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_tapi_upgrade_write(hi_uchar8 *puc_buff, hi_uint32 ui_len)
{
	hi_upgrade_block_write_s st_write;

	st_write.ui_section = g_omci_tapi_section;
	st_write.ui_len = ui_len;

	HI_OS_MEMSET_S(st_write.pc_buffer, sizeof(st_write.pc_buffer), 0, sizeof(st_write.pc_buffer));
	HI_OS_MEMCPY_S(st_write.pc_buffer, sizeof(st_write.pc_buffer), puc_buff, ui_len);
	HI_OMCI_RET_CHECK(HI_IPC_CALL("hi_upgrade_image_sharding_write", &st_write));

	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
