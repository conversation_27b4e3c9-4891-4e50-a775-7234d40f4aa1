/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_tapi_br.c
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : 桥配置接口
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
//#include "hi_omci_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_sml_br.h"
#include "hi_ipc.h"
extern hi_omci_tapi_sysinfo_s gst_sysinfo;
/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/*****************************************************************************
  Function     : hi_omci_tapi_br_learn_set
  Description  : 配置端口MAC学习使能
  Input Param  : hi_omci_tapi_port_e em_port
                 hi_uint32 ui_enable
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_br_learn_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_enable)
{
	hi_sml_br_learn_s st_lrn = {0};
	hi_int32 i_ret;

	st_lrn.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_br_learn_get", &st_lrn);
	HI_OMCI_RET_CHECK(i_ret);

	st_lrn.ui_enable = ui_enable;
	i_ret = HI_IPC_CALL("hi_sml_br_learn_set", &st_lrn);
	//HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_enable, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_get_br_learn
  Description  : 获取端口MAC学习使能
  Input Param  : hi_omci_tapi_port_e em_port
                 hi_uint32 ui_enable
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_br_learn_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_enable)
{
	hi_sml_br_learn_s st_lrn = {0};
	hi_int32 i_ret;

	st_lrn.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_br_learn_get", &st_lrn);
	HI_OMCI_RET_CHECK(i_ret);

	*pui_enable = st_lrn.ui_enable;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}


/*****************************************************************************
  Function     : hi_omci_tapi_br_num_set
  Description  : 配置端口MAC最大数目
  Input Param  : hi_omci_tapi_port_e port 端口号
                 hi_uint32 num 最大学习数目，0表示禁止学习数目限制
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_br_num_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_num)
{
	hi_sml_br_learn_s st_lrn = {0};
	hi_int32 i_ret;
	if (HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E == gst_sysinfo.ui_product_mode) {
		hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}
	st_lrn.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_br_learn_get", &st_lrn);
	HI_OMCI_RET_CHECK(i_ret);

	st_lrn.ui_num = ui_num;
	i_ret = HI_IPC_CALL("hi_sml_br_learn_set", &st_lrn);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_num, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_br_age_set
  Description  : 配置端口MAC地址老化时间
  Input Param  : hi_uint32 ui_time，0标识禁止老化
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_br_age_set(hi_uint32 ui_time)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, ui_time, 0, 0, 0);
	return HI_IPC_CALL("hi_sml_br_age_set", &ui_time);
}

/*****************************************************************************
  Function     : hi_omci_tapi_get_br_age
  Description  : 获取端口MAC地址老化时间
  Input Param  : 无
  Output Param ：hi_uint32 ui_time
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_br_age_get(hi_uint32 *ui_time)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_IPC_CALL("hi_sml_br_age_get", ui_time);
}

/*****************************************************************************
  Function     : hi_omci_tapi_br_bridging_set
  Description  : 设置UNI端口桥接使能
  Input Param  : 无
  Output Param ：hi_uint32 ui_enable
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_br_enable(hi_uint32 *ui_enable)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_IPC_CALL("hi_sml_br_port_isolate", ui_enable);
}

/*****************************************************************************
  Function     : hi_omci_tapi_set_br_filter
  Description  : 设置MAC地址过滤
                 如果配置的是白名单，且当前MAC学习禁止，需要添加静态MAC转发表项
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_omci_tapi_br_filter_s *pst_filter
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_br_filter_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_br_filter_s *pst_filter)
{
	hi_sml_br_filter_s st_filter = {0};
	hi_int32 i_ret;
	hi_void *pv_data = &st_filter;

	st_filter.em_port = (hi_sml_port_e)em_port;
	HI_OS_MEMCPY_S(((hi_uchar8 *)pv_data + 4), sizeof(hi_sml_br_filter_s) - 4, pst_filter,
		       sizeof(hi_omci_tapi_br_filter_s));
	i_ret = HI_IPC_CALL("hi_sml_br_filter_set", &st_filter);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_get_br_filter
  Description  : 基于端口获取MAC地址过滤
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_omci_tapi_br_filter_s *pst_filter
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_br_filter_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_br_filter_s *pst_filter)
{
	hi_sml_br_filter_s st_filter = {0};
	hi_int32 i_ret;
	hi_void *pv_data = &st_filter;

	st_filter.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_br_filter_get", &st_filter);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMCPY_S(pst_filter, sizeof(hi_omci_tapi_br_filter_s), ((hi_uchar8 *)pv_data + 4),
		       sizeof(hi_omci_tapi_br_filter_s));

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_del_br_filter
  Description  : 删除MAC地址过滤
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_omci_tapi_br_filter_s *pst_filter
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_br_filter_del(hi_omci_tapi_port_e em_port, hi_omci_tapi_br_filter_s *pst_filter)
{
	hi_sml_br_filter_s st_filter = {0};
	hi_int32 i_ret;
	hi_void *pv_data = &st_filter;

	st_filter.em_port = (hi_sml_port_e)em_port;
	HI_OS_MEMCPY_S(((hi_uchar8 *)pv_data + 4), sizeof(hi_sml_br_filter_s) - 4, pst_filter,
		       sizeof(hi_omci_tapi_br_filter_s));
	i_ret = HI_IPC_CALL("hi_sml_br_filter_del", &st_filter);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_br_table_get
 Description : 获取bridge转发表项
               根据ui_offset查找ui_size个转发表项
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_offset,
               hi_uint32 *pui_size
 Output Parm : hi_uint32 *pui_size
               hi_omci_tapi_br_table_s *pst_table,
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_br_table_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_size,
				   hi_omci_tapi_br_table_s *pst_table)
{
	hi_sml_br_table_s st_table = {0};
	hi_uint32 ui_idx = 0, ui_index = 0;
	hi_uint32 ui_min_num = 0;
	hi_int32 i_ret;

	st_table.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_br_table_get", &st_table);
	if (HI_RET_SUCC != i_ret) {
		*pui_size = 0;
		hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	if (0 < st_table.ui_cnt) {
		ui_min_num = (st_table.ui_cnt < *pui_size) ? st_table.ui_cnt : *pui_size;
		for (ui_idx = 0; ui_idx < ui_min_num; ui_idx ++) {
			HI_OS_MEMCPY_S(pst_table->aui_mac, sizeof(pst_table->aui_mac), st_table.st_item[ui_idx].aui_mac,
				       sizeof(st_table.st_item[ui_idx].aui_mac));
			pst_table->ui_age = st_table.st_item[ui_idx].ui_age;
			pst_table++;
			ui_index++;
		}

		*pui_size = ui_index;
	} else {
		*pui_size = 0;
		hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, *pui_size, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
