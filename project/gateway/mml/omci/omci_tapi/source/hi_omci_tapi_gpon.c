/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_tapi_gpon.c
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : ANI配置接口
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
//#include "hi_omci_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_sml_gpon.h"
#include "hi_sml_qos.h"
#include "hi_ipc.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
//#define HI_OMCI_TAPI_TCONT_INVALID 255

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_omcc_create
 Description : 建立OMCC通道
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_omcc_create()
{
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_omcc_destory
 Description : 拆除OMCC通道
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_omcc_destory()
{
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_gponmode_get
 Description : 获取GPON模式 1G/10G
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_gponmode_get(hi_omci_tapi_gpon_mode_e *pem_mode)
{
	hi_int32 i_ret;
	hi_sml_gpon_mode_e em_mode;

	i_ret = HI_IPC_CALL("hi_sml_gpon_mode_get", &em_mode);
	HI_OMCI_RET_CHECK(i_ret);

	*pem_mode = (hi_omci_tapi_gpon_mode_e)em_mode;

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_tcont_num_get
 Description : 获取系统支持的最大TCONT数目
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tcont_num_get(hi_uint32 *pui_num)
{
	hi_int32 i_ret;
	i_ret = HI_IPC_CALL("hi_sml_gpon_tcont_num_get", pui_num);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_tcont_set
 Description : 配置TCONT
 Input Parm  : hi_uint32 ui_tcontid,
               hi_uint32 ui_allocid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tcont_set(hi_uint32 ui_tcontid, hi_uint32 ui_allocid)
{
	hi_sml_tcont_s st_tcont;
	hi_int32 i_ret;

	st_tcont.ui_tcontid = ui_tcontid;
	st_tcont.ui_allocid = ui_allocid;
	i_ret = HI_IPC_CALL("hi_sml_gpon_tcont_set", &st_tcont);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, ui_tcontid, ui_allocid, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_tcont_get
 Description : 获取TCONT
 Input Parm  : hi_uint32 ui_tcontid,
 Output Parm : hi_uint32 *pui_allocid
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tcont_get(hi_uint32 ui_tcontid, hi_uint32 *pui_allocid)
{
	hi_sml_tcont_s st_tcont;
	hi_uint32 ui_onuid;
	hi_int32 i_ret;

	if (0 == ui_tcontid) {
		//i_ret = hi_ploam_onuid_get(&ui_onuid);
		i_ret = HI_IPC_CALL("hi_sml_gpon_onuid_get", &ui_onuid);
		HI_OMCI_RET_CHECK(i_ret);
		*pui_allocid = ui_onuid;
	} else {
		st_tcont.ui_tcontid = ui_tcontid;
		i_ret = HI_IPC_CALL("hi_sml_gpon_tcont_get", &st_tcont);
		HI_OMCI_RET_CHECK(i_ret);

		*pui_allocid = st_tcont.ui_allocid;
	}

	hi_omci_tapi_systrace(HI_RET_SUCC, ui_tcontid, *pui_allocid, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_tcont_del
 Description : 删除TCONT
 Input Parm  : hi_uint32 ui_tcontid,
               hi_uint32 ui_allocid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tcont_del(hi_uint32 ui_tcontid)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, ui_tcontid, 0, 0, 0);
	return HI_IPC_CALL("hi_sml_gpon_tcont_del", &ui_tcontid);
}

/******************************************************************************
 Function    : hi_omci_tapi_tcont_policy_set
 Description : 配置TCONT调度策略
 Input Parm  : hi_uint32 ui_tcontid,
               hi_omci_tapi_tcont_policy_type_e em_type
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tcont_policy_set(hi_uint32 ui_tcontid, hi_omci_tapi_tcont_policy_type_e em_type)
{
	hi_sml_tcont_policy_s st_policy = {0};
	hi_int32 i_ret;

	st_policy.ui_tcontid = ui_tcontid;
	st_policy.st_policy.em_type = (hi_sml_policy_type_e)em_type;
	i_ret = HI_IPC_CALL("hi_sml_gpon_tcont_policy_set", &st_policy);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, ui_tcontid, em_type, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_dba_mode_get
 Description : 获取DBA上报模式
 Input Parm  : 无
 Output Parm : hi_omci_tapi_dba_rpt_mode_e *pem_mode
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_dba_mode_get(hi_omci_tapi_dba_rpt_mode_e *pem_mode)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_IPC_CALL("hi_sml_gpon_dba_mode_get", (hi_sml_dba_rpt_mode_e *)pem_mode);
}

/******************************************************************************
 Function    : hi_omci_tapi_gem_block_set
 Description : 配置GEM帧切片长度
 Input Parm  : hi_uint32 ui_len
 Output Parm : 无
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_gem_block_set(hi_uint32 ui_len)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, ui_len, 0, 0, 0);
	return HI_IPC_CALL("hi_sml_gpon_gemblock_set", &ui_len);
}

/******************************************************************************
 Function    : hi_omci_tapi_gem_block_get
 Description : 获取GEM帧切片长度
 Input Parm  : 无
 Output Parm : hi_uint32 *pui_len
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_gem_block_get(hi_uint32 *pui_len)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_IPC_CALL("hi_sml_gpon_gemblock_get", pui_len);
}

/******************************************************************************
 Function    : hi_omci_tapi_gemport_num_get
 Description : 获取GEMPORT数目
 Input Parm  : 无
 Output Parm : hi_uint32 *pui_num
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_gemport_num_get(hi_uint32 *pui_num)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_IPC_CALL("hi_sml_gpon_gemport_num_get", pui_num);
}

/******************************************************************************
 Function    : hi_omci_tapi_gemport_set
 Description : 配置GEMPORT
 Input Parm  : hi_uint32 ui_gemport,
               hi_omci_tapi_gemport_para_s *pst_para  GEMPORT参数
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_gemport_set(hi_uint32 ui_gemportid, hi_omci_tapi_gemport_para_s *pst_para)
{
	hi_sml_gemport_para_s st_para = {0};
	hi_int32 i_ret;
	hi_void *pv_data = &st_para;

	st_para.ui_gemportid = ui_gemportid;
	HI_OS_MEMCPY_S(((hi_uchar8 *)pv_data + 4), sizeof(hi_sml_gemport_para_s) - 4, pst_para,
		       sizeof(hi_omci_tapi_gemport_para_s));
	i_ret = HI_IPC_CALL("hi_sml_gpon_gemport_set", &st_para);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, ui_gemportid, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_gemport_set
 Description : 获取GEMPORT参数
 Input Parm  : hi_uint32 ui_gemport,
 Output Parm : hi_omci_tapi_gemport_para_s *pst_para  GEMPORT参数
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_gemport_get(hi_uint32 ui_gemportid, hi_omci_tapi_gemport_para_s *pst_para)
{
	hi_sml_gemport_para_s st_para = {0};
	hi_int32 i_ret;
	hi_void *pv_data = &st_para;

	st_para.ui_gemportid = ui_gemportid;
	i_ret = HI_IPC_CALL("hi_sml_gpon_gemport_get", &st_para);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMCPY_S(pst_para, sizeof(hi_omci_tapi_gemport_para_s), ((hi_uchar8 *)pv_data + 4),
		       sizeof(hi_omci_tapi_gemport_para_s));

	hi_omci_tapi_systrace(HI_RET_SUCC, ui_gemportid, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_gemport_set
 Description : 删除GEMPORT
 Input Parm  : hi_uint32 ui_gemport,
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_gemport_del(hi_uint32 ui_gemportid)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, ui_gemportid, 0, 0, 0);
	return HI_IPC_CALL("hi_sml_gpon_gemport_del", &ui_gemportid);
}

/******************************************************************************
 Function    : hi_omci_tapi_gemport_loopback_set
 Description : 配置GEMPORT环回
 Input Parm  : hi_uint32 ui_gemportid, GEMPORT ID
               hi_uint32 ui_enable     使能/禁止标识
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_gemport_loopback_set(hi_uint32 ui_gemportid, hi_uint32 ui_tcont, hi_uint32 ui_enable)
{
	hi_sml_gemport_loopback_s st_loopback = {0};

	st_loopback.ui_gemportid = ui_gemportid;
	st_loopback.ui_tcont = ui_tcont;
	st_loopback.ui_enable = ui_enable;
	return HI_IPC_CALL("hi_sml_gpon_gemport_loopback_set", &st_loopback);
}

/*****************************************************************************
  Function     : hi_omci_tapi_map_set
  Description  : 配置映射规则
  Input Param  : hi_omci_tapi_map_s *pst_mapping
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_tapi_map_set(hi_omci_tapi_map_s *pst_map)
{
	hi_sml_map_s st_map = {0};
	hi_int32 i_ret;

	hi_omci_tapi_sysinfo_s st_info;
	hi_omci_tapi_mc_igmp_attr_s st_igmp_attr;
	HI_OS_MEMSET_S(&st_info, sizeof(st_info), 0, sizeof(st_info));
	HI_OS_MEMSET_S(&st_igmp_attr, sizeof(st_igmp_attr), 0, sizeof(st_igmp_attr));
	hi_omci_tapi_systrace(HI_RET_SUCC, pst_map->ui_mask,
			      pst_map->ui_vlan, pst_map->ui_pri, pst_map->em_port);

	HI_OS_MEMSET_S(&st_map, sizeof(st_map), 0, sizeof(st_map));
	st_map.ui_mask = pst_map->ui_mask;
	st_map.ui_vlan = pst_map->ui_vlan;
	st_map.ui_pri = pst_map->ui_pri;
	st_map.em_port = (hi_sml_port_e)pst_map->em_port;
	st_map.ui_carid = HI_SML_CARID_INVALID;
	st_map.ui_gemport = pst_map->ui_gemport;
	st_map.ui_tcont = pst_map->ui_tcont;
	hi_omci_debug("[INFO]:ui_mask=0x%x em_port=0x%x\n", pst_map->ui_mask, pst_map->em_port);
	hi_omci_debug("[INFO]:ui_tcont=0x%x ui_gemport=0x%x\n", pst_map->ui_tcont, pst_map->ui_gemport);
	i_ret = HI_IPC_CALL("hi_sml_gpon_map_set", &st_map);
	HI_OMCI_RET_CHECK(i_ret);
	/* sfu模式，如果vlan和pri与组播业务相等，则刷新组播配置 */
	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);
	if ((HI_OMCI_ME_ONU_CAPABILITY_TYPE_SFU_E == st_info.ui_product_mode) && \
	    ((st_map.ui_mask & HI_OMCI_TAPI_MAP_IGR) == HI_OMCI_TAPI_MAP_IGR)) {
		i_ret = hi_omci_tapi_mc_igmp_attr_get(pst_map->em_port, &st_igmp_attr);
		/* 查询失败也返回成功 */
		if (i_ret) {
			return HI_RET_SUCC;
		}

		if ((st_igmp_attr.ui_vlan == pst_map->ui_vlan) && (st_igmp_attr.ui_pri == pst_map->ui_pri)) {
			st_igmp_attr.ui_gemportid = pst_map->ui_gemport;

			i_ret = hi_omci_tapi_mc_igmp_attr_set(pst_map->em_port, &st_igmp_attr);
			/* 配置失败也返回成功 */
			if (i_ret) {
				return HI_RET_SUCC;
			}
		}
	}
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_map_get
  Description  : 获取映射规则
  Input Param  : hi_omci_tapi_map_s *pst_mapping
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_tapi_map_get(hi_omci_tapi_map_s *pst_map)
{
	hi_sml_map_s st_map = {0};
	hi_int32 i_ret;

	hi_omci_tapi_systrace(HI_RET_SUCC, pst_map->ui_mask,
			      pst_map->ui_vlan, pst_map->ui_pri, pst_map->em_port);

	HI_OS_MEMSET_S(&st_map, sizeof(st_map), 0, sizeof(st_map));
	st_map.ui_mask = pst_map->ui_mask;
	st_map.ui_vlan = pst_map->ui_vlan;
	st_map.ui_pri = pst_map->ui_pri;
	st_map.em_port = (hi_sml_port_e)pst_map->em_port;
	st_map.ui_carid = HI_SML_CARID_INVALID;
	i_ret = HI_IPC_CALL("hi_sml_gpon_map_get", &st_map);
	if (i_ret != HI_RET_SUCC)
		return i_ret;

	pst_map->ui_gemport = st_map.ui_gemport;
	pst_map->ui_tcont = st_map.ui_tcont;

	hi_omci_tapi_systrace(HI_RET_SUCC, pst_map->ui_gemport, pst_map->ui_tcont, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_map_del
  Description  : 删除映射规则
  Input Param  : hi_omci_tapi_map_s *pst_mapping
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_tapi_map_del(hi_omci_tapi_map_s *pst_map)
{
	hi_sml_map_s st_map = {0};

	hi_omci_tapi_systrace(HI_RET_SUCC, pst_map->ui_mask,
			      pst_map->ui_vlan, pst_map->ui_pri, pst_map->em_port);

	HI_OS_MEMSET_S(&st_map, sizeof(st_map), 0, sizeof(st_map));
	st_map.ui_mask = pst_map->ui_mask;
	st_map.ui_vlan = pst_map->ui_vlan;
	st_map.ui_pri = pst_map->ui_pri;
	st_map.em_port = (hi_sml_port_e)pst_map->em_port;
	st_map.ui_carid = HI_SML_CARID_INVALID;
	st_map.ui_tcont = pst_map->ui_tcont;
	st_map.ui_gemport = pst_map->ui_gemport;
	hi_omci_debug("[INFO]:em_port=0x%x ui_mask=0x%x\n", pst_map->em_port, pst_map->ui_mask);
	return HI_IPC_CALL("hi_sml_gpon_map_del", &st_map);
}

/*****************************************************************************
  Function     : hi_omci_tapi_msk_set
  Description  : 配置MSK
  Input Param  : hi_uchar8 *puc_msk
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_tapi_msk_set(hi_uchar8 *puc_msk)
{
	hi_sml_xgpon_msk_s st_msk;

	HI_OS_MEMCPY_S(st_msk.auc_key, sizeof(st_msk.auc_key), puc_msk, sizeof(st_msk.auc_key));
	hi_omci_debug("st_msk.auc_key: %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x %x\n", st_msk.auc_key[0]
		      , st_msk.auc_key[1], st_msk.auc_key[2], st_msk.auc_key[3], st_msk.auc_key[4]
		      , st_msk.auc_key[5], st_msk.auc_key[6], st_msk.auc_key[7], st_msk.auc_key[8]
		      , st_msk.auc_key[9], st_msk.auc_key[10], st_msk.auc_key[11], st_msk.auc_key[12]
		      , st_msk.auc_key[13], st_msk.auc_key[14], st_msk.auc_key[15]);

	return HI_IPC_CALL("hi_sml_xgpon_msk_set", (hi_sml_xgpon_msk_s *)&st_msk);
}

/*****************************************************************************
  Function     : hi_omci_tapi_aes_cmac
  Description  : AES_CMAC_128
  Input Param  : hi_omci_tapi_aes_cmac_s *pst_cmac
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_tapi_aes_cmac(hi_omci_tapi_aes_cmac_s *pst_cmac)
{
	hi_int32 i_ret;
	hi_sml_xgpon_aes_cmac_s st_aes_cmac = {0};
	HI_OS_MEMCPY_S(&st_aes_cmac, sizeof(hi_sml_xgpon_aes_cmac_s), pst_cmac, sizeof(hi_omci_tapi_aes_cmac_s));
	i_ret = HI_IPC_CALL("hi_sml_xgpon_aes_cmac_set", &st_aes_cmac);
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMCPY_S(pst_cmac->auc_result, sizeof(st_aes_cmac.auc_result), st_aes_cmac.auc_result,
		       sizeof(st_aes_cmac.auc_result));
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_mckey_set
  Description  : 配置多播数据流密钥
  Input Param  : hi_uint32 ui_index,
                 hi_uint32 ui_len,
                 hi_uchar8 *puc_mckey
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_tapi_mckey_set(hi_uint32 ui_index, hi_uint32 ui_len, hi_uchar8 *puc_mckey)
{
	hi_sml_xgpon_mckey_s st_mckey = {0};

	st_mckey.ui_keyindex = ui_index;
	st_mckey.ui_keylen = ui_len;
	HI_OS_MEMCPY_S(st_mckey.auc_key, sizeof(st_mckey.auc_key), puc_mckey, ui_len);

	return HI_IPC_CALL("hi_sml_xgpon_mckey_set", &st_mckey);
}
/*****************************************************************************
  Function     : hi_omci_tapi_mckey_set
  Description  : 删除多播数据流密钥
  Input Param  : hi_uint32 ui_index,
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_tapi_mckey_del(hi_uint32 ui_index)
{
	hi_sml_xgpon_mckey_s st_mckey = {0};
	st_mckey.ui_keyindex = ui_index;
	return HI_IPC_CALL("hi_sml_xgpon_mckey_del", &st_mckey);
}

/*****************************************************************************
 函 数 名  : hi_omci_tapi_time_set
 功能描述  : 解析omci报文supercount和s,ns
 输入参数  : hi_uchar8 * puc_time
 输出参数  : 无
 返 回 值  : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_time_set(hi_uchar8 *puc_time)
{
	hi_sml_gpon_time_s st_time;

	st_time.ui_sfc   = (hi_uint32)(((hi_uint32)(puc_time[0])) << 24)
			   + (hi_uint32)(((hi_uint32)(puc_time[1])) << 16)
			   + (hi_uint32)(((hi_uint32)(puc_time[2])) << 8)
			   + (hi_uint32)(puc_time[3]);
	st_time.ui_s_h16 = (hi_uint32)(((hi_uint32)(puc_time[4])) << 8)
			   + (hi_uint32)(puc_time[5]);
	st_time.ui_s_l32 = (hi_uint32)(((hi_uint32)(puc_time[6])) << 24)
			   + (hi_uint32)(((hi_uint32)(puc_time[7])) << 16)
			   + (hi_uint32)(((hi_uint32)(puc_time[8])) << 8)
			   + (hi_uint32)(puc_time[9]);
	st_time.ui_ns    = (hi_uint32)(((hi_uint32)(puc_time[10])) << 24)
			   + (hi_uint32)(((hi_uint32)(puc_time[11])) << 16)
			   + (hi_uint32)(((hi_uint32)(puc_time[12])) << 8)
			   + (hi_uint32)(puc_time[13]);

	return HI_IPC_CALL("hi_sml_gpon_time_set", &st_time);
}
/*****************************************************************************
  Function     : hi_omci_tapi_gmac_alarm_get
  Description  : 获取GMAC的相关告警统计
  Input Param  : hi_omci_tapi_map_s *pst_mapping
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_tapi_gpon_alarm_get(hi_omci_tapi_gpon_alarm_s *pst_gpon_alarm)
{
	hi_int32 ui_ret;
	hi_sml_gpon_alarm_s st_alarm = {0};
	ui_ret = HI_IPC_CALL("hi_sml_gpon_alarm_get", &st_alarm);
	HI_OMCI_RET_CHECK(ui_ret);

	pst_gpon_alarm->ui_los_alarm_count = st_alarm.ui_los_alarm_count;
	pst_gpon_alarm->ui_lof_alarm_count = st_alarm.ui_lof_alarm_count;
	pst_gpon_alarm->ui_dyinggasp_alarm_count = st_alarm.ui_dyinggasp_alarm_count;
	pst_gpon_alarm->ui_rei_counter_alarm_count = st_alarm.ui_rei_counter_alarm_count;
	pst_gpon_alarm->ui_sf_alarm_count = st_alarm.ui_sf_alarm_count;
	pst_gpon_alarm->ui_sd_alarm_count = st_alarm.ui_sd_alarm_count;
	pst_gpon_alarm->ui_lcdg_alarm_count = st_alarm.ui_lcdg_alarm_count;
	pst_gpon_alarm->ui_bm_idle_alarm_count = st_alarm.ui_bm_idle_alarm_count;
	pst_gpon_alarm->ui_rogue_alarm_count = st_alarm.ui_rogue_alarm_count;
	pst_gpon_alarm->ui_silent_alarm_count = st_alarm.ui_silent_alarm_count;
	pst_gpon_alarm->ui_fwi_alarm_count = st_alarm.ui_fwi_alarm_count;
	pst_gpon_alarm->ui_lwi_alarm_count = st_alarm.ui_lwi_alarm_count;
	pst_gpon_alarm->ui_ldi_alarm_count = st_alarm.ui_ldi_alarm_count;
	pst_gpon_alarm->ui_lsi_alarm_count = st_alarm.ui_lsi_alarm_count;

	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
