/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_stat.c
  Version    : 初稿
  Author     : owen
  Creation   : 2014-1-15
  Description: OMCI TAPI 统计
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
//#include "hi_omci_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_sml_gpon.h"
#include "hi_sml_uni.h"
#include "hi_ipc.h"
//#include "hi_drv_common.h"
/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_tapi_stat_fec_get
 Description : 获取FEC统计
 Input Parm  : 无
 Output Parm : hi_omci_tapi_stat_fecpm_s *pst_stat
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_stat_fec_get(hi_omci_tapi_stat_fecpm_s *pst_stat)
{
	hi_int32 i_ret;
	hi_sml_fec_stat_s st_stat = {0};

	i_ret = HI_IPC_CALL("hi_sml_gpon_fec_stat_get", &st_stat);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMCPY_S(pst_stat, sizeof(*pst_stat), &st_stat, sizeof(st_stat));

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_stat_gemport_get
 Description : 获取GEMPORT统计
 Input Parm  : hi_uint32 gemportid,
               hi_omci_tapi_stat_gemctppm_s *pst_stat
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_stat_gemport_get(hi_uint32 gemportid, hi_omci_tapi_stat_gemctppm_s *pst_stat)
{
	hi_int32 i_ret;
	hi_sml_gemport_stat_s st_stat = {0};

	st_stat.ui_gemportid = gemportid;
	i_ret = HI_IPC_CALL("hi_sml_gpon_gemport_stat_get", &st_stat);
	HI_OMCI_RET_CHECK(i_ret);

	pst_stat->ui_transframes = st_stat.ui_transframes;
	pst_stat->ui_rcvframes = st_stat.ui_rcvframes;
	pst_stat->ui_encryerrs = st_stat.ui_encryerrs;
	pst_stat->ul_rcvpayload = HI_NET_NTOLL(st_stat.ul_rcvpayload);
	pst_stat->ul_transpayload = HI_NET_NTOLL(st_stat.ul_transpayload);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_stat_eth_get
 Description : 获取以太端口统计
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_stat_eth_s *pst_stat
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_stat_eth_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_stat_eth_s *pst_stat)
{
	hi_sml_port_stat_s st_stat = {0};
	hi_int32 i_ret;

	st_stat.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_stat_get", &st_stat);
	HI_OMCI_RET_CHECK(i_ret);

	pst_stat->ui_fcserrors          = st_stat.ui_fcserrors;
	pst_stat->ui_excesscnt          = st_stat.ui_excesscnt;
	pst_stat->ui_latecnt            = st_stat.ui_latecnt;
	pst_stat->ui_frmlong            = st_stat.ui_frmlong;
	pst_stat->ui_overflowrcv        = st_stat.ui_overflowrcv;
	pst_stat->ui_overflowtrans      = st_stat.ui_overflowtrans;
	pst_stat->ui_singlcoll          = st_stat.ui_singlcoll;
	pst_stat->ui_mulcoll            = st_stat.ui_mulcoll;
	pst_stat->ui_sqe                = st_stat.ui_sqe;
	pst_stat->ui_defertrans         = st_stat.ui_defertrans;
	pst_stat->ui_mactranserr        = st_stat.ui_mactranserr;
	pst_stat->ui_senseerr           = st_stat.ui_senseerr;
	pst_stat->ui_alignerr           = st_stat.ui_alignerr;
	pst_stat->ui_macrcverr          = st_stat.ui_macrcverr;
	pst_stat->ui_fragments          = st_stat.ui_fragments;

	pst_stat->ui_dropsevt           = st_stat.ui_dropsevt;
	pst_stat->ui_octets             = st_stat.ui_octets;
	pst_stat->ui_packets            = st_stat.ui_packets;
	pst_stat->ui_bcpkt              = st_stat.ui_bcpkt;
	pst_stat->ui_mcpkt              = st_stat.ui_mcpkt;
	pst_stat->ui_crcerror           = st_stat.ui_macrcverr;
	pst_stat->ui_undersizepkt       = st_stat.ui_undersizepkt;
	pst_stat->ui_jabber             = st_stat.ui_jabber;
	pst_stat->ui_pkt64              = st_stat.ui_pkt64;
	pst_stat->ui_pkt65to127         = st_stat.ui_pkt65to127;
	pst_stat->ui_pkt128to255        = st_stat.ui_pkt128to255;
	pst_stat->ui_pkt256to511        = st_stat.ui_pkt256to511;
	pst_stat->ui_pkt512to1023       = st_stat.ui_pkt512to1023;
	pst_stat->ui_pkt1024to1518      = st_stat.ui_pkt1024to1518;

	pst_stat->ui_tx_dropsevt        = st_stat.ui_tx_dropsevt;
	pst_stat->ui_tx_octets          = st_stat.ui_tx_octets;
	pst_stat->ui_tx_packets         = st_stat.ui_tx_packets;
	pst_stat->ui_tx_bcpkt           = st_stat.ui_tx_bcpkt;
	pst_stat->ui_tx_mcpkt           = st_stat.ui_tx_mcpkt;
	pst_stat->ui_tx_crcerror        = 0;
	pst_stat->ui_tx_undersizepkt    = st_stat.ui_tx_undersizepkt;
	pst_stat->ui_tx_jabber          = st_stat.ui_tx_jabber;
	pst_stat->ui_tx_pkt64           = st_stat.ui_tx_pkt64;
	pst_stat->ui_tx_pkt65to127      = st_stat.ui_tx_pkt65to127;
	pst_stat->ui_tx_pkt128to255     = st_stat.ui_tx_pkt128to255;
	pst_stat->ui_tx_pkt256to511     = st_stat.ui_tx_pkt256to511;
	pst_stat->ui_tx_pkt512to1023    = st_stat.ui_tx_pkt512to1023;
	pst_stat->ui_tx_pkt1024to1518   = st_stat.ui_tx_pkt1024to1518;

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_stat_eth_clr
 Description : 清空以太端口统计
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_stat_eth_clr(hi_omci_tapi_port_e em_port)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_stat_bridge_get
 Description : 获取桥统计
 Input Parm  : hi_omci_tapi_stat_bridge_s *pst_stat
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_stat_bridge_get(hi_omci_tapi_stat_bridge_s *pst_stat)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_stat_brport_get
 Description : 获取桥端口统计
 Input Parm  : hi_omci_tapi_stat_brport_s *pst_stat
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_stat_brport_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_stat_brport_s *pst_stat)
{
	hi_sml_port_stat_s st_stat = {0};
	hi_int32 i_ret;

	st_stat.em_port = (hi_sml_port_e)em_port;
	i_ret = HI_IPC_CALL("hi_sml_port_stat_get", &st_stat);
	HI_OMCI_RET_CHECK(i_ret);

	pst_stat->ui_rcvcnt          = st_stat.ui_packets;
	pst_stat->ui_forwardcnt          = st_stat.ui_tx_packets;
	pst_stat->ui_delaydiscardcnt          = 0;
	pst_stat->ui_mtudiscardcnt          = 0;
	pst_stat->ui_discardcnt          = 0;
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_stat_tc_get
 Description : 获取XGPON TC统计
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_stat_tc_get(hi_omci_tapi_stat_xgpontcpm_s *pst_stat)
{
	hi_sml_xgpon_tc_s st_stat = {0};
	hi_int32 i_ret;

	i_ret = HI_IPC_CALL("hi_sml_xgpon_tc_get", &st_stat);
	HI_OMCI_RET_CHECK(i_ret);

	pst_stat->ui_psbdhecerr          = st_stat.ui_psbdhecerr;
	pst_stat->ui_xgtchecerr          = st_stat.ui_xgtchecerr;
	pst_stat->ui_unknownprofile      = st_stat.ui_unknownprofile;
	pst_stat->ui_transxgem           = st_stat.ui_transxgem;
	pst_stat->ui_fragxgem            = st_stat.ui_fragxgem;
	pst_stat->ui_xgemheclost         = st_stat.ui_xgemheclost;
	pst_stat->ui_xgemkeyerr          = st_stat.ui_xgemkeyerr;
	pst_stat->ui_xgemhecerr          = st_stat.ui_xgemhecerr;

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_stat_downmng_get
 Description : 获取XGPON DOWNSTREAM MANAGEMENT统计
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_stat_downmng_get(hi_omci_tapi_stat_xgpondownmng_pm_s *pst_stat)
{
	hi_sml_xgpon_downmng_s st_stat = {0};
	hi_int32 i_ret;
	HI_OS_MEMSET_S(&st_stat, sizeof(st_stat), 0, sizeof(st_stat));

	i_ret = HI_IPC_CALL("hi_sml_xgpon_downmng_get", &st_stat);
	HI_OMCI_RET_CHECK(i_ret);

	pst_stat->ui_ploammicerr          = st_stat.ui_ploammicerr;
	pst_stat->ui_dsploammesgcnt          = st_stat.ui_dsploammesgcnt;
	pst_stat->ui_profilemesgrcv      = st_stat.ui_profilemesgrcv;
	pst_stat->ui_rangingtimemesgrcv           = st_stat.ui_rangingtimemesgrcv;
	pst_stat->ui_deactiveonuidmesgrcv            = st_stat.ui_deactiveonuidmesgrcv;
	pst_stat->ui_disablesnmesgrcv         = st_stat.ui_disablesnmesgrcv;
	pst_stat->ui_reqregmesgrcv          = st_stat.ui_reqregmesgrcv;
	pst_stat->ui_assignallocidmesgrcv          = st_stat.ui_assignallocidmesgrcv;
	pst_stat->ui_keycontrolmesgrcv          = st_stat.ui_keycontrolmesgrcv;
	pst_stat->ui_sleepallowmesgrcv          = st_stat.ui_sleepallowmesgrcv;
	pst_stat->ui_baselineomcimesgrcv          = st_stat.ui_baselineomcimesgrcv;
	pst_stat->ui_extomcimesgrcv          = st_stat.ui_extomcimesgrcv;
	pst_stat->ui_assignonuidmesgrcv          = st_stat.ui_assignonuidmesgrcv;
	pst_stat->ui_omcimicerr          = st_stat.ui_omcimicerr;

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_stat_upmng_get
 Description : 获取XGPON UPSTREAM MANAGEMENT统计
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_stat_upmng_get(hi_omci_tapi_stat_xgponupmng_pm_s *pst_stat)
{
	hi_sml_xgpon_upmng_s st_stat = {0};
	hi_int32 i_ret;
	HI_OS_MEMSET_S(&st_stat, sizeof(st_stat), 0, sizeof(st_stat));

	i_ret = HI_IPC_CALL("hi_sml_xgpon_upmng_get", &st_stat);
	HI_OMCI_RET_CHECK(i_ret);

	pst_stat->ui_upsploammesgcnt    = st_stat.ui_upsploammesgcnt;
	pst_stat->ui_snonumesgcnt       = st_stat.ui_snonumesgcnt;
	pst_stat->ui_regmemesgcnt       = st_stat.ui_regmemesgcnt;
	pst_stat->ui_keyrepmesgcnt      = st_stat.ui_keyrepmesgcnt;
	pst_stat->ui_ackmesgcnt         = st_stat.ui_ackmesgcnt;
	pst_stat->ui_sleepreqmesgcnt    = st_stat.ui_sleepreqmesgcnt;

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_stat_galeth_get
 Description : 获取GAL ETH统计
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_stat_galeth_get(hi_uint32 *pui_stat)
{
	hi_sml_xgpon_tc_s st_stat = {0};
	hi_int32 i_ret;

	i_ret = HI_IPC_CALL("hi_sml_xgpon_tc_get", &st_stat);
	HI_OMCI_RET_CHECK(i_ret);

	*pui_stat = st_stat.ui_xgemkeyerr + st_stat.ui_xgemhecerr;

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
