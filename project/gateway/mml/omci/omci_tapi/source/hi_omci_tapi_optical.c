/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_optical.c
  Version    : 初稿
  Author     : owen
  Creation   : 2014-1-17
  Description: OMCI光器件TAPI
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_sml_optical.h"
#include "hi_sml_gpon.h"
#include "hi_omci_tapi_lib.h"
#include "hi_board.h"
#include "hi_ipc.h"
#include <math.h>

/******************************************************************************
 Function    : hi_omci_tapi_optical_attr_get
 Description : 获取光器件属性
 Input Parm  : hi_omci_tapi_optical_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_optical_attr_get(hi_uint32 ui_creat_flag, hi_omci_tapi_optical_attr_s *pst_attr)
{
	hi_sml_optical_sta_s st_sta;
	hi_uint32 ui_state;
	if (ui_creat_flag == HI_DISABLE) {
		HI_IPC_CALL("hi_sml_gpon_regstate_get", &ui_state);
		if (ui_state != HI_OMCI_PLOAM_REGSTATE_E) {
			return HI_RET_STOP;
		}
	}
	HI_IPC_CALL("hi_sml_optical_sta_get", &st_sta);
	pst_attr->ui_voltage = st_sta.voltage;
	pst_attr->ui_temprature = st_sta.temprature;
	pst_attr->ui_ibias = st_sta.ibias;
	pst_attr->ui_rxpower = st_sta.rxpower;
	pst_attr->ui_txpower = st_sta.txpower;
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

int32_t hi_omci_tapi_optical_info_get(struct hi_omci_tapi_optical_info *attr)
{
	int32_t ret;
	struct hi_sml_optical_info_s opt_info = {0};

	ret = HI_IPC_CALL("hi_sml_optical_info_get", &opt_info);
	if (ret != HI_RET_SUCC) {
		hi_omci_err("%s[%d] fail, ret=%d\n", __func__, __LINE__, ret);
		return ret;
	}

	ret = memcpy_s(attr, sizeof(struct hi_omci_tapi_optical_info), &opt_info, sizeof(struct hi_sml_optical_info_s));
	if (ret != HI_RET_SUCC) {
		hi_omci_err("%s[%d] fail, ret=%d\n", __func__, __LINE__, ret);
		return ret;
	}

	return HI_RET_SUCC;
}


/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
