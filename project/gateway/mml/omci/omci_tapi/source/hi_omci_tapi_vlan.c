/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_tapi_vlan.c
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : VLAN配置接口
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
//#include "hi_omci_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_sml_vlan.h"
#include "hi_ipc.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/*****************************************************************************
  Function     : hi_omci_tapi_vlan_filter_set
  Description  : 配置VLAN过滤使能
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_uint32 ui_enable
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_vlan_filter_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_enable)
{
	hi_sml_vlan_filter_s st_filter = {0};
	hi_int32 i_ret;

	st_filter.em_port = (hi_sml_port_e)em_port;
	st_filter.ui_enable = ui_enable;
	st_filter.ui_igr_en = ui_enable;
	st_filter.ui_egr_en = ui_enable;
	i_ret = HI_IPC_CALL("hi_sml_vlan_filter_set", &st_filter);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_enable, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_vlan_filter_get
  Description  : 获取VLAN过滤使能状态
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_uint32 pui_enable
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_vlan_filter_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_enable)
{
	hi_sml_vlan_filter_s st_filter = {0};
	hi_int32 i_ret;

	st_filter.em_port = (hi_sml_port_e)em_port;

	i_ret = HI_IPC_CALL("hi_sml_vlan_filter_get", &st_filter);
	HI_OMCI_RET_CHECK(i_ret);

	*pui_enable = st_filter.ui_enable;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, *pui_enable, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_vlan_set
  Description  : 配置端口VLAN
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_uint32 ui_vid
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_vlan_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_vid)
{
	hi_sml_vlan_s st_vlan = {0};
	hi_int32 i_ret;

	st_vlan.em_port = (hi_sml_port_e)em_port;
	st_vlan.ui_vid = ui_vid;
	i_ret = HI_IPC_CALL("hi_sml_vlan_set", &st_vlan);
	st_vlan.em_port = (hi_sml_port_e)HI_SML_PON_E;
	st_vlan.ui_vid = ui_vid;
	i_ret = HI_IPC_CALL("hi_sml_vlan_set", &st_vlan);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_vid, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_vlan_del
  Description  : 删除端口VLAN
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_uint32 ui_vid
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
hi_int32 hi_omci_tapi_vlan_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_vid)
{
	hi_sml_vlan_s st_vlan = {0};
	hi_int32 i_ret;

	st_vlan.em_port = (hi_sml_port_e)em_port;
	st_vlan.ui_vid = ui_vid;
	i_ret = HI_IPC_CALL("hi_sml_vlan_del", &st_vlan);
	st_vlan.em_port = (hi_sml_port_e)HI_SML_PON_E;
	st_vlan.ui_vid = ui_vid;
	i_ret = HI_IPC_CALL("hi_sml_vlan_del", &st_vlan);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_vid, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_tagact_set
 Description : 端口TAG处理动作
               UNTAG报文不能配置无处理动作
               添加缺省TAG动作对TAG报文无效
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_tag_type_e em_type,
               hi_omci_tapi_tag_direction_e em_dir,
               hi_omci_tapi_tag_act_e em_act
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tagact_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_tag_type_e em_type,
				 hi_omci_tapi_tag_direction_e em_dir, hi_omci_tapi_tag_act_e em_act)
{
	hi_sml_tagact_s st_tagact = {0};
	hi_int32 i_ret;

	st_tagact.em_port = (hi_sml_port_e)em_port;
	st_tagact.em_type = (hi_sml_tag_type_e)em_type;
	st_tagact.em_dir = (hi_sml_tag_direction_e)em_dir;
	st_tagact.em_act = (hi_sml_tag_act_e)em_act;
	i_ret = HI_IPC_CALL("hi_sml_tagact_set", &st_tagact);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, em_type, em_dir, em_act);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_deftag_set
 Description : 配置端口缺省TAG
 Input Parm  : hi_omci_tapi_port_e em_port,       报文输入端口
               hi_uint32 ui_vlan,           缺省VLAN
               hi_omci_tapi_tpid_type_e em_tpid   TPID选择:CTAG或STAG
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_deftag_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_vlan, hi_omci_tapi_tpid_type_e em_tpid)
{
	hi_sml_deftag_s st_deftag = {0};
	hi_int32 i_ret;

	st_deftag.em_port = (hi_sml_port_e)em_port;
	st_deftag.ui_vlan = ui_vlan;
	st_deftag.em_tpid = (hi_sml_tpid_type_e)em_tpid;
	i_ret = HI_IPC_CALL("hi_sml_deftag_set", &st_deftag);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_vlan, em_tpid, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_deftag_get
 Description : 配置端口缺省TAG
 Input Parm  : hi_omci_tapi_port_e em_port,       报文输入端口
 Output Parm : hi_uint32 *pui_vlan,           缺省VLAN
               hi_omci_tapi_tpid_type_e *pem_tpid   TPID选择:CTAG或STAG
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_deftag_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_vlan, hi_omci_tapi_tpid_type_e *pem_tpid)
{
	hi_sml_deftag_s st_deftag = {0};
	hi_int32 i_ret;

	st_deftag.em_port = (hi_sml_port_e)(hi_uint32)em_port;
	i_ret = HI_IPC_CALL("hi_sml_deftag_get", &st_deftag);
	HI_OMCI_RET_CHECK(i_ret);

	*pui_vlan = st_deftag.ui_vlan;
	*pem_tpid = (hi_omci_tapi_tpid_type_e)st_deftag.em_tpid;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, st_deftag.ui_vlan, st_deftag.em_tpid, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_tpid_set
 Description : 配置TPID
               提供3种TPID配置
 Input Parm  : hi_omci_tapi_tpid_type_e em_type,
               hi_uint32 ui_tpid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tpid_set(hi_omci_tapi_tpid_type_e em_type, hi_uint32 ui_tpid)
{
	hi_sml_tpid_s st_tpid = {0};
	hi_int32 i_ret;

	st_tpid.em_type = (hi_sml_tpid_type_e)em_type;
	st_tpid.ui_tpid = ui_tpid;
	i_ret = HI_IPC_CALL("hi_sml_tpid_set", &st_tpid);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_type, ui_tpid, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_tapi_untag_addtag_set(hi_omci_tapi_port_e em_port,
				       hi_omci_tapi_mark_s *pst_mark)
{
	hi_sml_vlan_translation_opt_s st_trans = {0};
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_trans, sizeof(hi_sml_vlan_translation_opt_s), 0, sizeof(hi_sml_vlan_translation_opt_s));
	st_trans.em_port = (hi_sml_port_e)em_port;
	st_trans.ui_svlan = pst_mark->ui_svlan;
	st_trans.ui_ethtype = pst_mark->ui_ethtype;

	i_ret = HI_IPC_CALL("hi_sml_vlan_untag_set", &st_trans);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

hi_int32 hi_omci_tapi_untag_addtag_del(hi_omci_tapi_port_e em_port,
				       hi_omci_tapi_mark_s *pst_mark)
{
	hi_sml_vlan_translation_opt_s st_trans = {0};
	hi_int32 i_ret;

	st_trans.em_port = (hi_sml_port_e)em_port;
	st_trans.ui_svlan = pst_mark->ui_svlan;
	st_trans.ui_ethtype = pst_mark->ui_ethtype;

	i_ret = HI_IPC_CALL("hi_sml_vlan_untag_del", &st_trans);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_vlan_translation_set
 Description : 配置一层VLAN切换
               配置前先根据treatment规则确定有多少个CVLAN切换成SVLAN，
               以确定是1:1切换，还是N:1切换。当新增规则，可能导致1:1到N:1
               的模式切换。当规则的ui_cvlan==ui_svlan，且ui_tpid_en==0，
               实际上不会产生切换动作.
 Input Parm  : hi_omci_tapi_port_e em_port,   报文输入端口
               hi_uint32 ui_cvlan,      用户VLAN
               hi_uint32 ui_svlan,      要切换的服务VLAN
               hi_uint32 ui_tpid_en     TPID切换标识。一般业务都会使能切换，将TPID
                                        切换为0x88a8，此值可通过aui_tpid[HI_OMCI_TAPI_TPID_OUTPUT]
                                        配置。1:1 VLAN可以带用户TAG上行，此时需要禁止TPID切换
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_vlan_translation_set(hi_omci_tapi_port_e em_port,
		hi_uint32 ui_cvlan, hi_uint32 ui_svlan, hi_uint32 ui_tpid_en)
{
	hi_sml_vlan_translation_s st_trans = {0};
	hi_int32 i_ret;

	st_trans.em_port = (hi_sml_port_e)em_port;
	st_trans.ui_cvlan = ui_cvlan;
	st_trans.ui_svlan = ui_svlan;
	st_trans.ui_tpid_en = ui_tpid_en;
	i_ret = HI_IPC_CALL("hi_sml_vlan_translation_set", &st_trans);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_cvlan, ui_svlan, ui_tpid_en);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_vlan_translation_del
 Description : 删除VLAN切换规则
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_cvlan
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_vlan_translation_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_cvlan)
{
	hi_sml_vlan_translation_s st_trans = {0};
	hi_int32 i_ret;

	st_trans.em_port = (hi_sml_port_e)em_port;
	st_trans.ui_cvlan = ui_cvlan;
	i_ret = HI_IPC_CALL("hi_sml_vlan_translation_del", &st_trans);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_cvlan, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_tapi_vlan_translation_opt_set(hi_omci_tapi_port_e em_port,
		hi_omci_tapi_mark_s *pst_mark)
{
	hi_sml_vlan_translation_opt_s st_trans = {0};
	hi_int32 i_ret;

	st_trans.em_port = (hi_sml_port_e)em_port;
	st_trans.ui_cvlan = pst_mark->ui_cvlan;
	st_trans.ui_cpri = pst_mark->ui_cpri;
	st_trans.ui_ethtype = pst_mark->ui_ethtype;
	st_trans.ui_svlan = pst_mark->ui_svlan;
	st_trans.ui_tpid_en = pst_mark->ui_tpid_en;

	i_ret = HI_IPC_CALL("hi_sml_vlan_translation_opt_set", &st_trans);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, st_trans.ui_cvlan, st_trans.ui_svlan, st_trans.ui_tpid_en);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_tapi_vlan_translation_opt_del(hi_omci_tapi_port_e em_port, hi_omci_tapi_mark_s *pst_mark)
{
	hi_sml_vlan_translation_opt_s st_trans = {0};
	hi_int32 i_ret;

	st_trans.em_port = (hi_sml_port_e)em_port;
	st_trans.ui_cvlan = pst_mark->ui_cvlan;
	st_trans.ui_cpri = pst_mark->ui_cpri;
	st_trans.ui_ethtype = pst_mark->ui_ethtype;
	st_trans.ui_svlan = pst_mark->ui_svlan;
	st_trans.ui_tpid_en = pst_mark->ui_tpid_en;

	i_ret = HI_IPC_CALL("hi_sml_vlan_translation_opt_del", &st_trans);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, pst_mark->ui_cvlan, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_tapi_tls_ctag_set
 Description : TLS(transparent LAN service)的方法1: untag和ctag报文都加上
               SVLAN tag。可与VLAN切换规则共存，当数据流未能VLAN切换规则时，
               执行TLS处理；untag报文不会添加端口缺省TAG，而是添加TLS SVLAN TAG
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_svlan
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tls_ctag_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_svlan)
{
	hi_sml_tls_ctag_s st_tls = {0};
	hi_int32 i_ret;

	st_tls.em_port = (hi_sml_port_e)em_port;
	st_tls.ui_svlan = ui_svlan;
	i_ret = HI_IPC_CALL("hi_sml_tls_ctag_set", &st_tls);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_svlan, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_tls_ctag_del
 Description : 删除TLS方法1规则
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_svlan
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tls_ctag_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_svlan)
{
	hi_sml_tls_ctag_s st_tls = {0};
	hi_int32 i_ret;

	st_tls.em_port = (hi_sml_port_e)em_port;
	st_tls.ui_svlan = ui_svlan;
	i_ret = HI_IPC_CALL("hi_sml_tls_ctag_del", &st_tls);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_svlan, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_tls_stag_set
 Description : TLS方法2: double-tag报文，如果SVLAN-TAG合法则通过或者切换外层VLAN
               否则丢弃报文。此函数配置
 Input Parm  : hi_omci_tapi_port_e em_port,       报文输入端口
               hi_uint32 ui_svlan,          外层TAG的匹配VLAN
               hi_uint32 ui_svlan_trans     外层VLAN切换值，如果是非法值，表示不切换
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tls_stag_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_svlan, hi_uint32 ui_svlan_trans)
{
	hi_sml_tls_stag_s st_tls = {0};
	hi_int32 i_ret;

	st_tls.em_port = (hi_sml_port_e)em_port;
	st_tls.ui_svlan = ui_svlan;
	st_tls.ui_svlan_trans = ui_svlan_trans;
	i_ret = HI_IPC_CALL("hi_sml_tls_stag_set", &st_tls);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_svlan, ui_svlan_trans, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_tls_stag_del
 Description : 删除TLS方法2规则
 Input Parm  : hi_omci_tapi_port_e em_port,       报文输入端口
               hi_uint32 ui_svlan           外层TAG的匹配VLAN
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tls_stag_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_svlan)
{
	hi_sml_tls_stag_s st_tls = {0};
	hi_int32 i_ret;

	st_tls.em_port = (hi_sml_port_e)em_port;
	st_tls.ui_svlan = ui_svlan;
	i_ret = HI_IPC_CALL("hi_sml_tls_stag_del", &st_tls);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_svlan, 0, 0);
	return HI_RET_SUCC;
}


hi_int32 hi_omci_tapi_port_qinq_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_spri, hi_uint32 ui_svlan,
				    hi_uint32 tpid_en)
{
	hi_int32 i_ret;
	hi_sml_port_qinq_s st_port_qinq = {0};


	st_port_qinq.em_port = (hi_sml_port_e)em_port;
	st_port_qinq.ui_spri = ui_spri;
	st_port_qinq.ui_svlan = ui_svlan;
	st_port_qinq.ui_tpid_en = tpid_en;
	i_ret = HI_IPC_CALL("hi_sml_port_qinq_set", &st_port_qinq);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_spri, ui_svlan, tpid_en);
	return HI_RET_SUCC;

}

/******************************************************************************
 Function    : hi_omci_tapi_port_qinq_del
 Description : 删除QinQ规则
 Input Parm  : hi_omci_tapi_port_e em_port  报文输入端口
               hi_uint32 ui_cvlan           用户VLAN
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_port_qinq_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_svlan)
{
	hi_sml_port_qinq_s st_port_qinq = {0};
	hi_int32 i_ret;

	st_port_qinq.em_port = (hi_sml_port_e)em_port;
	st_port_qinq.ui_svlan = ui_svlan;
	i_ret = HI_IPC_CALL("hi_sml_port_qinq_del", &st_port_qinq);
	HI_OMCI_RET_CHECK(i_ret);

	//hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_cvlan, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_qinq_set
 Description : 配置QinQ规则
 Input Parm  : hi_omci_tapi_port_e em_port  报文输入端口
               hi_uint32 ui_cvlan           用户VLAN
               hi_uint32 ui_svlan           添加的Service TAG VID
               hi_uint32 tpid_en            使能则，指定TPID为Output TPID；
                                            否则拷贝内层VLAN TAG TPID
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_qinq_set(hi_omci_tapi_port_e em_port,
			       hi_uint32 ui_cvlan, hi_uint32 ui_svlan, hi_uint32 tpid_en)
{
	hi_sml_qinq_s st_qinq = {0};
	hi_int32 i_ret;

	st_qinq.em_port = (hi_sml_port_e)em_port;
	st_qinq.ui_cvlan = ui_cvlan;
	st_qinq.ui_svlan = ui_svlan;
	st_qinq.ui_tpid_en = tpid_en;
	i_ret = HI_IPC_CALL("hi_sml_qinq_set", &st_qinq);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_cvlan, ui_svlan, tpid_en);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_qinq_del
 Description : 删除QinQ规则
 Input Parm  : hi_omci_tapi_port_e em_port  报文输入端口
               hi_uint32 ui_cvlan           用户VLAN
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_qinq_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_cvlan)
{
	hi_sml_qinq_s st_qinq = {0};
	hi_int32 i_ret;

	st_qinq.em_port = (hi_sml_port_e)em_port;
	st_qinq.ui_cvlan = ui_cvlan;
	i_ret = HI_IPC_CALL("hi_sml_qinq_del", &st_qinq);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_cvlan, 0, 0);
	return HI_RET_SUCC;
}

hi_int32 hi_omci_tapi_qinq_full_set(hi_omci_tapi_port_e em_port,
				    hi_omci_tapi_mark_s *pst_mark)
{
	hi_sml_qinq_full_s st_qinq = {0};
	hi_int32 i_ret;

	st_qinq.em_port = (hi_sml_port_e)em_port;
	st_qinq.ui_cvlan   = pst_mark->ui_cvlan;
	st_qinq.ui_cpri    = pst_mark->ui_cpri;
	st_qinq.ui_ethtype = pst_mark->ui_ethtype;

	st_qinq.ui_svlan   = pst_mark->ui_svlan;
	st_qinq.ui_spri    = pst_mark->ui_spri;
	st_qinq.ui_tpid_en = pst_mark->ui_tpid_en;

	i_ret = HI_IPC_CALL("hi_sml_qinq_full_set", &st_qinq);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

hi_int32 hi_omci_tapi_qinq_full_del(hi_omci_tapi_port_e em_port,
				    hi_omci_tapi_mark_s *pst_mark)
{
	hi_sml_qinq_full_s st_qinq = {0};
	hi_int32 i_ret;

	st_qinq.em_port = (hi_sml_port_e)em_port;
	st_qinq.ui_cvlan   = pst_mark->ui_cvlan;
	st_qinq.ui_cpri    = pst_mark->ui_cpri;
	st_qinq.ui_ethtype = pst_mark->ui_ethtype;

	st_qinq.ui_svlan   = pst_mark->ui_svlan;
	st_qinq.ui_spri    = pst_mark->ui_spri;
	st_qinq.ui_tpid_en = pst_mark->ui_tpid_en;

	i_ret = HI_IPC_CALL("hi_sml_qinq_full_del", &st_qinq);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

hi_int32 hi_omci_tapi_qinq_vlan_set(hi_omci_tapi_port_e em_port,
				    hi_omci_tapi_mark_s *pst_mark)
{
	hi_sml_qinq_full_s st_qinq = {0};
	hi_int32 i_ret;

	st_qinq.em_port = (hi_sml_port_e)em_port;
	st_qinq.ui_cvlan   = pst_mark->ui_cvlan;
	st_qinq.ui_cpri    = pst_mark->ui_cpri;
	st_qinq.ui_ethtype = pst_mark->ui_ethtype;

	st_qinq.ui_svlan   = pst_mark->ui_svlan;
	st_qinq.ui_spri    = pst_mark->ui_spri;
	st_qinq.ui_tpid_en = pst_mark->ui_tpid_en;

	i_ret = HI_IPC_CALL("hi_sml_qinq_vlan_set", &st_qinq);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

hi_int32 hi_omci_tapi_qinq_vlan_del(hi_omci_tapi_port_e em_port,
				    hi_omci_tapi_mark_s *pst_mark)
{
	hi_sml_qinq_full_s st_qinq = {0};
	hi_int32 i_ret;

	st_qinq.em_port = (hi_sml_port_e)em_port;
	st_qinq.ui_cvlan   = pst_mark->ui_cvlan;
	st_qinq.ui_cpri    = pst_mark->ui_cpri;
	st_qinq.ui_ethtype = pst_mark->ui_ethtype;

	st_qinq.ui_svlan   = pst_mark->ui_svlan;
	st_qinq.ui_spri    = pst_mark->ui_spri;
	st_qinq.ui_tpid_en = pst_mark->ui_tpid_en;

	i_ret = HI_IPC_CALL("hi_sml_qinq_vlan_del", &st_qinq);
	HI_OMCI_RET_CHECK(i_ret);

	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

