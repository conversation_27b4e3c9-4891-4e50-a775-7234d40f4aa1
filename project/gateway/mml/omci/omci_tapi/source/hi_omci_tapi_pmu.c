/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_pmu.c
  Version    : 初稿
  Author     : owen
  Creation   : 2013-12-25
  Description: OMCI节能TAPI
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
//#include "hi_omci_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_ipc.h"
//#include "hi_kernel_pmu_gpon.h"


/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_pmu_attr_set
 Description : 配置节能属性
 Input Parm  : hi_omci_tapi_pmu_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_pmu_attr_set(hi_omci_tapi_pmu_attr_s *pst_attr)
{
	/* TODO:pdu */
#if 0
	hi_pmu_gpon_odmc_s st_odmc;

	hi_int32 i_ret;

	i_ret = HI_IPC_CALL("hi_kernel_pmu_gpon_odmc_get", &st_odmc, sizeof(st_odmc));
	HI_OMCI_RET_CHECK(i_ret);

	st_odmc.uc_reductmode = (hi_uchar8)pst_attr->ui_reductcap;
	st_odmc.ui_maxsleep   = pst_attr->ui_maxsleep;
	st_odmc.ui_minaware   = pst_attr->ui_minaware;
	st_odmc.us_minactive  = (hi_ushort16)pst_attr->ui_minaware;

	i_ret = HI_IPC_CALL("hi_kernel_pmu_gpon_odmc_set", &st_odmc, sizeof(st_odmc));
	HI_OMCI_RET_CHECK(i_ret);
#endif
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_pmu_attr_get
 Description : 获取节能属性
 Input Parm  : 无
 Output Parm : hi_omci_tapi_pmu_attr_s *pst_att
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_pmu_attr_get(hi_omci_tapi_pmu_attr_s *pst_att)
{
	/* TODO:pdu */
#if 0
	hi_pmu_gpon_odmc_s st_odmc;
	hi_int32 i_ret;

	i_ret = HI_IPC_CALL("hi_kernel_pmu_gpon_odmc_get", &st_odmc, sizeof(st_odmc));
	HI_OMCI_RET_CHECK(i_ret);

	pst_att->ui_reductcap  = st_odmc.uc_reductcap;
	pst_att->ui_reductmode = st_odmc.uc_reductmode;
	pst_att->ui_itransinit = st_odmc.us_itransinit;
	pst_att->ui_itxinit    = st_odmc.us_itxinit;
	pst_att->ui_maxsleep   = st_odmc.ui_maxsleep;;
	pst_att->ui_minaware   = st_odmc.ui_minaware;
	pst_att->ui_minactive  = st_odmc.us_minactive;
#endif
	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
