/******************************************************************************

                  Copyright (C), 2012-2022, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_tr069.c
  Version    : 初稿
  Author     :
  Creation   :
  Description: OMCI_TAPI VOIP端口配置
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_tapi_lib.h"
#include "igdCmModulePub.h"
/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_tr069_server_set
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tr069_server_set(hi_omci_tapi_tr069_server_s *pst_server)
{
	IgdRmtMgtTr069AttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	HI_OS_MEMCPY_S(st_data.aucAcsUrl, sizeof(st_data.aucAcsUrl), pst_server->uc_acsaddr, HI_OMCI_TAPI_TR069_ACS_LEN);
	HI_OS_MEMCPY_S(st_data.aucUsername, sizeof(st_data.aucUsername), pst_server->uc_auth_username,
		       HI_OMCI_TAPI_TR069_STR_LEN);
	HI_OS_MEMCPY_S(st_data.aucPassword, sizeof(st_data.aucPassword), pst_server->uc_auth_password,
		       HI_OMCI_TAPI_TR069_STR_LEN);

	st_data.ulBitmap |= TR069_ATTR_MASK_BIT6_ACS_URL | TR069_ATTR_MASK_BIT8_ACS_USERNAME | TR069_ATTR_MASK_BIT9_ACS_PASSWORD
			    ;
	i_ret = igdCmConfSet(IGD_REMOTEMGT_TR069_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : hi_omci_tapi_tr069_server_get
 Description :
 Input Parm  :
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_tr069_server_get(hi_omci_tapi_tr069_server_s *pst_server)
{
	IgdRmtMgtTr069AttrConfTab st_data;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(&st_data, sizeof(st_data), 0, sizeof(st_data));
	i_ret = igdCmConfGet(IGD_REMOTEMGT_TR069_ATTR_TAB, (unsigned char *)&st_data, sizeof(st_data));
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMCPY_S(pst_server->uc_acsaddr, sizeof(pst_server->uc_acsaddr), st_data.aucAcsUrl, HI_OMCI_TAPI_TR069_ACS_LEN);
	HI_OS_MEMCPY_S(pst_server->uc_auth_username, sizeof(pst_server->uc_auth_username), st_data.aucUsername,
		       HI_OMCI_TAPI_TR069_STR_LEN);
	HI_OS_MEMCPY_S(pst_server->uc_auth_password, sizeof(pst_server->uc_auth_password), st_data.aucPassword,
		       HI_OMCI_TAPI_TR069_STR_LEN);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

