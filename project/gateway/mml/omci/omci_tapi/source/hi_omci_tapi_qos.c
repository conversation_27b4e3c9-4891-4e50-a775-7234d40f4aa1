/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_tapi_qos.c
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : Qos配置接口
******************************************************************************/


/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
//#include "hi_omci_lib.h"
#include "hi_omci_tapi_lib.h"
#include "hi_sml_qos.h"
#include "hi_ipc.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_TAPI_CAR_KBIT_RATE(rate) ((rate) * 8 / 1000)
#define HI_OMCI_TAPI_CAR_BYTE_RATE(rate) ((rate) * 1000 / 8)

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_defpri_set
 Description : 配置端口缺省优先级
 Input Parm  : hi_omci_tapi_port_e em_port 以太端口
               hi_uint32 ui_pri 缺省优先级
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_defpri_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_pri)
{
	hi_sml_defpri_s st_defpri;
	hi_int32 i_ret;

	st_defpri.em_port = (hi_sml_port_e)em_port;
	st_defpri.ui_pri = ui_pri;
	i_ret = HI_IPC_CALL("hi_sml_defpri_set", &st_defpri);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, ui_pri, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
  Function     : hi_omci_tapi_dscp_map_set
  Description  : 配置DSCP到P-bit的映射
  Input Param  : hi_omci_tapi_dscp_map_s *pst_map DSCP到PBIT的映射表
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
hi_int32 hi_omci_tapi_dscp_map_set(hi_omci_tapi_dscp_map_s *pst_map)
{
	hi_sml_dscp_map_s st_dscp;
	hi_int32 i_ret;

	HI_OS_MEMCPY_S(st_dscp.aui_pri, sizeof(st_dscp.aui_pri), pst_map->aui_pri, sizeof(st_dscp.aui_pri));
	i_ret = HI_IPC_CALL("hi_sml_dscp_map_set", &st_dscp);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_pbit_marking_set
 Description : 配置802.1p优先级marking规则
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_qos_pri_s *pst_mark
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_pri_marking_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_qos_pri_s *pst_mark)
{
	hi_sml_qos_pri_s st_mark;
	hi_int32 i_ret;
	hi_void *pv_data = &st_mark;

	st_mark.em_port = (hi_sml_port_e)em_port;
	HI_OS_MEMCPY_S(((hi_uchar8 *)pv_data + 4), sizeof(st_mark) - 4, pst_mark, sizeof(hi_omci_tapi_qos_pri_s));
	i_ret = HI_IPC_CALL("hi_sml_pri_marking_set", &st_mark);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_pbit_marking_del
 Description : 删除802.1p优先级marking规则
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_qos_pri_s *pst_mark
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_pri_marking_del(hi_omci_tapi_port_e em_port, hi_omci_tapi_qos_pri_s *pst_mark)
{
	hi_sml_qos_pri_s st_mark;
	hi_int32 i_ret;
	hi_void *pv_data = &st_mark;

	st_mark.em_port = (hi_sml_port_e)em_port;
	HI_OS_MEMCPY_S(((hi_uchar8 *)pv_data + 4), sizeof(st_mark) - 4, pst_mark, sizeof(hi_omci_tapi_qos_pri_s));
	i_ret = HI_IPC_CALL("hi_sml_pri_marking_del", &st_mark);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_port, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_car_set
 Description : 配置CAR模板
 Input Parm  : hi_uint32 ui_carid,      CAR模板标识
               hi_omci_tapi_car_s *pst_car    CAR模板参数
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_car_set(hi_uint32 *ui_carid, hi_omci_tapi_car_s *pst_car)
{
	hi_sml_car_s st_car;
	hi_int32 i_ret;

	st_car.st_cardata.ui_pir = pst_car->ui_pir;
	i_ret = HI_IPC_CALL("hi_sml_car_set", &st_car);
	HI_OMCI_RET_CHECK(i_ret);
	*ui_carid = st_car.ui_carid;
	hi_omci_tapi_systrace(HI_RET_SUCC, *ui_carid, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_car_get
 Description : 获取CAR模板参数
 Input Parm  : hi_uint32 ui_carid,      CAR模板标识
 Output Parm : hi_omci_tapi_car_s *pst_car    CAR模板参数
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_car_get(hi_uint32 ui_carid, hi_omci_tapi_car_s *pst_car)
{
	hi_sml_car_s st_car = {0};
	hi_int32 i_ret;

	st_car.ui_carid = ui_carid;
	i_ret = HI_IPC_CALL("hi_sml_car_get", &st_car);
	HI_OMCI_RET_CHECK(i_ret);
	pst_car->ui_pir = st_car.st_cardata.ui_pir;
	hi_omci_tapi_systrace(HI_RET_SUCC, ui_carid, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_car_del
 Description : 删除CAR模板
 Input Parm  : hi_uint32 ui_carid,      CAR模板标识
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_car_release(hi_uint32 ui_carid)
{
	hi_omci_tapi_systrace(HI_RET_SUCC, ui_carid, 0, 0, 0);
	return HI_IPC_CALL("hi_sml_car_release", &ui_carid);
}

/******************************************************************************
 Function    : hi_omci_tapi_queue_set
 Description : 配置队列参数
               一个端口下有多个优先级队列，可通过端口和优先级索引到队列
 Input Parm  : hi_omci_tapi_egress_type_e em_egr,
               hi_uint32 ui_pri,
               hi_omci_tapi_queue_para_s *pst_para
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_queue_set(hi_omci_tapi_egress_type_e em_egr, hi_uint32 ui_pri,
				hi_omci_tapi_queue_para_s *pst_para)
{
	hi_sml_queue_para_s st_para;
	hi_int32 i_ret;
	hi_void *pv_data = &st_para;

	HI_OS_MEMSET_S(&st_para, sizeof(hi_sml_queue_para_s), 0x0, sizeof(hi_sml_queue_para_s));
	st_para.em_egr = (hi_sml_egress_type_e)em_egr;
	st_para.ui_pri = ui_pri;
	HI_OS_MEMCPY_S(((hi_uchar8 *)pv_data + 8), sizeof(st_para) - 8, pst_para, sizeof(hi_omci_tapi_queue_para_s));
	i_ret = HI_IPC_CALL("hi_sml_queue_set", &st_para);
	HI_OMCI_RET_CHECK(i_ret);

	hi_omci_tapi_systrace(HI_RET_SUCC, em_egr, ui_pri, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_queue_set
 Description : 获取队列参数
 Input Parm  : hi_omci_tapi_egress_type_e em_egr,
               hi_uint32 ui_pri,
 Output Parm : hi_omci_tapi_queue_para_s *pst_para
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_queue_get(hi_omci_tapi_egress_type_e em_egr, hi_uint32 ui_pri,
				hi_omci_tapi_queue_para_s *pst_para)
{
	hi_sml_queue_para_s st_para = {0};
	hi_int32 i_ret;
	hi_void *pv_data = &st_para;

	st_para.em_egr = (hi_sml_egress_type_e)em_egr;
	st_para.ui_pri = ui_pri;
	i_ret = HI_IPC_CALL("hi_sml_queue_get", &st_para);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMCPY_S(pst_para, sizeof(hi_omci_tapi_queue_para_s), ((hi_uchar8 *)pv_data + 8),
		       sizeof(hi_omci_tapi_queue_para_s));

	hi_omci_tapi_systrace(HI_RET_SUCC, em_egr, ui_pri, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_tapi_queue_num_get
 Description : 获取端口的队列数目
 Input Parm  : hi_omci_tapi_egress_type_e em_egr,
 Output Parm : hi_uint32 *pui_num
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_tapi_queue_num_get(hi_omci_tapi_egress_type_e em_egr, hi_uint32 *pui_num)
{
	hi_sml_queue_num_s st_qnum = {0};
	hi_int32 i_ret;

	st_qnum.em_egr = (hi_sml_egress_type_e)em_egr;
	i_ret = HI_IPC_CALL("hi_sml_queue_num_get", &st_qnum);
	HI_OMCI_RET_CHECK(i_ret);

	*pui_num = st_qnum.ui_num;

	hi_omci_tapi_systrace(HI_RET_SUCC, em_egr, *pui_num, 0, 0);
	return HI_RET_SUCC;
}


/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
