/******************************************************************************

                  Copyright (C), 2012-2022, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_tr069.h
  Version    : 初稿
  Author     :
  Creation   :
  Description: OMCI_TAPI TR069头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_TR069_H__
#define __HI_OMCI_TAPI_TR069_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_tapi_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_TAPI_TR069_ACS_LEN (256)
#define HI_OMCI_TAPI_TR069_STR_LEN (64)

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef struct {
	hi_uchar8      uc_acsaddr[HI_OMCI_TAPI_TR069_ACS_LEN];
	hi_uchar8      uc_auth_username[HI_OMCI_TAPI_TR069_STR_LEN];
	hi_uchar8      uc_auth_password[HI_OMCI_TAPI_TR069_STR_LEN];
} hi_omci_tapi_tr069_server_s;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
extern hi_int32 hi_omci_tapi_tr069_server_set(hi_omci_tapi_tr069_server_s *pst_server);
extern hi_int32 hi_omci_tapi_tr069_server_get(hi_omci_tapi_tr069_server_s *pst_server);
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_VOIP_H__*/

