/******************************************************************************

                  Copyright (C), 2012-2013, HSA

 ******************************************************************************
  Filename   : hi_omci_tapi_upgrade.h
  Version    : 初稿
  Author     : chenshibin 00184653
  Creation   : 2015-1-27
  Description:
******************************************************************************/

#ifndef __HI_OMCI_TAPI_UPGRADE_H__
#define __HI_OMCI_TAPI_UPGRADE_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#include "hi_upgrade.h"
/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
extern hi_uint32 hi_omci_tapi_upgrade_start(hi_uint32 ui_section, hi_upgrade_sharding_header_s *pst_sharding_header);
extern hi_uint32 hi_omci_tapi_upgrade_stop(hi_uint32 ui_krnlcrc, hi_uint32 ui_rtfscrc, hi_uint32 ui_pkgcrc);
extern hi_uint32 hi_omci_tapi_upgrade_write(hi_uchar8 *puc_buff, hi_uint32 ui_len);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_UPGRADE_H__*/

