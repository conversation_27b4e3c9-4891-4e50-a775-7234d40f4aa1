/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_uni.h
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-24
  Description: OMCI_TAPI UNI头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_UNI_H__
#define __HI_OMCI_TAPI_UNI_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_tapi_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_ETH_MAX_NUM 8

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef enum {
	HI_OMCI_TAPI_PORT_10_100_BASET_E = 0,
	HI_OMCI_TAPI_PORT_10_100_1000_BASET_E
} hi_omci_tapi_port_type_e;

typedef enum {
	HI_OMCI_TAPI_ETH_AUTO_NEG_E = 0,   /* 自动协商 */
	HI_OMCI_TAPI_ETH_HALF_10M_E,       /* 半双工10M */
	HI_OMCI_TAPI_ETH_HALF_100M_E,      /* 半双工100M */
	HI_OMCI_TAPI_ETH_HALF_1000M_E,     /* 半双工1000M */
	HI_OMCI_TAPI_ETH_DUP_10M_E,        /* 全双工10M */
	HI_OMCI_TAPI_ETH_DUP_100M_E,       /* 全双工100M */
	HI_OMCI_TAPI_ETH_DUP_1000M_E,       /* 全双工1000M */
	HI_OMCI_TAPI_ETH_DUP_2500M_E       /* 全双工2500M */
} hi_omci_tapi_port_ethmode_e;

typedef enum {
	HI_OMCI_TAPI_LINK_UP_E = 0,
	HI_OMCI_TAPI_LINK_DOWN_E
} hi_omci_tapi_port_link_type_e;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_port_type_get
 Description : 获取端口类型
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_port_type_e *pem_type
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_type_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_port_type_e *pem_type);

/******************************************************************************
 Function    : hi_omci_tapi_port_state_set
 Description : 配置端口使能、禁止
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_enable
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_state_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_enable);

/******************************************************************************
 Function    : hi_omci_tapi_port_state_get
 Description : 获取端口使能、禁止状态
 Input Parm  : hi_omci_tapi_port_e em_port,
 Output Parm : hi_uint32 *pui_enable
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_state_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_enable);

/******************************************************************************
 Function    : hi_omci_tapi_port_ethmode_set
 Description : 设置以太端口工作模式
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_port_ethmode_e em_mode
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_ethmode_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_port_ethmode_e em_mode);

/******************************************************************************
 Function    : hi_omci_tapi_port_ethmode_get
 Description : 获取以太端口当前的工作模式
               如果端口工作模式配置为自适应，此时获取的是协商后的工作模式
               如果端口工作模式配置为强制速率双工，如果对端不能适应，则双方使用最低
               工作模式。因此函数获取的工作模式，不一定与hi_omci_tapi_port_ethmode_set
               配置的一致。
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_port_ethmode_e em_mode
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_ethmode_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_port_ethmode_e *pem_mode);

/******************************************************************************
 Function    : hi_omci_tapi_port_link_get
 Description : 获取端口LINK状态
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : hi_omci_tapi_port_link_type_e *pem_link
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_link_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_port_link_type_e *pem_link);

/******************************************************************************
 Function    : hi_omci_tapi_port_loopback_set
 Description : 配置端口环回
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_uint32 ui_enable
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_loopback_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_enable);

/******************************************************************************
 Function    : hi_omci_tapi_port_loopback_get
 Description : 获取端口环回状态
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : hi_uint32 *pui_enable
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_loopback_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_enable);

/******************************************************************************
 Function    : hi_omci_tapi_port_loopback_get
 Description : 配置端口流控
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_uint32 ui_enable
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_pause_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_enable);

/******************************************************************************
 Function    : hi_omci_tapi_port_pause_get
 Description : 获取端口流控是否使能
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : hi_uint32 *pui_enable
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_pause_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_enable);

/******************************************************************************
 Function    : hi_omci_tapi_port_incar_set
 Description : 配置以太端口流量抑制
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_carid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_incar_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_cir, hi_uint32 ui_cbs,
		hi_uint32 ui_pir, hi_uint32 ui_pbs);

/******************************************************************************
 Function    : hi_omci_tapi_port_outcar_set
 Description : 配置以太端口流量整形
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_carid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_outcar_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_cir);

/******************************************************************************
 Function    : hi_omci_tapi_port_mtu_set
 Description : 配置端口最大允许帧长
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_uint32 ui_mtu
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_mtu_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_mtu);

/******************************************************************************
 Function    : hi_omci_tapi_port_mtu_set
 Description : 配置端口最大允许帧长
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : hi_uint32 *pui_mtu
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_mtu_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_mtu);

/******************************************************************************
 Function    : hi_omci_tapi_port_pptpinstid_get
 Description : 根据端口类型获取pptp实例编号
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : hi_uint32 *pui_mtu
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_pptpinstid_get(hi_omci_tapi_port_e em_port, hi_ushort16 *pus_instid);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_UNI_H__*/
