/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_stat.h
  Version    : 初稿
  Author     : owen
  Creation   : 2014-1-15
  Description: OMCI TAPI STAT头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_STAT_H__
#define __HI_OMCI_TAPI_STAT_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_tapi_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef struct {
	hi_uint32   ui_correctbytes; /*Corrected bytes*/
	hi_uint32   ui_correctcode;  /*Corrected code words*/
	hi_uint32   ui_uncorrectcode;/*Uncorrectable code words*/
	hi_uint32   ui_totalcode;    /*Total code words*/
	hi_ushort16 us_fecseconds;   /*FEC seconds*/
	hi_ushort16 us_resv;
} hi_omci_tapi_stat_fecpm_s;

typedef struct {
	hi_uint32   ui_gemport;
	hi_uint32   ui_transframes;    /*Transmitted GEM frames*/
	hi_uint32   ui_rcvframes;      /*Received GEM frames*/
	hi_ulong64  ul_rcvpayload;     /*Received payload bytes*/
	hi_ulong64  ul_transpayload;   /*Transmitted payload bytes*/
	hi_uint32   ui_encryerrs;      /*Encryption key errors*/
} hi_omci_tapi_stat_gemctppm_s;

typedef struct {
	hi_ulong64  ui_fcserrors;
	hi_ulong64  ui_excesscnt;    /*Excessive collision counter*/
	hi_ulong64  ui_latecnt;      /*Late collision counter*/
	hi_ulong64  ui_frmlong;      /*Frames too long*/
	hi_ulong64  ui_overflowrcv;  /*Buffer overflows on receive*/
	hi_ulong64  ui_overflowtrans;/*Buffer overflows on transmit*/
	hi_ulong64  ui_singlcoll;    /*Single collision frame counter*/
	hi_ulong64  ui_mulcoll;      /*Multiple collisions frame counter*/
	hi_ulong64  ui_sqe;          /*SQE counter*/
	hi_ulong64  ui_defertrans;   /*Deferred transmission counter*/
	hi_ulong64  ui_mactranserr;  /*Internal MAC transmit error counter*/
	hi_ulong64  ui_senseerr;     /*Carrier sense error counter*/
	hi_ulong64  ui_alignerr;     /*Alignment error counter*/
	hi_ulong64  ui_macrcverr;    /*Internal MAC receive error counter*/
	hi_ulong64  ui_fragments;

	hi_ulong64  ui_dropsevt;
	hi_ulong64  ui_octets;
	hi_ulong64  ui_packets;
	hi_ulong64  ui_bcpkt;
	hi_ulong64  ui_mcpkt;
	hi_ulong64  ui_crcerror;
	hi_ulong64  ui_undersizepkt;
	hi_ulong64  ui_jabber;
	hi_ulong64  ui_pkt64;
	hi_ulong64  ui_pkt65to127;
	hi_ulong64  ui_pkt128to255;
	hi_ulong64  ui_pkt256to511;
	hi_ulong64  ui_pkt512to1023;
	hi_ulong64  ui_pkt1024to1518;

	hi_ulong64  ui_tx_dropsevt;
	hi_ulong64  ui_tx_octets;
	hi_ulong64  ui_tx_packets;
	hi_ulong64  ui_tx_bcpkt;
	hi_ulong64  ui_tx_mcpkt;
	hi_ulong64  ui_tx_crcerror;
	hi_ulong64  ui_tx_undersizepkt;
	hi_ulong64  ui_tx_jabber;
	hi_ulong64  ui_tx_pkt64;
	hi_ulong64  ui_tx_pkt65to127;
	hi_ulong64  ui_tx_pkt128to255;
	hi_ulong64  ui_tx_pkt256to511;
	hi_ulong64  ui_tx_pkt512to1023;
	hi_ulong64  ui_tx_pkt1024to1518;
} hi_omci_tapi_stat_eth_s;

typedef struct {
	hi_uint32   ui_discardcnt;          /* BridgeLearningEntry DiscardCount */
} hi_omci_tapi_stat_bridge_s;

typedef struct {
	hi_uint32   ui_forwardcnt;        /* ForwardedFrame Counter */
	hi_uint32   ui_delaydiscardcnt;   /* DelayExceededDiscardCounter */
	hi_uint32   ui_mtudiscardcnt;     /* MTUExceededDiscardCounter */
	hi_uint32   ui_rcvcnt;            /* ReceivedFrame Counter */
	hi_uint32   ui_discardcnt;        /* ReceivedAndDiscardedCounter */
} hi_omci_tapi_stat_brport_s ;

typedef struct {
	hi_uint32 ui_psbdhecerr;
	hi_uint32 ui_xgtchecerr;
	hi_uint32 ui_unknownprofile;
	hi_uint32 ui_transxgem;
	hi_uint32 ui_fragxgem;
	hi_uint32 ui_xgemheclost;
	hi_uint32 ui_xgemkeyerr;
	hi_uint32 ui_xgemhecerr;
} hi_omci_tapi_stat_xgpontcpm_s;

typedef struct {
	hi_uint32 ui_ploammicerr;           /* PLOAM MIC error count */
	hi_uint32 ui_dsploammesgcnt;        /* Downstream PLOAM messages count */
	hi_uint32 ui_profilemesgrcv;        /* Profile messages received */
	hi_uint32 ui_rangingtimemesgrcv;    /* Ranging_time messages received */
	hi_uint32 ui_deactiveonuidmesgrcv;  /* Deactivate_ONU-ID messages received */
	hi_uint32 ui_disablesnmesgrcv;      /* Disable_serial_number messages received */
	hi_uint32 ui_reqregmesgrcv;         /* Request_registration messages received */
	hi_uint32 ui_assignallocidmesgrcv;  /* Assign_alloc-ID messages received */
	hi_uint32 ui_keycontrolmesgrcv;     /* Key_control messages received */
	hi_uint32 ui_sleepallowmesgrcv;     /* Sleep_allow messages received */
	hi_uint32 ui_baselineomcimesgrcv;   /* Baseline OMCI messages received count */
	hi_uint32 ui_extomcimesgrcv;        /* Extended OMCI messages received count */
	hi_uint32 ui_assignonuidmesgrcv;    /* Assign_ONU-ID messages received */
	hi_uint32 ui_omcimicerr;            /* OMCI MIC error count */
} hi_omci_tapi_stat_xgpondownmng_pm_s;

typedef struct {
	hi_uint32 ui_upsploammesgcnt;       /* Upstream PLOAM messages count */
	hi_uint32 ui_snonumesgcnt;          /* Serial_number_ONU message count */
	hi_uint32 ui_regmemesgcnt;          /* Registration message count */
	hi_uint32 ui_keyrepmesgcnt;         /* Key_report message count */
	hi_uint32 ui_ackmesgcnt;            /* Acknowledge message count */
	hi_uint32 ui_sleepreqmesgcnt;       /* Sleep_request message count */
} hi_omci_tapi_stat_xgponupmng_pm_s;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_stat_fec_get
 Description : 获取FEC统计
 Input Parm  : 无
 Output Parm : hi_omci_tapi_stat_fecpm_s *pst_stat
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_stat_fec_get(hi_omci_tapi_stat_fecpm_s *pst_stat);

/******************************************************************************
 Function    : hi_omci_tapi_stat_gemport_get
 Description : 获取GEMPORT统计
 Input Parm  : hi_uint32 gemportid,
               hi_omci_tapi_stat_gemctppm_s *pst_stat
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_stat_gemport_get(hi_uint32 gemportid, hi_omci_tapi_stat_gemctppm_s *pst_stat);

/******************************************************************************
 Function    : hi_omci_tapi_stat_eth_get
 Description : 获取以太端口统计
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_stat_eth_s *pst_stat
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_stat_eth_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_stat_eth_s *pst_stat);

/******************************************************************************
 Function    : hi_omci_tapi_stat_eth_clr
 Description : 清空以太端口统计
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_stat_eth_clr(hi_omci_tapi_port_e em_port);

/******************************************************************************
 Function    : hi_omci_tapi_stat_bridge_get
 Description : 获取桥统计
 Input Parm  : hi_omci_tapi_stat_bridge_s *pst_stat
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_stat_bridge_get(hi_omci_tapi_stat_bridge_s *pst_stat);

/******************************************************************************
 Function    : hi_omci_tapi_stat_brport_get
 Description : 获取桥端口统计
 Input Parm  : hi_omci_tapi_stat_brport_s *pst_stat
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_stat_brport_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_stat_brport_s *pst_stat);

extern hi_int32 hi_omci_tapi_stat_tc_get(hi_omci_tapi_stat_xgpontcpm_s *pst_stat);
extern hi_int32 hi_omci_tapi_stat_galeth_get(hi_uint32 *pui_stat);

extern hi_int32 hi_omci_tapi_stat_downmng_get(hi_omci_tapi_stat_xgpondownmng_pm_s *pst_stat);
extern hi_int32 hi_omci_tapi_stat_upmng_get(hi_omci_tapi_stat_xgponupmng_pm_s *pst_stat);
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_STAT_H__*/
