/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_tapi_vlan.h
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : VLAN配置接口头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_VLAN_H__
#define __HI_OMCI_TAPI_VLAN_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_tapi_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_TAPI_TPID_NUM 3

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef enum {
	HI_OMCI_TAPI_UNTAG_E = 0,
	HI_OMCI_TAPI_PRI_TAG_E,
	HI_OMCI_TAPI_VLAN_TAG_E
} hi_omci_tapi_tag_type_e;

typedef enum {
	HI_OMCI_TAPI_TAG_IGNORE_E = 0,   /* 在端口上不处理 */
	HI_OMCI_TAPI_TAG_DISC_E,         /* 丢弃 */
	HI_OMCI_TAPI_TAG_ADD_DEFAULT_E,  /* 入端口添加缺省TAG */
	HI_OMCI_TAPI_TAG_DEL_DEFAULT_E   /* 出端口删除 */
} hi_omci_tapi_tag_act_e;

typedef enum {
	HI_OMCI_TAPI_TAG_IGR_E = 0,   /* 输入方向 */
	HI_OMCI_TAPI_TAG_EGR_E,       /* 输出方向 */
} hi_omci_tapi_tag_direction_e;

typedef enum {
	HI_OMCI_TAPI_TPID_DEFAULT_E = 0,  /* 0x8100 */
	HI_OMCI_TAPI_TPID_INPUT_E,        /* 0x88a8 / 0x9100 for filter */
	HI_OMCI_TAPI_TPID_OUTPUT_E        /* 0x88a8 / 0x9100 for treatment */
} hi_omci_tapi_tpid_type_e;

typedef struct {
	hi_uint32 aui_tpid[HI_OMCI_TAPI_TPID_NUM];
} hi_omci_tapi_tpid_s;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/

/*****************************************************************************
  Function     : hi_omci_tapi_vlan_filter_set
  Description  : 配置VLAN过滤使能
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_uint32 ui_enable
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_vlan_filter_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_enable);

/*****************************************************************************
  Function     : hi_omci_tapi_vlan_set
  Description  : 配置端口VLAN
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_uint32 ui_vid
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_vlan_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_vid);

/*****************************************************************************
  Function     : hi_omci_tapi_vlan_filter_get
  Description  : 获取VLAN过滤使能状态
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_uint32 pui_enable
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_vlan_filter_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_enable);

/*****************************************************************************
  Function     : hi_omci_tapi_vlan_del
  Description  : 删除端口VLAN
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_uint32 ui_vid
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_vlan_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_vid);

/******************************************************************************
 Function    : hi_omci_tapi_tagact_set
 Description : 端口TAG处理动作
               UNTAG报文不能配置无处理动作
               添加缺省TAG动作对TAG报文无效
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_tag_type_e em_type,
               hi_omci_tapi_tag_direction_e em_dir,
               hi_omci_tapi_tag_act_e em_act
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tagact_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_tag_type_e em_type,
					hi_omci_tapi_tag_direction_e em_dir, hi_omci_tapi_tag_act_e em_act);

/******************************************************************************
 Function    : hi_omci_tapi_deftag_set
 Description : 配置端口缺省TAG
 Input Parm  : hi_omci_tapi_port_e em_port,       报文输入端口
               hi_uint32 ui_vlan,           缺省VLAN
               hi_omci_tapi_tpid_type_e em_tpid   TPID选择:CTAG或STAG
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_deftag_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_vlan,
					hi_omci_tapi_tpid_type_e em_tpid);

/******************************************************************************
 Function    : hi_omci_tapi_deftag_get
 Description : 配置端口缺省TAG
 Input Parm  : hi_omci_tapi_port_e em_port,       报文输入端口
 Output Parm : hi_uint32 *pui_vlan,           缺省VLAN
               hi_omci_tapi_tpid_type_e *pem_tpid   TPID选择:CTAG或STAG
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_deftag_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_vlan,
					hi_omci_tapi_tpid_type_e *pem_tpid);

/******************************************************************************
 Function    : hi_omci_tapi_tpid_set
 Description : 配置TPID
               提供3种TPID配置
 Input Parm  : hi_omci_tapi_tpid_type_e em_type,
               hi_uint32 ui_tpid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tpid_set(hi_omci_tapi_tpid_type_e em_type, hi_uint32 ui_tpid);

extern hi_int32 hi_omci_tapi_untag_addtag_set(hi_omci_tapi_port_e em_port,
		hi_omci_tapi_mark_s *pst_mark);

extern hi_int32 hi_omci_tapi_untag_addtag_del(hi_omci_tapi_port_e em_port,
		hi_omci_tapi_mark_s *pst_mark);

/******************************************************************************
 Function    : hi_omci_tapi_vlan_translation_set
 Description : 配置一层VLAN切换
               配置前先根据treatment规则确定有多少个CVLAN切换成SVLAN，
               以确定是1:1切换，还是N:1切换。当新增规则，可能导致1:1到N:1
               的模式切换
 Input Parm  : hi_omci_tapi_port_e em_port,   报文输入端口
               hi_uint32 ui_cvlan,      用户VLAN
               hi_uint32 ui_svlan,      要切换的服务VLAN
               hi_uint32 ui_tpid_en     TPID切换标识。一般业务都会使能切换，将TPID
                                        切换为0x88a8，此值可通过aui_tpid[HI_OMCI_TAPI_TPID_OUTPUT]
                                        配置。1:1 VLAN可以带用户TAG上行，此时需要禁止TPID切换
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_vlan_translation_set(hi_omci_tapi_port_e em_port,
		hi_uint32 ui_cvlan, hi_uint32 ui_svlan, hi_uint32 ui_tpid_en);

/******************************************************************************
 Function    : hi_omci_tapi_vlan_translation_del
 Description : 删除VLAN切换规则
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_cvlan
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_vlan_translation_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_cvlan);

extern hi_int32 hi_omci_tapi_vlan_translation_opt_set(hi_omci_tapi_port_e em_port,
		hi_omci_tapi_mark_s *pst_mark);

hi_int32 hi_omci_tapi_vlan_translation_opt_del(hi_omci_tapi_port_e em_port, hi_omci_tapi_mark_s *pst_mark);

/******************************************************************************
 Function    : hi_omci_tapi_tls_ctag_set
 Description : TLS(transparent LAN service)的方法1: untag和ctag报文都加上
               SVLAN tag。可与VLAN切换规则共存，当数据流未能VLAN切换规则时，
               执行TLS处理；untag报文不会添加端口缺省TAG，而是添加TLS SVLAN TAG
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_svlan
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tls_ctag_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_svlan);

/******************************************************************************
 Function    : hi_omci_tapi_tls_ctag_del
 Description : 删除TLS方法1规则
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_svlan
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tls_ctag_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_svlan);

/******************************************************************************
 Function    : hi_omci_tapi_tls_stag_set
 Description : TLS方法2: double-tag报文，如果SVLAN-TAG合法则通过或者切换外层VLAN
               否则丢弃报文。此函数配置
 Input Parm  : hi_omci_tapi_port_e em_port,       报文输入端口
               hi_uint32 ui_svlan,          外层TAG的匹配VLAN
               hi_uint32 ui_svlan_trans     外层VLAN切换值，如果是非法值，表示不切换
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tls_stag_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_svlan, hi_uint32 ui_svlan_trans);

/******************************************************************************
 Function    : hi_omci_tapi_tls_stag_del
 Description : 删除TLS方法2规则
 Input Parm  : hi_omci_tapi_port_e em_port,       报文输入端口
               hi_uint32 ui_svlan           外层TAG的匹配VLAN
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tls_stag_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_svlan);


/******************************************************************************
 Function    : hi_omci_tapi_port_qinq_set
 Description : 配置端口QinQ规则
 Input Parm  : hi_omci_tapi_port_e em_port  报文输入端口
               hi_uint32 ui_cvlan           用户VLAN
               hi_uint32 ui_svlan           添加的Service TAG VID
               hi_uint32 tpid_en            使能则，指定TPID为Output TPID；
                                            否则拷贝内层VLAN TAG TPID
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_qinq_set(hi_omci_tapi_port_e em_port,
		hi_uint32 ui_cvlan, hi_uint32 ui_svlan, hi_uint32 tpid_en);


/******************************************************************************
 Function    : hi_omci_tapi_qinq_set
 Description : 配置QinQ规则
 Input Parm  : hi_omci_tapi_port_e em_port  报文输入端口
               hi_uint32 ui_cvlan           用户VLAN
               hi_uint32 ui_svlan           添加的Service TAG VID
               hi_uint32 tpid_en            使能则，指定TPID为Output TPID；
                                            否则拷贝内层VLAN TAG TPID
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_qinq_set(hi_omci_tapi_port_e em_port,
				      hi_uint32 ui_cvlan, hi_uint32 ui_svlan, hi_uint32 tpid_en);


/******************************************************************************
 Function    : hi_omci_tapi_port_qinq_del
 Description : 删除QinQ规则
 Input Parm  : hi_omci_tapi_port_e em_port  报文输入端口
               hi_uint32 ui_svlan           添加的Service TAG VID
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_port_qinq_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_svlan);

/******************************************************************************
 Function    : hi_omci_tapi_qinq_del
 Description : 删除QinQ规则
 Input Parm  : hi_omci_tapi_port_e em_port  报文输入端口
               hi_uint32 ui_cvlan           用户VLAN
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_qinq_del(hi_omci_tapi_port_e em_port, hi_uint32 ui_cvlan);

extern hi_int32 hi_omci_tapi_qinq_full_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_mark_s *pst_mark);

extern hi_int32 hi_omci_tapi_qinq_full_del(hi_omci_tapi_port_e em_port, hi_omci_tapi_mark_s *pst_mark);

extern hi_int32 hi_omci_tapi_qinq_vlan_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_mark_s *pst_mark);

extern hi_int32 hi_omci_tapi_qinq_vlan_del(hi_omci_tapi_port_e em_port, hi_omci_tapi_mark_s *pst_mark);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_VLAN_H__*/

