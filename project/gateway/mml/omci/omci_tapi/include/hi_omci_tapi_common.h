/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_tapi_common.h
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : Qos配置接口头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_COMMON_H__
#define __HI_OMCI_TAPI_COMMON_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_uspace.h"
#include "hi_basic.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_TAPI_INVALID    (-1)
#define HI_OMCI_TAPI_TPID_INVALID    HI_OMCI_TAPI_INVALID
#define HI_OMCI_TAPI_PRI_INVALID     HI_OMCI_TAPI_INVALID
#define HI_OMCI_TAPI_CFI_INVALID     HI_OMCI_TAPI_INVALID
#define HI_OMCI_TAPI_VLAN_INVALID    HI_OMCI_TAPI_INVALID

#define HI_OMCI_TAPI_PRI_MAX        (7)
#define HI_OMCI_TAPI_PRI_UNTAG      (HI_OMCI_TAPI_PRI_MAX + 1)
#define HI_OMCI_TAPI_PRI_DSCP       (10)
#define HI_OMCI_TAPI_IPHOST_INSTID  (0)
#define HI_MAC_LEN 6
#define HI_IPV4_LEN 4
#define HI_IPV6_LEN 16

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef enum {
	HI_OMCI_TAPI_ETH0_E   = 0,
	HI_OMCI_TAPI_ETH1_E   = 1,
	HI_OMCI_TAPI_ETH2_E   = 2,
	HI_OMCI_TAPI_ETH3_E   = 3,
	HI_OMCI_TAPI_ETH4_E   = 4,
	HI_OMCI_TAPI_ETH5_E   = 5,
	HI_OMCI_TAPI_ETH6_E   = 6,
	HI_OMCI_TAPI_ETH7_E   = 7,
	HI_OMCI_TAPI_PON_E    = 8,
	HI_OMCI_TAPI_VEIP_E   = 9,
	HI_OMCI_TAPI_IPHOST_E   = 10,
	HI_OMCI_TAPI_VOIP_E   = 11,
} hi_omci_tapi_port_e;

typedef enum {
	HI_OMCI_TAPI_IPV4 = 0,
	HI_OMCI_TAPI_IPV6
} hi_omci_tapi_ip_version_e;

typedef struct {
	hi_uint32 ui_cir;
	hi_uint32 ui_cbs;
	hi_uint32 ui_pir;
	hi_uint32 ui_pbs;
	hi_uint32 ui_pps_en;
} hi_omci_tapi_car_s;

typedef struct {
	hi_uint32 ui_cvlan;
	hi_uint32 ui_cpri;
	hi_int32 ui_ethtype;
	hi_uint32 ui_svlan;
	hi_uint32 ui_spri;      /* 无效值表示根据DSCP映射 */

	/* TPID切换标识。一般业务都会使能切换，将TPID
	 * 切换为0x88a8，此值可通过aui_tpid[HI_SML_TPID_OUTPUT]
	 * 配置。1:1 VLAN可以带用户TAG上行，此时需要禁止TPID切换
	 */
	hi_uint32 ui_tpid_en;

} hi_omci_tapi_mark_s;
/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_COMMON_H__*/

