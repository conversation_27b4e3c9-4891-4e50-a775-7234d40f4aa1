/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_optical.h
  Version    : 初稿
  Author     : owen
  Creation   : 2014-1-17
  Description: OMCI光器件TAPI头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_OPTICAL_H__
#define __HI_OMCI_TAPI_OPTICAL_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_PLOAM_REGSTATE_E  5
#define HI_OMCI_OPT_STRLEN 16
/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef struct {
	float ui_temprature;    /* degree C */
	float ui_voltage;       /* V */
	float ui_ibias;         /* mA */
	float ui_txpower;       /* mW */
	float ui_rxpower;       /* mW */
} hi_omci_tapi_optical_attr_s;

struct hi_omci_tapi_optical_info {
	uint8_t module_type;
	uint8_t module_sub_type;
	uint8_t used_type;
	uint8_t encapsulation;
	uint16_t tx_wavelength;
	uint16_t rx_wavelength;
	uint8_t vendor_name[HI_OMCI_OPT_STRLEN];
	uint8_t vendor_pn[HI_OMCI_OPT_STRLEN];
	uint8_t vendor_sn[HI_OMCI_OPT_STRLEN];
};

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_optical_attr_get
 Description : 获取光器件属性
 Input Parm  : hi_omci_tapi_optical_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_optical_attr_get(hi_uint32 ui_creat_flag, hi_omci_tapi_optical_attr_s *pst_attr);
int32_t hi_omci_tapi_optical_info_get(struct hi_omci_tapi_optical_info *attr);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_OPTICAL_H__*/
