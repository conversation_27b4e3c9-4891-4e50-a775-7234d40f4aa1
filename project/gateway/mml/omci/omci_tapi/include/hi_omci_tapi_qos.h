/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_tapi_qos.h
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : Qos配置接口头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_QOS_H__
#define __HI_OMCI_TAPI_QOS_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_tapi_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_TAPI_DSCP_NUM     64

#define HI_OMCI_TAPI_CARID_INVALID    (0xffffffff)
#define HI_OMCI_TAPI_ETHTYPE_INVALID  (-1)

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef enum {
	HI_OMCI_TAPI_EGRESS_TCONT0_E = 0,
	HI_OMCI_TAPI_EGRESS_TCONT1_E,
	HI_OMCI_TAPI_EGRESS_TCONT2_E,
	HI_OMCI_TAPI_EGRESS_TCONT3_E,
	HI_OMCI_TAPI_EGRESS_TCONT4_E,
	HI_OMCI_TAPI_EGRESS_TCONT5_E,
	HI_OMCI_TAPI_EGRESS_TCONT6_E,
	HI_OMCI_TAPI_EGRESS_TCONT7_E,
	HI_OMCI_TAPI_EGRESS_TCONT8_E,
	HI_OMCI_TAPI_EGRESS_UNI0_E = 17,
	HI_OMCI_TAPI_EGRESS_UNI1_E,
	HI_OMCI_TAPI_EGRESS_UNI2_E,
	HI_OMCI_TAPI_EGRESS_UNI3_E,
    HI_OMCI_TAPI_EGRESS_UNI4_E,
    HI_OMCI_TAPI_EGRESS_UNI5_E,
    HI_OMCI_TAPI_EGRESS_UNI6_E,
    HI_OMCI_TAPI_EGRESS_UNI7_E,
	HI_OMCI_TAPI_EGRESS_VEIP_E,
	HI_OMCI_TAPI_EGRESS_NUM_E,
} hi_omci_tapi_egress_type_e;

typedef struct {
	hi_uint32 aui_pri[HI_OMCI_TAPI_DSCP_NUM];
} hi_omci_tapi_dscp_map_s;

typedef struct {
	hi_uint32 ui_cvlan;
	hi_uint32 ui_cpri;
	hi_uint32 ui_ethtype;
	hi_int32 ui_spri;      /* 无效值表示根据DSCP映射 */
} hi_omci_tapi_qos_pri_s;

typedef struct {
	hi_uint32 ui_size;              /* 私有长度 */
	hi_uint32 ui_max_size;          /* 最大深度 */
	hi_uint32 ui_weight;            /* WRR调度下的队列权重 */
	hi_uint32 ui_min_green_thrd;    /* 绿色报文最少丢弃阈值，队列深度小于阈值报文全部通过；大于则概率丢弃 */
	hi_uint32 ui_max_green_thrd;    /* 绿色报文最大丢弃阈值，队列深度大于阈值报文全部丢弃；小于则概率丢弃 */
	hi_uint32 ui_green_prblt;       /* 绿色报文丢弃概率 */
	hi_uint32 ui_min_yellow_thrd;   /* 黄色报文最少丢弃阈值，队列深度小于阈值报文全部通过；大于则概率丢弃 */
	hi_uint32 ui_max_yellow_thrd;   /* 黄色报文最大丢弃阈值，队列深度大于阈值报文全部丢弃；小于则概率丢弃 */
	hi_uint32 ui_yellow_prblt;      /* 黄色报文丢弃概率 */
} hi_omci_tapi_queue_para_s;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_tapi_defpri_set
 Description : 配置端口缺省优先级
 Input Parm  : hi_omci_tapi_port_e em_port 以太端口
               hi_uint32 ui_pri 缺省优先级
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_defpri_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_pri);

/*****************************************************************************
  Function     : hi_omci_tapi_dscp_map_set
  Description  : 配置DSCP到P-bit的映射
  Input Param  : hi_omci_tapi_dscp_map_s *pst_map DSCP到PBIT的映射表
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_dscp_map_set(hi_omci_tapi_dscp_map_s *pst_map);

/******************************************************************************
 Function    : hi_omci_tapi_pbit_marking_set
 Description : 配置802.1p优先级marking规则
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_qos_pri_s *pst_mark
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_pri_marking_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_qos_pri_s *pst_mark);

/******************************************************************************
 Function    : hi_omci_tapi_pbit_marking_del
 Description : 删除802.1p优先级marking规则
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_omci_tapi_qos_pri_s *pst_mark
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_pri_marking_del(hi_omci_tapi_port_e em_port, hi_omci_tapi_qos_pri_s *pst_mark);

/******************************************************************************
 Function    : hi_omci_tapi_car_set
 Description : 配置CAR模板
 Input Parm  : hi_uint32 ui_carid,      CAR模板标识
               hi_omci_tapi_car_s *pst_car    CAR模板参数
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_car_set(hi_uint32 *ui_carid, hi_omci_tapi_car_s *pst_car);

/******************************************************************************
 Function    : hi_omci_tapi_car_get
 Description : 获取CAR模板参数
 Input Parm  : hi_uint32 ui_carid,      CAR模板标识
 Output Parm : hi_omci_tapi_car_s *pst_car    CAR模板参数
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_car_get(hi_uint32 ui_carid, hi_omci_tapi_car_s *pst_car);

/******************************************************************************
 Function    : hi_omci_tapi_car_del
 Description : 删除CAR模板
 Input Parm  : hi_uint32 ui_carid,      CAR模板标识
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_car_release(hi_uint32 ui_carid);

/******************************************************************************
 Function    : hi_omci_tapi_queue_num_get
 Description : 获取端口的队列数目
 Input Parm  : hi_omci_tapi_egress_type_e em_egr,
 Output Parm : hi_uint32 *pui_num
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_queue_num_get(hi_omci_tapi_egress_type_e em_egr, hi_uint32 *pui_num);

/******************************************************************************
 Function    : hi_omci_tapi_queue_set
 Description : 配置队列参数
               一个端口下有多个优先级队列，可通过端口和优先级索引到队列
 Input Parm  : hi_omci_tapi_egress_type_e em_egr,
               hi_uint32 ui_pri,
               hi_omci_tapi_queue_para_s *pst_para
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_queue_set(hi_omci_tapi_egress_type_e em_egr, hi_uint32 ui_pri,
				       hi_omci_tapi_queue_para_s *pst_para);

/******************************************************************************
 Function    : hi_omci_tapi_queue_set
 Description : 获取队列参数
 Input Parm  : hi_omci_tapi_egress_type_e em_egr,
               hi_uint32 ui_pri,
 Output Parm : hi_omci_tapi_queue_para_s *pst_para
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_queue_get(hi_omci_tapi_egress_type_e em_egr, hi_uint32 ui_pri,
				       hi_omci_tapi_queue_para_s *pst_para);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_QOS_H__*/
