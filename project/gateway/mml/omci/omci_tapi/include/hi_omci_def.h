/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_def.h
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_10_27

******************************************************************************/
#ifndef __HI_OMCI_DEF_H__
#define __HI_OMCI_DEF_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */
#include "hi_util_log.h"
#define HI_DISABLE 0
#define HI_ENABLE 1
#define HI_MAC_LEN 6
#define HI_MEMBER_OFFSET(type, mem) ((hi_uint32)&((type *)0)->mem)/*lint !e413*/
#define HI_PRINT_MEM( len, ptr )    { \
		hi_uint32 i; hi_uchar8 *ptr_tmp = (hi_uchar8*)ptr; \
		for ( i=0; i < len; i++ ) \
		{  if ( 0 == (i%16)) hi_os_printf("\r\n[0x%08x] : ",i+(hi_uint32)ptr); hi_os_printf("%02x ",(*(hi_uchar8*)(ptr_tmp+i))); } hi_os_printf("\r\n"); \
	}
#define HI_PRINT_MEM_UINT( len, ptr )    { \
		hi_uint32 i; hi_uchar8 *ptr_tmp = ptr; \
		for ( i=0; i < len; i+=sizeof(hi_uint32) ) \
		{  if ( 0 == (i%16)) hi_os_printf("\r\n[0x%08x] : ",i+(hi_uint32)ptr); hi_os_printf("%08x ",(*(hi_uint32*)(ptr_tmp+i))); } hi_os_printf("\r\n"); \
	}

#define HI_PRINT_MEM_UINT_HTON_OMCI_DBG( len, ptr )    { \
		hi_uint32 i; hi_uchar8 *ptr_tmp = (hi_uchar8*)ptr; \
		for ( i=0; i < len; i+=sizeof(hi_uint32) ) \
		{  if ( 0 == (i%16)) hi_os_printf("\r\n"); hi_os_printf("%08x ",HI_NTOHL(*(hi_uint32*)(ptr_tmp+i))); } hi_os_printf("\r\n"); \
	}

typedef hi_uint32(* HI_FUNCCALLBACK_EXT)(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);

/*数据库callbacktable表项里面的不同回调函数分类,
  目前分为两类，实体动作函数和告警函数*/
typedef enum {
	HI_OMCI_FUNC_ME_ACTION_E = 1,
	HI_OMCI_FUNC_ALARM_E,
	HI_OMCI_FUNC_STAT_E,
	HI_OMCI_FUNC_MAX_E,
} hi_omci_sql_function_type_e;

/*omci消息通用返回错误*/
typedef enum {
	HI_OMCI_PRO_ERR_NO_E             =   0x0,     /*command processed successfully*/
	HI_OMCI_PRO_ERR_PROCESS_ERR_E    =   0x1,     /*command processing error*/
	HI_OMCI_PRO_ERR_NOT_SUPPORTED_E  =   0x2,     /*command not supported*/
	HI_OMCI_PRO_ERR_PARA_ERR_E       =   0x3,     /*parameter error*/
	HI_OMCI_PRO_ERR_UNKNOWN_ME_E     =   0x4,     /*unknown managed entity*/
	HI_OMCI_PRO_ERR_UNKNOWN_IN_E     =   0x5,     /*unknown managed entity instance*/
	HI_OMCI_PRO_ERR_DEV_BUSY_E       =   0x6,     /*device busy*/
	HI_OMCI_PRO_ERR_IN_EXIST_E       =   0x7,     /*instance exists*/
	HI_OMCI_PRO_ERR_ATT_FAIL_E       =   0x9,     /*attribute(s) failed or unknown*/
} hi_omci_msg_errcode_e;


#define HI_OMCI_CALLBACK_MAIN_LIB   "main"

#define hi_omci_systrace( ui_ret, arg1, arg2, arg3, arg4)  \
	hi_systrace((hi_uint32)HI_SUBMODULE_OMCI_COM, ui_ret, arg1, arg2, arg3, arg4)

#define hi_omci_tapi_systrace( ui_ret, arg1, arg2, arg3, arg4)  \
	hi_systrace((hi_uint32)HI_SUBMODULE_OMCI_API, ui_ret, arg1, arg2, arg3, arg4)

#define hi_omci_ani_systrace( ui_ret, arg1, arg2, arg3, arg4)  \
	hi_systrace((hi_uint32)HI_SUBMODULE_OMCI_ME_ANI, ui_ret, arg1, arg2, arg3, arg4)

#define hi_omci_equ_systrace( ui_ret, arg1, arg2, arg3, arg4)  \
	hi_systrace((hi_uint32)HI_SUBMODULE_OMCI_ME_EQUIP, ui_ret, arg1, arg2, arg3, arg4)

#define hi_omci_l2_systrace( ui_ret, arg1, arg2, arg3, arg4)  \
	hi_systrace((hi_uint32)HI_SUBMODULE_OMCI_ME_LAYER2, ui_ret, arg1, arg2, arg3, arg4)

#define hi_omci_misc_systrace( ui_ret, arg1, arg2, arg3, arg4)  \
	hi_systrace((hi_uint32)HI_SUBMODULE_OMCI_ME_MISC, ui_ret, arg1, arg2, arg3, arg4)

#define hi_omci_err(fmt, arg...) printf(fmt, ##arg)

#define hi_omci_debug(fmt,arg...)                                        \
	do                                                                   \
	{                                                                    \
		if (hi_log_print_on(HI_SUBMODULE_OMCI_LIB, HI_DBG_LEVEL_DEBUG))  \
		{                                                                \
			printf("[%s(%d)]" fmt, __func__, __LINE__, ##arg);       \
		}                                                                \
	} while (0)

#define hi_omci_print( ui_level, fmt,arg...)                             \
	do                                                                   \
	{                                                                    \
		if (hi_log_print_on(HI_SUBMODULE_OMCI_COM, ui_level))            \
		{                                                                \
			printf("[%s(%d)]" fmt, __func__, __LINE__, ##arg);       \
		}                                                                \
	} while(0)

#define hi_omci_printmem(ui_dbglevel,puc_src,ui_len,fmt,arg...)          \
	do                                                                  \
	{                                                                    \
		if (hi_log_print_on(HI_SUBMODULE_OMCI_COM, ui_dbglevel))         \
		{                                                                \
			printf(fmt"\n", ##arg);                                      \
			hi_log_mem(puc_src, ui_len);                                 \
		}                                                                \
	} while(0)

#define HI_OMCI_RET_CHECK(ui_ret) if(HI_RET_SUCC != ui_ret) \
	{\
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);  \
		hi_omci_systrace( ui_ret, 0, 0, 0, 0);\
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E; \
	}

#define HI_OMCI_RET_EXT_CHECK(ui_ret, erropt) \
	if(HI_RET_SUCC != ui_ret) \
	{\
		erropt; \
		hi_omci_debug("[INFO]:ui_ret =0x%x, ui_line =%d\n", ui_ret, __LINE__);  \
		hi_omci_systrace( ui_ret, 0, 0, 0, 0);\
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E; \
	}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OMCI_DEF_H__ */
