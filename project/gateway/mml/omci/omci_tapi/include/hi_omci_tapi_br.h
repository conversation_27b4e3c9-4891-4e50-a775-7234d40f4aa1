/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_tapi_br.h
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : 桥配置接口头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_BR_H__
#define __HI_OMCI_TAPI_BR_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_tapi_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_TAPI_BR_FILTER_TBL_NUM 8

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef enum {
	HI_OMCI_TAPI_BR_FILTER_FWD_E = 0,
	HI_OMCI_TAPI_BR_FILTER_DROP_E,
} hi_omci_tapi_br_filter_mode_e;

typedef struct {
	hi_omci_tapi_br_filter_mode_e em_mode;
	hi_uint32 ui_index;
	hi_uchar8 auc_mac[HI_MAC_LEN];
	hi_ushort16 us_reserve;
} hi_omci_tapi_br_filter_s;

typedef struct {
	hi_uchar8 aui_mac[HI_MAC_LEN];
	hi_ushort16 us_static;
	hi_uint32 ui_age;
} hi_omci_tapi_br_table_s;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/

/*****************************************************************************
  Function     : hi_omci_tapi_br_learn_set
  Description  : 配置端口MAC学习使能
  Input Param  : hi_omci_tapi_port_e em_port
                 hi_uint32 ui_enable
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_br_learn_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_enable);

/*****************************************************************************
  Function     : hi_omci_tapi_br_learn_get
  Description  : 获取端口MAC学习使能
  Input Param  : hi_omci_tapi_port_e em_port
                 hi_uint32 ui_enable
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_br_learn_get(hi_omci_tapi_port_e em_port, hi_uint32 *ui_enable);

/*****************************************************************************
  Function     : hi_omci_tapi_br_num_set
  Description  : 配置端口MAC最大数目
  Input Param  : hi_omci_tapi_port_e port 端口号
                 hi_uint32 num 最大学习数目，0表示禁止学习数目限制
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_br_num_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_num);

/*****************************************************************************
  Function     : hi_omci_tapi_br_age_set
  Description  : 配置端口MAC地址老化时间
  Input Param  : hi_uint32 ui_time，0标识禁止老化
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_br_age_set(hi_uint32 ui_time);

/*****************************************************************************
  Function     : hi_omci_tapi_br_bridging_set
  Description  : 设置UNI端口桥接使能
  Input Param  : 无
  Output Param ：hi_uint32 ui_time
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_br_enable(hi_uint32 *ui_enable);

/*****************************************************************************
  Function     : hi_omci_tapi_get_br_age
  Description  : 获取端口MAC地址老化时间
  Input Param  : 无
  Output Param ：hi_uint32 ui_time
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_br_age_get(hi_uint32 *ui_time);

/*****************************************************************************
  Function     : hi_omci_tapi_br_filter_set
  Description  : 设置MAC地址过滤
                 如果配置的是白名单，且当前MAC学习禁止，需要添加静态MAC转发表项
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_omci_tapi_br_filter_s *pst_filter
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_br_filter_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_br_filter_s *pst_filter);

/*****************************************************************************
  Function     : hi_omci_tapi_br_filter_get
  Description  : 基于端口获取MAC地址过滤
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_omci_tapi_br_filter_s *pst_filter
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_br_filter_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_br_filter_s *pst_filter);

/*****************************************************************************
  Function     : hi_omci_tapi_del_br_filter
  Description  : 删除MAC地址过滤
  Input Param  : hi_omci_tapi_port_e em_port,
                 hi_omci_tapi_br_filter_s *pst_filter
  Output Param ：
  Return       : hi_int32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_br_filter_del(hi_omci_tapi_port_e em_port, hi_omci_tapi_br_filter_s *pst_filter);

/******************************************************************************
 Function    : hi_omci_tapi_br_table_get
 Description : 获取bridge转发表项
               根据ui_offset查找ui_size个转发表项
 Input Parm  : hi_omci_tapi_port_e em_port,
               hi_uint32 ui_offset,
               hi_uint32 *pui_size 输入希望获取的表项个数
 Output Parm : hi_uint32 *pui_size 输出实际获取的表项个数
               hi_omci_tapi_br_table_s *pst_table,
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_br_table_get(hi_omci_tapi_port_e em_port, hi_uint32 *pui_size,
		hi_omci_tapi_br_table_s *pst_table);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_BR_H__*/
