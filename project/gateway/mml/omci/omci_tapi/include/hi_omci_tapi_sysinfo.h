/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_sysinfo.h
  Version    : 初稿
  Author     : owen
  Creation   : 2013-10-25
  Description: 系统信息头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_SYSINFO_H__
#define __HI_OMCI_TAPI_SYSINFO_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_tapi_common.h"
#define HI_PON_LOID_LEN 24
#define HI_PON_LOIDPWD_LEN 12

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef struct {
	hi_uint32 ui_vendor_id;         /* Vendor id */
	hi_uchar8 auc_version[14];      /* product Version */
	hi_uchar8 auc_soft_version[14]; /* software version */
	hi_uchar8 auc_equid[20];        /* Equipment id */
	hi_uint32 ui_product_code;      /* Vendor Product Code */
	hi_uint32 ui_product_mode;      /* 产品形态 0:HGU 1:SFU */
	hi_uint32 ui_port_num;          /* 端口数目1~4 */
	hi_uint32 ui_ge_num;          /* 端口GE数目1~4 */
	hi_uint32 ui_fe_num;          /* 端口FE数目1~4 */
	hi_uint32 ui_pots_num;
	hi_uchar8 auc_operator[12];
	uint32_t  omic_ext;
} hi_omci_tapi_sysinfo_s;

typedef struct {
	hi_uchar8 auc_mac[HI_MAC_LEN];  /* onu mac address */
	hi_uchar8 auc_sn[8];            /* Serial Number */
	hi_uchar8 auc_loid[HI_PON_LOID_LEN];         /* Logical ONU ID */
	hi_uchar8 auc_lopwd[HI_PON_LOIDPWD_LEN];        /* Logical password */
	hi_uchar8 auc_psk[16];          /* Enhanced security control PSK */
	hi_uchar8 auc_regid[36];        /* Registration_ID */
} hi_omci_tapi_sn_loid_s;

typedef enum {
	HI_OMCI_ME_ONU_CAPABILITY_TYPE_SFU_E = 0x0,
	HI_OMCI_ME_ONU_CAPABILITY_TYPE_HGU_E,
	HI_OMCI_ME_ONU_CAPABILITY_TYPE_SBU_E,
	HI_OMCI_ME_ONU_CAPABILITY_TYPE_CBU_E,
	HI_OMCI_ME_ONU_CAPABILITY_TYPE_MDU_E,
	HI_OMCI_ME_ONU_CAPABILITY_TYPE_MTU_E,
	HI_OMCI_ME_ONU_CAPABILITY_TYPE_RESV_E = 0xF
} hi_omci_me_onu_capability_type_e;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
extern hi_int32 hi_omci_tapi_sysinfo_get(hi_omci_tapi_sysinfo_s *pst_info);
extern hi_int32 hi_omci_tapi_sn_loid_get(hi_omci_tapi_sn_loid_s *pst_info);
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_SYSINFO_H__*/
