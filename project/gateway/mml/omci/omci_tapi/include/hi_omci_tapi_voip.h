/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_voip.h
  Version    : 初稿
  Author     :
  Creation   :
  Description: OMCI_TAPI VOIP头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_VOIP_H__
#define __HI_OMCI_TAPI_VOIP_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_tapi_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_TAPI_VOIP_STR_LEN (128)
#define HI_OMCI_TAPI_VOIP_IP_LEN (4)
#define HI_OMCI_TAPI_VOIP_DNS_SERVERS_LEN (256)
#define HI_OMCI_TAPI_VOIP_MAC_LEN (6)
#define HI_OMCI_TAPI_VOIP_DIGITMAP_LEN (4096)
#define HI_OMCI_TAPI_EXTVLAN_TABLE_NUM 8

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef struct {
	hi_ushort16   us_lineidx;
	hi_ushort16   us_portid;
	hi_ushort16   us_protocol;
	hi_ushort16   us_tosfield;
} hi_omci_tapi_voip_udptcp_cfg_s;
typedef struct {
	hi_uchar8      uc_server[HI_OMCI_TAPI_VOIP_STR_LEN];
	hi_ushort16   us_portid;
	hi_ushort16   us_protocol;
} hi_omci_tapi_voip_server_s;
typedef struct {
	hi_uint32       ui_lineidx;
	hi_ushort16   us_reg_time;
	hi_ushort16   us_rereg_time;
} hi_omci_tapi_voip_time_s;
typedef struct {
	hi_uint32       ui_lineidx;
	hi_uchar8      uc_username[HI_OMCI_TAPI_VOIP_STR_LEN];
	hi_uchar8      uc_password[HI_OMCI_TAPI_VOIP_STR_LEN];
	hi_uchar8      uc_number[HI_OMCI_TAPI_VOIP_STR_LEN];
} hi_omci_tapi_voip_auth_s;
typedef struct {
	hi_uint32       ui_lineidx;
	hi_uint32       ui_txgain;
	hi_uint32       ui_rxgain;
} hi_omci_tapi_voip_gain_s;

typedef struct {
	hi_uint32       ui_lineidx;
	hi_uint32       ui_status;
} hi_omci_tapi_voip_hookstatus_s;

typedef struct {
	hi_ushort16 us_dhcpmode;
	hi_ushort16 us_dhcpen;
	hi_uint32 ui_vlan;
	hi_uint32 ui_vlanen;
	hi_ushort16 us_8021p;
	hi_ushort16 us_8021pen;
	hi_uint32 ui_ipmask;             /*网络掩码，获取地址方式为Static时可配置*/
	hi_uint32 ui_ipmasken;
	hi_uint32 ui_defagateway;    /*默认网关，获取地址方式为Static时可配置*/
	hi_uint32 ui_gatewayen;
	hi_uint32 ui_extipaddr;          /*IP地址，获取地址方式为Static时可配置*/
	hi_uint32 ui_pridns;
	hi_uint32 ui_secdns;
	hi_uint32 ui_ipaddren;
	hi_uchar8 auc_dnsserv[HI_OMCI_TAPI_VOIP_DNS_SERVERS_LEN]; /* DNS Servers，多个DNS服务器以逗号隔开*/
	hi_uint32 ui_dnsen;
	hi_uchar8 uc_mac[HI_OMCI_TAPI_VOIP_MAC_LEN];
	hi_ushort16 us_flag;                                                                    /*0: VOIP  1:TR069*/
} hi_omci_tapi_voip_iphost_s;
typedef struct {
	hi_uint32       ui_lineidx;
	hi_uint32       ui_portstart;
	hi_uint32       ui_portend;
	hi_uint32       ui_dscp;
} hi_omci_tapi_voip_rtp_s;
typedef struct {
	hi_uchar8       uc_entryid;
	hi_uchar8       uc_codec;
	hi_uchar8       uc_pri;
	hi_uchar8       uc_enable;
	hi_uchar8       uc_packperi;
} hi_omci_tapi_voip_codec_s;

typedef struct {
	hi_uchar8       auc_digitmap[HI_OMCI_TAPI_VOIP_DIGITMAP_LEN];
} hi_omci_tapi_voip_digitmap_s;
typedef struct {
	hi_uint32       ui_lineidx;
	hi_uint32       ui_silence_en;
} hi_omci_tapi_voip_silence_s;
typedef struct {
	hi_uint32       ui_lineidx;
	hi_uint32       ui_codec_used;
	hi_uint32       ui_server_status;
	hi_uint32       ui_session_type;
	hi_uint32       ui_line_state;
} hi_omci_tapi_line_status_s;
typedef struct {
	hi_uint32       ui_lineidx;
	hi_uint32       ui_state;
} hi_omci_tapi_voip_adminstate_s;

typedef struct {
	hi_uint32       ui_lineidx;
	hi_uint32       ui_time;
	hi_uint32       ui_rtp_errors;
	hi_uint32       ui_packet_loss;
	hi_uint32       ui_maxi_jitter;
	hi_uint32       ui_maxi_betw_rtcp;
	hi_uint32       ui_buffer_under;
	hi_uint32       ui_buffer_over;
} hi_omci_tapi_voip_rtppm_s;

typedef struct {
	hi_uint32           ui_index;
	hi_ushort16       us_vlan;
	hi_ushort16       us_pri;
	hi_uint32           ui_mode;                          /*0:无效  1:voip wan 2:tr069 wan*/
} hi_omci_tapi_voip_extvlan_table_s;

typedef struct {
	hi_omci_tapi_voip_extvlan_table_s st_extvlan[HI_OMCI_TAPI_EXTVLAN_TABLE_NUM];
} hi_omci_tapi_voip_extvlan_s;

typedef struct {
	hi_uint32       ui_lineidx;
	hi_uint32       ui_initiated_cnt;
	hi_uint32       ui_rx_invite_cnt;
	hi_uint32       ui_rx_reinvite_cnt;
	hi_uint32       ui_rx_noinvite_cnt;
	hi_uint32       ui_rx_renoinvite_cnt;
	hi_uint32       ui_rx_response_cnt;
	hi_uint32       ui_rx_reresponse_cnt;
	hi_uint32       ui_tx_invite_cnt;
	hi_uint32       ui_tx_reinvite_cnt;
	hi_uint32       ui_tx_noinvite_cnt;
	hi_uint32       ui_tx_renoinvite_cnt;
	hi_uint32       ui_tx_response_cnt;
	hi_uint32       ui_tx_reresponse_cnt;
} hi_omci_tapi_voip_sip_agentpm_s;

typedef struct {
	hi_uint32       ui_lineidx;
	hi_uint32       ui_connect_fail_cnt;
	hi_uint32       ui_check_fail_cnt;
	hi_uint32       ui_timeout_cnt;
	hi_uint32       ui_recv_err_code_cnt;
	hi_uint32       ui_authentication_fail_cnt;
} hi_omci_tapi_voip_sip_callinitpm_s;

typedef struct {
	hi_uint32       ui_lineidx;
	hi_uint32       ui_callsetup_fail;
	hi_uint32       ui_callsetup_timer;
	hi_uint32       ui_callterm_fail;
	hi_uint32       ui_analogport_releases;
	hi_uint32       ui_analogport_offhooktimer;
} hi_omci_tapi_voip_callctrlpm_s;

typedef enum {
	HI_OMCI_TAPI_VOIP_INITIAL = 0,
	HI_OMCI_TAPI_VOIP_REGISTERED,
	HI_OMCI_TAPI_VOIP_INSESSION,
	HI_OMCI_TAPI_VOIP_FAIL_REGIST_ICMP_ERR,
	HI_OMCI_TAPI_VOIP_FAIL_REGIST_TCP,
	HI_OMCI_TAPI_VOIP_FAIL_REGIST_AUTHENTICATION,
	HI_OMCI_TAPI_VOIP_FAIL_REGIST_TIMEOUT,
	HI_OMCI_TAPI_VOIP_FAIL_REGIST_SERVER_CODE,
	HI_OMCI_TAPI_VOIP_FAIL_INTVITE_ICMP_ERR,
	HI_OMCI_TAPI_VOIP_FAIL_INTVITE_TCP,
	HI_OMCI_TAPI_VOIP_FAIL_INTVITE_AUTHENTICATION,
	HI_OMCI_TAPI_VOIP_FAIL_INTVITE_TIMEOUT,
	HI_OMCI_TAPI_VOIP_FAIL_INTVITE_SERVER_CODE,
	HI_OMCI_TAPI_VOIP_PORT_NOT_CONFIGURED,
	HI_OMCI_TAPI_VOIP_CONFIG_DONE,
	HI_OMCI_TAPI_VOIP_CONFIG_DISABLE_BY_SWITCH = 15,

} hi_omci_tapi_voip_server_status_s;

typedef enum {
	HI_OMCI_TAPI_VOIP_ADMIN_UNLOCK = 0,
	HI_OMCI_TAPI_VOIP_ADMIN_LOCK,
	HI_OMCI_TAPI_VOIP_ADMIN_SHUTDOWN,
} hi_omci_tapi_voip_adminstate_e;

typedef enum {
	HI_OMCI_VOIP_CONFIG_METHOD_ONU_DEFAULT = 0,
	HI_OMCI_VOIP_CONFIG_METHOD_OMCI = 1,
	HI_OMCI_VOIP_CONFIG_METHOD_CONFIG_FILE,
	HI_OMCI_VOIP_CONFIG_METHOD_TR069 = 3,
	HI_OMCI_VOIP_CONFIG_METHOD_IETF,
	HI_OMCI_VOIP_CONFIG_METHOD_TR369,
} hi_omci_tapi_voip_config_method_e;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
extern hi_int32 hi_omci_tapi_voip_udptcp_set(hi_omci_tapi_voip_udptcp_cfg_s *pst_udptcp);
extern hi_int32 hi_omci_tapi_voip_proxyserver_set(hi_omci_tapi_voip_server_s *pst_server);
extern hi_int32 hi_omci_tapi_voip_regserver_set(hi_omci_tapi_voip_server_s *pst_server);
extern hi_int32 hi_omci_tapi_voip_agent_set(hi_omci_tapi_voip_server_s *pst_server);
extern hi_int32 hi_omci_tapi_voip_regtime_set(hi_omci_tapi_voip_time_s *pst_server);
extern hi_int32 hi_omci_tapi_voip_regtime_get(hi_omci_tapi_voip_time_s *pst_server);
extern hi_int32 hi_omci_tapi_voip_offhooktime_set(hi_uint32 *pui_offhook_time);
extern hi_int32 hi_omci_tapi_voip_offhooktime_get(hi_uint32 *pui_offhook_time);
extern hi_int32 hi_omci_tapi_voip_sipauth_set(hi_omci_tapi_voip_auth_s *pst_auth);
extern hi_int32 hi_omci_tapi_voip_sipauth_get(hi_omci_tapi_voip_auth_s *pst_auth);
extern hi_int32 hi_omci_tapi_voip_gain_set(hi_omci_tapi_voip_gain_s *pst_gain);
extern hi_int32 hi_omci_tapi_voip_gain_get(hi_omci_tapi_voip_gain_s *pst_gain);
extern hi_int32 hi_omci_tapi_voip_hookstatus_get(hi_omci_tapi_voip_hookstatus_s *pst_status);
extern hi_int32 hi_omci_tapi_voip_type_set(hi_uint32 *pui_type);
extern hi_int32 hi_omci_tapi_voip_type_get(hi_uint32 *pui_type);
extern hi_uint32 hi_omci_tapi_voip_wan_set(hi_omci_tapi_voip_iphost_s *pst_iphost);
extern hi_uint32 hi_omci_tapi_voip_wan_get(hi_omci_tapi_voip_iphost_s *pst_iphost);
extern hi_uint32 hi_omci_tapi_voip_wan_del(hi_uint32 ui_flag);
extern hi_int32 hi_omci_tapi_voip_rtp_set(hi_omci_tapi_voip_rtp_s *pst_rtp);
extern hi_int32 hi_omci_tapi_voip_rtp_get(hi_omci_tapi_voip_rtp_s *pst_rtp);
extern hi_int32 hi_omci_tapi_voip_faxmode_set(hi_uint32 *pui_mode);
extern hi_int32 hi_omci_tapi_voip_codec_set(hi_omci_tapi_voip_codec_s *pst_codec);
extern hi_int32 hi_omci_tapi_voip_codec_get(hi_omci_tapi_voip_codec_s *pst_codec);
extern hi_int32 hi_omci_tapi_voip_digitmap_set(hi_omci_tapi_voip_digitmap_s *pst_map);
extern hi_int32 hi_omci_tapi_voip_silence_set(hi_omci_tapi_voip_silence_s *pst_silence);
extern hi_int32 hi_omci_tapi_voip_line_status_get(hi_omci_tapi_line_status_s *pst_status);
extern hi_int32 hi_omci_tapi_voip_adminstate_set(hi_omci_tapi_voip_adminstate_s *pst_state);
extern hi_int32 hi_omci_tapi_voip_adminstate_get(hi_omci_tapi_voip_adminstate_s *pst_state);
extern hi_int32 hi_omci_tapi_voip_outbserver_set(hi_omci_tapi_voip_server_s *pst_server);
extern hi_int32 hi_omci_tapi_voip_conf_set(hi_uint32 *pui_en);
extern hi_int32 hi_omci_tapi_voip_conf_get(hi_uint32 *pui_en);
extern hi_int32 hi_omci_tapi_tr069_conf_set(hi_uint32 *pui_en);
extern hi_int32 hi_omci_tapi_voip_rtppm_get(hi_omci_tapi_voip_rtppm_s *pst_state);
extern hi_int32 hi_omci_tapi_voip_sip_agentpm_get(hi_omci_tapi_voip_sip_agentpm_s *pst_state);
extern hi_int32 hi_omci_tapi_voip_sip_callinitpm_get(hi_omci_tapi_voip_sip_callinitpm_s *pst_state);
extern hi_int32 hi_omci_tapi_voip_callctrlpm_get(hi_omci_tapi_voip_callctrlpm_s *pst_state);
extern hi_int32 hi_omci_tapi_tr069_status_get(hi_uint32 *pui_en);
extern hi_int32 hi_omci_tapi_voip_emergency_get(hi_uchar8 *status);
extern hi_int32 hi_omci_tapi_voip_cnfig_method_set(hi_uint32 ui_config_method);
extern hi_int32 hi_omci_tapi_voip_cnfig_method_get(void);
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_VOIP_H__*/
