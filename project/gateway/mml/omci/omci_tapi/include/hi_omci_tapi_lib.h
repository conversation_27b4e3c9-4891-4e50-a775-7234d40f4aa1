/******************************************************************************

                  Copyright (C), 2012-2022, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_lib.h
  Version    : 初稿
  Author     :
  Creation   :
  Description: OMCI_TAPI头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_LIB_H__
#define __HI_OMCI_TAPI_LIB_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_uspace.h"
#include "hi_basic.h"
#include "hi_omci_def.h"

#include "hi_omci_tapi_common.h"
#include "hi_omci_tapi_gpon.h"
#include "hi_omci_tapi_br.h"
#include "hi_omci_tapi_mc.h"
#include "hi_omci_tapi_vlan.h"
#include "hi_omci_tapi_qos.h"
#include "hi_omci_tapi_uni.h"
#include "hi_omci_tapi_pmu.h"
#include "hi_omci_tapi_optical.h"
#include "hi_omci_tapi_stat.h"
#include "hi_omci_tapi_sysinfo.h"
#include "hi_omci_tapi_upgrade.h"
#include "hi_omci_tapi_voip.h"
#include "hi_omci_tapi_tr069.h"

#endif /* __HI_OMCI_TAPI_LIB_H__*/

