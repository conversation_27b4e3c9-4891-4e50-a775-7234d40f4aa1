/******************************************************************************
                  Copyright (C), 2012-2013, HSAN
 ******************************************************************************
  Filename    : hi_omci_tapi_gpon.h
  Version     : 初稿
  Author      : owen
  Creation    : 2013-10-9
  Description : ANI相关配置接口头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_GPON_H__
#define __HI_OMCI_TAPI_GPON_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_tapi_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_TAPI_MAP_IGR      0x00000001
#define HI_OMCI_TAPI_MAP_VLAN     0x00000002
#define HI_OMCI_TAPI_MAP_PRI      0x00000004

#define HI_OMCI_TAPI_KEY_LEN            16
#define HI_OMCI_TAPI_AES_CMAC_DATA_LEN  128

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef enum {
	HI_OMCI_TAPI_1GGPON_1G_E = 0,
	HI_OMCI_TAPI_10GGPON_U2DOT5G_E = 5,
	HI_OMCI_TAPI_10GGPON_10G_E = 13,
} hi_omci_tapi_gpon_mode_e;

typedef enum {
	HI_OMCI_TAPI_DBA_RPT_NSR = 0,
	HI_OMCI_TAPI_DBA_RPT_SR_MODE0,        /* Piggyback DBA Reporting mode0 */
	HI_OMCI_TAPI_DBA_RPT_SR_MODE0_1,      /* Piggyback DBA Reporting mode0/1 */
	HI_OMCI_TAPI_DBA_RPT_SR_MODE0_1_2,    /* Piggyback DBA Reporting mode0/1/2 */
} hi_omci_tapi_dba_rpt_mode_e;

typedef enum {
	HI_OMCI_TAPI_TCONT_SP_E = 0,
	HI_OMCI_TAPI_TCONT_WRR_E
} hi_omci_tapi_tcont_policy_type_e;

typedef enum {
	HI_OMCI_TAPI_GENERAL_GEMPORT_E = 0,
	HI_OMCI_TAPI_MULTICAST_GEMPORT_E
} hi_omci_tapi_gemport_type_e;

typedef struct {
	hi_uint32 ui_mask;
	hi_uint32 ui_vlan;
	hi_uint32 ui_pri;
	hi_omci_tapi_port_e em_port;
	hi_uint32 ui_gemport;
	hi_uint32 ui_tcont;
} hi_omci_tapi_map_s;

typedef struct {
	hi_omci_tapi_gemport_type_e em_type;
	hi_uint32 ui_upcarid;           /* 上行CAR模板ID */
	hi_uint32 ui_dncarid;           /* 下行CAR模板ID */
	hi_uint32 ui_uppri;             /* 上行强制调度优先级 */
	hi_uint32 ui_dnpri;             /* 下行强制调度优先级 */
	hi_uint32 ui_decrypt_en;        /* 下行解密使能 */
	hi_uint32 ui_encrypt_en;        /* 上行加密使能 */
} hi_omci_tapi_gemport_para_s;

typedef struct {
	hi_uchar8 auc_data[HI_OMCI_TAPI_AES_CMAC_DATA_LEN];
	hi_uint32 ui_len;
	hi_uchar8 auc_key[HI_OMCI_TAPI_KEY_LEN];
	hi_uchar8 auc_result[HI_OMCI_TAPI_KEY_LEN];
} hi_omci_tapi_aes_cmac_s;

typedef struct {
	hi_ulong64 ui_los_alarm_count;
	hi_ulong64 ui_lof_alarm_count;
	hi_ulong64 ui_dyinggasp_alarm_count;
	hi_ulong64 ui_rei_counter_alarm_count;
	hi_ulong64 ui_sf_alarm_count;
	hi_ulong64 ui_sd_alarm_count;
	hi_ulong64 ui_lcdg_alarm_count;
	hi_ulong64 ui_bm_idle_alarm_count;
	hi_ulong64 ui_rogue_alarm_count;
	hi_ulong64 ui_silent_alarm_count;
	hi_ulong64 ui_fwi_alarm_count;
	hi_ulong64 ui_lwi_alarm_count;
	hi_ulong64 ui_ldi_alarm_count;
	hi_ulong64 ui_lsi_alarm_count;
} hi_omci_tapi_gpon_alarm_s;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_tapi_omcc_create
 Description : 建立OMCC通道
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_omcc_create();

/******************************************************************************
 Function    : hi_omci_tapi_omcc_destory
 Description : 拆除OMCC通道
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_omcc_destory();

/******************************************************************************
 Function    : hi_omci_tapi_gponmode_get
 Description : 获取GPON模式 1G/10G
 Input Parm  : 无
 Output Parm : hi_omci_tapi_gpon_mode_e *pem_mode
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_gponmode_get(hi_omci_tapi_gpon_mode_e *pem_mode);

/******************************************************************************
 Function    : hi_omci_tapi_tcont_num_get
 Description : 获取系统支持的最大TCONT数目
 Input Parm  : 无
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tcont_num_get(hi_uint32 *pui_num);

/******************************************************************************
 Function    : hi_omci_tapi_tcont_set
 Description : 配置TCONT
 Input Parm  : hi_uint32 ui_tcontid,
               hi_uint32 ui_allocid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tcont_set(hi_uint32 ui_tcontid, hi_uint32 ui_allocid);

/******************************************************************************
 Function    : hi_omci_tapi_tcont_get
 Description : 获取TCONT
 Input Parm  : hi_uint32 ui_tcontid,
 Output Parm : hi_uint32 *pui_allocid
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tcont_get(hi_uint32 ui_tcontid, hi_uint32 *pui_allocid);

/******************************************************************************
 Function    : hi_omci_tapi_tcont_del
 Description : 删除TCONT
 Input Parm  : hi_uint32 ui_tcontid,
               hi_uint32 ui_allocid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tcont_del(hi_uint32 ui_tcontid);

/******************************************************************************
 Function    : hi_omci_tapi_tcont_policy_set
 Description : 配置TCONT调度策略
 Input Parm  : hi_uint32 ui_tcontid,
               hi_omci_tapi_tcont_policy_type_e em_type
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_tcont_policy_set(hi_uint32 ui_tcontid, hi_omci_tapi_tcont_policy_type_e em_type);

/******************************************************************************
 Function    : hi_omci_tapi_dba_mode_get
 Description : 获取DBA上报模式
 Input Parm  : 无
 Output Parm : hi_omci_tapi_dba_rpt_mode_e *pem_mode
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_dba_mode_get(hi_omci_tapi_dba_rpt_mode_e *pem_mode);

/******************************************************************************
 Function    : hi_omci_tapi_gem_block_set
 Description : 配置GEM帧切片长度
 Input Parm  : hi_uint32 ui_len
 Output Parm : 无
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_gem_block_set(hi_uint32 ui_len);

/******************************************************************************
 Function    : hi_omci_tapi_gem_block_get
 Description : 获取GEM帧切片长度
 Input Parm  : 无
 Output Parm : hi_uint32 *pui_len
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_gem_block_get(hi_uint32 *pui_len);

/******************************************************************************
 Function    : hi_omci_tapi_gemport_num_get
 Description : 获取GEMPORT数目
 Input Parm  : 无
 Output Parm : hi_uint32 *pui_num
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_gemport_num_get(hi_uint32 *pui_num);

/******************************************************************************
 Function    : hi_omci_tapi_gemport_set
 Description : 配置GEMPORT
 Input Parm  : hi_uint32 ui_gemport,
               hi_omci_tapi_gemport_para_s *pst_para  GEMPORT参数
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_gemport_set(hi_uint32 ui_gemportid, hi_omci_tapi_gemport_para_s *pst_para);

/******************************************************************************
 Function    : hi_omci_tapi_gemport_set
 Description : 获取GEMPORT参数
 Input Parm  : hi_uint32 ui_gemport,
 Output Parm : hi_omci_tapi_gemport_para_s *pst_para  GEMPORT参数
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_gemport_get(hi_uint32 ui_gemportid, hi_omci_tapi_gemport_para_s *pst_para);

/******************************************************************************
 Function    : hi_omci_tapi_gemport_set
 Description : 删除GEMPORT
 Input Parm  : hi_uint32 ui_gemport,
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_gemport_del(hi_uint32 ui_gemportid);

/******************************************************************************
 Function    : hi_omci_tapi_gemport_loopback_set
 Description : 配置GEMPORT环回
 Input Parm  : hi_uint32 ui_gemportid, GEMPORT ID
               hi_uint32 ui_enable     使能/禁止标识
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_gemport_loopback_set(hi_uint32 ui_gemportid, hi_uint32 ui_tcont, hi_uint32 ui_enable);

/*****************************************************************************
  Function     : hi_omci_tapi_map_set
  Description  : 配置映射规则
  Input Param  : hi_omci_tapi_map_s *pst_mapping
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_map_set(hi_omci_tapi_map_s *pst_map);

/*****************************************************************************
  Function     : hi_omci_tapi_map_get
  Description  : 获取映射规则
  Input Param  : hi_omci_tapi_map_s *pst_mapping
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_map_get(hi_omci_tapi_map_s *pst_map);

/*****************************************************************************
  Function     : hi_omci_tapi_map_del
  Description  : 删除映射规则
  Input Param  : hi_omci_tapi_map_s *pst_mapping
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_map_del(hi_omci_tapi_map_s *pst_map);

/*****************************************************************************
  Function     : hi_omci_tapi_msk_set
  Description  : 配置MSK
  Input Param  : hi_uchar8 *puc_msk
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_msk_set(hi_uchar8 *puc_msk);

/*****************************************************************************
  Function     : hi_omci_tapi_aes_cmac
  Description  : AES_CMAC_128
  Input Param  : hi_omci_tapi_aes_cmac_s *pst_cmac
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_aes_cmac(hi_omci_tapi_aes_cmac_s *pst_cmac);

/*****************************************************************************
  Function     : hi_omci_tapi_mckey_set
  Description  : 配置多播数据流密钥
  Input Param  : hi_uint32 ui_index,
                 hi_uint32 ui_len,
                 hi_uchar8 *puc_mckey
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_mckey_set(hi_uint32 ui_index, hi_uint32 ui_len, hi_uchar8 *puc_mckey);

/*****************************************************************************
  Function     : hi_omci_tapi_mckey_set
  Description  : 删除多播数据流密钥
  Input Param  : hi_uint32 ui_index,
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_mckey_del(hi_uint32 ui_index);

/*****************************************************************************
  Function     : hi_omci_tapi_time_set
  Description  : GPON时间同步信息下发
  Input Param  : hi_uchar8 * puc_time
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_time_set(hi_uchar8 *puc_time);

/*****************************************************************************
  Function     : hi_omci_tapi_gmac_alarm_get
  Description  : 获取GMAC的相关告警统计
  Input Param  : hi_omci_tapi_map_s *pst_mapping
  Output Param ：
  Return       : hi_uint32
*****************************************************************************/
extern hi_int32 hi_omci_tapi_gpon_alarm_get(hi_omci_tapi_gpon_alarm_s *pst_gmac_alarm);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_GPON_H__*/
