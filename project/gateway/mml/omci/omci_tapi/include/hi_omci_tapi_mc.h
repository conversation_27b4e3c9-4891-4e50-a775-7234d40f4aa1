/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_mc.h
  Version    : 初稿
  Author     : owen
  Creation   : 2014-1-18
  Description: OMCI Multicast TAPI头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_MC_H__
#define __HI_OMCI_TAPI_MC_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */
#include "hi_sml_common.h"
#include "hi_omci_tapi_common.h"
/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef enum {
	HI_OMCI_TAPI_MC_IGMPV1_E = 1,    /* 不建议使用 */
	HI_OMCI_TAPI_MC_IGMPV2_E,
	HI_OMCI_TAPI_MC_IGMPV3_E,
	HI_OMCI_TAPI_MC_MLDV1_E = 16,
	HI_OMCI_TAPI_MC_MLDV2_E = 17,
} hi_omci_tapi_mc_igmp_version_e;

typedef enum {
	HI_OMCI_TAPI_MC_IGMP_SNOOPING = 0,
	HI_OMCI_TAPI_MC_IGMP_PROXY,
} hi_omci_tapi_mc_igmp_func_e;

typedef enum {
	HI_OMCI_TAPI_MC_IGMP_TRANSPARENT = 0,
	HI_OMCI_TAPI_MC_IGMP_TAG_ADD,
	HI_OMCI_TAPI_MC_IGMP_TAG_MOD_TCI,
	HI_OMCI_TAPI_MC_IGMP_TAG_MOD_VLAN,
} hi_omci_tapi_mc_igmp_tag_act_e;

typedef struct {
	hi_omci_tapi_mc_igmp_version_e em_version;
	hi_omci_tapi_mc_igmp_func_e em_func;
	hi_uint32 ui_imme_leave_en;                         /* 快速离开使能 */
} hi_omci_tapi_mc_attr_s;

typedef struct {
	hi_omci_tapi_mc_igmp_tag_act_e em_tag_act;
	hi_uint32 ui_vlan;
	hi_uint32 ui_pri;
	hi_uint32 ui_gemportid;
} hi_omci_tapi_mc_igmp_attr_s;

typedef struct {
	union {
		hi_ipv4_s st_ipv4;
		hi_ipv6_s st_ipv6;
	} start_ip;

	union {
		hi_ipv4_s st_ipv4;
		hi_ipv6_s st_ipv6;
	} end_ip;
} hi_omci_tapi_mc_ip_addr_s;

typedef struct {
	hi_uint32 ui_static;                    /* 静态标识，如果有效，不用等待IGMP join报文，表项直接生效 */
	hi_uint32 ui_gemportid;
	hi_uint32 ui_mcvlan;
	hi_uint32 ui_usrvlan;
	hi_omci_tapi_mc_ip_addr_s st_ipaddr;
	hi_omci_tapi_port_e em_port;
	hi_uint32 ui_rate;
} hi_omci_tapi_mc_table_list_s;

typedef struct {
	hi_omci_tapi_port_e em_port;
	hi_uint32 ui_mcvlan;
	hi_uint32 ui_usrvlan;
} hi_omci_tapi_mc_vlan_s;

typedef enum {
	HI_OMCI_TAPI_MC_TRANSPARENT = 0,
	HI_OMCI_TAPI_MC_DEL,
	HI_OMCI_TAPI_MC_ADD,
	HI_OMCI_TAPI_MC_TRANSLATION_TCI,
	HI_OMCI_TAPI_MC_TRANSLATION_VLAN,
	HI_OMCI_TAPI_MC_ADD_SUBS,
	HI_OMCI_TAPI_MC_TRANSLATION_TCI_SUBS,
	HI_OMCI_TAPI_MC_TRANSLATION_VLAN_SUBS,
} hi_omci_tapi_mc_tagact_e;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_tapi_mc_attr_set
 Description : 配置组播处理属性
 Input Parm  : hi_omci_tapi_mc_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_attr_set(hi_omci_tapi_mc_attr_s *pst_attr);

/******************************************************************************
 Function    : hi_omci_tapi_mc_attr_get
 Description : 获取组播配置属性
 Input Parm  : hi_omci_tapi_mc_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_attr_get(hi_omci_tapi_mc_attr_s *pst_attr);

/******************************************************************************
 Function    : hi_omci_tapi_mc_igmp_attr_set
 Description : 配置IGMP报文处理属性
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_omci_tapi_mc_igmp_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_igmp_attr_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_mc_igmp_attr_s *pst_attr);

/******************************************************************************
 Function    : hi_omci_tapi_mc_igmp_attr_get
 Description : 获取IGMP报文配置属性
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_omci_tapi_mc_igmp_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_igmp_attr_get(hi_omci_tapi_port_e em_port, hi_omci_tapi_mc_igmp_attr_s *pst_attr);

/******************************************************************************
 Function    : hi_omci_tapi_mc_filter_set
 Description : 配置基于GEMPORT的组播白名单过滤
 Input Parm  : hi_uint32 ui_gemportid,
               hi_omci_tapi_mc_ip_filter_s *pst_filter
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_filter_set(hi_uint32 ui_gemportid, hi_omci_tapi_mc_ip_addr_s *pst_filter);

/******************************************************************************
 Function    : hi_omci_tapi_mc_filter_del
 Description : 删除组播过滤规则
 Input Parm  : hi_uint32 ui_gemportid,
               hi_omci_tapi_mc_ip_filter_s *pst_filter
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_filter_del(hi_uint32 ui_gemportid, hi_omci_tapi_mc_ip_addr_s *pst_filter);

/******************************************************************************
 Function    : hi_omci_tapi_mc_table_set
 Description : 配置组播转发表项
 Input Parm  : hi_omci_tapi_mc_table_list_s *pst_list
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_table_set(hi_omci_tapi_mc_table_list_s *pst_list);

/******************************************************************************
 Function    : hi_omci_tapi_mc_table_del
 Description : 删除组播转发表项
 Input Parm  : hi_omci_tapi_mc_table_list_s *pst_list
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_table_del(hi_omci_tapi_mc_table_list_s *pst_list);

/******************************************************************************
 Function    : hi_omci_tapi_mc_table_set
 Description : 配置组播VLAN
 Input Parm  : hi_omci_tapi_mc_table_list_s *pst_list
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_vlan_set(hi_omci_tapi_mc_vlan_s *pst_vlan);

/******************************************************************************
 Function    : hi_omci_tapi_mc_table_del
 Description : 删除组播VLAN
 Input Parm  : hi_omci_tapi_mc_table_list_s *pst_list
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_vlan_del(hi_omci_tapi_mc_vlan_s *pst_vlan);

/******************************************************************************
 Function    : hi_omci_tapi_mc_vlan_clr
 Description : 清空端口的组播VLAN
 Input Parm  : hi_omci_tapi_port_e em_port
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_vlan_clr(hi_omci_tapi_port_e em_port);

/******************************************************************************
 Function    : hi_omci_tapi_mc_group_num_set
 Description : 配置最大组播组数目
 Input Parm  : hi_omci_tapi_port_e em_port, 用户端口
               hi_uint32 ui_group_num   最大组播组数目，0表示不限制
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_group_num_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_group_num);

/******************************************************************************
 Function    : hi_omci_tapi_mc_tagact_set
 Description : 配置下行组播数据TAG操作
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_omci_tapi_mc_tagact_e em_tagact,
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_tagact_set(hi_omci_tapi_port_e em_port, hi_omci_tapi_mc_tagact_e em_tagact);

/******************************************************************************
 Function    : hi_omci_tapi_mc_uprate_set
 Description : 配置组播上行速率限制
 Input Parm  : hi_omci_tapi_port_e em_port
               hi_uint32 ui_rate,
               hi_uint32 ui_carid
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_mc_uprate_set(hi_omci_tapi_port_e em_port, hi_uint32 ui_rate, hi_uint32 ui_carid);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_MC_H__*/

