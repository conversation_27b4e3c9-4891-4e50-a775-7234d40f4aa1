/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_tapi_pmu.h
  Version    : 初稿
  Author     : owen
  Creation   : 2013-12-25
  Description: OMCI节能TAPI头文件
******************************************************************************/

#ifndef __HI_OMCI_TAPI_PMU_H__
#define __HI_OMCI_TAPI_PMU_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef struct {
	hi_uint32 ui_reductcap; /* Power reduction management capability*/
	hi_uint32 ui_reductmode;
	hi_uint32 ui_itransinit;
	hi_uint32 ui_itxinit;
	hi_uint32 ui_maxsleep; /* Maximum sleep interval*/
	hi_uint32 ui_minaware; /* Minimum aware interval*/
	hi_uint32 ui_minactive; /* Minimum active held interval*/
} hi_omci_tapi_pmu_attr_s;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_tapi_pmu_attr_set
 Description : 配置节能属性
 Input Parm  : hi_omci_tapi_pmu_attr_s *pst_attr
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_pmu_attr_set(hi_omci_tapi_pmu_attr_s *pst_attr);

/******************************************************************************
 Function    : hi_omci_tapi_pmu_attr_get
 Description : 获取节能属性
 Input Parm  : 无
 Output Parm : hi_omci_tapi_pmu_attr_s *pst_att
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_tapi_pmu_attr_get(hi_omci_tapi_pmu_attr_s *pst_att);


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_TAPI_PMU_H__*/

