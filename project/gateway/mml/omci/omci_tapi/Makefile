include $(HI_EXT_CONFIG)
#===============================================================================
# export sub dir
#===============================================================================
HI_SUB_DIR :=
HI_SUB_DIR += source
#===============================================================================
# export lib
#===============================================================================
HI_LOC_LIB += -lhi_ipc -lhi_sml -lhi_odlapi
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/util/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/ipc/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/pon/sml/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/network/common/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/network/emu/u_space/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/sysinfo/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/board/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/upgrade/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/network/qos/u_space/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/easymesh/controller_api
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/easymesh/libs/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_SERVICE_DIR)/network/easymesh/agent_api
#
#===============================================================================
# target
#===============================================================================
TARGET 		= libhi_omci_tapi.so
TARGET_TYPE	= so

include $(HI_HISI_GW_APP_SCRIPT_DIR)/app.mk
