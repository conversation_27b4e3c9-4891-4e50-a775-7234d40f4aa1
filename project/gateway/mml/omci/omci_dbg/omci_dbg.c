/******************************************************************************

******************************************************************************/
#include "hi_uspace.h"
#include "hi_basic.h"
#include "hi_ipc.h"

#define OMCI_LOG_FILE "/log/hisi/hi_omci.log"
#define OMCI_LOG_BUF (256)
#define OMCI_LOG_BUF_TMP (64)
#define OMCI_LOG_PKT_BUF (1024)

int c2i(char ch)
{
	if (hi_os_isdigit(ch)) {
		return ch - 48;
	}

	if (ch < 'A' || (ch > 'F' && ch < 'a') || ch > 'z') {
		return -1;
	}

	if (hi_os_isalpha(ch)) {
		return hi_os_isupper(ch) ? ch - 55 : ch - 87;
	}

	return -1;
}

int hex2dec(char *hex)
{
	int len;
	int num = 0;
	int temp;
	int bits;
	int i;

	len = strlen(hex);

	for (i = 0, temp = 0; i < len; i++, temp = 0) {
		temp = c2i(*(hex + i));
		bits = (len - i - 1) * 4;
		temp = temp << bits;
		num = num | temp;
	}
	num = ntohl(num);
	return num;
}

hi_int32 main(hi_int32 argc, hi_char8 *argv[])
{
	HI_FILE_S *pf_file;
	hi_char8 *puc_chr = HI_NULL, *puc_pkt = HI_NULL;
	hi_char8 uc_buff[OMCI_LOG_BUF], uc_pkt_tmp[OMCI_LOG_BUF], uc_config_flag = 0, uc_data_flag = 0, uc_debug_mode = 1;
	hi_int32 i_pkt[OMCI_LOG_BUF_TMP] = {0}, i_index = 0;
	hi_int32 i_log = access(argv[1], 0);
	hi_char8 *savePtr = HI_NULL;

	if (0 != i_log) {
		return -1;
	}
	pf_file = hi_os_fopen(argv[1], "r+");
	if (HI_NULL == pf_file) {
		printf("failt to open :%s\n", argv[1]);
		return -1;
	}
	HI_IPC_CALL("hi_ipc_omci_dbg_mode_set", &uc_debug_mode, sizeof(uc_debug_mode));
	while (HI_NULL != hi_os_fgets(uc_buff, sizeof(uc_buff), pf_file)) {
		uc_buff[hi_os_strlen(uc_buff) - 1] = '\0';
		if (HI_NULL != hi_os_strstr(uc_buff, "OLT => ONT")) {
			HI_OS_MEMSET_S(i_pkt, sizeof(i_pkt), 0, sizeof(i_pkt));
			uc_config_flag = 1;
			uc_data_flag = 0;
			i_index = 0;
		}

		if (0 == uc_config_flag) {
			continue;
		}

		if (HI_NULL != hi_os_strstr(uc_buff, "MSGTYPE")) {
			if ((HI_NULL == hi_os_strstr(uc_buff, "setc")) && (HI_NULL == hi_os_strstr(uc_buff, "createc")) &&
			    (HI_NULL == hi_os_strstr(uc_buff, "settable")) && (HI_NULL == hi_os_strstr(uc_buff, "deletec"))) {
				uc_config_flag = 0;
				continue;
			}
		}
		if (HI_NULL != hi_os_strstr(uc_buff, "] :")) {
			puc_chr = hi_os_strchr(uc_buff, ':');
			if (HI_NULL != puc_chr) {
				HI_OS_MEMSET_S(uc_pkt_tmp, sizeof(uc_pkt_tmp), 0, sizeof(uc_pkt_tmp));
				HI_OS_MEMCPY_S(uc_pkt_tmp, sizeof(uc_pkt_tmp), puc_chr + 2, hi_os_strlen(puc_chr) - 2);
				puc_pkt = hi_os_strtok_r(uc_pkt_tmp, " ", &savePtr);
				while (puc_pkt) {
					if (8 != hi_os_strlen(puc_pkt)) {
						break;
					}
					i_pkt[i_index] = hex2dec(puc_pkt);
					i_index ++;
					puc_pkt = hi_os_strtok_r(HI_NULL, " ", &savePtr);
				}
				uc_data_flag = 1;
			}
		} else {
			if (uc_data_flag) {
				uc_data_flag = 0;
				uc_config_flag = 0;
				HI_IPC_CALL("hi_ipc_omci_dbg_recv_msg", i_pkt, sizeof(i_pkt));
			}
		}
	}
	hi_os_fclose(pf_file);

	uc_debug_mode = 0;
	HI_IPC_CALL("hi_ipc_omci_dbg_mode_set", &uc_debug_mode, sizeof(uc_debug_mode));
	return 0;
}
