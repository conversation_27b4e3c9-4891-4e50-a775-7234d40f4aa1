include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

# 组件名称
set(USERAPP_NAME hi_omci_sql)

# 组件类型
set(USERAPP_TARGET_TYPE so)

# 依赖的源文件
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source/sql OMCI_SQL_SOURCE_DIR)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source/mib OMCI_SQL_MIB_SOURCE_DIR)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source/protocol OMCI_SQL_PRO_SOURCE_DIR)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source/upgrade OMCI_SQL_UPG_SOURCE_DIR)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source/log OMCI_SQL_LOG_SOURCE_DIR)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source/diagnose OMCI_SQL_DIA_SOURCE_DIR)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/source/init OMCI_SQL_INIT_SOURCE_DIR)


set(USERAPP_PRIVATE_SRC
    ${OMCI_SQL_SOURCE_DIR}
    ${OMCI_SQL_MIB_SOURCE_DIR}
    ${OMCI_SQL_PRO_SOURCE_DIR}
    ${OMCI_SQL_UPG_SOURCE_DIR}
    ${OMCI_SQL_LOG_SOURCE_DIR}
    ${OMCI_SQL_DIA_SOURCE_DIR}
    ${OMCI_SQL_INIT_SOURCE_DIR}
)

# 依赖的头文件
set(USERAPP_PRIVATE_INC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${HGW_BASIC_DIR}/include
    ${HGW_BASIC_DIR}/include/os
    ${HGW_CML_DIR}/odl/include
    ${HGW_CML_DIR}/odlapi/include
    ${HGW_FWK_DIR}/include
    ${HGW_FWK_DIR}/timer/include
    ${HGW_FWK_DIR}/util/include
    ${HGW_FWK_DIR}/ipc/ipc_lib
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_MML_DIR}/omci/omci_tapi/include
    ${HGW_MML_DIR}/omci/omci_ctl/include
    ${HGW_SERVICE_DIR}/pon/api/include
    ${HGW_SERVICE_DIR}/pon/sml/include
    ${HGW_SERVICE_DIR}/dms/upgrade/include
    ${HGW_SERVICE_DIR}/network/common/include
    ${CONFIG_LINUX_DRV_DIR}/net/pon/include
    ${OPEN_SOURCE_SQLITE_BINARY_DIR}/sqlite-autoconf-${CONFIG_OPENSRC_VERSION_SQLITE}
    ${HGW_SERVICE_DIR}/dms/cfm/cfm_lib
)

set(USERAPP_PRIVATE_LIB
    hi_basic hi_timer
    hi_util sqlite3 hi_omci_tapi
    hi_ipc hi_odlapi hi_owal_ssf openwrt_lib
)

# 自定义宏
set(USERAPP_PRIVATE_DEFINE)

# 自定义编译选项
set(USERAPP_PRIVATE_COMPILE)

build_app_feature()
