/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_api.h
  Version    : 初稿
  Author     : owen
  Creation   : 2013-11-19
  Description: OMCI二次开发头文件
******************************************************************************/

#ifndef __HI_OMCI_API_H__
#define __HI_OMCI_API_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/
#define HI_OMCI_NOTIFIY_NAME "omci_notifier"

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
typedef enum {
	HI_OMCI_NOTIFY_ONUSTATE_E = 0,
	HI_OMCI_NOTIFY_ROGUE_ONU_E,
	HI_OMCI_NOTIFY_LOID_REGSTA_E
} hi_omci_notifier_type_e;

typedef struct {
	hi_omci_notifier_type_e em_type;
	hi_uint32 ui_data;
} hi_omci_notifier_data_s;

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
extern hi_int32 hi_omci_start();
extern hi_int32 hi_omci_stop();


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_API_H__*/
