/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_dbg.h
  版 本 号   : 初稿
  作    者   : t00185260
  生成日期   : D2011_10_26

******************************************************************************/
#ifndef __HI_OMCI_DBG_H__
#define __HI_OMCI_DBG_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define HI_OMCI_DBG_DIR_IN          1
#define HI_OMCI_DBG_DIR_OUT         2

extern hi_void hi_omci_dbg_pkt(hi_void *pv_data, hi_uchar8 uc_dir);
extern hi_void hi_omci_dbg_onusta_file(hi_uint32 ui_status);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OMCI_DBG_H__ */
