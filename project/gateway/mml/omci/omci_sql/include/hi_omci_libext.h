/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_libext.h
  Version    : 初稿
  Author     : owen
  Creation   : 2014-1-13
  Description: OMCI扩展库头文件
******************************************************************************/

#ifndef __HI_OMCI_LIBEXT_H__
#define __HI_OMCI_LIBEXT_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_libext_name_get
 Description : 获取当前的库文件名
 Input Parm  : 无
 Output Parm : hi_char8 *pc_name 输出的库文件名
 Return      : N/A
******************************************************************************/
//extern hi_void hi_omci_libext_name_get(hi_char8 *pc_name);

/******************************************************************************
 Function    : hi_omci_libext_init
 Description : 扩展库初始化加载
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
extern hi_int32 hi_omci_libext_init();

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_LIBEXT_H__*/

