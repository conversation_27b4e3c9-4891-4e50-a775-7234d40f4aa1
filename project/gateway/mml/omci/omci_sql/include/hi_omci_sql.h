/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_sql.h
  版 本 号   : 初稿
  作    者   : 获取实体实例个数
  生成日期   : D2011_11_07

******************************************************************************/
#ifndef __HI_OMCI_SQL_H__
#define __HI_OMCI_SQL_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define HI_OMCI_ALM_BYTE_NUM                    8
#define HI_OMCI_ALM_NUM                         100 /*支持的告警数目*/
#define HI_ATT_ONE                              0x80/*属性第一位*/
#define HI_OMCI_MIB_ENTITY_NAME_LEN             255

#define HI_OMCI_MIB_DBDATABASE_NAME             "/tmp/hi_omci_data.db"
#define HI_OMCI_MIB_DBDATABASE_SRCDIR           "/usr/sbin/hi_omci_data.db"
#define HI_OMCI_MIB_DBDATABASE_BACKUP           "/tmp/hi_omci_backup.db"

#define HI_OMCI_MIB_CALLBACKTABLE_NAME          "callbacktable"
#define HI_OMCI_MIB_ATTRTABLE_NAME              "attrtable"
#define HI_OMCI_MIB_ENTITYTABLE_NAME            "entitytable"
#define HI_OMCI_MIB_ENTITYFWDTABLE_NAME         "entityfwdtable"
#define HI_OMCI_MIB_ENTITYNEXTTABLE_NAME        "entitynexttable"
#define HI_OMCI_MIB_ENTITYDEFINETABLE_NAME      "entitydefinetable"
#define HI_OMCI_MIB_ENTITYEXTLIBTABLE_NAME      "entityextlibtable"
#define HI_OMCI_MIB_ALARMTABLE_NAME             "entityalarmtable"
#define HI_OMCI_MIB_STATTABLE_NAME              "entitystattable"
#define HI_OMCI_MIB_ALARMMSGTABLE_NAME          "alarmmsgtable"

#define HI_OMCI_MIB_ATTRMASK                    0xFFFF

#define HI_OMCI_MIB_FUNCNAME                    36

typedef enum {
	HI_OMCI_SQL_DB_BLOB_E      = 1,
	HI_OMCI_SQL_DB_INT32_E     = 2,
	HI_OMCI_SQL_DB_TEXT_E      = 3,
} hi_omci_sql_dbtype_e;

typedef enum {
	HI_OMCI_SQL_DATA_UCHAR_E   = 1,
	HI_OMCI_SQL_DATA_USHORT_E  = 2,
	HI_OMCI_SQL_DATA_UINT_E    = 3,
	HI_OMCI_SQL_DATA_BINARY_E  = 4,
	HI_OMCI_SQL_DATA_TEXT_E    = 5,
	HI_OMCI_SQL_DATA_TABLEATTR_E = 6,
} hi_omci_sql_datatype_e;

typedef enum {
	HI_OMCI_SQL_ENTITYTABLE_MEID_E = 0,
	HI_OMCI_SQL_ENTITYTABLE_DESC_E,
} hi_omci_sql_entitytable_e;

typedef enum {
	HI_OMCI_SQL_ENTITYACTTABLE_MEID_E = 0,
	HI_OMCI_SQL_ENTITYACTTABLE_CREATE_E,
	HI_OMCI_SQL_ENTITYACTTABLE_CREATECC_E,
	HI_OMCI_SQL_ENTITYACTTABLE_DELETE_E,
	HI_OMCI_SQL_ENTITYACTTABLE_DELETECC_E,
	HI_OMCI_SQL_ENTITYACTTABLE_SET_E,
	HI_OMCI_SQL_ENTITYACTTABLE_GET_E,
	HI_OMCI_SQL_ENTITYACTTABLE_GETCC_E,
	HI_OMCI_SQL_ENTITYACTTABLE_GETALARM_E,
	HI_OMCI_SQL_ENTITYACTTABLE_GETALARMNEXT_E,
	HI_OMCI_SQL_ENTITYACTTABLE_MIBUPLOAD_E,
	HI_OMCI_SQL_ENTITYACTTABLE_MIBUPLOADNEXT_E,
	HI_OMCI_SQL_ENTITYACTTABLE_MIBRESET_E,
	HI_OMCI_SQL_ENTITYACTTABLE_ALARM_E,
	HI_OMCI_SQL_ENTITYACTTABLE_AVC_E,
	HI_OMCI_SQL_ENTITYACTTABLE_TEST_E,
	HI_OMCI_SQL_ENTITYACTTABLE_STARTDOWNDLOAD_E,
	HI_OMCI_SQL_ENTITYACTTABLE_DOWNLOADING_E,
	HI_OMCI_SQL_ENTITYACTTABLE_ENDDOWNLOAD_E,
	HI_OMCI_SQL_ENTITYACTTABLE_ACTIVEIMAGE_E,
	HI_OMCI_SQL_ENTITYACTTABLE_COMMITIMAGE_E,
	HI_OMCI_SQL_ENTITYACTTABLE_SYNCTIME_E,
	HI_OMCI_SQL_ENTITYACTTABLE_REBOOT_E,
	HI_OMCI_SQL_ENTITYACTTABLE_GETNEXT_E,
	HI_OMCI_SQL_ENTITYACTTABLE_TESTRESULT_E,
	HI_OMCI_SQL_ENTITYACTTABLE_GETCURRENTDATA_E,
	HI_OMCI_SQL_ENTITYACTTABLE_SETTABLE_E,
	HI_OMCI_SQL_ENTITYACTTABLE_BUTT_E,
} hi_omci_sql_entityacttable_e;

typedef enum {
	HI_OMCI_SQL_ENTITYDEFINETABLE_MEID_E = 0,
	HI_OMCI_SQL_ENTITYDEFINETABLE_DEFINE_E,
} hi_omci_sql_entitydefinetable_e;

typedef enum {
	HI_OMCI_SQL_ATTRTABLE_MEID_E = 0,
	HI_OMCI_SQL_ATTRTABLE_POS_E,
	HI_OMCI_SQL_ATTRTABLE_SIZE_E,
	HI_OMCI_SQL_ATTRTABLE_TYPE_E,
	HI_OMCI_SQL_ATTRTABLE_OPT_E,
	HI_OMCI_SQL_ATTRTABLE_AVC_E,
	HI_OMCI_SQL_ATTRTABLE_TABNUM_E,
	HI_OMCI_SQL_ATTRTABLE_DESC_E,
	HI_OMCI_SQL_ATTRTABLE_NAME_E,
	HI_OMCI_SQL_ATTRTABLE_VERSION_E,
} hi_omci_sql_attrtable_e;

typedef enum {
	HI_OMCI_SQL_CALLBACKTABLE_CMDTYPE_E = 0,
	HI_OMCI_SQL_CALLBACKTABLE_ADDRESS_E,
	HI_OMCI_SQL_CALLBACKTABLE_DESC_E,
	HI_OMCI_SQL_CALLBACKTABLE_LIB_E,
} hi_omci_sql_callbacktable_e;

typedef enum {
	HI_OMCI_SQL_ALARMTABLE_MEID_E = 0,
	HI_OMCI_SQL_ALARMTABLE_ALARMID_E,
	HI_OMCI_SQL_ALARMTABLE_ALARMCMD_E,
	HI_OMCI_SQL_ALARMTABLE_DESC_E,
} hi_omci_sql_alarmtable_e;

typedef enum {
	HI_OMCI_SQL_ALARMMSGTABLE_MEID_E = 0,
	HI_OMCI_SQL_ALARMMSGTABLE_INSTID_E,
	HI_OMCI_SQL_ALARMMSGTABLE_ALARMID_E,
	HI_OMCI_SQL_ALARMMSGTABLE_CMDTYPE_E,
	HI_OMCI_SQL_ALARMMSGTABLE_STATUS_E,
} hi_omci_sql_alarmmsgtable_e;

typedef enum {
	HI_OMCI_SQL_INSTTABLE_INSTID_E = 0,
	HI_OMCI_SQL_INSTTABLE_ATTR1_E,
	HI_OMCI_SQL_INSTTABLE_ATTR2_E,
	HI_OMCI_SQL_INSTTABLE_ATTR3_E,
	HI_OMCI_SQL_INSTTABLE_ATTR4_E,
	HI_OMCI_SQL_INSTTABLE_ATTR5_E,
	HI_OMCI_SQL_INSTTABLE_ATTR6_E,
	HI_OMCI_SQL_INSTTABLE_ATTR7_E,
	HI_OMCI_SQL_INSTTABLE_ATTR8_E,
	HI_OMCI_SQL_INSTTABLE_ATTR9_E,
	HI_OMCI_SQL_INSTTABLE_ATTR10_E,
	HI_OMCI_SQL_INSTTABLE_ATTR11_E,
	HI_OMCI_SQL_INSTTABLE_ATTR12_E,
	HI_OMCI_SQL_INSTTABLE_ATTR13_E,
	HI_OMCI_SQL_INSTTABLE_ATTR14_E,
	HI_OMCI_SQL_INSTTABLE_ATTR15_E,
	HI_OMCI_SQL_INSTTABLE_ATTR16_E,
} hi_omci_sql_insttable_e;


/*function define*/
extern sqlite3 *g_pst_mibdb;

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OMCI_SQL_H__ */
