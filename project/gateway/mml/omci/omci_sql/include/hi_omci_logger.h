/******************************************************************************

                  Copyright (C), 2012-2013, HSA

 ******************************************************************************
  Filename   : hi_omci_logger.h
  Version    : 初稿
  Author     : chenshibin 00184653
  Creation   : 2015-1-30
  Description:
******************************************************************************/

#ifndef __HI_OMCI_LOGGER_H__
#define __HI_OMCI_LOGGER_H__

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                DEFINE                                     *
 *****************************************************************************/

#include <stdio.h>
#include <stdarg.h>
extern int def_level;
extern int log_out_mode;
extern int log_size;
extern int log_module;

/*****************************************************************************
 *                                TYPEDEF                                    *
 *****************************************************************************/
// Log levels
#define FATAL 0
#define ERROR 1
#define WARN  2
#define INFO  3
#ifndef DEBUG
#define DEBUG 4
#endif

// modules
#define all 0
#define m_init  9


#define PATH_LEN         100

#define LOG_FILE "/tmp/hi_omci.log"

// GLOBAL MACROS
#undef LOG_ARGS

//macro of current file ,line, and function name
#define LOG_ARGS __FILE__, __LINE__, __FUNCTION__
#undef LOG

#define     LOG(module, level, fmt, args...)                            \
	switch(log_module) {                                            \
	case 0:                                                     \
		do {                                                  \
			if (level <= def_level) {                         \
				logger (level, LOG_ARGS, fmt, ## args);        \
			}                                                \
		} while (0);                                          \
		break;                                                \
	default:                                                   \
		if (module == log_module)                             \
		{                                                     \
			do {                                              \
				if (level <= def_level) {                     \
					logger (level, LOG_ARGS, fmt, ## args);   \
				}                                             \
			} while (0);                                      \
		}                                                     \
	}

/*****************************************************************************
 *                                FUNCTION                                   *
 *****************************************************************************/
// FUNCTION PROTOTYPES
int logger(int level, const char *file, int line, const char *function, const char *fmt, ...);
int  init_logger(void);
void exit_logger(void);
int set_logger(int l_level, int l_mode, int l_size, int l_module, unsigned int l_switch);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

#endif /* __HI_OMCI_LOGGER_H__*/

