/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_proc.h
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_11_08

******************************************************************************/
#ifndef __HI_OMCI_PROC_H__
#define __HI_OMCI_PROC_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define HI_OMCI_DB_VALUE                               0 /* the value of db is fixed to 0 */
#define HI_OMCI_NOREQ_ACK                              0 /* not request ack */
#define HI_OMCI_REQ_ACK                                1 /* request ack */
#define HI_OMCI_CPCS_SDU_LENGTH                        40

typedef hi_uint32(*hi_omci_proc_actcallback)(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);

typedef union {
	hi_ushort16     us_tci;
	struct {
		hi_ushort16 us_sn : 15;     /*Serial Number*/
		hi_ushort16 us_pri: 1 ;     /*Priority*/
	} st_tci;
	hi_ushort16     ui_resv;
} hi_omci_proc_tci_u;

typedef union {
	hi_uchar8       uc_msgtype;
	struct {
		hi_uchar8   uc_mt: 5;      /*message type*/
		hi_uchar8   uc_ak: 1;      /*acknowledgement*/
		hi_uchar8   uc_ar: 1;      /*acknowledge request*/
		hi_uchar8   uc_db: 1;      /*destination bit, this bit is always 0*/
	} st_msgtype;
	hi_uchar8       uc_resv[3];
} hi_omci_proc_msg_type_u;

typedef struct {
	hi_ushort16  us_tci;           /*Transction Correlation ID*/
	hi_uchar8    uc_msgtype;       /*Message type Field*/
	hi_uchar8    uc_deviceid;      /*Device ID, always equal 0x0a*/
	hi_ushort16  us_entityclass;   /*Managed Entity class*/
	hi_ushort16  us_instance;      /*msb Managed Entity Instance*/
} hi_omci_proc_msg_head_s;

#if 0 //don't use, only read
/*Create/Delete/MIB Reset/Test Response Message Layout*/
typedef struct {
	hi_omci_proc_msg_head_s st_omcimsghead;
	hi_uchar8               uc_result;
	hi_uchar8               uc_msgcontent[];
} hi_omci_proc_stdrsp_msg_s;

typedef struct {
	hi_omci_proc_msg_head_s st_omcimsghead;
	hi_uchar8               uc_msgcontent[];
} hi_omci_proc_std_msg_s;

typedef struct {
	hi_omci_proc_msg_head_s st_omcimsghead;
	hi_ushort16             us_msgsize;       /*size of msg content field */
	hi_uchar8               uc_result;
	hi_uchar8               uc_msgcontent[];
	hi_uchar8               resv;
} hi_omci_proc_rsp_msg_988_s;

typedef struct {
	hi_omci_proc_msg_head_s st_omcimsghead;
	hi_ushort16             us_msgsize;       /*size of msg content field */
	hi_uchar8               uc_msgcontent[];
} hi_omci_proc_ext_msg_s;
#endif

/*omci head*/
#define HI_OMCI_PROC_GET_TCI(pv_data)                        ntohs(*(uint16_t *)(pv_data + 0))
#define HI_OMCI_PROC_GET_MSGTYPE(pv_data)                    (*(uint8_t *)(pv_data + 2))
#define HI_OMCI_PROC_GET_MT(pv_data)                         ((hi_omci_proc_msg_type_u)HI_OMCI_PROC_GET_MSGTYPE(pv_data)).st_msgtype.uc_mt
#define HI_OMCI_PROC_GET_DEVID(pv_data)                      (*(uint8_t *)(pv_data + 3))
#define HI_OMCI_PROC_GET_ENTITY(pv_data)                     ntohs(*(uint16_t *)(pv_data + 4))
#define HI_OMCI_PROC_GET_INST(pv_data)                       ntohs(*(uint16_t *)(pv_data + 6))
#define HI_OMCI_PROC_SET_MSGTYPE(pv_data,value)              (*(uint8_t *)(pv_data + 2) = value)

/*std msg*/
#define HI_OMCI_PROC_STD_GET_CONTENT(pv_data)                (uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0)
#define HI_OMCI_PROC_STD_GET_MASK(pv_data)                   ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0))
#define HI_OMCI_PROC_STD_GET_SETCONTENT(pv_data)             (uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2)
#define HI_OMCI_PROC_STD_GET_UPLOAD_NUM(pv_data)             ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0))
#define HI_OMCI_PROC_STD_GET_ALARM_ARC(pv_data)              (*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0))
#define HI_OMCI_PROC_STD_GET_ALARM_NUM(pv_data)              ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0))
#define HI_OMCI_PROC_STD_GET_IMAGE_SIZE(pv_data)             ntohl(*(uint32_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 1))
#define HI_OMCI_PROC_STD_GET_ESDL_INST(pv_data)              ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 9))
#define HI_OMCI_PROC_STD_GET_WINDOW_SIZE(pv_data)            (*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s)))
#define HI_OMCI_PROC_STD_GET_DS_NUM(pv_data)                 (*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0))

/* std rsp msg */
#define HI_OMCI_PROC_STDRSP_GET_CONTENT(pv_data)             (uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 1)
#define HI_OMCI_PROC_STDRSP_SET_RESULT(pv_data, result)      ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0)) = result)
#define HI_OMCI_PROC_STDRSP_SET_UPLOAD_NUM(pv_data, num)     ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0)) = htons(num))
#define HI_OMCI_PROC_STDRSP_SET_MASK(pv_data,mask)           ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 1)) = htons(mask))
#define HI_OMCI_PROC_STDRSP_SET_SETACT_UNATTR(pv_data, mask) ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 1)) = htons(mask))
#define HI_OMCI_PROC_STDRSP_SET_GETACT_UNATTR(pv_data, mask) ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 28)) = htons(mask))
#define HI_OMCI_PROC_STDRSP_GET_RESULT(pv_data)              (*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0))
#define HI_OMCI_PROC_STDRSP_SET_ALARM_NUM(pv_data, num)      ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0)) = htons(num))
/* start software download */
#define HI_OMCI_PROC_STDRSP_SET_SSDL_WINSIZE(pv_data, val)   ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 1)) = val)
#define HI_OMCI_PROC_STDRSP_SET_SSDL_INST_NUM(pv_data, val)  ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2)) = val)
#define HI_OMCI_PROC_STDRSP_SET_SSDL_MEID(pv_data, val)      ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 3)) = htons(val))
#define HI_OMCI_PROC_STDRSP_SET_SSDL_RESULT(pv_data, val)    ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 5)) = val)
/* download section */
#define HI_OMCI_PROC_STDRSP_SET_DS_NUM(pv_data, num)         ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 1)) = num)
/* end software download */
#define HI_OMCI_PROC_STDRSP_SET_ESDL_INST_NUM(pv_data, val)  ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 1)) = val)
#define HI_OMCI_PROC_STDRSP_SET_ESDL_MEID(pv_data, val)      ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2)) = htons(val))
#define HI_OMCI_PROC_STDRSP_SET_ESDL_RESULT(pv_data, val)    ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 4)) = val)

/*ext msg*/
#define HI_OMCI_PROC_EXT_GET_SIZE(pv_data)                   ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0))
#define HI_OMCI_PROC_EXT_GET_CONTENT(pv_data)                (uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2)
#define HI_OMCI_PROC_EXT_GET_MASK(pv_data)                   ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2))
#define HI_OMCI_PROC_EXT_GET_SETCONTENT(pv_data)             (uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 4)
#define HI_OMCI_PROC_EXT_GET_ALARM_ARC(pv_data)              (*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2))
#define HI_OMCI_PROC_EXT_GET_ALARM_NUM(pv_data)              ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2))
#define HI_OMCI_PROC_EXT_GET_UPLOAD_NUM(pv_data)             ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2))
#define HI_OMCI_PROC_EXT_GET_IMAGE_SIZE(pv_data)             ntohl(*(uint32_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 3))
#define HI_OMCI_PROC_EXT_GET_GN_SEQ(pv_data)                 ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 4))
#define HI_OMCI_PROC_EXT_GET_WINDOW_SIZE(pv_data)            (*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2))
#define HI_OMCI_PROC_EXT_GET_ESDL_INST(pv_data)              ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 11))
#define HI_OMCI_PROC_EXT_GET_DS_NUM(pv_data)                 (*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2))

/*ext rsp msg*/
#define HI_OMCI_PROC_EXTRSP_GET_SIZE(pv_data)                 HI_OMCI_PROC_EXT_GET_SIZE(pv_data)
#define HI_OMCI_PROC_EXTRSP_GET_RESULT(pv_data)               (*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2))
#define HI_OMCI_PROC_EXTRSP_GET_CONTENT(pv_data)              (uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2)
#define HI_OMCI_PROC_EXTRSP_SET_SIZE(pv_data, len)            ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 0)) = htons(len))
#define HI_OMCI_PROC_EXTRSP_SET_RESULT(pv_data, result)       ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2)) = result)
#define HI_OMCI_PROC_EXTRSP_SET_SETACT_UNATTR(pv_data, mask)  ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 3)) = htons(mask))
#define HI_OMCI_PROC_EXTRSP_SET_GETACT_MASK(pv_data, mask)    ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 3))  = htons(mask))
#define HI_OMCI_PROC_EXTRSP_SET_GETACT_UNATTR(pv_data, mask)  ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 5))  = htons(mask))
#define HI_OMCI_PROC_EXTRSP_SET_ALARM_NUM(pv_data, num)       ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2)) = htons(num))
#define HI_OMCI_PROC_EXTRSP_SET_UPLOAD_NUM(pv_data, num)      ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2)) = htons(num))
#define HI_OMCI_PROC_EXTRSP_GET_INST_SIZE(pv_data)            ntohs(*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 2))

/* start software download */
#define HI_OMCI_PROC_EXTRSP_SET_SSDL_WINSIZE(pv_data, val)    ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 3)) = val)
#define HI_OMCI_PROC_EXTRSP_SET_SSDL_INST_NUM(pv_data, val)   ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 4)) = val)
#define HI_OMCI_PROC_EXTRSP_SET_SSDL_MEID(pv_data, val)       ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 5)) = htons(val))
#define HI_OMCI_PROC_EXTRSP_SET_SSDL_RESULT(pv_data, val)     ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 7)) = val)
/* download section */
#define HI_OMCI_PROC_EXTRSP_SET_DS_NUM(pv_data, num)          ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 3)) = num)
/* end software download */
#define HI_OMCI_PROC_EXTRSP_SET_ESDL_INST_NUM(pv_data, val)   ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 3)) = val)
#define HI_OMCI_PROC_EXTRSP_SET_ESDL_MEID(pv_data, val)       ((*(uint16_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 4)) = htons(val))
#define HI_OMCI_PROC_EXTRSP_SET_ESDL_RESULT(pv_data, val)     ((*(uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 6)) = val)
/* get next */
#define HI_OMCI_PROC_EXTRSP_GET_GN_VAL(pv_data)               (uint8_t *)(pv_data + sizeof(hi_omci_proc_msg_head_s) + 5)
/*******************************************************************************************************************************/
typedef struct {
	hi_uint32 ui_rxtotal;            /* total Received msg conuter */
	hi_uint32 ui_rxnoack;            /* Rx no-Acknowledgement msg counter */
	hi_uint32 ui_rxrepeat;           /* Rx repeated msg counter */
	hi_uint32 ui_processok;          /* Command process successfully counter */
	hi_uint32 ui_processfail;        /* Commadn process error counter */
	hi_uint32 ui_discfordown;        /* discarding counter for ont down */
	hi_uint32 ui_txtotal;            /* total sent message counter */
	hi_uint32 ui_txalarm;            /* Tx Alarm msg counter */
	hi_uint32 ui_txavc;              /* Tx AVC msg counter */
	hi_uint32 ui_txtestresult;       /* Tx Test Result counter */
	hi_uint32 ui_nullpointer;        /* NULL pointer counter */
	hi_uint32 ui_paraerr;            /* Parameters error counter */
	hi_uint32 ui_writeqerr;          /* Writing queue error counter */
	hi_uint32 ui_cleanque;           /* cleaning Queue counter */
} hi_omci_proc_stat_s;

typedef struct {
	hi_uchar8           uc_workmode;
	hi_omci_proc_stat_s st_stat;    /*OMCI模块统计信息*/
} hi_omci_proc_global_s;

typedef struct {
	hi_uint32                   ui_acttype;
	hi_omci_proc_actcallback    pv_action;
} hi_omci_mib_act_s;


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OMCI_PROC_H__ */
