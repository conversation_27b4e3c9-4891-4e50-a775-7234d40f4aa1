/*
 * Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: omci extended message
 * Author: hsan
 * Create: 2023-12-11
 * History: 2023-12-11 coding
 */

#ifndef __HI_OMCI_EXTEND_H__
#define __HI_OMCI_EXTEND_H__

/* minimum length of message contents */
#define HI_OMCI_EXT_PRO_NULL_RSP_LEN            0
#define HI_OMCI_EXT_PRO_CREATE_RSP_LEN          3
#define HI_OMCI_EXT_PRO_CREATECC_RSP_LEN        3
#define HI_OMCI_EXT_PRO_DELETE_RSP_LEN          1
#define HI_OMCI_EXT_PRO_DELETECC_RSP_LEN        1
#define HI_OMCI_EXT_PRO_SET_RSP_LEN             5
#define HI_OMCI_EXT_PRO_GET_RSP_LEN             7
#define HI_OMCI_EXT_PRO_GETCC_RSP_LEN           7
#define HI_OMCI_EXT_PRO_GETALLALARM_RSP_LEN     2
#define HI_OMCI_EXT_PRO_GETALLALARMNEXT_RSP_LEN 32
#define HI_OMCI_EXT_PRO_MIBUPLOAD_RSP_LEN       2
#define HI_OMCI_EXT_PRO_MIBUPLOADNEXT_RSP_LEN   8
#define HI_OMCI_EXT_PRO_MIBRESET_RSP_LEN        1
#define HI_OMCI_EXT_PRO_TEST_RSP_LEN            1
#define HI_OMCI_EXT_PRO_STARTDOWNLOAD_RSP_LEN   5
#define HI_OMCI_EXT_PRO_DOWNLOADING_RSP_LEN     2
#define HI_OMCI_EXT_PRO_ENDDOWNLOAD_RSP_LEN     5
#define HI_OMCI_EXT_PRO_ACTIVEIMAGE_RSP_LEN     1
#define HI_OMCI_EXT_PRO_COMMITIMAGE_RSP_LEN     1
#define HI_OMCI_EXT_PRO_SYNCTIME_RSP_LEN        1
#define HI_OMCI_EXT_PRO_REBOOT_RSP_LEN          1
#define HI_OMCI_EXT_PRO_GETNEXT_RSP_LEN         3
#define HI_OMCI_EXT_PRO_GET_CURRDATA_RSP_LEN    7
#define HI_OMCI_EXT_PRO_SET_TABLE_RSP_LEN       1

#define HI_OMCI_EXT_MIC_LEN                     4

#endif /* __HI_OMCI_EXTEND_H__ */
