/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_libdef.h
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_10_27

******************************************************************************/
#ifndef __HI_OMCI_LIBDEF_H__
#define __HI_OMCI_LIBDEF_H__

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define hi_omci_lib_systrace(ui_ret, arg1, arg2, arg3, arg4)  \
	hi_systrace((hi_uint32)HI_SUBMODULE_OMCI_LIB, ui_ret, arg1, arg2, arg3, arg4)

#define hi_omci_lib_debug(fmt,arg...)                                    \
	do                                                                   \
	{                                                                    \
		if (hi_log_print_on(HI_SUBMODULE_OMCI_LIB, HI_DBG_LEVEL_DEBUG))  \
		{                                                                \
			printf(fmt, ##arg);                                           \
		}                                                                \
	} while (0)

#define hi_omci_lib_print(ui_level,fmt,arg...)                           \
	do                                                                   \
	{                                                                    \
		if (hi_log_print_on(HI_SUBMODULE_OMCI_LIB, ui_level))            \
		{                                                                \
			printf(fmt, ##arg);                                          \
		}                                                                \
	} while(0)


#define hi_omci_lib_printmem(puc_src,ui_len,fmt,arg...)                  \
	do                                                                  \
	{                                                                    \
		if (hi_log_print_on(HI_SUBMODULE_OMCI_LIB, HI_DBG_LEVEL_DEBUG))  \
		{                                                                \
			printf(fmt"\n", ##arg);                                      \
			hi_log_mem(puc_src, ui_len);                                 \
		}                                                                \
	} while(0)

#define HI_OMCI_LIB_FAIL_RET(ui_ret) do {    \
		if(HI_RET_SUCC != ui_ret){   \
			hi_omci_lib_debug("[ui_ret = 0x%08x]", ui_ret);\
			hi_omci_lib_systrace( ui_ret, 0, 0, 0, 0);\
			return ui_ret;} }while(0)

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OMCI_LIBDEF_H__ */
