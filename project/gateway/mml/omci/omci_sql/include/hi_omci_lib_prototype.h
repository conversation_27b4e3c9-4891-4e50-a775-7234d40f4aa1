/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_lib_prototype.h
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_11_08

******************************************************************************/
#ifndef __HI_OMCI_LIB_PROTOTYPE_H__
#define __HI_OMCI_LIB_PROTOTYPE_H__


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*function define*/

/*******************HI_OMCI_SQL.H********************/
extern hi_uint32 hi_omci_sql_countsql(hi_char8 *pc_sql);
extern hi_uint32 hi_omci_sql_counttable(hi_char8 *pc_tablename);
extern hi_uint32 hi_omci_sql_dbstart(hi_void);
extern hi_uint32 hi_omci_sql_dbstop(hi_void);
extern hi_uint32 hi_omci_sql_exec(hi_char8 *pc_sql);
extern hi_uint32 hi_omci_sql_getcolumn(hi_char8 *pc_sql, hi_uchar8 uc_col,
				       hi_uchar8 uc_type, hi_uchar8 *pv_data, hi_uint32 ui_size);
extern hi_uint32 hi_omci_sql_setcolumn(hi_char8 *pc_sql,  hi_uchar8 uc_type,
				       hi_void *pv_data, hi_uint32 ui_size);


/*******************HI_OMCI_SQL_ENTITY.H********************/
extern hi_uint32 hi_omci_sql_entity_set(hi_uint32 ui_cmdtype, hi_uint32 ui_callback, hi_uchar8 *puc_filename,
					hi_uchar8 *puc_funcname);
extern hi_uint32 hi_omci_sql_entity_del(hi_void *pv_callback);
extern hi_uint32 hi_omci_sql_entity_get(hi_uchar8 ui_act, hi_ushort16 us_meid, hi_char8 *pc_tablename,
					hi_void * * pv_callback);
extern hi_uint32 hi_omci_sql_entity_table_set(hi_uint32 ui_meid, hi_uint32 ui_act, hi_char8 *pc_tablename,
		hi_char8 *pc_funcname);
extern hi_uchar8 *hi_omci_sql_entity_msgtype(hi_uchar8 uc_msgtype);
extern hi_uint32 hi_omci_sql_entity_desc(hi_ushort16 us_meid, hi_uchar8 *puc_name, hi_uint32 ui_len);
extern hi_uint32 hi_omci_sql_entity_check_meid(hi_ushort16 us_meid);
extern hi_uint32 hi_omci_sql_entity_stat_set(hi_uint32 ui_meid, hi_uint32 ui_callback, hi_char8 *pc_name);
extern hi_uint32 hi_omci_sql_entity_stat_get(hi_uint32 ui_meid, hi_uint32 *pui_callback);
extern hi_uint32 hi_omci_sql_entity_alarm_set(hi_uint32 ui_meid, hi_uint32 ui_callback, hi_char8 *pc_name);
extern hi_uint32 hi_omci_sql_entity_alarm_get(hi_uint32 ui_meid, hi_uint32 *pui_callback);
extern hi_uint32 hi_omci_sql_entity_extlib_set(hi_char8 *pc_name, hi_void *pv_handler);
extern hi_uint32 hi_omci_sql_entity_extlib_get(hi_char8 *pc_name, hi_void *pv_handler);

/*******************HI_OMCI_SQL_INST.H********************/
extern hi_uint32 hi_omci_sql_inst_check(hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_sql_inst_checkinst(hi_ushort16 us_meid, hi_ushort16 us_instid);
extern hi_uint32 hi_omci_sql_inst_checktable(hi_ushort16 us_meid);
extern hi_uint32 hi_omci_sql_inst_create(hi_ushort16 us_meid);
extern hi_uint32 hi_omci_sql_inst_del(hi_ushort16 us_meid, hi_ushort16 us_instid);
extern hi_uint32 hi_omci_sql_inst_get(hi_omci_mib_attrlist_s *pst_attrlist, hi_uchar8 *puc_buf);
extern hi_uint32 hi_omci_sql_inst_clear(hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_sql_inst_getcolblob(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_void *pv_data, hi_uint32 ui_size);
extern hi_uint32 hi_omci_sql_inst_getcolint32(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_uint32 *pui_data);
extern hi_uint32 hi_omci_sql_inst_getcolshort16(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_ushort16 *pus_data);
extern hi_uint32 hi_omci_sql_inst_getcoltext(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_void *pv_data, hi_uint32 ui_size);
extern hi_uint32 hi_omci_sql_inst_getcoluchar8(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_uchar8 *puc_data);
extern hi_uint32 hi_omci_sql_inst_getcount(hi_ushort16 us_meid);
extern hi_uint32 hi_omci_sql_inst_insert(hi_omci_mib_attrlist_s  *pst_attrlist);
extern hi_uint32 hi_omci_sql_inst_set(hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_sql_inst_setcolblob(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_void *pv_data, hi_uint32 ui_size);
extern hi_uint32 hi_omci_sql_inst_setcolint32(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_uint32 ui_data);
extern hi_uint32 hi_omci_sql_inst_setcolshort16(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_ushort16 us_data);
extern hi_uint32 hi_omci_sql_inst_setcoltext(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_void *pv_data, hi_uint32 ui_size);
extern hi_uint32 hi_omci_sql_inst_setcoluchar8(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_uchar8 uc_data);
extern hi_uint32 hi_omci_sql_inst_setsql(hi_omci_mib_attrlist_s *pst_attrlist, hi_char8 *pc_sql, hi_uint32 sql_size);
extern hi_uint32 hi_omci_sql_inst_getall(hi_omci_mib_attrlist_s *pst_attrlist, hi_uchar8 *puc_buf);

/*******************HI_OMCI_SQL_ATTR.H***********************/
extern hi_uint32 hi_omci_sql_attr_check(hi_omci_mib_attrlist_s *pst_attrlist, hi_uchar8 uc_indx);
extern hi_void hi_omci_sql_attr_decode(hi_void *pv_data, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_void hi_omci_sql_attr_decodentoh(hi_void *pv_data, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_void hi_omci_sql_attr_encode(hi_void *pv_data, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_void hi_omci_sql_attr_encodehton(hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_sql_attr_getlist(hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_sql_attr_tabnum_set(hi_uint32 ui_meid, hi_uint32 ui_index, hi_uint32 tabnum);
extern hi_void hi_omci_sql_attr_dump(hi_omci_mib_attrlist_s *pst_attrlist);

/********************HI_OMCI_SQL_UPLOAD.H***********************/
extern hi_uint32 hi_omci_sql_upload(uint32_t mode);
extern hi_uint32 hi_omci_sql_upload_attr(hi_omci_mib_attrlist_s *pst_attrlist, hi_uint32 *pui_num);
extern hi_uint32 hi_omci_sql_upload_exit(hi_void);
extern hi_void hi_omci_sql_upload_free(hi_void);
extern hi_uint32 hi_omci_sql_upload_getblock(hi_void *pv_outdata, hi_uint32 ui_seqnum, uint16_t *blk_size, uint32_t mode);
extern hi_uint32 hi_omci_sql_upload_getnum(hi_void);
extern hi_uint32 hi_omci_sql_upload_init(uint32_t mode);
extern hi_void hi_omci_sql_upload_data_init(hi_void);
extern hi_uint32 hi_omci_sql_upload_inst(hi_omci_mib_attrlist_s *pst_attrlist, uint32_t mode);
extern hi_uint32 hi_omci_sql_upload_dump(hi_uint32 ui_cmdtype, hi_void *pv_data, hi_uint32 ui_inlen,
		hi_uint32 *pui_outlen);

/********************HI_OMCI_SQL_ALARM.H***********************/
extern hi_int32 hi_omci_sql_alarm_inst_create(hi_ushort16 us_meid, hi_ushort16 us_instid);
extern hi_int32 hi_omci_sql_alarm_inst_delete(hi_ushort16 us_meid, hi_ushort16 us_instid);
extern hi_int32 hi_omci_sql_alarm_inst_set(hi_ushort16 us_meid,
		hi_ushort16 us_instid, hi_ushort16 us_bitmap);
extern hi_int32 hi_omci_sql_alarm_inst_get(hi_ushort16 us_meid,
		hi_ushort16 us_instid, hi_ushort16 *pus_bitmap);
extern hi_int32 hi_omci_sql_alarm_upload(hi_uint32 ui_arc);
extern hi_int32 hi_omci_sql_alarm_upload_num_get(hi_void);
extern hi_int32 hi_omci_sql_alarm_upload_get(hi_void *pv_data, hi_uint32 ui_seqnum);
extern hi_void hi_omci_sql_alarm_upload_init(hi_void);
extern hi_void hi_omci_sql_alarm_upload_exit(hi_void);

/********************hi_omci_proc.c********************/
extern hi_uint32 hi_omci_proc_act(hi_void *pv_data, hi_void *pv_outdata,
				  hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_proc_actmanage(hi_void *pv_data, hi_void *pv_outdata,
					hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_proc_checkmsg(hi_void *pv_data, hi_void *pv_outdata,
				       hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_proc_checktci(hi_void *pv_data);
extern hi_uint32 hi_omci_proc_clearlst(hi_void *pv_data);
extern hi_void   hi_omci_proc_first(hi_void);
extern hi_uint32 hi_omci_proc_getlen(hi_void *pv_data);
extern hi_uint32 hi_omci_proc_init(hi_void);
extern hi_uint32 hi_omci_proc_msg(hi_void *pv_data, hi_uint32 ui_len);
extern hi_uint32 hi_omci_proc_recordlst(hi_void *pv_data);
extern hi_uint32 hi_omci_proc_resend(hi_void *pv_data);
extern hi_uint32 hi_omci_proc_sendfail(hi_void *pv_data, hi_void *pv_outdata, hi_uchar8 uc_result);
extern hi_uint32 hi_omci_proc_sendmsg(hi_void *pv_data, hi_void *pv_outdata);
extern hi_uint32 hi_omci_proc_sendrsp(hi_void *pv_data, hi_void *pv_outdata);
extern hi_int32 hi_omci_proc_sendalarm(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_ushort16 us_bitmap);
extern hi_int32 hi_omci_proc_sendavc(hi_ushort16 us_meid, hi_ushort16 us_instid,
				     hi_ushort16 us_bitmap, hi_void *pv_data, hi_uint32 ui_len);
extern hi_int32 hi_omci_proc_sendtestresult(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_void *pv_data);
extern hi_void   hi_omci_proc_alarm_init(hi_void);
void hi_omci_init_alarm_seq(void);
/********************hi_omci_mib_std.c********************/
extern hi_uint32 hi_omci_mib_actactiveimage(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actcommitimage(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actcreate(hi_void *pv_data, hi_void *pv_outdata,
				       hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actdelete(hi_void *pv_data, hi_void *pv_outdata,
				       hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actdownloading(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actenddownload(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actget(hi_void *pv_data, hi_void *pv_outdata,
				    hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actgetalarm(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actgetcurrdata(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actgetnext(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actgetnextalarm(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actmibreset(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actmibupload(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actmibuploadnext(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actreboot(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actset(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actstartdownload(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_actsynctime(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_acttest(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_acttestresult(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_std(hi_void *pv_data, hi_void *pv_outdata,
				 hi_omci_mib_attrlist_s *pst_attrlist);

/********************hi_omci_mib_ext.c********************/
extern hi_uint32 hi_omci_mib_ext(hi_void *pv_data, hi_void *pv_outdata,
				 hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actactiveimage(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actcommitimage(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actcreate(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actdelete(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actdownloading(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actenddownload(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actget(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actgetalarm(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actgetcurrdata(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actgetnext(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actgetnextalarm(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actmibreset(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actmibupload(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actmibuploadnext(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actreboot(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actset(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actstartdownload(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_actsynctime(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_acttest(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_ext_acttestresult(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);

/********************hi_omci_mib.c********************/
extern hi_uint32 hi_omci_mib_checkdb(hi_void *pv_data, hi_void *pv_outdata,
				     hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_void hi_omci_mib_datasync(hi_uchar8 uc_msgtype);
extern hi_uint32 hi_omci_mib_exit(hi_void);
extern hi_uint32 hi_omci_mib_ext_checkdb(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_forward(hi_void *pv_data, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_getattrmask(hi_omci_mib_attrlist_s *pst_attrlist, hi_uchar8 uc_opt);
extern hi_uint32 hi_omci_mib_init(hi_void);
extern hi_uint32 hi_omci_mib_next(hi_void *pv_data, hi_omci_mib_attrlist_s *pst_attrlist);
extern hi_uint32 hi_omci_mib_std_checkdb(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist);

/********************hi_omci_upgrade.c********************/
extern hi_uint32 hi_omci_upgrade_end(hi_void *pv_data, hi_void *pv_outdata);
extern hi_uint32 hi_omci_upgrade_active(hi_void *pv_data, hi_void *pv_outdata);
extern hi_uint32 hi_omci_upgrade_commit(hi_void *pv_data, hi_void *pv_outdata);

extern hi_uint32 hi_omci_upgrade_init(hi_void);
extern hi_uint32 hi_omci_upgrade_loading(hi_void *pv_data, hi_void *pv_outdata, hi_uint32 ui_len);
/*****************************************************************************
 函 数 名  : hi_omci_upgrade_set_filename
 功能描述  : 外部函数调用接口在升级前调用更改输出文件全路径
 输入参数  : hi_uchar8 *puc_filename
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
extern hi_uint32 hi_omci_upgrade_set_filename(hi_uchar8 *puc_filename);
extern hi_uint32 hi_omci_upgrade_start(hi_void *pv_data, hi_void *pv_outdata);

/********************hi_omci_mib_cmdtype.c********************/
extern hi_uint32 hi_omci_mib_cmdtype_init(hi_void);
extern hi_uint32 hi_omci_mib_saveapi(hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen);
extern hi_uint32 hi_omci_mib_backup(hi_void);
extern hi_uint32 hi_omci_mib_reinit(hi_void);

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */


#endif /* __HI_OMCI_LIB_PROTOTYPE_H__ */
