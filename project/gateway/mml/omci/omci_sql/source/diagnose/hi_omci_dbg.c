/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_dbg.c
  版 本 号   : 初稿
  作    者   : t00185260
  生成日期   : D2011_10_25
  功能描述   : OMCI模块dbg处理
******************************************************************************/

#include "hi_omci_lib.h"
#include "hi_ipc.h"
//#include "hi_drv_common.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#ifndef HI_NTOHL
#define HI_NTOHL(x) ((((x) & 0x000000FF) << 24) \
		     | (((x) & 0x0000FF00) << 8)  \
		     | (((x) & 0x00FF0000) >> 8)  \
		     | (((x) & 0xFF000000) >> 24))     /**< 大端到小端, 4 字节      */
#endif
/*uint print*/
#define HI_LOG_MEM_UINT_HTON( len, ptr )    { \
		hi_uint32 i; hi_uchar8 *ptr_tmp = (hi_uchar8*)ptr; \
		for ( i=0; i < len; i+=sizeof(hi_uint32) ) \
		{  if ( 0 == (i%16)) LOG(all, ERROR, "\r\n[0x%08x] : ",i+(hi_uint32)ptr); LOG(all, ERROR, "%08x ",HI_NTOHL(*(hi_uint32*)(ptr_tmp+i))); } LOG(all, ERROR, "\r\n"); \
	}
hi_uchar8  g_uc_debug_mode = 0;
/*****************************************************************************
 函 数 名  : hi_omci_dbg_pkt_file
 功能描述  : 打印omci消息
 输入参数  : hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_dbg_pkt_file(hi_void *pv_data, hi_uchar8 uc_dir)
{
	hi_uchar8 uc_mename[HI_OMCI_MIB_ENTITY_NAME_LEN];
	hi_os_timeval_s st_tv;

	hi_omci_sql_entity_msgtype(HI_OMCI_PROC_GET_MSGTYPE((hi_uchar8 *)pv_data));

	HI_OS_MEMSET_S(uc_mename, HI_OMCI_MIB_ENTITY_NAME_LEN, 0, HI_OMCI_MIB_ENTITY_NAME_LEN);

	hi_os_gettimeofday(&st_tv);

	hi_omci_sql_entity_desc((hi_ushort16)HI_OMCI_PROC_GET_ENTITY((hi_uchar8 *)pv_data), uc_mename,
				HI_OMCI_MIB_ENTITY_NAME_LEN);
	LOG(all, ERROR, "\r\n======================[%0d][%0d]==========================", (hi_int32)st_tv.tv_sec, (hi_int32)st_tv.tv_usec);
	if (HI_OMCI_DBG_DIR_IN == uc_dir) {
		LOG(all, ERROR, "\r\nOLT => ONT ");
	} else {
		LOG(all, ERROR, "\r\nONT => OLT ");
	}
	LOG(all, ERROR, "\r\n Entity[%04u]:%s",
	    HI_OMCI_PROC_GET_ENTITY((hi_uchar8 *)pv_data), uc_mename);
	LOG(all, ERROR, "\r\n Instance-H[0x%02x] Instance-L[0x%02x]",
	    (HI_OMCI_PROC_GET_INST((hi_uchar8 *)pv_data) & 0xff00) >> 8,
	    HI_OMCI_PROC_GET_INST((hi_uchar8 *)pv_data) & 0x00ff);
	LOG(all, ERROR, "\r\n PRI[%01u] SN[%04u] DEVID[0x%02x] MSGTYPE[%s]",
	    (HI_OMCI_PROC_GET_TCI((hi_uchar8 *)pv_data) & 0x8000 >> 15),
	    HI_OMCI_PROC_GET_TCI((hi_uchar8 *)pv_data) & 0x7FFF,
	    HI_OMCI_PROC_GET_DEVID((hi_uchar8 *)pv_data),
	    hi_omci_sql_entity_msgtype(HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data)));
	if (HI_OMCI_PROC_DEVICEID_STD == HI_OMCI_PROC_GET_DEVID((hi_uchar8 *)pv_data)) {
		LOG(all, ERROR, "\r\n CONTENT:");

		HI_LOG_MEM_UINT_HTON(HI_OMCI_PROC_MSGSTCONTEXT_LEN, pv_data);
	} else if(HI_OMCI_PROC_GET_DEVID((hi_uchar8 *)pv_data) == HI_OMCI_PROC_DEVICEID_EXT){
		LOG(all, ERROR, "\r\n CONTENT:");
		uint16_t len = HI_OMCI_PROC_HEADLEN_EXT + HI_OMCI_PROC_EXT_GET_SIZE((uint8_t *)pv_data);
		if (len <= HI_OMCI_PROC_HEADLEN_EXT + HI_OMCI_PROC_CONTENTLEN_EXT)
			HI_LOG_MEM_UINT_HTON(len, pv_data);
	} else {
		LOG(all, ERROR, "\r\n NO CONTENT");
	}

	return;
}

/*****************************************************************************
 函 数 名  : hi_omci_dbg_onusta_file
 功能描述  : 打印omci 注册状态
 输入参数  : hi_uchar8 uc_status
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_dbg_onusta_file(hi_uint32 ui_status)
{
	if (hi_log_print_on((hi_uint32)HI_SUBMODULE_OMCI_FILELOG, HI_DBG_LEVEL_DEBUG)) {
		LOG(all, ERROR, "\r\n**************************[onu status : %u ]******************************", ui_status);
	}
	return;
}
/*****************************************************************************
 函 数 名  : hi_omci_dbg_pkt
 功能描述  : 打印omci消息
 输入参数  : hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_dbg_pkt(hi_void *pv_data, hi_uchar8 uc_dir)
{
	hi_uchar8 uc_mename[HI_OMCI_MIB_ENTITY_NAME_LEN];
	hi_os_timeval_s st_tv;

	if (hi_log_print_on((hi_uint32)HI_SUBMODULE_OMCI_FILELOG, HI_DBG_LEVEL_DEBUG)) {
		hi_omci_dbg_pkt_file(pv_data, uc_dir);
	}

	if (!hi_log_print_on((hi_uint32)HI_SUBMODULE_OMCI_LIB, HI_DBG_LEVEL_DEBUG)) {
		return;
	}

	hi_omci_sql_entity_msgtype(HI_OMCI_PROC_GET_MSGTYPE((hi_uchar8 *)pv_data));

	HI_OS_MEMSET_S(uc_mename, HI_OMCI_MIB_ENTITY_NAME_LEN, 0, HI_OMCI_MIB_ENTITY_NAME_LEN);

	hi_os_gettimeofday(&st_tv);

	hi_omci_sql_entity_desc((hi_ushort16)HI_OMCI_PROC_GET_ENTITY((hi_uchar8 *)pv_data), uc_mename,
				HI_OMCI_MIB_ENTITY_NAME_LEN);
	hi_omci_lib_print(HI_DBG_LEVEL_DEBUG, "\r\n======================[%lld][%lld]==========================", st_tv.tv_sec,
			  st_tv.tv_usec);
	if (HI_OMCI_DBG_DIR_IN == uc_dir) {
		hi_omci_lib_print(HI_DBG_LEVEL_DEBUG, "\r\nOLT => ONT ");
	} else {
		hi_omci_lib_print(HI_DBG_LEVEL_DEBUG, "\r\nONT => OLT ");
	}
	hi_omci_lib_print(HI_DBG_LEVEL_DEBUG, "\r\n Entity[%04u]:%s",
			  HI_OMCI_PROC_GET_ENTITY((hi_uchar8 *)pv_data), uc_mename);
	hi_omci_lib_print(HI_DBG_LEVEL_DEBUG, "\r\n Instance-H[0x%02x] Instance-L[0x%02x]",
			  (HI_OMCI_PROC_GET_INST((hi_uchar8 *)pv_data) & 0xff00) >> 8,
			  HI_OMCI_PROC_GET_INST((hi_uchar8 *)pv_data) & 0x00ff);
	hi_omci_lib_print(HI_DBG_LEVEL_DEBUG, "\r\n PRI[%01d] SN[%04u] DEVID[0x%02x] MSGTYPE[%s]",
			  ((HI_OMCI_PROC_GET_TCI((hi_uchar8 *)pv_data) & 0x8000) >> 15),
			  HI_OMCI_PROC_GET_TCI((hi_uchar8 *)pv_data) & 0x7FFF,
			  HI_OMCI_PROC_GET_DEVID((hi_uchar8 *)pv_data),
			  hi_omci_sql_entity_msgtype(HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data)));
	if (HI_OMCI_PROC_DEVICEID_STD == HI_OMCI_PROC_GET_DEVID((hi_uchar8 *)pv_data)) {
		hi_omci_lib_print(HI_DBG_LEVEL_DEBUG, "\r\n CONTENT:");
		HI_PRINT_MEM_UINT_HTON_OMCI_DBG(HI_OMCI_PROC_MSGSTCONTEXT_LEN, pv_data);
	} else {
		hi_omci_lib_print(HI_DBG_LEVEL_DEBUG, "\r\n CONTENT:");
		HI_PRINT_MEM_UINT_HTON_OMCI_DBG(HI_OMCI_PROC_HEADLEN_EXT + HI_OMCI_PROC_EXT_GET_SIZE((uint8_t *)pv_data), pv_data);
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return;
}
#if 0
typedef union {
	hi_uchar8       uc_msgtype;
	struct {
		hi_uchar8   uc_mt: 5;      /*message type*/
		hi_uchar8   uc_ak: 1;      /*acknowledgement*/
		hi_uchar8   uc_ar: 1;      /*acknowledge request*/
		hi_uchar8   uc_db: 1;      /*destination bit, this bit is always 0*/
	} st_msgtype;
} omcidbg_msg_type_u;

typedef struct {
	hi_ushort16 us_meid;
	hi_ushort16 us_instid;
	hi_uchar8   uc_msgtype;
	hi_uchar8   uc_content[3];
} omcidbg_msg_s;
#endif

hi_uint32 hi_omci_dbg_recv_msg(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{
#if 0
	omcidbg_msg_s *pst_msg = (omcidbg_msg_s *)pv_data;
	static hi_ushort16 us_tci = 0;
	hi_uchar8 uc_msg[40] = {0};
	omcidbg_msg_type_u u_msgtype;
	u_msgtype.st_msgtype.uc_mt = pst_msg->uc_msgtype;
	u_msgtype.st_msgtype.uc_ak = 0;
	u_msgtype.st_msgtype.uc_ar = 1;
	u_msgtype.st_msgtype.uc_db = 0;

	us_tci += 1;
	us_tci |= 0x8000;

	*(hi_ushort16 *)(uc_msg + 0) = us_tci;
	uc_msg[2] = u_msgtype.uc_msgtype;
	uc_msg[3] = 0x0a;

	*(hi_ushort16 *)(uc_msg + 4) = ntohs(pst_msg->us_meid);
	*(hi_ushort16 *)(uc_msg + 6) = ntohs(pst_msg->us_instid);
	HI_OS_MEMCPY_S((hi_void *)(uc_msg + 8), sizeof(uc_msg) - 8, pst_msg->uc_content, 32);

	/**/
	HI_PRINT_MEM(40, pv_data);
	HI_PRINT_MEM(40, uc_msg);
	(hi_void) hi_omci_proc_msg(uc_msg, ui_in);
#endif
	(hi_void) hi_omci_proc_msg(pv_data, ui_in);
	return HI_RET_SUCC;
}


hi_uint32 hi_omci_dbg_set_upgradefile(hi_void *pv_data, hi_uint32 ui_in, hi_uint32 *pui_outlen)
{

	hi_omci_upgrade_set_filename((hi_uchar8 *)pv_data);
	return HI_RET_SUCC;
}
#if 0
typedef struct {
	hi_uchar8 uc_data[40];
} omci_dgb_msg_s;
#endif
HI_DEF_IPC(hi_ipc_omci_dbg_recv_msg, hi_uchar8 *, puc_data, ui_size)
{
	hi_uint32 ui_length = 0;
	return hi_omci_proc_msg(puc_data, ui_length);
}

HI_DEF_IPC(hi_ipc_omci_dbg_mode_set, hi_uchar8 *, puc_data, ui_size)
{
	g_uc_debug_mode = *puc_data;
	return HI_RET_SUCC;
}
#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
