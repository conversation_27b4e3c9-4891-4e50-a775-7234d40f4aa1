/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_mib_cmdtype.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_11_11
  功能描述   : 一些命令
******************************************************************************/

#include "hi_omci_lib.h"
#include "hi_ipc.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


/*****************************************************************************
 函 数 名  : hi_omci_mib_dump_meid
 功能描述  : 根据meid获取数据
 输入参数  : hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
HI_DEF_IPC(hi_omci_mib_dump_meid, hi_ushort16 *, pv_data)
{
	hi_ushort16 us_meid;
	hi_uint32   ui_ret = HI_RET_SUCC;
	hi_uchar8   *puc_buf = HI_NULL;
	hi_omci_mib_attrlist_s st_attrlist;

	HI_OS_MEMSET_S(&st_attrlist, sizeof(hi_omci_mib_attrlist_s), 0, sizeof(hi_omci_mib_attrlist_s));
	us_meid = *(hi_ushort16 *)pv_data;
	st_attrlist.us_meid = us_meid;
	st_attrlist.uc_opt    = HI_OMCI_ATT_R_W_S_E;
	st_attrlist.us_mask   = 0xFFFF;

	ui_ret = hi_omci_sql_attr_getlist(&st_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	/*malloc mem for data*/
	puc_buf = (hi_uchar8 *)hi_os_malloc(st_attrlist.us_totalsize);
	if (HI_NULL == puc_buf) {
		hi_omci_lib_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	/*get inst content*/
	hi_omci_sql_inst_getall(&st_attrlist, puc_buf);

	hi_os_free(puc_buf);
	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}
