/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_mib .c
  版 本 号   : 初稿
  作    者   : t00185260
  生成日期   : D2011_09_22
  功能描述   : MIB数据库管理
******************************************************************************/

#include "hi_omci_lib.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*****************************************************************************
 函 数 名  : hi_omci_mib_parse_set
 功能描述  : 解析出set类型消息中的属性值
 输出参数  : 无
 返 回 值  :
*****************************************************************************/
hi_uint32 hi_omci_mib_parse_set(hi_omci_msg_s *pst_msg, hi_void *pv_indata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_cnt;
	hi_uchar8 *puc_outdata = pst_msg->uc_content;
	hi_uchar8 *puc_intdata = pv_indata;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;
	for (ui_cnt = 0; ui_cnt < pst_attrlist->uc_num; ui_cnt++) {
		pst_attr = &(pst_attrlist->st_attr[ui_cnt]);
		if (!HI_OMCI_CHECK_ATTR(pst_msg->st_msghead.us_attmask, (ui_cnt + 1))) {
			puc_outdata += pst_attr->uc_size;
			continue;
		}

		switch (pst_attr->uc_type) {
		case HI_OMCI_SQL_DATA_UCHAR_E :
			*(hi_uchar8 *)puc_outdata = *(hi_uchar8 *)puc_intdata;
			break;
		case HI_OMCI_SQL_DATA_USHORT_E :
			*(hi_ushort16 *)puc_outdata = ntohs(*(hi_ushort16 *)puc_intdata);
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			*(hi_uint32 *)puc_outdata = ntohl(*(hi_uint32 *)puc_intdata);
			break;
		case HI_OMCI_SQL_DATA_BINARY_E :
		case HI_OMCI_SQL_DATA_TABLEATTR_E :
		case HI_OMCI_SQL_DATA_TEXT_E :
			HI_OS_MEMCPY_S(puc_outdata, sizeof(pst_msg->uc_content) - (puc_outdata - pst_msg->uc_content), puc_intdata,
				       pst_attr->uc_size);
			break;
		default:
			break;
		}
		puc_intdata += pst_attr->uc_size;
		puc_outdata += pst_attr->uc_size;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

static void hi_omci_mib_proc_std_msg(uint8_t act_type, void *data,
				     hi_omci_mib_attrlist_s *attrlis, hi_omci_msg_s *msg)
{
	int32_t ret = HI_RET_SUCC;
	switch (act_type) {
	case HI_OMCI_PRO_ACTIONS_SET_E:
		msg->st_msghead.us_attmask = (uint16_t)HI_OMCI_PROC_STD_GET_MASK((uint8_t *)data);
		hi_omci_mib_parse_set(msg, HI_OMCI_PROC_STD_GET_SETCONTENT((uint8_t *)data), attrlis);
		break;
	case HI_OMCI_PRO_ACTIONS_TEST_E:
		ret = memcpy_s(msg, sizeof(hi_omci_msg_s), data, HI_OMCI_PROC_MSGSTCONTEXT_LEN);
		break;
	default:
		msg->st_msghead.us_attmask = (uint16_t)HI_OMCI_PROC_STD_GET_MASK((uint8_t *)data);
		ret = memcpy_s(msg->uc_content, sizeof(msg->uc_content),
			       HI_OMCI_PROC_STD_GET_CONTENT((uint8_t *)data), HI_OMCI_PROC_CONTENTLEN_STD);
		break;
	}
	if (ret != HI_RET_SUCC)
		hi_omci_err("%s,%d :memcpy_s return[%d] failed\n", __func__, __LINE__, ret);
}

static void hi_omci_mib_proc_ext_msg(uint8_t act_type, void *data,
				     hi_omci_mib_attrlist_s *attrlis, hi_omci_msg_s *msg)
{
	int32_t ret = HI_RET_SUCC;
	switch (act_type) {
	case HI_OMCI_PRO_ACTIONS_SET_E:
		msg->st_msghead.us_attmask = (uint16_t)HI_OMCI_PROC_EXT_GET_MASK((uint8_t *)data);
		hi_omci_mib_parse_set(msg, HI_OMCI_PROC_EXT_GET_SETCONTENT((uint8_t *)data), attrlis);
		break;
	case HI_OMCI_PRO_ACTIONS_TEST_E:
		ret = memcpy_s(msg, sizeof(hi_omci_msg_s), data,
			       HI_OMCI_PROC_HEADLEN_EXT + HI_OMCI_PROC_EXT_GET_SIZE((uint8_t *)data));
		break;
	default:
		msg->st_msghead.us_attmask = (uint16_t)HI_OMCI_PROC_EXT_GET_MASK((uint8_t *)data);
		ret = memcpy_s(msg->uc_content, sizeof(msg->uc_content),
			       HI_OMCI_PROC_EXT_GET_CONTENT((uint8_t *)data),
			       HI_OMCI_PROC_EXT_GET_SIZE((uint8_t *)data));
		break;
	}
	if (ret != HI_RET_SUCC)
		hi_omci_err("%s,%d :memcpy_s return[%d] failed\n", __func__, __LINE__, ret);
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_forward
 功能描述  : 配置前动作
 输入参数  : hi_void *pv_data
 输出参数  : hi_void *pv_outdata
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_forward(hi_void *pv_data, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32  ui_ret, ui_outlen;
	hi_uchar8  uc_act;
	hi_void   *pv_callback = HI_NULL;
	hi_omci_msg_s st_msg;
	hi_omci_mib_attrlist_s st_attrlist;

	/*check if cfg callback*/
	ui_ret = hi_omci_sql_entity_get(HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data), pst_attrlist->us_meid,
					HI_OMCI_MIB_ENTITYFWDTABLE_NAME, &pv_callback);

	hi_omci_lib_print(HI_DBG_LEVEL_INFO, "%s %d us_meid %hu\r\n", __func__, __LINE__, pst_attrlist->us_meid);
	if (HI_RET_SUCC != ui_ret) {
		return HI_RET_SUCC;
	}

	/*init*/
	HI_OS_MEMSET_S(&st_msg, sizeof(hi_omci_msg_s), 0, sizeof(hi_omci_msg_s));
	st_msg.st_msghead.us_meid    = (hi_ushort16)HI_OMCI_PROC_GET_ENTITY((hi_uchar8 *)pv_data);
	st_msg.st_msghead.us_instid  = (hi_ushort16)HI_OMCI_PROC_GET_INST((hi_uchar8 *)pv_data);
	st_msg.st_msghead.us_msgsize = pst_attrlist->us_totalsize;
	st_msg.st_msghead.uc_attnum  = pst_attrlist->uc_num;

	/*get data from msg */
	HI_OS_MEMSET_S(&st_attrlist, sizeof(hi_omci_mib_attrlist_s), 0, sizeof(hi_omci_mib_attrlist_s));
	HI_OS_MEMCPY_S(&st_attrlist, sizeof(st_attrlist), pst_attrlist, sizeof(hi_omci_mib_attrlist_s));

	uc_act = HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data);

	hi_omci_lib_print(HI_DBG_LEVEL_INFO, "%s %d us_meid %hu uc_act %hhu\r\n", __func__, __LINE__, pst_attrlist->us_meid,
			  uc_act);
	if (HI_OMCI_PROC_GET_DEVID((uint8_t *)pv_data) == HI_OMCI_PROC_DEVICEID_STD)
		hi_omci_mib_proc_std_msg(uc_act, pv_data, &st_attrlist, &st_msg);
	else
		hi_omci_mib_proc_ext_msg(uc_act, pv_data, &st_attrlist, &st_msg);

	/*callback*/
	ui_ret = (hi_uint32)((HI_FUNCCALLBACK_EXT)pv_callback)(&st_msg, sizeof(hi_omci_msg_s), &ui_outlen);  /*lint !e611*/
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_next
 功能描述  : 配置后动作
 输入参数  : hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_next(hi_void *pv_data, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32  ui_ret, ui_outlen;
	hi_void   *pv_callback = HI_NULL;
	hi_omci_msg_s st_msg;

	/*check if cfg callback*/
	ui_ret = hi_omci_sql_entity_get(HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data), pst_attrlist->us_meid,
					HI_OMCI_MIB_ENTITYNEXTTABLE_NAME, &pv_callback);

	hi_omci_lib_print(HI_DBG_LEVEL_INFO, "%s %d us_meid %hu\r\n", __func__, __LINE__, pst_attrlist->us_meid);
	if (HI_RET_SUCC != ui_ret) {
		return HI_RET_SUCC;
	}

	/*init*/
	HI_OS_MEMSET_S(&st_msg, sizeof(hi_omci_msg_s), 0, sizeof(hi_omci_msg_s));
	st_msg.st_msghead.us_meid    = (hi_ushort16)HI_OMCI_PROC_GET_ENTITY((hi_uchar8 *)pv_data);
	st_msg.st_msghead.us_instid  = (hi_ushort16)HI_OMCI_PROC_GET_INST((hi_uchar8 *)pv_data);
	st_msg.st_msghead.us_attmask = (HI_OMCI_PROC_GET_DEVID((uint8_t *)pv_data) == HI_OMCI_PROC_DEVICEID_STD) ?
				       (uint16_t)HI_OMCI_PROC_STD_GET_MASK((uint8_t *)pv_data) :
				       (uint16_t)HI_OMCI_PROC_EXT_GET_MASK((uint8_t *)pv_data);
	st_msg.st_msghead.us_msgsize = pst_attrlist->us_totalsize;
	st_msg.st_msghead.uc_attnum  = pst_attrlist->uc_num;

	/* get data from db */
	pst_attrlist->uc_opt = HI_OMCI_ATT_R_W_S_E;
	pst_attrlist->us_mask = HI_OMCI_MIB_ATTRMASK;
	ui_ret = hi_omci_sql_inst_get(pst_attrlist, st_msg.uc_content);

	hi_omci_lib_print(HI_DBG_LEVEL_INFO, "%s %d us_meid %hu\r\n", __func__, __LINE__, pst_attrlist->us_meid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	/*callback*/
	ui_ret = ((HI_FUNCCALLBACK_EXT)pv_callback)(&st_msg, sizeof(hi_omci_msg_s), &ui_outlen);  /*lint !e611*/
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_datasync
 功能描述  : MIB数据库同步更新
 输入参数  : hi_uchar8 uc_msgtype
 输出参数  : 无
 返 回 值  :
*****************************************************************************/
hi_void hi_omci_mib_datasync(hi_uchar8 uc_msgtype)
{
	hi_uchar8 uc_synnum;

	switch (uc_msgtype) {
	case HI_OMCI_PRO_ACTIONS_CREATE_E:
	case HI_OMCI_PRO_ACTIONS_DELETE_E:
	case HI_OMCI_PRO_ACTIONS_SET_E:
	case HI_OMCI_PRO_ACTIONS_STARTDOWNLOAD_E:
	case HI_OMCI_PRO_ACTIONS_ENDDOWNLOAD_E:
	case HI_OMCI_PRO_ACTIONS_ACTIVEIMAGE_E:
	case HI_OMCI_PRO_ACTIONS_COMMITIMAGE_E:
		break;
	default:
		hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return;
	}

	hi_omci_sql_inst_getcoluchar8(HI_OMCI_PRO_ME_ONT_DATA_E, 0,
				      HI_OMCI_SQL_INSTTABLE_ATTR1_E, &uc_synnum);

	uc_synnum = (HI_OMCI_SYNCNUM_MAX == uc_synnum) ? (1) : (uc_synnum + 1);

	hi_omci_sql_inst_setcoluchar8(HI_OMCI_PRO_ME_ONT_DATA_E, 0,
				      HI_OMCI_SQL_INSTTABLE_ATTR1_E, uc_synnum);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_getattrmask
 功能描述  : 获取属性掩码
 输入参数  : hi_ushort16 us_me
             hi_uchar8 uc_opt
 输出参数  : 无
 返 回 值  : hi_ushort16
*****************************************************************************/
hi_uint32 hi_omci_mib_getattrmask(hi_omci_mib_attrlist_s *pst_attrlist, hi_uchar8 uc_opt)
{
	hi_uchar8   uc_index;
	hi_uint32   ui_mask = 0;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	for (uc_index = 0;  uc_index < pst_attrlist->uc_num; uc_index++) {
		pst_attr = &pst_attrlist->st_attr[uc_index];
		if (pst_attr->uc_opt & uc_opt) {
			ui_mask |= HI_ATT_MASK_ONE >> uc_index;
		}
	}

	hi_omci_lib_systrace(HI_RET_SUCC, uc_opt, ui_mask, 0, 0);
	return ui_mask;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_std_checkdb
 功能描述  : 标准协议检查数据库属性支持情况
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
             hi_omci_mib_attrlist_s *pst_attrlist
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_std_checkdb(hi_void *pv_data, hi_void *pv_outdata,
				  hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32     ui_ret ;
	hi_uchar8     uc_maskflag = 0;
	hi_ushort16   us_mask, us_attmask    = 0;

	us_mask = (hi_ushort16)HI_OMCI_PROC_STD_GET_MASK((hi_uchar8 *)pv_data);
	switch (HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data)) {
	case HI_OMCI_PRO_ACTIONS_SET_E:
		uc_maskflag = HI_OMCI_ATT_W_E;
		break;
	case HI_OMCI_PRO_ACTIONS_GET_CURRDATA_E:
	case HI_OMCI_PRO_ACTIONS_GET_E:
		uc_maskflag = HI_OMCI_ATT_R_E;
		break;
	case HI_OMCI_PRO_ACTIONS_CREATE_E:
		ui_ret = hi_omci_sql_inst_checktable(pst_attrlist->us_meid);
		if (0 == ui_ret) {
			ui_ret = hi_omci_sql_inst_create(pst_attrlist->us_meid);
			if (HI_RET_SUCC != ui_ret) {
				hi_omci_lib_systrace(HI_OMCI_PRO_ERR_DEV_BUSY_E, us_mask, ui_ret, 0, 0);
				return HI_OMCI_PRO_ERR_DEV_BUSY_E;
			}
		}
		/*check if instance repeat*/
		ui_ret = hi_omci_sql_inst_checkinst(pst_attrlist->us_meid, pst_attrlist->us_instid);
		if (0 != ui_ret) {
			hi_omci_lib_systrace(HI_OMCI_PRO_ERR_IN_EXIST_E, us_mask, ui_ret, 0, 0);
			return HI_OMCI_PRO_ERR_IN_EXIST_E;
		}
		hi_omci_lib_systrace(HI_RET_SUCC, us_mask, 0, 0, 0);
		return HI_RET_SUCC;
	default :
		hi_omci_lib_systrace(HI_RET_SUCC, us_mask, 0, 0, 0);
		return HI_RET_SUCC;
	}

	ui_ret = hi_omci_sql_inst_check(pst_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, us_mask, uc_maskflag, 0, 0);
		return ui_ret;
	}

	us_attmask = (hi_ushort16)hi_omci_mib_getattrmask(pst_attrlist, uc_maskflag);
	us_attmask = ~us_attmask & us_mask;
	if (us_attmask) {
		HI_OMCI_PROC_STDRSP_SET_GETACT_UNATTR((hi_uchar8 *)pv_outdata, us_attmask);
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_ATT_FAIL_E, us_mask, uc_maskflag, us_attmask, 0);
		return HI_OMCI_PRO_ERR_ATT_FAIL_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, us_mask, uc_maskflag, us_attmask, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_ext_checkdb
 功能描述  : 扩展协议检查数据库属性支持情况
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
             hi_omci_mib_attrlist_s *pst_attrlist
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_ext_checkdb(hi_void *pv_data, hi_void *pv_outdata,
				  hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32     ui_ret ;
	hi_uchar8     uc_maskflag;
	hi_ushort16   us_mask, us_attmask    = 0;

	us_mask = (hi_ushort16)HI_OMCI_PROC_EXT_GET_MASK((hi_uchar8 *)pv_data);
	switch (HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data)) {
	case HI_OMCI_PRO_ACTIONS_SET_E:
		uc_maskflag = HI_OMCI_ATT_W_E;
		break;
	case HI_OMCI_PRO_ACTIONS_GET_CURRDATA_E:
	case HI_OMCI_PRO_ACTIONS_GET_E:
		uc_maskflag = HI_OMCI_ATT_R_E;
		break;
	case HI_OMCI_PRO_ACTIONS_CREATE_E:
		ui_ret = hi_omci_sql_inst_checktable(pst_attrlist->us_meid);
		if (0 == ui_ret) {
			ui_ret = hi_omci_sql_inst_create(pst_attrlist->us_meid);
			if (HI_RET_SUCC != ui_ret) {
				hi_omci_lib_systrace(HI_OMCI_PRO_ERR_DEV_BUSY_E, 0, 0, 0, 0);
				return HI_OMCI_PRO_ERR_DEV_BUSY_E;
			}
		}
		/*check if instance repeat*/
		ui_ret = hi_omci_sql_inst_checkinst(pst_attrlist->us_meid, pst_attrlist->us_instid);
		if (0 != ui_ret) {
			hi_omci_lib_systrace(HI_OMCI_PRO_ERR_IN_EXIST_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_IN_EXIST_E;
		}
	/* fall through */
	default :
		hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	ui_ret = hi_omci_sql_inst_check(pst_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	us_attmask = (hi_ushort16)hi_omci_mib_getattrmask(pst_attrlist, uc_maskflag);
	us_attmask = ~us_attmask & us_mask;
	if (us_attmask) {
		HI_OMCI_PROC_EXTRSP_SET_GETACT_UNATTR((hi_uchar8 *)pv_outdata, us_attmask);
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_ATT_FAIL_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_ATT_FAIL_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_checkdb
 功能描述  : 检查实例属性操作
 输入参数  : hi_void * pv_data
 输出参数  : hi_ushort16 * pus_mask
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_checkdb(hi_void *pv_data, hi_void *pv_outdata,
			      hi_omci_mib_attrlist_s *pst_attrlist)
{
	if (HI_OMCI_PROC_DEVICEID_STD == HI_OMCI_PROC_GET_DEVID((hi_uchar8 *)pv_data)) {
		hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return hi_omci_mib_std_checkdb(pv_data, pv_outdata, pst_attrlist);
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_mib_ext_checkdb(pv_data, pv_outdata, pst_attrlist);
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_init
 功能描述  : MIB数据库初始化
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_init(hi_void)
{
	hi_uint32 ui_ret;

	hi_omci_sql_upload_data_init();
	ui_ret = hi_os_fcopy(HI_OMCI_MIB_DBDATABASE_SRCDIR, HI_OMCI_MIB_DBDATABASE_NAME);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_omci_sql_dbstart();
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}
	return HI_RET_SUCC;
}

hi_uint32 hi_os_fcopy_inner(hi_char8 *pc_srcfile, hi_char8 *pc_destfile)
{
	hi_uint32   ui_ret       = HI_RET_SUCC;
	hi_char8    c_accmd[256]  = {0};

	/* 先把目的文件删除掉 */
	//(hi_void)HI_OS_SPRINTF_S(c_accmd, sizeof(c_accmd), "rm -f %s", pc_destfile);
	//(hi_void)system(c_accmd);

	/* 然后把源文件拷贝到目的文件 */
	(hi_void)HI_OS_SPRINTF_S(c_accmd, sizeof(c_accmd), "cp -f %s %s", pc_srcfile, pc_destfile);
	ui_ret = system(c_accmd);
	printf("\r\n %u \r\n", ui_ret);;

	return HI_RET_SUCC;
}

hi_uint32 hi_omci_mib_backup(hi_void)
{
	hi_uint32 ui_ret;
	ui_ret = hi_os_fcopy(HI_OMCI_MIB_DBDATABASE_NAME, HI_OMCI_MIB_DBDATABASE_BACKUP);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	return HI_RET_SUCC;
}

hi_uint32 hi_omci_mib_reinit(hi_void)
{
	hi_uint32 ui_ret;
	(hi_void)hi_omci_sql_dbstop();

	ui_ret = hi_os_fcopy(HI_OMCI_MIB_DBDATABASE_BACKUP, HI_OMCI_MIB_DBDATABASE_NAME);
	if (HI_RET_SUCC != ui_ret) {
		return ui_ret;
	}

	ui_ret = hi_omci_sql_dbstart();
	if (HI_RET_SUCC != ui_ret) {
		return ui_ret;
	}

	return HI_RET_SUCC;
}
/*****************************************************************************
 函 数 名  : hi_omci_mib_exit
 功能描述  : MIB数据库去初始化
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_exit(hi_void)
{
	(hi_void)hi_omci_sql_dbstop();
	(hi_void)hi_omci_sql_upload_exit();
	return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
