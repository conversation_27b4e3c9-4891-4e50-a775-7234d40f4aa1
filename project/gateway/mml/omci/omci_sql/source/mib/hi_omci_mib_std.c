/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_mib_std.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_11_01
  功能描述   : 对协议动作进行处理
******************************************************************************/

#include "hi_omci_lib.h"
#include "hi_omci_tapi_sysinfo.h"
#include "hi_omci_tapi_gpon.h"
#include "hi_odl_ctrl_cmd.h"
#include "hi_ipc.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

static struct {
	hi_uint32   ui_totalsize;
	hi_ushort16 us_seqnum;
	hi_ushort16 us_lastlen;
	hi_ushort16 us_mask;
} g_tableattrinfo;

hi_uint32 g_table_avail_size = 0xffffffff;
#pragma pack(1)
/*****************LOID authentication*******************/
typedef struct {
	hi_omci_me_msg_head_s st_msghead;                   /* Instance ID */
	hi_uchar8   uc_operaid[4];                          /* Operator ID. */
	hi_uchar8   uc_loid[24];                            /* LOID */
	hi_uchar8   uc_password[12];                        /* password */
	hi_uchar8   uc_authstatus;                          /*Authentication status*/
} hi_omci_me_loid_auth_bak_s;
#pragma pack()
/*****************************************************************************
 函 数 名  : hi_omci_mib_actcreate
 功能描述  : create动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actcreate(hi_void *pv_data, hi_void *pv_outdata,
				hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;

	pst_attrlist->uc_opt = HI_OMCI_ATT_S_E;
	hi_omci_sql_attr_decodentoh(HI_OMCI_PROC_STD_GET_CONTENT((hi_uchar8 *)pv_data), pst_attrlist);

	ui_ret = hi_omci_sql_inst_insert(pst_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_DEV_BUSY_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_DEV_BUSY_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actdelete
 功能描述  : delete动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actdelete(hi_void *pv_data, hi_void *pv_outdata,
				hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;

	ui_ret = hi_omci_sql_inst_del(pst_attrlist->us_meid, pst_attrlist->us_instid);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_DEV_BUSY_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_DEV_BUSY_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actset
 功能描述  : set动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actset(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;

	pst_attrlist->uc_opt = HI_OMCI_ATT_W_E;
	pst_attrlist->us_mask = (hi_ushort16)HI_OMCI_PROC_STD_GET_MASK((hi_uchar8 *)pv_data);
	hi_omci_sql_attr_decodentoh(HI_OMCI_PROC_STD_GET_SETCONTENT((hi_uchar8 *)pv_data), pst_attrlist);
	ui_ret = hi_omci_sql_inst_set(pst_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_NOT_SUPPORTED_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_getattrinfo
 功能描述  : 获取table类型的属性信息
 输入参数  : hi_omci_mib_attrlist_s *pst_attrlist
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_getattrinfo(hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 i;
	hi_uint32 ui_size = 0;

	for (i = 0; i < HI_OMCI_MIB_MAXATTR_NUM; i++) {
		if (HI_OMCI_SQL_DATA_TABLEATTR_E == pst_attrlist->st_attr[i].uc_type) {
			if ((0x8000 >> i) & pst_attrlist->us_mask) {
				if ((0x8000 >> i) == pst_attrlist->us_mask) {
					ui_size = (g_table_avail_size < (pst_attrlist->st_attr[i].uc_size * pst_attrlist->st_attr[i].us_tabnum)) ?
						  g_table_avail_size : (pst_attrlist->st_attr[i].uc_size * pst_attrlist->st_attr[i].us_tabnum);
					g_tableattrinfo.ui_totalsize = ui_size;
					g_tableattrinfo.us_mask    = pst_attrlist->us_mask;
					g_tableattrinfo.us_lastlen = ui_size % HI_OMCI_PROC_GET_MSGLEN;

					g_tableattrinfo.us_seqnum  = ui_size / HI_OMCI_PROC_GET_MSGLEN +
								     ((g_tableattrinfo.us_lastlen == 0) ? 0 : 1);
					return HI_RET_SUCC;
				} else {
					return HI_OMCI_PRO_ERR_ATT_FAIL_E;
				}
			}
		}
	}

	return (hi_uint32)HI_RET_FAIL;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actget
 功能描述  : get动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actget(hi_void *pv_data, hi_void *pv_outdata,
			     hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;
	hi_uchar8   *puc_content = HI_NULL;

	pst_attrlist->uc_opt = HI_OMCI_ATT_R_E;
	pst_attrlist->us_mask = HI_OMCI_PROC_STD_GET_MASK((hi_uchar8 *)pv_data);
	puc_content = HI_OMCI_PROC_STDRSP_GET_CONTENT((hi_uchar8 *)pv_outdata);
	HI_OMCI_PROC_STDRSP_SET_MASK((hi_uchar8 *)pv_outdata, pst_attrlist->us_mask);

	ui_ret = hi_omci_mib_getattrinfo(pst_attrlist);
	if (HI_RET_FAIL == ui_ret) { /*没有表类型的属性*/
		ui_ret = hi_omci_sql_inst_get(pst_attrlist, puc_content + 2);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}

		hi_omci_sql_attr_encodehton(pst_attrlist);

		hi_omci_lib_systrace(HI_RET_SUCC, pst_attrlist->us_mask, 0, 0, 0);
	} else if (HI_RET_SUCC == ui_ret) { /*有表类型的属性*/
		if (0xffffffff == g_table_avail_size) {
			*(hi_uint32 *)(puc_content + 2) = htonl(g_tableattrinfo.ui_totalsize);
		} else {
			*(hi_uint32 *)(puc_content + 2) = htonl(g_table_avail_size);
		}
	} else if (HI_OMCI_PRO_ERR_ATT_FAIL_E == ui_ret) { /*属性处理错误*/
		return HI_OMCI_PRO_ERR_ATT_FAIL_E;
	}

	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actgetalarm
 功能描述  : alarm动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actgetalarm(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_ushort16 us_num;

	hi_omci_sql_alarm_upload_init();
	hi_omci_sql_alarm_upload(HI_OMCI_PROC_STD_GET_ALARM_ARC((hi_uchar8 *)pv_data));
	us_num = (hi_ushort16)hi_omci_sql_alarm_upload_num_get();

	/*set num*/
	HI_OMCI_PROC_STDRSP_SET_ALARM_NUM((hi_uchar8 *)pv_outdata, us_num);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actgetnextalarm
 功能描述  : alarmnext动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actgetnextalarm(hi_void *pv_data, hi_void *pv_outdata,
				      hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_ushort16 us_seqnum;
	hi_uint32 ui_ret;

	us_seqnum = HI_OMCI_PROC_STD_GET_ALARM_NUM((hi_uchar8 *)pv_data);

	/*set alarm*/
	ui_ret = hi_omci_sql_alarm_upload_get(HI_OMCI_PROC_STD_GET_CONTENT((hi_uchar8 *)pv_outdata), us_seqnum);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_printf(" %d  %s \n", __LINE__, __func__);
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_NOT_SUPPORTED_E, us_seqnum, ui_ret, 0, 0);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actmibupload
 功能描述  : mibupload动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actmibupload(hi_void *pv_data, hi_void *pv_outdata,
				   hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_ushort16 us_num = 0;

	hi_omci_sql_upload_init(HI_OMCI_STD_MODE);
	us_num = (hi_ushort16)hi_omci_sql_upload_getnum();

	HI_OMCI_PROC_STDRSP_SET_UPLOAD_NUM((hi_uchar8 *)pv_outdata, us_num);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actmibuploadnext
 功能描述  : mibuploadnext动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actmibuploadnext(hi_void *pv_data, hi_void *pv_outdata,
				       hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_ushort16 us_seqnum, ui_ret, blk_size;

	us_seqnum = HI_OMCI_PROC_STD_GET_UPLOAD_NUM((hi_uchar8 *)pv_data);

	hi_omci_lib_systrace(0, us_seqnum, 0, 0, 0);

	ui_ret = hi_omci_sql_upload_getblock(HI_OMCI_PROC_STD_GET_CONTENT((hi_uchar8 *)pv_outdata), us_seqnum, &blk_size, HI_OMCI_STD_MODE);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_printf(" %d  %s \n", __LINE__, __func__);
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_NOT_SUPPORTED_E, us_seqnum, ui_ret, 0, 0);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}
	hi_omci_lib_systrace(HI_RET_SUCC, us_seqnum, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actmibreset
 功能描述  : mibreset动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actmibreset(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;
	hi_uint32 ui_allocid;
	hi_omci_me_loid_auth_bak_s st_entity;
	hi_omci_tapi_sn_loid_s st_sn_loid;
	hi_ushort16 us_instid = 0;
	hi_uint32 aui_instid[128] = {0};
	hi_uint32 ui_instnum = 0;
	hi_uint32 ui_i;
	hi_uint32 us_gemportid;

	/* 删除所有gemport,gemport规格128 */
	hi_omci_ext_get_install(HI_OMCI_PRO_ME_GEM_CTP_E, aui_instid, 128, &ui_instnum);

	hi_omci_debug("[INFO]:ui_instnum[%u] \n", ui_instnum);

	for (ui_i = 0; ui_i < ui_instnum; ui_i++) {
		hi_omci_debug("[INFO]:aui_instid[%u]=0x%x\n", ui_i, aui_instid[ui_i]);
		ui_ret = hi_omci_ext_get_attr(HI_OMCI_PRO_ME_GEM_CTP_E, aui_instid[ui_i], HI_OMCI_ATTR1, &us_gemportid);
		if (ui_ret != HI_RET_SUCC) {
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			continue;
		}
		hi_omci_debug("[INFO]:us_gemportid=%u\n", us_gemportid);
		ui_ret = hi_omci_tapi_gemport_del(us_gemportid);
		if (ui_ret != HI_RET_SUCC) {
			hi_omci_systrace(ui_ret, 0, 0, 0, 0);
			continue;
		}
	}
	/* 根据电信要求，onu不应更改loid authentication的所有属性
	 * omci切换数据库之前需要获取历史loid实例属性。
	 */
	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	ui_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_LOID_AUTH_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(ui_ret);

	/*Re-initial database file*/
	ui_ret = hi_omci_mib_reinit();
	if (HI_RET_SUCC != ui_ret) {
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	/* 上线后，将TCONT0的ALLOCID写入数据库
	 * 这个信息需要上报OLT
	 */
	ui_ret = hi_omci_tapi_tcont_get(0, &ui_allocid);
	if (ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_omci_ext_set_attr(HI_OMCI_PRO_ME_TCONT_E, 0, HI_OMCI_ATTR1, &ui_allocid);
	if (ui_ret) {
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_omci_tapi_sn_loid_get(&st_sn_loid);
	HI_OMCI_RET_CHECK(ui_ret);

	st_entity.st_msghead.us_instid = us_instid;
	HI_OS_MEMSET_S(&st_entity.uc_operaid, sizeof(st_entity.uc_operaid), 0, sizeof(st_entity.uc_operaid));
	st_entity.uc_operaid[1] = 'T';
	st_entity.uc_operaid[2] = 'C';

	HI_OS_MEMCPY_S(st_entity.uc_loid, sizeof(st_entity.uc_loid), st_sn_loid.auc_loid, sizeof(st_entity.uc_loid));
	HI_OS_MEMCPY_S(st_entity.uc_password, sizeof(st_entity.uc_password), st_sn_loid.auc_lopwd,
		       sizeof(st_entity.uc_password));

	ui_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_LOID_AUTH_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(ui_ret);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_acttest
 功能描述  : test动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_acttest(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_ushort16 us_meid;
	hi_uchar8 *puc_data = HI_NULL;

	us_meid = (hi_ushort16)HI_OMCI_PROC_GET_ENTITY((hi_uchar8 *)pv_data);
	puc_data = HI_OMCI_PROC_STD_GET_CONTENT((hi_uchar8 *)pv_data);
	if ((HI_OMCI_PRO_ME_PPTP_POTS_UNI_E == us_meid) && (0x7 != *puc_data)) {
		/*填充response消息*/
		*(hi_uchar8 *)((hi_uchar8 *)pv_outdata + sizeof(hi_omci_proc_msg_head_s)) = HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actstartdownload
 功能描述  : startdownload动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actstartdownload(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;

	ui_ret = hi_omci_upgrade_start(pv_data, pv_outdata);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_OMCI_PRO_ERR_NO_E;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actdownloading
 功能描述  : download动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actdownloading(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	/*为提高升级速度，此动作在hi_omci_proc.c中处理*/

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_OMCI_PRO_ERR_NO_E;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actenddownload
 功能描述  : enddownload动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actenddownload(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;

	ui_ret = hi_omci_upgrade_end(pv_data, pv_outdata);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_OMCI_PRO_ERR_NO_E;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actactiveimage
 功能描述  : activeimage动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actactiveimage(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;
	ui_ret = hi_omci_upgrade_active(pv_data, pv_outdata);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actcommitimage
 功能描述  : commiteimage动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actcommitimage(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;
	ui_ret = hi_omci_upgrade_commit(pv_data, pv_outdata);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actsynctime
 功能描述  : synctime动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actsynctime(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actreboot
 功能描述  : reboot动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actreboot(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t cmd = IGD_CM_CMD_REBOOT;

	HI_IPC_CALL("IPC_igdCmOamCtrl", &cmd);
#ifdef CONFIG_PLATFORM_OPENWRT
    system("reboot");
#endif
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actgetnext
 功能描述  : getnext动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actgetnext(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32   ui_ret;
	hi_ushort16 us_mask = 0;
	hi_uint32   ui_pos  = 0;
	hi_ushort16 us_seq  = 0;
	hi_uint32   ui_size = 0;
	hi_uchar8   *puc_rspcontent = HI_OMCI_PROC_STDRSP_GET_CONTENT((hi_uchar8 *)pv_outdata);
	hi_uchar8   *puc_data = HI_NULL;
	pst_attrlist->us_mask = HI_OMCI_PROC_STD_GET_MASK((hi_uchar8 *)pv_data);

	HI_OMCI_PROC_STDRSP_SET_MASK((hi_uchar8 *)pv_outdata, pst_attrlist->us_mask);

	if (pst_attrlist->us_mask != g_tableattrinfo.us_mask) {
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	us_mask = pst_attrlist->us_mask;
	/*获取属性的位置*/
	while (us_mask) {
		ui_pos++;
		us_mask = us_mask << 1;
	}

	us_seq = ntohs(*(hi_ushort16 *)((hi_uchar8 *)pv_data + sizeof(hi_omci_proc_msg_head_s) + 2));

	ui_size = pst_attrlist->st_attr[ui_pos - 1].uc_size * pst_attrlist->st_attr[ui_pos - 1].us_tabnum;
	ui_size = (g_table_avail_size < ui_size) ? g_table_avail_size : ui_size;
	if (ui_size == 0) {
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}
	/*申请空间保存属性数据*/
	puc_data = hi_os_malloc(ui_size);
	if (HI_NULL == puc_data) {
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	HI_OS_MEMSET_S(puc_data, ui_size, 0, ui_size);

	hi_omci_sql_inst_getcolblob(pst_attrlist->us_meid, pst_attrlist->us_instid,
				    ui_pos, puc_data, ui_size);
	//HI_PRINT_MEM(ui_size, puc_data);

	if (us_seq < (g_tableattrinfo.us_seqnum - 1)) {
		HI_OS_MEMCPY_S((hi_void *)(puc_rspcontent + 2), HI_OMCI_PROC_GET_MSGLEN, puc_data + us_seq * HI_OMCI_PROC_GET_MSGLEN,
			       HI_OMCI_PROC_GET_MSGLEN);
		ui_ret = HI_OMCI_PRO_ERR_NO_E;
	} else if (us_seq == (g_tableattrinfo.us_seqnum - 1)) {
		HI_OS_MEMCPY_S((hi_void *)(puc_rspcontent + 2), g_tableattrinfo.us_lastlen, puc_data + us_seq * HI_OMCI_PROC_GET_MSGLEN,
			       g_tableattrinfo.us_lastlen);
		g_table_avail_size = 0xffffffff;
		ui_ret = HI_OMCI_PRO_ERR_NO_E;
	} else {
		ui_ret = HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	/*释放空间*/
	hi_os_free(puc_data);
	puc_data = HI_NULL;

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return ui_ret;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_acttestresult
 功能描述  : testresult动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_acttestresult(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_actgetcurrdata
 功能描述  : getcurrentdata动作处理
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_mib_actgetcurrdata(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;
	/*动作执行与GET一致*/
	ui_ret = hi_omci_mib_actget(pv_data, pv_outdata, pst_attrlist);

	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}


static hi_omci_mib_act_s g_st_omci_mib_std[] = {
	{ HI_OMCI_PRO_ACTIONS_NULL0_E,             HI_NULL                      },//0
	{ HI_OMCI_PRO_ACTIONS_NULL1_E,             HI_NULL                      },//1
	{ HI_OMCI_PRO_ACTIONS_NULL2_E,             HI_NULL                      },//2
	{ HI_OMCI_PRO_ACTIONS_NULL3_E,             HI_NULL                      },//3
	{ HI_OMCI_PRO_ACTIONS_CREATE_E,            hi_omci_mib_actcreate        },//4
	{ HI_OMCI_PRO_ACTIONS_CREATECC_E,          HI_NULL                      },//5
	{ HI_OMCI_PRO_ACTIONS_DELETE_E,            hi_omci_mib_actdelete        },//6
	{ HI_OMCI_PRO_ACTIONS_DELETECC_E,          HI_NULL                      },//7
	{ HI_OMCI_PRO_ACTIONS_SET_E,               hi_omci_mib_actset           },//8
	{ HI_OMCI_PRO_ACTIONS_GET_E,               hi_omci_mib_actget           },//9
	{ HI_OMCI_PRO_ACTIONS_GETCC_E,             HI_NULL                      },//10
	{ HI_OMCI_PRO_ACTIONS_GETALLALARM_E,       hi_omci_mib_actgetalarm      },//11
	{ HI_OMCI_PRO_ACTIONS_GETALLALARMNEXT_E,   hi_omci_mib_actgetnextalarm  },//12
	{ HI_OMCI_PRO_ACTIONS_MIBUPLOAD_E,         hi_omci_mib_actmibupload     },//13
	{ HI_OMCI_PRO_ACTIONS_MIBUPLOADNEXT_E,     hi_omci_mib_actmibuploadnext },//14
	{ HI_OMCI_PRO_ACTIONS_MIBRESET_E,          hi_omci_mib_actmibreset      },//15
	{ HI_OMCI_PRO_NOTIFICATIONS_ALARM_E,       HI_NULL                      },//16
	{ HI_OMCI_PRO_NOTIFICATIONS_AVC_E,         HI_NULL                      },//17
	{ HI_OMCI_PRO_ACTIONS_TEST_E,              hi_omci_mib_acttest          },//18
	{ HI_OMCI_PRO_ACTIONS_STARTDOWNLOAD_E,     hi_omci_mib_actstartdownload },//19
	{ HI_OMCI_PRO_ACTIONS_DOWNLOADING_E,       hi_omci_mib_actdownloading   },//20
	{ HI_OMCI_PRO_ACTIONS_ENDDOWNLOAD_E,       hi_omci_mib_actenddownload   },//21
	{ HI_OMCI_PRO_ACTIONS_ACTIVEIMAGE_E,       hi_omci_mib_actactiveimage   },//22
	{ HI_OMCI_PRO_ACTIONS_COMMITIMAGE_E,       hi_omci_mib_actcommitimage   },//23
	{ HI_OMCI_PRO_ACTIONS_SYNCTIME_E,          hi_omci_mib_actsynctime      },//24
	{ HI_OMCI_PRO_ACTIONS_REBOOT_E,            hi_omci_mib_actreboot        },//25
	{ HI_OMCI_PRO_ACTIONS_GETNEXT_E,           hi_omci_mib_actgetnext       },//26
	{ HI_OMCI_PRO_NOTIFICATIONS_TESTRESULT_E,  hi_omci_mib_acttestresult    },//27
	{ HI_OMCI_PRO_ACTIONS_GET_CURRDATA_E,      hi_omci_mib_actgetcurrdata   },//28
};

hi_uint32 hi_omci_mib_std(hi_void *pv_data, hi_void *pv_outdata,
			  hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret = HI_RET_SUCC;
	hi_uchar8 uc_act = HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data);

	if (uc_act >= (sizeof(g_st_omci_mib_std) / (sizeof(hi_omci_mib_act_s)))) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_NOT_SUPPORTED_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}
	if (HI_NULL == g_st_omci_mib_std[uc_act].pv_action) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_NOT_SUPPORTED_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}
	/*before*/
	ui_ret = hi_omci_mib_forward(pv_data, pst_attrlist);
	if (HI_RET_SUCC == ui_ret) { // 成功即需要操作数据库
		/*proc*/
		ui_ret = g_st_omci_mib_std[uc_act].pv_action(pv_data, pv_outdata, pst_attrlist);
		if (HI_RET_SUCC !=  ui_ret) {
			hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}
	} else if ((hi_uint32)HI_RET_OMCI_MIB_DONT_UPDATEDB_E != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}
	/*next*/
	ui_ret = hi_omci_mib_next(pv_data, pst_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}



#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
