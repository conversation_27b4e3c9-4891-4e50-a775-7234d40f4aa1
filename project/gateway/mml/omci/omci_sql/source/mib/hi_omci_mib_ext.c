/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_mib_ext.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_11_01
  功能描述   : 对协议动作进行处理
******************************************************************************/

#include "hi_omci_lib.h"
#include "hi_omci_extend.h"
#include "hi_odl_ctrl_cmd.h"
#include "hi_ipc.h"

static struct {
	uint32_t totalsize;
	uint16_t mask;
} g_ext_tableattrinfo;

static uint32_t g_ext_tab_avail_size = 0xffffffff;
static uint32_t hi_omci_mib_ext_getattrinfo(hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t i;
	uint32_t size = 0;
	uint16_t mask_bit;

	for (i = 0; i < HI_OMCI_MIB_MAXATTR_NUM; i++) {
		mask_bit = 0x8000 >> i;
		if (pst_attrlist->st_attr[i].uc_type != HI_OMCI_SQL_DATA_TABLEATTR_E)
			continue;
		if (!(mask_bit & pst_attrlist->us_mask))
			continue;

		if (mask_bit == pst_attrlist->us_mask) {
			size = (g_ext_tab_avail_size < (pst_attrlist->st_attr[i].uc_size * pst_attrlist->st_attr[i].us_tabnum)) ?
			       g_ext_tab_avail_size : (pst_attrlist->st_attr[i].uc_size * pst_attrlist->st_attr[i].us_tabnum);
			g_ext_tableattrinfo.totalsize = size;
			g_ext_tableattrinfo.mask = pst_attrlist->us_mask;
			return HI_RET_SUCC;
		} else {
			return HI_OMCI_PRO_ERR_ATT_FAIL_E;
		}
	}

	return (uint32_t)HI_RET_FAIL;
}

uint32_t hi_omci_mib_ext_actcreate(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t ret;

	if (pv_data == NULL || pst_attrlist == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	pst_attrlist->uc_opt = HI_OMCI_ATT_S_E;
	hi_omci_sql_attr_decodentoh(HI_OMCI_PROC_EXT_GET_CONTENT((uint8_t *)pv_data), pst_attrlist);

	ret = hi_omci_sql_inst_insert(pst_attrlist);
	if (ret != HI_RET_SUCC)
		return HI_OMCI_PRO_ERR_DEV_BUSY_E;

	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actdelete(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t ret;

	if (pst_attrlist == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	ret = hi_omci_sql_inst_del(pst_attrlist->us_meid, pst_attrlist->us_instid);
	if (ret != HI_RET_SUCC)
		return HI_OMCI_PRO_ERR_DEV_BUSY_E;

	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actset(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t ret;

	if (pv_data == NULL || pst_attrlist == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	pst_attrlist->uc_opt = HI_OMCI_ATT_W_E;
	pst_attrlist->us_mask = (uint16_t)HI_OMCI_PROC_EXT_GET_MASK((uint8_t *)pv_data);
	hi_omci_sql_attr_decodentoh(HI_OMCI_PROC_EXT_GET_SETCONTENT((uint8_t *)pv_data), pst_attrlist);
	ret = hi_omci_sql_inst_set(pst_attrlist);
	if (ret != HI_RET_SUCC)
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;

	return HI_RET_SUCC;
}

static uint32_t hi_omci_mib_ext_get_attr_size(hi_omci_mib_attrlist_s *pst_attrlist, uint16_t *attr_size)
{
	uint32_t col;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	for (col = 0; col < pst_attrlist->uc_num; col++) {
		if (hi_omci_sql_attr_check(pst_attrlist, col) != HI_RET_SUCC)
			continue;

		pst_attr = &pst_attrlist->st_attr[col];
		switch (pst_attr->uc_type) {
		case HI_OMCI_SQL_DATA_UCHAR_E :
			*attr_size += sizeof(uint8_t);
			break;
		case HI_OMCI_SQL_DATA_USHORT_E :
			*attr_size += sizeof(uint16_t);
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			*attr_size += sizeof(uint32_t);
			break;
		case HI_OMCI_SQL_DATA_BINARY_E :
			*attr_size += pst_attr->uc_size;
			break;
		case HI_OMCI_SQL_DATA_TEXT_E :
			*attr_size += pst_attr->uc_size;
			break;
		case HI_OMCI_SQL_DATA_TABLEATTR_E :
			*attr_size += pst_attr->uc_size;
			break;
		default:
			break;
		}
	}
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actget(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t ret;
	uint8_t *content = HI_NULL;
	uint16_t attr_size;

	if (pv_data == NULL || pv_outdata == NULL || pst_attrlist == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	attr_size = HI_OMCI_PROC_EXTRSP_GET_SIZE(pv_outdata);
	pst_attrlist->uc_opt = HI_OMCI_ATT_R_E;
	pst_attrlist->us_mask = HI_OMCI_PROC_EXT_GET_MASK((uint8_t *)pv_data);
	content = HI_OMCI_PROC_EXTRSP_GET_CONTENT((uint8_t *)pv_outdata);
	HI_OMCI_PROC_EXTRSP_SET_GETACT_MASK((uint8_t *)pv_outdata, pst_attrlist->us_mask);

	ret = hi_omci_mib_ext_getattrinfo(pst_attrlist);
	if (ret == HI_RET_FAIL) { /* there are no attributes for the table type */
		ret = hi_omci_sql_inst_get(pst_attrlist, content + HI_OMCI_EXT_PRO_GET_RSP_LEN);
		if (ret != HI_RET_SUCC) {
			hi_omci_lib_systrace(ret, 0, 0, 0, 0);
			return ret;
		}
		hi_omci_mib_ext_get_attr_size(pst_attrlist, &attr_size);
		hi_omci_sql_attr_encodehton(pst_attrlist);
	} else if (ret == HI_RET_SUCC) { /* attributes with table types */
		if (g_ext_tab_avail_size == 0xffffffff) {
			*(uint32_t *)(content + HI_OMCI_EXT_PRO_GET_RSP_LEN) = htonl(g_ext_tableattrinfo.totalsize);
		} else {
			*(uint32_t *)(content + HI_OMCI_EXT_PRO_GET_RSP_LEN) = htonl(g_ext_tab_avail_size);
		}
		attr_size += sizeof(uint32_t);
	} else if (ret == HI_OMCI_PRO_ERR_ATT_FAIL_E) { /* attribute processing error */
		return HI_OMCI_PRO_ERR_ATT_FAIL_E;
	}

	HI_OMCI_PROC_EXTRSP_SET_SIZE((uint8_t *)pv_outdata, attr_size);
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actgetalarm(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint16_t num;

	if (pv_data == NULL || pv_outdata == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	hi_omci_sql_alarm_upload_init();
	hi_omci_sql_alarm_upload(HI_OMCI_PROC_EXT_GET_ALARM_ARC((uint8_t *)pv_data));
	num = (uint16_t)hi_omci_sql_alarm_upload_num_get();

	/* set num */
	HI_OMCI_PROC_EXTRSP_SET_ALARM_NUM((uint8_t *)pv_outdata, num);
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actgetnextalarm(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint16_t attr_size;
	uint16_t seqnum;
	uint32_t ret;
	uint8_t *content = NULL;
	uint32_t total_num = hi_omci_sql_alarm_upload_num_get();

	if (pv_data == NULL || pv_outdata == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	attr_size = HI_OMCI_PROC_EXTRSP_GET_SIZE(pv_outdata);
	content = HI_OMCI_PROC_EXT_GET_CONTENT((uint8_t *)pv_outdata);

	/* set alarm */
	for (seqnum = HI_OMCI_PROC_EXT_GET_ALARM_NUM((uint8_t *)pv_data); seqnum < total_num; seqnum++) {
		ret = hi_omci_sql_alarm_upload_get(content, seqnum);
		if (ret != HI_RET_SUCC)
			return ret;
		attr_size += HI_OMCI_EXT_PRO_GETALLALARMNEXT_RSP_LEN;
		content += HI_OMCI_EXT_PRO_GETALLALARMNEXT_RSP_LEN;
	}

	HI_OMCI_PROC_EXTRSP_SET_SIZE((uint8_t *)pv_outdata, attr_size);
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actmibupload(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint16_t num = 0;

	if (pv_outdata == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	hi_omci_sql_upload_init(HI_OMCI_EXT_MODE);
	num = (uint16_t)hi_omci_sql_upload_getnum();

	HI_OMCI_PROC_EXTRSP_SET_UPLOAD_NUM((uint8_t *)pv_outdata, num);
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actmibuploadnext(hi_void *pv_data, hi_void *pv_outdata,
		hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint16_t seqnum;
	uint32_t ret;
	uint16_t blk_size;

	if (pv_data == NULL || pv_outdata == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	seqnum = HI_OMCI_PROC_EXT_GET_UPLOAD_NUM((uint8_t *)pv_data);
	ret = hi_omci_sql_upload_getblock(HI_OMCI_PROC_EXT_GET_CONTENT((uint8_t *)pv_outdata), seqnum, &blk_size, HI_OMCI_EXT_MODE);
	if (ret != HI_RET_SUCC)
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;

	HI_OMCI_PROC_EXTRSP_SET_SIZE((uint8_t *)pv_outdata, blk_size);
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actmibreset(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	if (pv_data == NULL || pv_outdata == NULL || pst_attrlist == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	return hi_omci_mib_actmibreset(pv_data, pv_outdata, pst_attrlist);
}

uint32_t hi_omci_mib_ext_acttest(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint16_t meid;
	uint8_t *content = HI_NULL;

	if (pv_data == NULL || pv_outdata == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	meid = (uint16_t)HI_OMCI_PROC_GET_ENTITY((uint8_t *)pv_data);
	content = HI_OMCI_PROC_EXT_GET_CONTENT((uint8_t *)pv_data);
	if ((meid == HI_OMCI_PRO_ME_PPTP_POTS_UNI_E) && (*content != 0x7)) {
		/* fill in the response message */
		HI_OMCI_PROC_EXTRSP_SET_RESULT((uint8_t *)pv_outdata, HI_OMCI_PRO_ERR_NOT_SUPPORTED_E);
	}
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actstartdownload(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t ret;

	if (pv_data == NULL || pv_outdata == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	ret = hi_omci_upgrade_start(pv_data, pv_outdata);
	if (ret != HI_RET_SUCC) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_OMCI_PRO_ERR_NO_E;
}

uint32_t hi_omci_mib_ext_actdownloading(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	/* to improve the upgrade speed, this action is processed in hi_omci_proc.c */
	return HI_OMCI_PRO_ERR_NO_E;
}

uint32_t hi_omci_mib_ext_actenddownload(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t ret;

	if (pv_data == NULL || pv_outdata == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	ret = hi_omci_upgrade_end(pv_data, pv_outdata);
	if (ret != HI_RET_SUCC) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_OMCI_PRO_ERR_NO_E;
}

uint32_t hi_omci_mib_ext_actactiveimage(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t ret;

	if (pv_data == NULL || pv_outdata == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	ret = hi_omci_upgrade_active(pv_data, pv_outdata);
	if (ret != HI_RET_SUCC) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actcommitimage(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t ret;

	if (pv_data == NULL || pv_outdata == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	ret = hi_omci_upgrade_commit(pv_data, pv_outdata);
	if (ret != HI_RET_SUCC) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actsynctime(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actreboot(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t cmd = IGD_CM_CMD_REBOOT;

	HI_IPC_CALL("IPC_igdCmOamCtrl", &cmd);
#ifdef CONFIG_PLATFORM_OPENWRT
    system("reboot");
#endif
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actgetnext(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t ret;
	uint16_t mask = 0;
	uint32_t pos  = 0;
	uint16_t seq  = 0;
	uint16_t size = 0;
	uint8_t *rsp_val = NULL;
	uint8_t *puc_data = NULL;

	if (pv_data == NULL || pv_outdata == NULL || pst_attrlist == NULL) {
		hi_omci_err("err:%s para is null\n", __func__);
		return HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	rsp_val = HI_OMCI_PROC_EXTRSP_GET_GN_VAL((uint8_t *)pv_outdata);
	pst_attrlist->us_mask = HI_OMCI_PROC_EXT_GET_MASK((uint8_t *)pv_data);
	HI_OMCI_PROC_EXTRSP_SET_GETACT_MASK((uint8_t *)pv_outdata, pst_attrlist->us_mask);
	if (pst_attrlist->us_mask == 0 || pst_attrlist->us_mask != g_ext_tableattrinfo.mask)
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;

	/* Get the location of the property */
	mask = pst_attrlist->us_mask;
	while (mask) {
		pos++;
		mask = mask << 1;
	}
	if (pos == 0 || pos > HI_OMCI_MIB_MAXATTR_NUM)
		return HI_OMCI_PRO_ERR_PARA_ERR_E;

	seq = HI_OMCI_PROC_EXT_GET_GN_SEQ((uint8_t *)pv_data);
	size = pst_attrlist->st_attr[pos - 1].uc_size * pst_attrlist->st_attr[pos - 1].us_tabnum;
	if (size == 0)
		return HI_OMCI_PRO_ERR_PARA_ERR_E;

	/* alloc memory to save attribute data */
	puc_data = hi_os_malloc(size);
	if (HI_NULL == puc_data)
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	(void)memset_s(puc_data, size, 0, size);
	hi_omci_sql_inst_getcolblob(pst_attrlist->us_meid, pst_attrlist->us_instid, pos, puc_data, size);
	if (seq == 0) {
		(void)memcpy_s((void *)rsp_val, size, puc_data, size);
		ret = HI_OMCI_PRO_ERR_NO_E;
		HI_OMCI_PROC_EXTRSP_SET_SIZE((uint8_t *)pv_outdata, HI_OMCI_EXT_PRO_GETNEXT_RSP_LEN + size);
	} else {
		ret = HI_OMCI_PRO_ERR_PARA_ERR_E;
	}

	hi_os_free(puc_data);
	puc_data = HI_NULL;

	return ret;
}

uint32_t hi_omci_mib_ext_acttestresult(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	return HI_RET_SUCC;
}

uint32_t hi_omci_mib_ext_actgetcurrdata(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	return hi_omci_mib_ext_actget(pv_data, pv_outdata, pst_attrlist);
}

uint32_t hi_omci_mib_ext_actsettable(hi_void *pv_data, hi_void *pv_outdata, hi_omci_mib_attrlist_s *pst_attrlist)
{
	return hi_omci_mib_ext_actset(pv_data, pv_outdata, pst_attrlist);
}

static hi_omci_mib_act_s g_st_omci_mib_ext[] = {
	{ HI_OMCI_PRO_ACTIONS_NULL0_E,             HI_NULL                          },//0
	{ HI_OMCI_PRO_ACTIONS_NULL1_E,             HI_NULL                          },//1
	{ HI_OMCI_PRO_ACTIONS_NULL2_E,             HI_NULL                          },//2
	{ HI_OMCI_PRO_ACTIONS_NULL3_E,             HI_NULL                          },//3
	{ HI_OMCI_PRO_ACTIONS_CREATE_E,            hi_omci_mib_ext_actcreate        },//4
	{ HI_OMCI_PRO_ACTIONS_CREATECC_E,          HI_NULL                          },//5
	{ HI_OMCI_PRO_ACTIONS_DELETE_E,            hi_omci_mib_ext_actdelete        },//6
	{ HI_OMCI_PRO_ACTIONS_DELETECC_E,          HI_NULL                          },//7
	{ HI_OMCI_PRO_ACTIONS_SET_E,               hi_omci_mib_ext_actset           },//8
	{ HI_OMCI_PRO_ACTIONS_GET_E,               hi_omci_mib_ext_actget           },//9
	{ HI_OMCI_PRO_ACTIONS_GETCC_E,             HI_NULL                          },//10
	{ HI_OMCI_PRO_ACTIONS_GETALLALARM_E,       hi_omci_mib_ext_actgetalarm      },//11
	{ HI_OMCI_PRO_ACTIONS_GETALLALARMNEXT_E,   hi_omci_mib_ext_actgetnextalarm  },//12
	{ HI_OMCI_PRO_ACTIONS_MIBUPLOAD_E,         hi_omci_mib_ext_actmibupload     },//13
	{ HI_OMCI_PRO_ACTIONS_MIBUPLOADNEXT_E,     hi_omci_mib_ext_actmibuploadnext },//14
	{ HI_OMCI_PRO_ACTIONS_MIBRESET_E,          hi_omci_mib_ext_actmibreset      },//15
	{ HI_OMCI_PRO_NOTIFICATIONS_ALARM_E,       HI_NULL                           },//16
	{ HI_OMCI_PRO_NOTIFICATIONS_AVC_E,         HI_NULL                           },//17
	{ HI_OMCI_PRO_ACTIONS_TEST_E,              hi_omci_mib_ext_acttest          },//18
	{ HI_OMCI_PRO_ACTIONS_STARTDOWNLOAD_E,     hi_omci_mib_ext_actstartdownload },//19
	{ HI_OMCI_PRO_ACTIONS_DOWNLOADING_E,       hi_omci_mib_ext_actdownloading   },//20
	{ HI_OMCI_PRO_ACTIONS_ENDDOWNLOAD_E,       hi_omci_mib_ext_actenddownload   },//21
	{ HI_OMCI_PRO_ACTIONS_ACTIVEIMAGE_E,       hi_omci_mib_ext_actactiveimage   },//22
	{ HI_OMCI_PRO_ACTIONS_COMMITIMAGE_E,       hi_omci_mib_ext_actcommitimage   },//23
	{ HI_OMCI_PRO_ACTIONS_SYNCTIME_E,          hi_omci_mib_ext_actsynctime      },//24
	{ HI_OMCI_PRO_ACTIONS_REBOOT_E,            hi_omci_mib_ext_actreboot        },//25
	{ HI_OMCI_PRO_ACTIONS_GETNEXT_E,           hi_omci_mib_ext_actgetnext       },//26
	{ HI_OMCI_PRO_NOTIFICATIONS_TESTRESULT_E,  hi_omci_mib_ext_acttestresult    },//27
	{ HI_OMCI_PRO_ACTIONS_GET_CURRDATA_E,      hi_omci_mib_ext_actgetcurrdata   },//28
	{ HI_0MCI_PRO_ACTIONS_SET_TABLE_E,         hi_omci_mib_ext_actsettable      },//29
};

uint32_t hi_omci_mib_ext(hi_void *pv_data, hi_void *pv_outdata,
			 hi_omci_mib_attrlist_s *pst_attrlist)
{
	uint32_t ui_ret = HI_RET_SUCC;
	uint8_t uc_act = HI_OMCI_PROC_GET_MT((uint8_t *)pv_data);

	if (uc_act >= (sizeof(g_st_omci_mib_ext) / (sizeof(hi_omci_mib_act_s)))) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_NOT_SUPPORTED_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}

	if (HI_NULL == g_st_omci_mib_ext[uc_act].pv_action) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_NOT_SUPPORTED_E, 0, 0, 0, 0);
		return HI_OMCI_PRO_ERR_NOT_SUPPORTED_E;
	}
	/*before*/
	ui_ret = hi_omci_mib_forward(pv_data, pst_attrlist);
	if (HI_RET_SUCC == ui_ret) { // 不为成功即不需要操作数据库
		/*proc*/
		ui_ret = g_st_omci_mib_ext[uc_act].pv_action(pv_data, pv_outdata, pst_attrlist);
		if (HI_RET_SUCC !=  ui_ret) {
			hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
			return ui_ret;
		}
	} else if ((hi_uint32)HI_RET_OMCI_MIB_DONT_UPDATEDB_E != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	/*next*/
	ui_ret = hi_omci_mib_next(pv_data, pst_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_PROCESS_ERR_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
