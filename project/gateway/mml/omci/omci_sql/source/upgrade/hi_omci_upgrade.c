/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_upgrade.c
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2011_12_12
  功能描述   : omci升级支持文件
******************************************************************************/

#include "hi_omci_lib.h"
#include "hi_ipc.h"
#include "hi_upgrade.h"
#include "hi_omci_tapi_upgrade.h"
#include "hi_omci_tapi_lib.h"
#include "hi_omci_me.h"
#include "hi_timer.h"
#include "igdGlobalTypeDef.h"
#include "igdCmApi.h"
#include "hi_cfm_api.h"
//#include "hi_drv_common.h"
#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

#define HI_OMCI_UPGRADE_DATA_LEN                31
#define HI_OMCI_UPGRADE_IMAGE_LEN               64
#define HI_OMCI_UPGRADE_EXT_IMAGE_LEN           1
#define HI_OMCI_UPGRADE_IMAGE_BUFFER_LEN        (HI_OMCI_UPGRADE_IMAGE_LEN * 256)
#define HI_OMCI_UPGRADE_IMAGE_WRITE_BUFFER_LEN  (4096)
#define HI_OMCI_UPGRADE_IMAGE_WRITE_TIME        (HI_OMCI_UPGRADE_IMAGE_BUFFER_LEN / HI_OMCI_UPGRADE_IMAGE_WRITE_BUFFER_LEN)

#define UPGRADE_STATE_FILE    HI_CONF_WORK_DIR"/upgrade_state.txt"

typedef struct {
	hi_uint32   ui_imagesize;
	hi_uint32   ui_curimagesize;//current image size;
	hi_int32    i_fd;

	hi_uchar8   uc_winsize;     //每个window的section数目
	hi_uchar8   uc_savedsection;//当前保存的section的数目
	hi_uchar8   uc_resv[2];
	hi_uint32   ui_krnl_crc;
	hi_uint32   ui_rtfs_crc;
	hi_uint32   ui_pkg_crc;
	hi_uint32   ui_crc_cnt;
	hi_uint32   ui_curwin_imagesize;//当前window 升级包长度
	hi_uchar8   uc_upgrade_version[64];
} hi_omci_upgrade_info_s;

static hi_uchar8 g_uc_omci_img[HI_OMCI_UPGRADE_IMAGE_BUFFER_LEN];
static hi_uchar8 *gp_uc_omci_img;
static hi_omci_upgrade_info_s g_st_upgradeinfo = {0};
static hi_uint32 g_omci_upgrade_timer;

static hi_uint32 gui_upgrade_sharding_count;
static hi_uint32 gui_upgrade_sharding_number;
static hi_uint32 gui_omci_upgrade_first_pkg = 0;

/*****************************************************************************
 函 数 名  : hi_omci_upgrade_start
 功能描述  : 升级函数开始标示,创建文件
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_upgrade_start(hi_void *pv_data, hi_void *pv_outdata)
{
	uint16_t image_inst = HI_OMCI_PROC_GET_INST((hi_uchar8 *)pv_data);

	/* initialize the upgrade information */
	HI_OS_MEMSET_S(&g_st_upgradeinfo, sizeof(hi_omci_upgrade_info_s), 0, sizeof(hi_omci_upgrade_info_s));

	/* fill in the response message */
	if (HI_OMCI_PROC_GET_DEVID((uint8_t *)pv_data) == HI_OMCI_PROC_DEVICEID_STD) {
		g_st_upgradeinfo.uc_winsize = HI_OMCI_PROC_STD_GET_WINDOW_SIZE((uint8_t *)pv_data);
		if (g_st_upgradeinfo.uc_winsize > (HI_OMCI_UPGRADE_IMAGE_LEN - 1))
			g_st_upgradeinfo.uc_winsize = HI_OMCI_UPGRADE_IMAGE_LEN - 1;
		g_st_upgradeinfo.ui_imagesize = HI_OMCI_PROC_STD_GET_IMAGE_SIZE((uint8_t *)pv_data);
		HI_OMCI_PROC_STDRSP_SET_RESULT((uint8_t *)pv_outdata, HI_OMCI_PRO_ERR_NO_E);
		HI_OMCI_PROC_STDRSP_SET_SSDL_WINSIZE((uint8_t *)pv_outdata, g_st_upgradeinfo.uc_winsize);
		HI_OMCI_PROC_STDRSP_SET_SSDL_INST_NUM((uint8_t *)pv_outdata, 1);
		HI_OMCI_PROC_STDRSP_SET_SSDL_MEID((uint8_t *)pv_outdata, image_inst);
		HI_OMCI_PROC_STDRSP_SET_SSDL_RESULT((uint8_t *)pv_outdata, HI_OMCI_PRO_ERR_NO_E);
	} else {
		g_st_upgradeinfo.uc_winsize = HI_OMCI_PROC_EXT_GET_WINDOW_SIZE((uint8_t *)pv_data);
		if (g_st_upgradeinfo.uc_winsize > (HI_OMCI_UPGRADE_EXT_IMAGE_LEN - 1))
			g_st_upgradeinfo.uc_winsize = HI_OMCI_UPGRADE_EXT_IMAGE_LEN - 1;
		g_st_upgradeinfo.ui_imagesize = HI_OMCI_PROC_EXT_GET_IMAGE_SIZE((uint8_t *)pv_data);
		HI_OMCI_PROC_EXTRSP_SET_RESULT((uint8_t *)pv_outdata, HI_OMCI_PRO_ERR_NO_E);
		HI_OMCI_PROC_EXTRSP_SET_SSDL_WINSIZE((uint8_t *)pv_outdata, g_st_upgradeinfo.uc_winsize);
		HI_OMCI_PROC_EXTRSP_SET_SSDL_INST_NUM((uint8_t *)pv_outdata, 1);
		HI_OMCI_PROC_EXTRSP_SET_SSDL_MEID((uint8_t *)pv_outdata, image_inst);
		HI_OMCI_PROC_EXTRSP_SET_SSDL_RESULT((uint8_t *)pv_outdata, HI_OMCI_PRO_ERR_NO_E);
	}
	/* debug info */
	hi_omci_lib_printmem((hi_uchar8 *)&g_st_upgradeinfo, sizeof(hi_omci_upgrade_info_s), "\r\nstart down images");

	gui_omci_upgrade_first_pkg = 1;
	return HI_RET_SUCC;
}

static void hi_omci_upgrade_load_proc(hi_void *data, hi_uint32 len, hi_uchar8 *pkt, hi_uint32 *iamgelen)
{
	uint32_t writenlen;

	if (g_st_upgradeinfo.ui_curimagesize >= g_st_upgradeinfo.ui_imagesize)
		return;

	writenlen = g_st_upgradeinfo.ui_imagesize - g_st_upgradeinfo.ui_curimagesize;
	if (writenlen > HI_OMCI_UPGRADE_DATA_LEN)
		writenlen = len - 0x8 - sizeof(hi_omci_proc_msg_head_s) - 1;

	if (HI_OMCI_PROC_GET_DEVID((uint8_t *)data) == HI_OMCI_PROC_DEVICEID_EXT) {
		writenlen = HI_OMCI_PROC_EXT_GET_SIZE((uint8_t *)data) - 1;
		if (writenlen > (HI_PON_REPORTMSG_MAX_LEN - HI_OMCI_PROC_HEADLEN_EXT - 1))
			writenlen = HI_PON_REPORTMSG_MAX_LEN - HI_OMCI_PROC_HEADLEN_EXT - 1;
	}
	if (memcpy_s(gp_uc_omci_img, HI_OMCI_UPGRADE_IMAGE_BUFFER_LEN - g_st_upgradeinfo.ui_curwin_imagesize,
		pkt, writenlen) != HI_RET_SUCC)
		printf("omci upgrade load proc writh len err = %d\n", writenlen);
	gp_uc_omci_img += writenlen;
	g_st_upgradeinfo.ui_curimagesize += writenlen;
	g_st_upgradeinfo.ui_curwin_imagesize += writenlen;
	g_st_upgradeinfo.uc_savedsection++;
	*iamgelen += writenlen;
}

/*****************************************************************************
 函 数 名  : hi_omci_upgrade_loading
 功能描述  : 加载升级软件
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_upgrade_loading(hi_void *pv_data, hi_void *pv_outdata, hi_uint32 ui_len)
{
	hi_int32  i_ret = 0;
	hi_uint32 ui_i  = 0;
	static hi_uint32 ui_writeflag   = HI_FALSE;
	static hi_uint32 ui_iamgelen = 0;
	hi_upgrade_sharding_header_s st_sharding_header;
	hi_upgrade_file_header_s st_upgrade_header;

	hi_uchar8 uc_sectionnum;
	hi_uchar8 uc_ar = ((hi_omci_proc_msg_type_u)HI_OMCI_PROC_GET_MSGTYPE((hi_uchar8 *)pv_data)).st_msgtype.uc_ar;
	hi_uchar8 *puc_data;

	if (HI_OMCI_PROC_GET_DEVID((uint8_t *)pv_data) == HI_OMCI_PROC_DEVICEID_STD)
		uc_sectionnum = HI_OMCI_PROC_STD_GET_DS_NUM((uint8_t *)pv_data);
	else
		uc_sectionnum = HI_OMCI_PROC_EXT_GET_DS_NUM((uint8_t *)pv_data);

	if ((uc_sectionnum == 0) && (ui_writeflag  == HI_FALSE)) {
		HI_OS_MEMSET_S(g_uc_omci_img, HI_OMCI_UPGRADE_IMAGE_BUFFER_LEN, 0, HI_OMCI_UPGRADE_IMAGE_BUFFER_LEN);
		gp_uc_omci_img = (hi_uchar8 *)&g_uc_omci_img;
		ui_writeflag   = HI_TRUE;
		ui_iamgelen = 0;
		g_st_upgradeinfo.uc_savedsection = 0;
		g_st_upgradeinfo.ui_curwin_imagesize = 0;
	} else if ((g_st_upgradeinfo.uc_savedsection == (uc_sectionnum + 1)) && (ui_writeflag  == HI_FALSE)) {
		/* 如果是window的最后一个section的重传报文，需回应response消息 */
		if (HI_OMCI_PROC_GET_DEVID((uint8_t *)pv_data) == HI_OMCI_PROC_DEVICEID_STD)
			HI_OMCI_PROC_STDRSP_SET_DS_NUM((uint8_t *)pv_outdata, uc_sectionnum);
		else
			HI_OMCI_PROC_EXTRSP_SET_DS_NUM((uint8_t *)pv_outdata, uc_sectionnum);
		return HI_RET_SUCC;
	}

	if (uc_sectionnum >= HI_OMCI_UPGRADE_IMAGE_LEN ||
	    ((HI_OMCI_PROC_GET_DEVID((uint8_t *)pv_data) == HI_OMCI_PROC_DEVICEID_EXT) &&
	     uc_sectionnum >= HI_OMCI_UPGRADE_EXT_IMAGE_LEN)) {
		hi_os_printf("%s %d %d\r\n", __func__, __LINE__, uc_sectionnum);
		ui_writeflag = HI_FALSE;
		return HI_RET_SUCC;
	}

	puc_data = (HI_OMCI_PROC_GET_DEVID((uint8_t *)pv_data) == HI_OMCI_PROC_DEVICEID_STD) ?
		   (uint8_t *)pv_data + HI_OMCI_PROC_HEADLEN_STD + 1 :
		   (uint8_t *)pv_data + HI_OMCI_PROC_HEADLEN_EXT + 1;

	hi_omci_upgrade_load_proc(pv_data, ui_len, puc_data, &ui_iamgelen);
	/*如果是window的最后一个section，需回应response消息*/
	if (uc_ar == 1) {
		/*填充response消息*/
		if (HI_OMCI_PROC_GET_DEVID((uint8_t *)pv_data) == HI_OMCI_PROC_DEVICEID_STD)
			HI_OMCI_PROC_STDRSP_SET_DS_NUM((uint8_t *)pv_outdata, uc_sectionnum);
		else
			HI_OMCI_PROC_EXTRSP_SET_DS_NUM((uint8_t *)pv_outdata, uc_sectionnum);

		/*对window内section个数进行校验*/
		if (g_st_upgradeinfo.uc_savedsection != (uc_sectionnum + 1)) {
			g_st_upgradeinfo.ui_curimagesize -= g_st_upgradeinfo.ui_curwin_imagesize;
			ui_writeflag = HI_FALSE;
			hi_os_printf("%s %d check fail, only recv %u section\r\n", __func__, __LINE__, g_st_upgradeinfo.uc_savedsection);
			return HI_RET_FAIL;
		}
		puc_data = &g_uc_omci_img[0];

		if (gui_omci_upgrade_first_pkg) {
			HI_OS_MEMSET_S(&st_sharding_header, sizeof(hi_upgrade_sharding_header_s), 0, sizeof(hi_upgrade_sharding_header_s));
			HI_OS_MEMCPY_S(&st_sharding_header, sizeof(st_sharding_header), puc_data, sizeof(hi_upgrade_sharding_header_s));
			gui_upgrade_sharding_number = st_sharding_header.ui_sharding_number;
#if defined(HG_OLT_UPGRADE_NO_SHARDING)
			st_sharding_header.ui_pkg_total_len = g_st_upgradeinfo.ui_imagesize;
#endif
			HI_OMCI_RET_CHECK(hi_omci_tapi_upgrade_start((hi_uint32)HI_SRCMODULE_CMS_OMCI, &st_sharding_header));

			/* 提取分片0中携带的升级包版本 */
			if (0 == st_sharding_header.ui_sharding_index) {
				HI_OS_MEMSET_S(&st_upgrade_header, sizeof(hi_upgrade_file_header_s), 0, sizeof(hi_upgrade_file_header_s));
				HI_OS_MEMCPY_S(&st_upgrade_header, sizeof(hi_upgrade_file_header_s), puc_data + sizeof(hi_upgrade_sharding_header_s),
					       sizeof(hi_upgrade_file_header_s));
				HI_OS_MEMCPY_S(g_st_upgradeinfo.uc_upgrade_version, sizeof(g_st_upgradeinfo.uc_upgrade_version),
					       st_upgrade_header.auc_soft_version, sizeof(st_upgrade_header.auc_soft_version));
			}
			gui_omci_upgrade_first_pkg = 0;
		}

		for (ui_i = 0; ui_i < HI_OMCI_UPGRADE_IMAGE_WRITE_TIME; ui_i++) {
			if (ui_iamgelen > HI_OMCI_UPGRADE_IMAGE_WRITE_BUFFER_LEN) {
				i_ret = hi_omci_tapi_upgrade_write(puc_data + (ui_i * HI_OMCI_UPGRADE_IMAGE_WRITE_BUFFER_LEN),
								   HI_OMCI_UPGRADE_IMAGE_WRITE_BUFFER_LEN);

				if (HI_RET_SUCC != i_ret) {
					hi_os_printf("%s %d\r\n", __func__, __LINE__);
					hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
					return i_ret;
				}

				ui_iamgelen -= HI_OMCI_UPGRADE_IMAGE_WRITE_BUFFER_LEN;
			} else if (ui_iamgelen == 0) {
				break;
			} else {
				i_ret = hi_omci_tapi_upgrade_write(puc_data + (ui_i * HI_OMCI_UPGRADE_IMAGE_WRITE_BUFFER_LEN), ui_iamgelen);
				if (HI_RET_SUCC != i_ret) {
					hi_os_printf("%s %d\r\n", __func__, __LINE__);
					hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
					return i_ret;
				}
				break;
			}
		}

		ui_writeflag = HI_FALSE;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, uc_sectionnum, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_upgrade_end
 功能描述  : 加载结束
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_upgrade_end(hi_void *pv_data, hi_void *pv_outdata)
{
	hi_int32  i_ret;
	char cmd_buff[256];
	hi_omci_me_software_image_msg_s st_entity;
	hi_omci_tapi_sysinfo_s st_info;
	uint16_t instid = HI_OMCI_PROC_GET_INST((uint8_t *)pv_data);
	uint16_t soft_image_inst;

	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));

	/* fill in the response message */
	if (HI_OMCI_PROC_GET_DEVID((uint8_t *)pv_data) == HI_OMCI_PROC_DEVICEID_STD) {
		soft_image_inst = HI_OMCI_PROC_STD_GET_ESDL_INST((uint8_t *)pv_data);
		HI_OMCI_PROC_STDRSP_SET_RESULT((uint8_t *)pv_outdata, HI_OMCI_PRO_ERR_NO_E);
		HI_OMCI_PROC_STDRSP_SET_ESDL_INST_NUM((uint8_t *)pv_outdata, 1);
		HI_OMCI_PROC_STDRSP_SET_ESDL_MEID((uint8_t *)pv_outdata, soft_image_inst);
		HI_OMCI_PROC_STDRSP_SET_ESDL_RESULT((uint8_t *)pv_outdata, HI_OMCI_PRO_ERR_NO_E);
	} else {
		soft_image_inst = HI_OMCI_PROC_EXT_GET_ESDL_INST((uint8_t *)pv_data);
		HI_OMCI_PROC_EXTRSP_SET_RESULT((uint8_t *)pv_outdata, HI_OMCI_PRO_ERR_NO_E);
		HI_OMCI_PROC_EXTRSP_SET_ESDL_INST_NUM((uint8_t *)pv_outdata, 1);
		HI_OMCI_PROC_EXTRSP_SET_ESDL_MEID((uint8_t *)pv_outdata, soft_image_inst);
		HI_OMCI_PROC_EXTRSP_SET_ESDL_RESULT((uint8_t *)pv_outdata, HI_OMCI_PRO_ERR_NO_E);
	}

	/* 修改software实体版本号 */
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	HI_OS_MEMCPY_S(st_entity.st_image.uc_version, sizeof(st_entity.st_image.uc_version),
		       g_st_upgradeinfo.uc_upgrade_version, sizeof(st_entity.st_image.uc_version));
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	hi_os_printf("End download success.\n");

	/* 修改升级状态文件中版本信息 */
	HI_OS_MEMSET_S(cmd_buff, sizeof(cmd_buff), 0, sizeof(cmd_buff));
	HI_OS_SPRINTF_S(cmd_buff, sizeof(cmd_buff), "sed -i 's/version_id=[01]/version_id=%hu/g' %s", instid,
			UPGRADE_STATE_FILE);
	system(cmd_buff);

	i_ret = hi_omci_tapi_sysinfo_get(&st_info);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(cmd_buff, sizeof(cmd_buff), 0, sizeof(cmd_buff));
	HI_OS_SPRINTF_S(cmd_buff, sizeof(cmd_buff), "sed -i 's/sw_version=.*/sw_version=%s/' %s", st_info.auc_soft_version,
			UPGRADE_STATE_FILE);
	system(cmd_buff);

	gui_upgrade_sharding_count++;
	return HI_RET_SUCC;
}

static hi_void hi_omci_upgrade_active_reboot(hi_void *pv_data)
{
	static hi_uint32 ui_flag = HI_FALSE;

	if (ui_flag == HI_FALSE) {
		ui_flag = HI_TRUE;
	} else {
		if (HI_RET_SUCC == HI_IPC_CALL("hi_upgrade_sharding_isdone")) {
			igdCmRebootInfo(IGD_CM_CMD_REBOOT_BY_ITMS, REBOOT_BY_UPGRADE_IMAGE);
			hi_timer_destroy_wo_lock(g_omci_upgrade_timer);
			hi_os_printf("Active download reboot.\n");
			hi_os_system("reboot");
			return;
		}

	}
	hi_timer_mod_wo_lock(g_omci_upgrade_timer, 500);
}

/*****************************************************************************
 函 数 名  : hi_omci_upgrade_active
 功能描述  : 加载结束
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_upgrade_active(hi_void *pv_data, hi_void *pv_outdata)
{
	hi_int32  i_ret;
	char cmd_buff[256];
	hi_omci_me_software_image_msg_s st_entity;
	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	/*填充response消息*/
	*(hi_uchar8 *)((hi_uchar8 *)pv_outdata + sizeof(hi_omci_proc_msg_head_s))       = HI_OMCI_PRO_ERR_NO_E;

	/* 切换software实例的激活状态 */
	hi_ushort16 us_instid = HI_OMCI_PROC_GET_INST((hi_uchar8 *)pv_data);
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	st_entity.st_image.uc_active = HI_TRUE;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(cmd_buff, sizeof(cmd_buff), 0, sizeof(cmd_buff));
	HI_OS_SPRINTF_S(cmd_buff, sizeof(cmd_buff), "sed -i 's/active=[01]/active=%hu/g' %s", us_instid, UPGRADE_STATE_FILE);
	system(cmd_buff);

	us_instid = (us_instid == 1 ? 0 : 1);
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	st_entity.st_image.uc_active = HI_FALSE;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_os_printf("Active download success.\n");
#if defined(HG_OLT_UPGRADE_NO_SHARDING)
	//no need check, or will fail to reboot
#else
	if (gui_upgrade_sharding_count == gui_upgrade_sharding_number)
#endif
	{
		if (HI_RET_SUCC != hi_timer_create(hi_omci_upgrade_active_reboot, 0, &g_omci_upgrade_timer)) {
			return (hi_uint32)HI_RET_FAIL;
		}
		hi_timer_mod(g_omci_upgrade_timer, 500);
	}


	return HI_RET_SUCC;
}


/*****************************************************************************
 函 数 名  : hi_omci_upgrade_commit
 功能描述  : 加载结束
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_upgrade_commit(hi_void *pv_data, hi_void *pv_outdata)
{
	hi_int32  i_ret;
	char cmd_buff[256];
	hi_omci_me_software_image_msg_s st_entity;
	HI_OS_MEMSET_S(&st_entity, sizeof(st_entity), 0, sizeof(st_entity));
	/*填充response消息*/
	*(hi_uchar8 *)((hi_uchar8 *)pv_outdata + sizeof(hi_omci_proc_msg_head_s))       = HI_OMCI_PRO_ERR_NO_E;

	/* 切换software实例的提交状态 */
	hi_ushort16 us_instid = HI_OMCI_PROC_GET_INST((hi_uchar8 *)pv_data);
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	st_entity.st_image.uc_commited = HI_TRUE;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	HI_OS_MEMSET_S(cmd_buff, sizeof(cmd_buff), 0, sizeof(cmd_buff));
	HI_OS_SPRINTF_S(cmd_buff, sizeof(cmd_buff), "sed -i 's/commited=[01]/commited=%hu/g' %s", us_instid, UPGRADE_STATE_FILE);
	system(cmd_buff);

	us_instid = (us_instid == 1 ? 0 : 1);
	i_ret = hi_omci_ext_get_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);
	st_entity.st_image.uc_commited = HI_FALSE;
	i_ret = hi_omci_ext_set_inst(HI_OMCI_PRO_ME_SW_IMAGE_E, us_instid, &st_entity);
	HI_OMCI_RET_CHECK(i_ret);

	hi_os_printf("Commit download success.\n");
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_upgrade_set_filename
 功能描述  : 外部函数调用接口在升级前调用更改输出文件全路径
 输入参数  : hi_uchar8 *puc_filename
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_upgrade_set_filename(hi_uchar8 *puc_filename)
{
	HI_INVALID_PTR(puc_filename);
	hi_omci_upgrade_init();

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_upgrade_init
 功能描述  : 升级模块初始化
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_upgrade_init(hi_void)
{
	HI_OS_MEMSET_S(&g_st_upgradeinfo, sizeof(hi_omci_upgrade_info_s), 0, sizeof(hi_omci_upgrade_info_s));

	gui_upgrade_sharding_count = 0;
	gui_upgrade_sharding_number = 0;

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
