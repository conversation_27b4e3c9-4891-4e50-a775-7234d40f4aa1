/******************************************************************************

                  Copyright (C), 2012-2013, HSA

 ******************************************************************************
  Filename   : hi_omci_logger.c
  Version    : 初稿
  Author     : chenshibin 00184653
  Creation   : 2015-1-30
  Description:
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include <string.h>
#include <sys/stat.h>
#include <unistd.h>
#include <errno.h>
#include <time.h>
#include <stdlib.h>
#include "hi_omci_logger.h"

#include <pthread.h>
#include "hi_os_securec.h"
#define TIMESTR_LEN 26

static FILE *log = NULL;
pthread_mutex_t mut;

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/
/* Global Varibles */
//int log_out_mode = 0;          //pattern chose to output, 0-stdout:1-logger:>2-stdout&logger
//int def_level = DEBUG;         //defined start level,only less than it will logger
//int log_size = 50;             //control the log file size
//int log_module = all;          //default module is all module
int def_level;
int log_out_mode;
int log_size;
int log_module;

/*
 ********************************************************************************************
 * logger function:write log
 * arguments:
 *     level: log start-level
 *     file: log occur which file
 *     line: log occur which line
 *     function: log occur which function
 *     fmt,...: support variable numbers of parameter
 * return value:
 *     void
 ********************************************************************************************
 */
int logger(int level, const char *file, int line, const char *function, const char *fmt, ...)
{
	FILE *logger = NULL;
	FILE *temp = NULL;
	va_list ap;
	char time_buf[TIMESTR_LEN];
	time_t time_now;
	struct tm *timenow = NULL;
	struct stat stat_buf;
	//char cmd[256];
	int  ret = 0;

	pthread_mutex_lock(&mut);

	time(&time_now);
	timenow = localtime(&time_now);
	HI_OS_MEMSET_S(time_buf, TIMESTR_LEN, 0, TIMESTR_LEN);
	HI_OS_STRCPY_S(time_buf, sizeof(time_buf), asctime(timenow));
	time_buf[sizeof(time_buf) - 2] = '\0';

	//judge the size of log file, if above max size, backup and rewrite log file
	if (stat(LOG_FILE, &stat_buf) < 0) {
		printf("%s, [%d](logger):read log file failure\n", time_buf, __LINE__);
		pthread_mutex_unlock(&mut);
		return -1;
	} else {
	}
#if 0
	if (stat_buf.st_size > log_size * 1024) {
		//backup log file
		logger = NULL;

		HI_OS_MEMSET_S(cmd, sizeof(cmd), '\0', sizeof(cmd));
		HI_OS_SPRINTF_S(cmd, sizeof(cmd), "mv  %s/hi_omci.log %s/hi_omci.log.bak -f", "/config/work");
		system(cmd);

	}
#endif
	temp = fopen(LOG_FILE, "a");
	if (!temp) {
		fprintf(stderr, "%s, [%d](logger): %s\n", time_buf, __LINE__, strerror(errno));

		pthread_mutex_unlock(&mut);
		return -1;
	}

	//start to parse variable arguments
	va_start(ap, fmt);

	//only output in screen
	if (log_out_mode == 0) {
		logger = stderr;
#if 0
		ret = fprintf(logger, "%s, %s[%d](%s): ", time_buf, file, line, function);
		if (ret < 0) {
			fclose(temp);
			goto __GET_OUT;
		}
#endif
		ret = vfprintf(logger, fmt, ap);
		if (ret < 0) {
			fclose(temp);
			goto __GET_OUT;
		}
	}
	//only write in log
	else if (log_out_mode == 1) {
		logger = temp;
#if 0
		ret = fprintf(logger, "%s, %s[%d](%s): ", time_buf, file, line, function);
		if (ret < 0) {
			fclose(temp);
			goto __GET_OUT;
		}
#endif
		ret = vfprintf(logger, fmt, ap);
		if (ret < 0) {
			fclose(temp);
			goto __GET_OUT;
		}
	}
	//output in screen and in log
	else if (log_out_mode == 2) {
		logger = stderr;
#if 0
		ret = fprintf(logger, "%s, %s[%d](%s): ", time_buf, file, line, function);
		if (ret < 0) {
			fclose(temp);
			goto __GET_OUT;
		}
#endif
		ret = vfprintf(logger, fmt, ap);
		if (ret < 0) {
			fclose(temp);
			goto __GET_OUT;
		}

		ret = fflush(logger);
		if (EOF == ret) {
			fclose(temp);
			goto __GET_OUT;
		}

		logger = temp;
#if 0
		ret = fprintf(logger, "%s, %s[%d](%s): ", time_buf, file, line, function);
		if (ret < 0) {
			fclose(temp);
			goto __GET_OUT;
		}
#endif
		ret = vfprintf(logger, fmt, ap);
		{
			fclose(temp);
			goto __GET_OUT;
		}
	} else {
		fclose(temp);
		goto __GET_OUT;
	}
	ret = fflush(logger);
	if (EOF == ret) {
		fclose(temp);
		goto __GET_OUT;
	}

	ret = fclose(temp);
	if (EOF == ret) {
		goto __GET_OUT;
	}

__GET_OUT:
	pthread_mutex_unlock(&mut);
	//end to parse variable arguments
	va_end(ap);

	return 0;
}


/*
 ********************************************************************************************
 * init_logger function: open log file
 * arguments:
 *     void
 * retuen value:
 *     if right return 0, else return -1
 * modified by zyzhou 2008-10-18
********************************************************************************************
 */
int init_logger(void)
{
	char cmd[128];

	HI_OS_SPRINTF_S(cmd, sizeof(cmd), "rm -rf %s", LOG_FILE);
	system(cmd);

	HI_OS_SPRINTF_S(cmd, sizeof(cmd), "touch %s", LOG_FILE);
	system(cmd);
	if (pthread_mutex_init(&mut, NULL) != 0) {
		LOG(m_init, ERROR, "Init Mutex tree_lock failed.\n");
		return -1;
	}


	set_logger(def_level, log_out_mode, log_size, all, 0);
	return 0;
}

/*
 ********************************************************************************************
 * exit_logger function: close the log file
 * arguments:
 *     void
 * return value:
 *     void
 ********************************************************************************************
 */

void exit_logger(void)
{
	LOG(all, DEBUG, "Closed logger\n");
	if (log) {
		fclose(log);
	}
}

/*
 ********************************************************************************************
 * logger_set  function: change the log level and output mode
 * arguments:
 *    level:   log start level
 *    mode:    log output mode
 *    size:size of log file
 *    module:  which module's log message will be out
 * return value:
 *    right return 0
 *    wrong return -1
 ********************************************************************************************
 */

int set_logger(int l_level, int l_mode, int l_size, int l_module, unsigned int l_switch)
{
	def_level = l_level;
	log_out_mode = l_mode;
	if (l_size != 0) {
		log_size = l_size;
	}
	log_module = l_module;

	log_out_mode = 1;//0-stdout:1-logger:>2-stdout&logger
	def_level = 3;
	log_size = 1000;
	return 0;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
