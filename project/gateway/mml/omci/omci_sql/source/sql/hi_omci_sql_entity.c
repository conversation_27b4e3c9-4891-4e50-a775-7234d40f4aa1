/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_sql_entity.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_11_04
  功能描述   : 对entitytable操作
******************************************************************************/

#include "hi_omci_lib.h"
#include <dlfcn.h>

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

static hi_uchar8 g_uc_acttablename[HI_OMCI_SQL_ENTITYACTTABLE_BUTT_E][32] = {
	{0},
	{"createc"},
	{"createcc"},
	{"deletec"},
	{"deletecc"},
	{"setc"},
	{"get"},
	{"getcc"},
	{"getalarm"},
	{"getalarmnext"},
	{"mibupload"},
	{"mibuploadnext"},
	{"mibreset"},
	{"alarm"},
	{"avc"},
	{"test"},
	{"startdowndload"},
	{"downloading"},
	{"enddownload"},
	{"activeimage"},
	{"commitimage"},
	{"synctime"},
	{"reboot"},
	{"getnext"},
	{"testresult"},
	{"getcurrentdata"},
	{"settable"},
};

/*****************************************************************************
 函 数 名  : hi_omci_sql_entity_msgtype
 功能描述  : 获取msgtype描述
 输入参数  : hi_uchar8 uc_msgtype
 输出参数  : 无
 返 回 值  : hi_uchar8 *
*****************************************************************************/
hi_uchar8 *hi_omci_sql_entity_msgtype(hi_uchar8 uc_msgtype)
{
	uc_msgtype = uc_msgtype - HI_OMCI_PRO_ACTIONS_CREATE_E + 1;
	if (uc_msgtype >= HI_OMCI_SQL_ENTITYACTTABLE_BUTT_E) {
		hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_NULL;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return (hi_uchar8 *)&g_uc_acttablename[uc_msgtype][0];
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_entity_set
 功能描述  : 保存注册函数内容
 输入参数  : hi_uint32 ui_cmdtype
             hi_void  *pv_callback
             hi_uchar8 *puc_filename
             hi_uchar8 *puc_funcname
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_entity_set(hi_uint32 ui_cmdtype,
				 hi_uint32 ui_callback, hi_uchar8 *puc_filename, hi_uchar8 *puc_funcname)
{
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "INSERT INTO callbacktable(cmdtype,address,desc,lib) VALUES(%u,%u,'%s','%s')",
			 ui_cmdtype, ui_callback, puc_funcname, puc_filename);
	hi_omci_lib_systrace(HI_RET_SUCC, ui_cmdtype, ui_callback, 0, 0);
	return hi_omci_sql_exec(c_sql);
}

/******************************************************************************
 Function    : hi_omci_sql_entity_del
 Description : 删除注册函数
 Input Parm  : hi_void *pv_callback 函数地址
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_sql_entity_del(hi_void *pv_callback)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "DELETE FROM callbacktable WHERE address=%u", (hi_uint32)pv_callback);
	return hi_omci_sql_exec(c_sql);
}

/******************************************************************************
 Function    : hi_omci_sql_entity_fwdtable_set
 Description : 配置注册函数名
 Input Parm  : hi_uint32 ui_meid,
               hi_uint32 ui_act,
               hi_char8 *pc_tablename
               hi_char8 *pc_name
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_sql_entity_table_set(hi_uint32 ui_meid,
				       hi_uint32 ui_act, hi_char8 *pc_tablename, hi_char8 *pc_funcname)
{
	hi_uint32 ui_index;
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	ui_index = ui_act - HI_OMCI_PRO_ACTIONS_CREATE_E + HI_OMCI_SQL_ENTITYACTTABLE_CREATE_E;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "UPDATE %s SET %s='%s' WHERE meid=%u",
			 pc_tablename, g_uc_acttablename[ui_index], pc_funcname, ui_meid);
	hi_omci_lib_systrace(HI_RET_SUCC, ui_meid, ui_act, 0, 0);
	return hi_omci_sql_exec(c_sql);
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_entity_get
 功能描述  : 获取动作
 输入参数  : hi_uchar8 ui_act
             hi_uchar8 *puc_tablename
             hi_void **pv_callback
             hi_uint32 *pui_cmdtype
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_entity_get(hi_uchar8 ui_act, hi_ushort16 us_meid, hi_char8 *pc_tablename,
				 hi_void **pv_callback)
{
	hi_uint32 ui_ret, ui_indx;
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	hi_char8  c_fname[HI_OMCI_SQL_MAX_FUNC_LEN];
	hi_char8 *pc_lib;
	sqlite3_stmt  *pst_mibstmt = 0;
	hi_uint32 ui_handler;

	ui_indx = ui_act - HI_OMCI_PRO_ACTIONS_CREATE_E + HI_OMCI_SQL_ENTITYACTTABLE_CREATE_E;
	if (ui_indx >= HI_OMCI_SQL_ENTITYACTTABLE_BUTT_E) {
		hi_omci_lib_systrace(HI_RET_OUTRANG, ui_indx, ui_act, 0, 0);
		return HI_RET_OUTRANG;
	}

	*pv_callback = 0;
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "select * from callbacktable where desc in ( select %s from %s where meid=%u )",
			 g_uc_acttablename[ui_indx], pc_tablename, us_meid);

	ui_ret = sqlite3_prepare(g_pst_mibdb, c_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_ROW != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	pc_lib = (hi_char8 *)sqlite3_column_text(pst_mibstmt, HI_OMCI_SQL_CALLBACKTABLE_LIB_E);
	if (hi_os_strcmp(pc_lib, HI_OMCI_CALLBACK_MAIN_LIB)) {
		ui_ret = hi_omci_sql_entity_extlib_get(pc_lib, &ui_handler);
		if (HI_RET_SUCC != ui_ret) {
			sqlite3_finalize(pst_mibstmt);
			hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
			return HI_OMCI_SQL_RET(ui_ret);
		}

		HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "select %s from %s where meid=%u",
				 g_uc_acttablename[ui_indx], pc_tablename, us_meid);

		HI_OS_MEMSET_S(c_fname, sizeof(c_fname), 0, sizeof(c_fname));
		ui_ret = hi_omci_sql_getcolumn(c_sql, 0, HI_OMCI_SQL_DB_TEXT_E, (hi_uchar8 *)c_fname, HI_OMCI_SQL_MAX_FUNC_LEN);
		if (HI_RET_SUCC != ui_ret) {
			sqlite3_finalize(pst_mibstmt);
			hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
			return HI_OMCI_SQL_RET(ui_ret);
		}

		*pv_callback = dlsym((hi_void *)ui_handler, c_fname);
	} else {
		*pv_callback = (hi_void *)sqlite3_column_int(pst_mibstmt, HI_OMCI_SQL_CALLBACKTABLE_ADDRESS_E);
	}

	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_entity_desc
 功能描述  : 获取实体表实体名字
 输入参数  : hi_ushort16 us_meid
             hi_uchar8 *puc_name
             hi_uint32 ui_len
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_entity_desc(hi_ushort16 us_meid, hi_uchar8 *puc_name, hi_uint32 ui_len)
{
	hi_uint32 ui_ret;
	hi_uchar8 *puc_desc = HI_NULL;
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	sqlite3_stmt  *pst_mibstmt = 0;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT desc FROM entitytable WHERE meid=%u", us_meid);
	ui_ret = sqlite3_prepare(g_pst_mibdb, c_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_ROW != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	puc_desc = (hi_uchar8 *)sqlite3_column_text(pst_mibstmt, 0);

    ui_ret = memcpy_s(puc_name, ui_len, puc_desc, strlen((hi_char8 *)puc_desc) + 1);
    if (ui_ret != EOK) {
        hi_omci_lib_debug("entity name memcpy fail , ret [%x]\n", ui_ret);
        sqlite3_finalize(pst_mibstmt);
        return HI_RET_FAIL;
    }

	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_uint32 hi_omci_sql_entity_check_meid(hi_ushort16 us_meid)
{
	hi_uint32 ui_ret;
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	sqlite3_stmt  *pst_mibstmt = 0;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT * FROM entitytable WHERE meid=%u", us_meid);
	ui_ret = sqlite3_prepare(g_pst_mibdb, c_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_ROW != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_sql_entity_stat_set
 Description : 配置统计注册函数
 Input Parm  : hi_uint32 ui_meid,
               hi_uint32 ui_callback
               hi_char8 *pc_name
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_sql_entity_stat_set(hi_uint32 ui_meid, hi_uint32 ui_callback, hi_char8 *pc_name)
{
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "INSERT INTO %s(meid,address,lib) VALUES(%u,%u,'%s')",
			 HI_OMCI_MIB_STATTABLE_NAME, ui_meid, ui_callback, pc_name);
	hi_omci_lib_systrace(HI_RET_SUCC, ui_meid, ui_callback, 0, 0);
	return hi_omci_sql_exec(c_sql);
}

/******************************************************************************
 Function    : hi_omci_sql_entity_stat_get
 Description : 获取统计注册函数
 Input Parm  : hi_uint32 ui_meid,
 Output Parm : hi_uint32 *pui_callback
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_sql_entity_stat_get(hi_uint32 ui_meid, hi_uint32 *pui_callback)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "SELECT address FROM %s WHERE meid=%u",
			 HI_OMCI_MIB_STATTABLE_NAME, ui_meid);

	return hi_omci_sql_getcolumn(c_sql, 0, HI_OMCI_SQL_DB_INT32_E, (hi_uchar8 *)pui_callback, sizeof(hi_uint32));
}

/******************************************************************************
 Function    : hi_omci_sql_entity_alarm_set
 Description : 配置告警回调函数
 Input Parm  : hi_uint32 ui_meid,
               hi_uint32 ui_callback
               hi_char8 *pc_name
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_sql_entity_alarm_set(hi_uint32 ui_meid, hi_uint32 ui_callback, hi_char8 *pc_name)
{
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "INSERT INTO %s(meid,address,lib) VALUES(%u,%u,'%s')",
			 HI_OMCI_MIB_ALARMTABLE_NAME, ui_meid, ui_callback, pc_name);
	hi_omci_lib_systrace(HI_RET_SUCC, ui_meid, ui_callback, 0, 0);
	return hi_omci_sql_exec(c_sql);
}

/******************************************************************************
 Function    : hi_omci_sql_entity_alarm_get
 Description : 获取告警回调函数
 Input Parm  : hi_uint32 ui_meid
 Output Parm : hi_uint32 *pui_callback
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_sql_entity_alarm_get(hi_uint32 ui_meid, hi_uint32 *pui_callback)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "SELECT address FROM %s WHERE meid=%u",
			 HI_OMCI_MIB_ALARMTABLE_NAME, ui_meid);

	return hi_omci_sql_getcolumn(c_sql, 0, HI_OMCI_SQL_DB_INT32_E, (hi_uchar8 *)pui_callback, sizeof(hi_uint32));
}

/******************************************************************************
 Function    : hi_omci_sql_entity_extlib_set
 Description : 配置扩展动态库句柄
 Input Parm  : hi_uint32 ui_meid
               hi_void *pv_handler
 Output Parm : 无
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_sql_entity_extlib_set(hi_char8 *pc_name, hi_void *pv_handler)
{
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "INSERT INTO %s(handler,lib) VALUES(%u,'%s')",
			 HI_OMCI_MIB_ENTITYEXTLIBTABLE_NAME, (hi_uint32)pv_handler, pc_name);
	hi_omci_lib_debug("insert into handler[%08x] lib[%s]\n", (hi_uint32)pv_handler, pc_name);
	return hi_omci_sql_exec(c_sql);
}

/******************************************************************************
 Function    : hi_omci_sql_entity_extlib_get
 Description : 获取扩展动态库句柄
 Input Parm  : hi_uint32 ui_meid
 Output Parm : hi_void **pv_handler
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_uint32 hi_omci_sql_entity_extlib_get(hi_char8 *pc_name, hi_void *pv_handler)
{
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "SELECT handler FROM %s WHERE lib='%s'",
			 HI_OMCI_MIB_ENTITYEXTLIBTABLE_NAME, pc_name);

	return hi_omci_sql_getcolumn(c_sql, 0, HI_OMCI_SQL_DB_INT32_E, (hi_uchar8 *)pv_handler, sizeof(hi_uint32));
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
