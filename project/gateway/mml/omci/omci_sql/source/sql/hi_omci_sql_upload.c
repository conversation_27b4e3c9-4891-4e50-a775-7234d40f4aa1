/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_sql_upload.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_11_05
  功能描述   : 用于mibupload处理
******************************************************************************/

#include "hi_omci_lib.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

static hi_omci_mibupload_s g_st_mib_data;
static hi_char8 *g_pc_errmsg = HI_NULL;

/*****************************************************************************
 函 数 名  : hi_omci_sql_upload_free
 功能描述  : 释放所有mib信息内存
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_sql_upload_free(hi_void)
{
	hi_list_head *pst_list_pos    = HI_NULL;
	hi_list_head *pst_list_next   = HI_NULL;
	hi_void      *pst_data        = HI_NULL;

	hi_list_for_each_safe(pst_list_pos, pst_list_next, &g_st_mib_data.st_listhead) {
		pst_data = (hi_void *)hi_list_entry(pst_list_pos, hi_omci_mibupload_seg_s, st_listhead);
		if (HI_NULL != pst_data) {
			hi_list_del(&((hi_omci_mibupload_seg_s *)pst_data)->st_listhead);
			hi_os_free(pst_data);
		}
	}
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_upload_attr
 功能描述  : 将一个实例中的数据内容保存到全局数据链表,同时返回数据块数
 输入参数  : hi_omci_mib_attrlist_s *pst_attrlist
             hi_uint32 *pui_num
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_upload_attr(hi_omci_mib_attrlist_s *pst_attrlist, hi_uint32 *pui_num)
{
	hi_uint32   ui_ret, ui_indx, ui_totalnum, ui_size, us_mask;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;
	hi_uchar8   *puc_buf, *puc_pos = HI_NULL;
	hi_omci_mibupload_seg_s *pst_mib = HI_NULL;

	ui_totalnum = ui_size = us_mask = *pui_num = 0;

	/*new block*/
	puc_buf   = (hi_uchar8 *)hi_os_malloc(sizeof(hi_omci_mibupload_seg_s) + g_st_mib_data.ui_blocksize +
	HI_OMCI_MIB_UPLOAD_ATTR_MASK_LEN);
	if (HI_NULL == puc_buf) {
		hi_omci_lib_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}
	HI_OS_MEMSET_S(puc_buf, sizeof(hi_omci_mibupload_seg_s) + g_st_mib_data.ui_blocksize, 0,
		       sizeof(hi_omci_mibupload_seg_s) + g_st_mib_data.ui_blocksize);
	pst_mib = (hi_omci_mibupload_seg_s *)puc_buf;
	puc_pos = puc_buf + HI_MEMBER_OFFSET(hi_omci_mibupload_seg_s, uc_text);   /*lint !e413 !e545*/
	HI_OMCI_MIB_UPLOAD_SET_ENTITY(puc_pos, pst_attrlist->us_meid);
	HI_OMCI_MIB_UPLOAD_SET_INST(puc_pos, pst_attrlist->us_instid);
	puc_pos += HI_OMCI_MIB_UPLOAD_OFFSET;

	/*loop get per attribute from list*/
	for (ui_indx = 0; ui_indx < pst_attrlist->uc_num; ui_indx++) {
		ui_ret = hi_omci_sql_attr_check(pst_attrlist, ui_indx);
		if (HI_RET_SUCC != ui_ret) {
			continue;
		}
		pst_attr  = &pst_attrlist->st_attr[ui_indx];
		if (pst_attr->uc_size > g_st_mib_data.ui_blocksize) {
			hi_os_free(puc_buf);
			hi_omci_lib_systrace(HI_RET_OUTRANG, ui_indx, 0, 0, 0);
			return HI_RET_OUTRANG;
		}
		ui_size += pst_attr->uc_size;
		if (ui_size > g_st_mib_data.ui_blocksize) {
			/*set size*/
			pst_mib->ui_size = ui_size - pst_attr->uc_size + HI_OMCI_MIB_UPLOAD_OFFSET;
			/*set mask*/
			puc_pos = puc_buf + HI_MEMBER_OFFSET(hi_omci_mibupload_seg_s, uc_text);   /*lint !e413 !e545*/
			HI_OMCI_MIB_UPLOAD_SET_ENTITY(puc_pos, pst_attrlist->us_meid);
			HI_OMCI_MIB_UPLOAD_SET_INST(puc_pos, pst_attrlist->us_instid);
			HI_OMCI_MIB_UPLOAD_SET_MASK(puc_pos, us_mask);
			/*add to list of golbal*/
			hi_list_add_tail(&pst_mib->st_listhead, &g_st_mib_data.st_listhead);

			/*new block*/
			puc_buf = (hi_uchar8 *)hi_os_malloc(sizeof(hi_omci_mibupload_seg_s) + g_st_mib_data.ui_blocksize +
			HI_OMCI_MIB_UPLOAD_ATTR_MASK_LEN);
			if (HI_NULL == puc_buf) {
				hi_omci_lib_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
				return HI_RET_MALLOC_FAIL;
			}
			pst_mib = (hi_omci_mibupload_seg_s *)puc_buf;
			puc_pos = puc_buf + HI_MEMBER_OFFSET(hi_omci_mibupload_seg_s, uc_text);   /*lint !e413 !e545*/
			HI_OMCI_MIB_UPLOAD_SET_ENTITY(puc_pos, pst_attrlist->us_meid);
			HI_OMCI_MIB_UPLOAD_SET_INST(puc_pos, pst_attrlist->us_instid);
			puc_pos += HI_OMCI_MIB_UPLOAD_OFFSET;

			/*recalc mask*/
			us_mask = 0;
			/*calc size and blocknum*/
			ui_size = pst_attr->uc_size;
			ui_totalnum++;
		}
		/*copy data to mem*/
		HI_OS_MEMCPY_S(puc_pos, pst_attr->uc_size, pst_attr->puc_data, pst_attr->uc_size);
		puc_pos += pst_attr->uc_size;
		/*calc mask*/
		us_mask |= HI_ATT_MASK_ONE >> ui_indx;
	}

	/*set mask*/
	puc_pos = puc_buf + HI_MEMBER_OFFSET(hi_omci_mibupload_seg_s, uc_text);   /*lint !e413 !e545*/
	HI_OMCI_MIB_UPLOAD_SET_MASK(puc_pos, us_mask);
	/*add to list of golbal*/
	hi_list_add_tail(&pst_mib->st_listhead, &g_st_mib_data.st_listhead);
	pst_mib->ui_size = ui_size + HI_OMCI_MIB_UPLOAD_OFFSET;
	ui_totalnum++;

	*pui_num = ui_totalnum;
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

#define HI_OMCI_MIB_SEG_MAXSIZE 1600
static uint8_t *g_omci_mib_content_pos = NULL;
static hi_omci_mibupload_seg_s *g_omci_mib_seg = NULL;
static uint8_t *hi_omci_sql_create_new_mib_seg(void)
{
	uint8_t *puc_buf = NULL;

	puc_buf = (uint8_t *)hi_os_malloc(sizeof(hi_omci_mibupload_seg_s) + g_st_mib_data.ui_blocksize);
	if (puc_buf == HI_NULL)
		return NULL;

	(void)memset_s(puc_buf, sizeof(hi_omci_mibupload_seg_s) + g_st_mib_data.ui_blocksize,
		0, sizeof(hi_omci_mibupload_seg_s) + g_st_mib_data.ui_blocksize);
	g_omci_mib_seg = (hi_omci_mibupload_seg_s *)puc_buf;
	g_omci_mib_content_pos = puc_buf + HI_MEMBER_OFFSET(hi_omci_mibupload_seg_s, uc_text);
	/* add to list of golbal */
	hi_list_add_tail(&g_omci_mib_seg->st_listhead, &g_st_mib_data.st_listhead);

	return puc_buf;
}

static uint32_t hi_omci_sql_upload_attr_ext(hi_omci_mib_attrlist_s *pst_attrlist, uint32_t instid, uint32_t *pui_num)
{
	uint32_t ui_indx, size, us_mask;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;
	uint8_t *puc_buf, *puc_pos = HI_NULL;

	size = us_mask = *pui_num = 0;
	/* new block */
	if (instid == 1) {
		puc_buf = hi_omci_sql_create_new_mib_seg();
		if (puc_buf == HI_NULL)
			return HI_RET_MALLOC_FAIL;
		*pui_num = 1;
	}

	if (g_omci_mib_seg == NULL || g_omci_mib_content_pos == NULL)
		return HI_RET_NULLPTR;

	puc_pos = g_omci_mib_content_pos;
	HI_OMCI_MIB_UPLOAD_EXT_SET_ENTITY(puc_pos, pst_attrlist->us_meid);
	HI_OMCI_MIB_UPLOAD_EXT_SET_INST(puc_pos, pst_attrlist->us_instid);
	puc_pos += HI_OMCI_MIB_UPLOAD_EXT_OFFSET;

	/*loop get per attribute from list*/
	for (ui_indx = 0; ui_indx < pst_attrlist->uc_num; ui_indx++) {
		if (hi_omci_sql_attr_check(pst_attrlist, ui_indx) != HI_RET_SUCC)
			continue;

		pst_attr = &pst_attrlist->st_attr[ui_indx];
		if (pst_attr->uc_size > g_st_mib_data.ui_blocksize) {
			hi_list_del(&g_omci_mib_seg->st_listhead);
			hi_os_free(g_omci_mib_seg);
			g_omci_mib_seg = NULL;
			hi_omci_lib_systrace(HI_RET_OUTRANG, ui_indx, 0, 0, 0);
			return HI_RET_OUTRANG;
		}
		size += pst_attr->uc_size;
		/* copy data to mem */
		HI_OS_MEMCPY_S(puc_pos, pst_attr->uc_size, pst_attr->puc_data, pst_attr->uc_size);
		puc_pos += pst_attr->uc_size;
		/* calc mask */
		us_mask |= HI_ATT_MASK_ONE >> ui_indx;
	}

	/* set mask and size */
	puc_pos = g_omci_mib_content_pos;
	HI_OMCI_MIB_UPLOAD_EXT_SET_MASK(puc_pos, us_mask);
	HI_OMCI_MIB_UPLOAD_EXT_SET_SIZE(puc_pos, size);

	/* size of message contents field including all sub-fields */
	g_omci_mib_seg->ui_size += size + HI_OMCI_MIB_UPLOAD_EXT_OFFSET;
	g_omci_mib_content_pos = puc_pos + size + HI_OMCI_MIB_UPLOAD_EXT_OFFSET;
	if (g_omci_mib_seg->ui_size > HI_OMCI_MIB_SEG_MAXSIZE) {
		puc_buf = hi_omci_sql_create_new_mib_seg();
		if (puc_buf == HI_NULL)
			return HI_RET_MALLOC_FAIL;

		*pui_num = 1;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_upload_inst
 功能描述  : 获取一个meid实体的所有实例数据信息
 输入参数  : hi_omci_mib_attrlist_s *pst_attrlist
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_upload_inst(hi_omci_mib_attrlist_s *pst_attrlist, uint32_t mode)
{
	hi_ushort16 us_instid;
	hi_uint32   ui_ret = HI_RET_SUCC;
	hi_uint32   ui_column, ui_row, ui_instindx, ui_num;
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;
	hi_char8    **ppc_result = 0;
	hi_uchar8   *puc_buf = HI_NULL;

	/*query inst table, get content*/
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT instid FROM insttable%u", pst_attrlist->us_meid);
	ui_ret = sqlite3_get_table(g_pst_mibdb, c_sql, &ppc_result, (hi_int32 *)&ui_row, (hi_int32 *)&ui_column, &g_pc_errmsg);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_column, ui_row, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	ui_ret = hi_omci_sql_attr_getlist(pst_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_free_table(ppc_result);
		return ui_ret;
	}

	/*malloc mem for data*/
	puc_buf = (hi_uchar8 *)hi_os_malloc(pst_attrlist->us_totalsize);
	if (HI_NULL == puc_buf) {
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	for (ui_instindx = 1; ui_instindx <= ui_row; ui_instindx++) {
		us_instid = hi_os_strtoul(ppc_result[ui_instindx], 0, 10);
		pst_attrlist->us_instid = us_instid;
		HI_OS_MEMSET_S(puc_buf, pst_attrlist->us_totalsize, 0, pst_attrlist->us_totalsize);

		/*get inst content from db*/
		ui_ret = hi_omci_sql_inst_get(pst_attrlist, puc_buf);
		if (HI_RET_SUCC != ui_ret) {
			break;
		}
		hi_omci_sql_attr_encodehton(pst_attrlist);

		/* calc num and save it will send mibdata */
		if (mode == HI_OMCI_STD_MODE)
			ui_ret = hi_omci_sql_upload_attr(pst_attrlist, &ui_num);
		else
			ui_ret = hi_omci_sql_upload_attr_ext(pst_attrlist, ui_instindx, &ui_num);
		if (HI_RET_SUCC != ui_ret) {
			break;
		}
		hi_omci_sql_inst_clear(pst_attrlist);
		/*refresh num*/
		g_st_mib_data.ui_totalnum += ui_num;
	}

	hi_os_free(puc_buf);
	sqlite3_free_table(ppc_result);

	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_upload
 功能描述  : 得到支持的meid,遍历获取mib信息
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_upload(uint32_t mode)
{
	hi_ushort16 us_meid;
	hi_uint32   ui_ret, ui_column, ui_row, ui_meindex;
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;
	hi_char8    **ppc_result = 0;
	hi_omci_mib_attrlist_s st_attrlist;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT meid FROM entitytable");
	ui_ret = sqlite3_get_table(g_pst_mibdb, c_sql, &ppc_result, (hi_int32 *)&ui_row, (hi_int32 *)&ui_column, &g_pc_errmsg);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_column, ui_row, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	/*query entitytable, get support meid*/
	for (ui_meindex = 1; ui_meindex <= ui_row; ui_meindex++) {
		HI_OS_MEMSET_S(&st_attrlist, sizeof(hi_omci_mib_attrlist_s), 0, sizeof(hi_omci_mib_attrlist_s));
		st_attrlist.uc_opt  = HI_OMCI_ATT_R_W_S_E;
		st_attrlist.us_mask = HI_OMCI_MIB_ATTRMASK;
		us_meid = hi_os_strtoul(ppc_result[ui_meindex], 0, 10);
		/*get meid attrlist*/
		st_attrlist.us_meid = us_meid;

		/*get inst content*/
		ui_ret = hi_omci_sql_upload_inst(&st_attrlist, mode);
		if (HI_RET_SUCC != ui_ret) {
			hi_omci_lib_systrace(ui_ret, ui_column, ui_row, 0, 0);
			continue;
		}
	}

	sqlite3_free_table(ppc_result);
	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_upload_getnum
 功能描述  : 获取mib表num
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_upload_getnum(hi_void)
{
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return g_st_mib_data.ui_totalnum;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_upload_getnum
 功能描述  : 获取mib表数据块
 输入参数  : hi_void *pv_outdata
             hi_uint32 ui_seqnum
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_upload_getblock(hi_void *pv_outdata, hi_uint32 ui_seqnum, uint16_t *blk_size, uint32_t mode)
{
	hi_list_head *pst_list_pos       = g_st_mib_data.pst_lastlist;
	hi_omci_mibupload_seg_s *pst_mib = HI_NULL;

	if (ui_seqnum >= g_st_mib_data.ui_totalnum) {
		hi_os_printf(" %d  %s \n", __LINE__, __func__);
		hi_omci_lib_systrace(HI_RET_OUTRANG, ui_seqnum, g_st_mib_data.ui_totalnum, 0, 0);
		return HI_RET_OUTRANG;
	}
	/*if last data*/
	if ((HI_NULL != pst_list_pos) &&
	    (hi_list_is_last(pst_list_pos, &g_st_mib_data.st_listhead))) {
		hi_os_printf(" %d  %s \n", __LINE__, __func__);
		hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return (hi_uint32)HI_RET_OMCI_SQL_NODATA_E;
	}

	/*first data report*/
	if (HI_NULL == pst_list_pos) {
		pst_list_pos = hi_list_getnext(&g_st_mib_data.st_listhead);
	} else {
		pst_list_pos = hi_list_getnext(pst_list_pos);
	}
	g_st_mib_data.pst_lastlist = pst_list_pos;

	/*report current data*/
	if (HI_NULL != pst_list_pos) {
		pst_mib = hi_list_entry(pst_list_pos, hi_omci_mibupload_seg_s, st_listhead);
	}
	if (HI_NULL == pst_mib) {
		hi_os_printf(" %d  %s \n", __LINE__, __func__);
		hi_omci_lib_systrace(HI_RET_OMCI_SQL_NODATA_E, ui_seqnum, 0, 0, 0);
		return (hi_uint32)HI_RET_OMCI_SQL_NODATA_E;
	}
	/*copy data*/
	HI_OS_MEMCPY_S(pv_outdata, pst_mib->ui_size, pst_mib->uc_text, pst_mib->ui_size);
	*blk_size = pst_mib->ui_size;
	if (ui_seqnum == (g_st_mib_data.ui_totalnum - 1)) {
		hi_omci_sql_upload_free();
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_mib_upload_dump
 功能描述  : 打印mibupload信息
 输入参数  : hi_uint32 ui_cmdtype
             hi_void *pv_data
             hi_uint32 ui_inlen
             hi_uint32 *pui_outlen
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_upload_dump(hi_uint32 ui_cmdtype, hi_void *pv_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_uint32     ui_indx = 0;
	hi_list_head *pst_msg_list    = HI_NULL;
	hi_list_head *pst_list_next   = HI_NULL;
	hi_omci_mibupload_seg_s *pst_mib = HI_NULL;

	hi_os_printf("\r\n===============================================");
	hi_os_printf("\r\n[mib-upload totalnum=%u blocksize=%u]",
		     g_st_mib_data.ui_totalnum, g_st_mib_data.ui_blocksize);
	hi_os_printf("\r\n-----------------------------------------------");
	hi_list_for_each_safe(pst_msg_list, pst_list_next, &g_st_mib_data.st_listhead) {
		pst_mib = hi_list_entry(pst_msg_list, hi_omci_mibupload_seg_s, st_listhead);
		if (HI_NULL != pst_mib) {
			hi_os_printf("\r\n[block=%u head=%u size=%u ]",
				     ui_indx++, HI_OMCI_MIB_UPLOAD_OFFSET, pst_mib->ui_size);
			HI_PRINT_MEM(pst_mib->ui_size, pst_mib->uc_text);
		}
	}
	hi_os_printf("\r\n===============================================\r\n");

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_upload_init
 功能描述  : mibupload初始化
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_upload_init(uint32_t mode)
{
	hi_omci_sql_upload_free();
	HI_OS_MEMSET_S(&g_st_mib_data, sizeof(hi_omci_mibupload_s), 0, sizeof(hi_omci_mibupload_s));
	hi_list_init_head(&g_st_mib_data.st_listhead);
	if (mode == HI_OMCI_STD_MODE)
		g_st_mib_data.ui_blocksize = HI_OMCI_STD_ATTR_MAXSIZE;
	else
		g_st_mib_data.ui_blocksize = HI_OMCI_EXT_ATTR_MAXSIZE;

	hi_omci_sql_upload(mode);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_void hi_omci_sql_upload_data_init(hi_void)
{
	hi_list_init_head(&g_st_mib_data.st_listhead);
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_upload_exit
 功能描述  : mibupload清除内存
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_upload_exit(hi_void)
{
	hi_omci_sql_upload_free();

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
