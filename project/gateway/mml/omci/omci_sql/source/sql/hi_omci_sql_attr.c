/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_sql_attr.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_11_04
  功能描述   : 对attrtable操作
******************************************************************************/

#include "hi_omci_lib.h"


#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

static hi_char8 *g_pc_errmsg = HI_NULL;

/*****************************************************************************
 函 数 名  : hi_omci_sql_attr_check
 功能描述  : 属性检查
 输入参数  : hi_omci_mib_attrlist_s * pst_attrlist
             hi_uchar8 uc_indx
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_attr_check(hi_omci_mib_attrlist_s *pst_attrlist, hi_uchar8 uc_indx)
{
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	pst_attr = &pst_attrlist->st_attr[uc_indx];
	if ((pst_attr->uc_opt & pst_attrlist->uc_opt)
	    && (pst_attrlist->us_mask & (HI_ATT_MASK_ONE >> uc_indx))) {
		hi_omci_lib_systrace(HI_RET_SUCC, pst_attrlist->us_mask, uc_indx, 0, 0);
		return HI_RET_SUCC;
	}

	//hi_omci_lib_systrace( HI_RET_OMCI_SQL_NORIGHT_E, pst_attrlist->us_mask, uc_indx, 0, 0);
	return (hi_uint32)HI_RET_OMCI_SQL_NORIGHT_E;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_attr_encode
 功能描述  : 根据数据库属性定义解析接收到的OMCI属性内容,输出到属性列表
 输入参数  : hi_void *pv_data
             hi_omci_mib_attrlist_s * pst_attrlist
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_sql_attr_encode(hi_void *pv_data, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uchar8 *puc_pos = pv_data;
	hi_uint32 ui_cnt, ui_ret;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	for (ui_cnt = 0; ui_cnt < pst_attrlist->uc_num; ui_cnt++) {
		ui_ret = hi_omci_sql_attr_check(pst_attrlist, ui_cnt);
		if (HI_RET_SUCC != ui_ret) {
			continue;
		}
		pst_attr = &pst_attrlist->st_attr[ui_cnt];
		switch (pst_attr->uc_type) {
		case HI_OMCI_SQL_DATA_UCHAR_E :
			*(hi_uchar8 *)puc_pos = *(hi_uchar8 *)pst_attr->puc_data;
			puc_pos += sizeof(hi_uchar8);
			break;
		case HI_OMCI_SQL_DATA_USHORT_E :
			*(hi_ushort16 *)puc_pos = *(hi_ushort16 *)pst_attr->puc_data;
			puc_pos += sizeof(hi_ushort16);
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			*(hi_uint32 *)puc_pos = *(hi_uint32 *)pst_attr->puc_data;
			puc_pos += sizeof(hi_uint32);
			break;
		case HI_OMCI_SQL_DATA_BINARY_E :
		case HI_OMCI_SQL_DATA_TEXT_E :
			HI_OS_MEMCPY_S(puc_pos, pst_attr->uc_size, pst_attr->puc_data, pst_attr->uc_size);
			puc_pos += pst_attr->uc_size;
			break;
		default:
			break;
		}
	}

	hi_omci_lib_systrace(HI_RET_SUCC, ui_cnt, 0, 0, 0);
	return;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_attr_decode
 功能描述  : 解码网络协议
 输入参数  : hi_void *pv_data
             hi_omci_mib_attrlist_s *pst_attrlist
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_sql_attr_decode(hi_void *pv_data, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uchar8 *puc_pos = pv_data;
	hi_uint32 ui_cnt, ui_ret;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	for (ui_cnt = 0; ui_cnt < pst_attrlist->uc_num; ui_cnt++) {
		ui_ret = hi_omci_sql_attr_check(pst_attrlist, ui_cnt);
		if (HI_RET_SUCC != ui_ret) {
			continue;
		}
		pst_attr = &pst_attrlist->st_attr[ui_cnt];
		switch (pst_attr->uc_type) {
		case HI_OMCI_SQL_DATA_UCHAR_E :
			pst_attr->puc_data = puc_pos;
			puc_pos += sizeof(hi_uchar8);
			break;
		case HI_OMCI_SQL_DATA_USHORT_E :
			pst_attr->puc_data = puc_pos;
			puc_pos += sizeof(hi_ushort16);
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			pst_attr->puc_data = puc_pos;
			puc_pos += sizeof(hi_uint32);
			break;
		case HI_OMCI_SQL_DATA_BINARY_E :
		case HI_OMCI_SQL_DATA_TEXT_E :
		case HI_OMCI_SQL_DATA_TABLEATTR_E:
			pst_attr->puc_data = puc_pos;
			puc_pos += pst_attr->uc_size;
			break;
		default:
			break;
		}
	}

	hi_omci_lib_systrace(HI_RET_SUCC, ui_cnt, 0, 0, 0);
	return;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_attr_encodehton
 功能描述  : 根据数据库属性定义解析接收到的OMCI属性内容,输出到属性列表
 输入参数  : hi_omci_mib_attrlist_s * pst_attrlist
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_sql_attr_encodehton(hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_cnt, ui_ret;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	for (ui_cnt = 0; ui_cnt < pst_attrlist->uc_num; ui_cnt++) {
		pst_attr = &pst_attrlist->st_attr[ui_cnt];

		ui_ret = hi_omci_sql_attr_check(pst_attrlist, ui_cnt);
		if ((HI_RET_SUCC != ui_ret) || (HI_NULL == pst_attr->puc_data)) {
			continue;
		}
		switch (pst_attr->uc_type) {
		case HI_OMCI_SQL_DATA_USHORT_E :
			*(hi_ushort16 *)pst_attr->puc_data = htons(*(hi_ushort16 *)pst_attr->puc_data);
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			*(hi_uint32 *)pst_attr->puc_data = htonl(*(hi_uint32 *)pst_attr->puc_data);
			break;
		case HI_OMCI_SQL_DATA_UCHAR_E :
		case HI_OMCI_SQL_DATA_BINARY_E :
		case HI_OMCI_SQL_DATA_TEXT_E :
		case HI_OMCI_SQL_DATA_TABLEATTR_E:
		default:
			break;
		}
	}

	hi_omci_lib_systrace(HI_RET_SUCC, ui_cnt, 0, 0, 0);
	return;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_attr_decodentoh
 功能描述  : 解码网络协议
 输入参数  : hi_void *pv_data
             hi_omci_mib_attrlist_s *pst_attrlist
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_sql_attr_decodentoh(hi_void *pv_data, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uchar8 *puc_pos = pv_data;
	hi_uint32 ui_cnt, ui_ret;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	for (ui_cnt = 0; ui_cnt < pst_attrlist->uc_num; ui_cnt++) {
		ui_ret = hi_omci_sql_attr_check(pst_attrlist, ui_cnt);
		if (HI_RET_SUCC != ui_ret) {
			continue;
		}
		pst_attr = &pst_attrlist->st_attr[ui_cnt];
		switch (pst_attr->uc_type) {
		case HI_OMCI_SQL_DATA_UCHAR_E :
			pst_attr->puc_data = puc_pos;
			puc_pos += sizeof(hi_uchar8);
			break;
		case HI_OMCI_SQL_DATA_USHORT_E :
			pst_attr->puc_data = puc_pos;
			*(hi_ushort16 *)pst_attr->puc_data = ntohs(*(hi_ushort16 *)puc_pos);
			puc_pos += sizeof(hi_ushort16);
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			pst_attr->puc_data = puc_pos;
			*(hi_uint32 *)pst_attr->puc_data = ntohl(*(hi_uint32 *)puc_pos);
			puc_pos += sizeof(hi_uint32);
			break;
		case HI_OMCI_SQL_DATA_BINARY_E :
		case HI_OMCI_SQL_DATA_TEXT_E :
		case HI_OMCI_SQL_DATA_TABLEATTR_E:
			pst_attr->puc_data = puc_pos;
			puc_pos += pst_attr->uc_size;
			break;
		default:
			break;
		}
	}

	hi_omci_lib_systrace(HI_RET_SUCC, ui_cnt, 0, 0, 0);
	return ;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_attr_dump
 功能描述  : 打印当前meid属性内容
 输入参数  : hi_omci_mib_attrlist_s * pst_attrlist
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_void hi_omci_sql_attr_dump(hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_indx;
	hi_omci_mib_attr_s *pst_mib;

	hi_os_printf("\r\n\r\n\r\n===============================================");
	hi_os_printf("\r\n[meid=%04u instid=%04u attrnum=%2u meopt=%2u mask=%8x mesize=%4u ]",
		     pst_attrlist->us_meid, pst_attrlist->us_instid, pst_attrlist->uc_num,
		     pst_attrlist->uc_opt, pst_attrlist->us_mask, pst_attrlist->us_totalsize);
	hi_os_printf("\r\n-----------------------------------------------");
	for (ui_indx = 0; ui_indx < pst_attrlist->uc_num; ui_indx++) {
		pst_mib = &pst_attrlist->st_attr[ui_indx];
		hi_os_printf("\r\n[attrid=%04u attrname=%s]",
			     ui_indx + 1, pst_mib->uc_desc);
		switch (pst_mib->uc_type) {
		case HI_OMCI_SQL_DATA_UCHAR_E :
			hi_os_printf("\r\n[attrsize=%04u attrtype=uchar  attropt=%2u attravc=%2u attrversion=%4u]",
				     pst_mib->uc_size, pst_mib->uc_opt,
				     pst_mib->uc_avc, pst_mib->ui_version);
			if (HI_NULL != pst_mib->puc_data) {
				hi_os_printf("\r\n[attrdata=%08x]",
					     *(hi_uchar8 *)pst_mib->puc_data);
			}
			break;
		case HI_OMCI_SQL_DATA_USHORT_E :
			hi_os_printf("\r\n[attrsize=%04u attrtype=ushort attropt=%2u attravc=%2u attrversion=%4u]",
				     pst_mib->uc_size, pst_mib->uc_opt,
				     pst_mib->uc_avc, pst_mib->ui_version);
			if (HI_NULL != pst_mib->puc_data) {
				hi_os_printf("\r\n[attrdata=%08x]",
					     ntohs(*(hi_ushort16 *)pst_mib->puc_data));
			}
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			hi_os_printf("\r\n[attrsize=%04u attrtype=uint   attropt=%2u attravc=%2u attrversion=%4u]",
				     pst_mib->uc_size, pst_mib->uc_opt,
				     pst_mib->uc_avc, pst_mib->ui_version);
			if (HI_NULL != pst_mib->puc_data) {
				hi_os_printf("\r\n[attrdata=%08x]",
					     ntohl(*(hi_uint32 *)pst_mib->puc_data));
			}
			break;
		case HI_OMCI_SQL_DATA_BINARY_E :
			hi_os_printf("\r\n[attrsize=%04u attrtype=binary attropt=%2u attravc=%2u attrversion=%4u]",
				     pst_mib->uc_size, pst_mib->uc_opt,
				     pst_mib->uc_avc, pst_mib->ui_version);
			if (HI_NULL != pst_mib->puc_data) {
				hi_os_printf("\r\n[attrdata=.]");
				HI_PRINT_MEM_UINT(pst_mib->uc_size, pst_mib->puc_data);
			}
			break;
		case HI_OMCI_SQL_DATA_TEXT_E :
			hi_os_printf("\r\n[attrsize=%04u attrtype=text   attropt=%2u attravc=%2u attrversion=%4u]",
				     pst_mib->uc_size, pst_mib->uc_opt,
				     pst_mib->uc_avc, pst_mib->ui_version);
			if (HI_NULL != pst_mib->puc_data) {
				hi_os_printf("\r\n[attrdata=%s]",
					     pst_mib->puc_data);
			}
			break;
		case HI_OMCI_SQL_DATA_TABLEATTR_E:
			hi_os_printf("\r\n[attrsize=%04u attrtype=table   attropt=%2u attravc=%2u attrversion=%4u attrtablenum=%04u]",
				     pst_mib->uc_size, pst_mib->uc_opt,
				     pst_mib->uc_avc, pst_mib->ui_version, pst_mib->us_tabnum);
			if (HI_NULL != pst_mib->puc_data) {
				hi_os_printf("\r\n[attrdata=.]");
				HI_PRINT_MEM_UINT(((pst_mib->uc_size) * (pst_mib->us_tabnum)), pst_mib->puc_data);
			}
			break;

		default:
			break;

		}
	}

	hi_os_printf("\r\n===============================================\r\n");

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return ;
}
/*****************************************************************************
 函 数 名  : hi_omci_sql_attr_getlist
 功能描述  : 从属性数据库中取出该meid对应的所有属性定义内容
 输入参数  : hi_omci_mib_attrlist_s * pst_attrlist
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_attr_getlist(hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32   ui_ret;
	hi_int32    i_dbrow = 0;
	hi_int32    i_dbcolumn = 0;
	hi_int32    i_row = 0;
	hi_int32    i_column = 0;
	hi_char8  **ppc_result = 0;
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	hi_int32    len;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT * FROM attrtable WHERE meid=%u", pst_attrlist->us_meid);

	ui_ret = sqlite3_get_table(g_pst_mibdb, c_sql, &ppc_result, &i_dbrow, &i_dbcolumn, &g_pc_errmsg);
	if ((HI_RET_SUCC != ui_ret) || (0 == i_dbrow) || (HI_OMCI_MIB_MAXATTR_NUM < i_dbrow)) {
		if (g_pc_errmsg != NULL) {
			sqlite3_free(g_pc_errmsg);
			g_pc_errmsg = NULL;
		}
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), i_dbrow, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}
	pst_attrlist->uc_num = i_dbrow;
	pst_attrlist->us_totalsize = 0;
	for (i_row = 1; i_row <= i_dbrow; i_row++) {
		pst_attr  = &pst_attrlist->st_attr[i_row - 1];
		i_column = i_row * i_dbcolumn;
		pst_attr->uc_size     = (hi_uchar8)hi_os_strtoul(ppc_result[i_column + HI_OMCI_SQL_ATTRTABLE_SIZE_E], 0, 10);
		pst_attr->uc_opt      = (hi_uchar8)hi_os_strtoul(ppc_result[i_column + HI_OMCI_SQL_ATTRTABLE_OPT_E], 0, 10);
		pst_attr->uc_avc      = (hi_uchar8)hi_os_strtoul(ppc_result[i_column + HI_OMCI_SQL_ATTRTABLE_AVC_E], 0, 10);
		pst_attr->us_tabnum   = (hi_ushort16)hi_os_strtoul(ppc_result[i_column + HI_OMCI_SQL_ATTRTABLE_TABNUM_E], 0, 10);
		pst_attr->uc_type     = (hi_uchar8)hi_os_strtoul(ppc_result[i_column + HI_OMCI_SQL_ATTRTABLE_TYPE_E], 0, 10);
		pst_attr->ui_version  = (hi_uint32)hi_os_strtoul(ppc_result[i_column + HI_OMCI_SQL_ATTRTABLE_VERSION_E], 0, 10);
		if (HI_NULL != ppc_result[i_column + HI_OMCI_SQL_ATTRTABLE_DESC_E]) {
			len = strlen(ppc_result[i_column + HI_OMCI_SQL_ATTRTABLE_DESC_E]);
			len = (len > sizeof(pst_attr->uc_desc)) ? sizeof(pst_attr->uc_desc) : len;
			HI_OS_MEMCPY_S(pst_attr->uc_desc, sizeof(pst_attr->uc_desc), ppc_result[i_column + HI_OMCI_SQL_ATTRTABLE_DESC_E], len);
			pst_attr->uc_desc[HI_OMCI_ATT_INFO_MAX - 1] = 0;
		}
		pst_attrlist->us_totalsize += (pst_attr->uc_size * pst_attr->us_tabnum);
	}

	sqlite3_free_table(ppc_result);
	hi_omci_lib_systrace(HI_RET_SUCC, i_dbrow, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_attr_tabnum_set
 功能描述  : 配置属性的table num
 输入参数  : hi_uint32 ui_meid,
             hi_uint32 ui_index,
             hi_uint32 tabnum
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_attr_tabnum_set(hi_uint32 ui_meid, hi_uint32 ui_index, hi_uint32 tabnum)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN];

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "UPDATE attrtable SET tabnum=%u WHERE meid=%u and pos=%u",
			 tabnum, ui_meid, ui_index);
	return hi_omci_sql_exec(c_sql);
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

