/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_sql_alarm.c
  Version    : 初稿
  Author     : owen
  Creation   : 2014-1-15
  Description: 访问SQL数据库ALARM表项接口
                instalarm:
                instid
                alarms bitmap
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include "hi_omci_lib.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
typedef struct {
	hi_ushort16 us_meid;
	hi_ushort16 us_instid;
	hi_ushort16 us_bitmap;
	hi_ushort16 us_resv;
} hi_omci_sql_alarm_data_s;

typedef struct {
	hi_omci_sql_alarm_data_s st_data;
	hi_list_head st_listhead;
} hi_omci_sql_alarm_list_s;

typedef struct {
	hi_uint32 ui_totalnum;
	hi_list_head *pst_lastlist;
	hi_list_head  st_listhead;
} hi_omci_sql_alarm_s;

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_char8 *g_pc_errmsg = HI_NULL;

/* 告警查询数据链表 */
static hi_omci_sql_alarm_s gst_alarm_upload;

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/
/******************************************************************************
 Function    : __omci_sql_alarm_checktable
 Description : 判断是否存在告警表
 Input Parm  : hi_ushort16 us_meid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_sql_alarm_checktable(hi_ushort16 us_meid)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT COUNT(*) as CNT FROM sqlite_master where type='table' and name='instalarm%u' ",
			 us_meid);

	return hi_omci_sql_countsql(c_sql);
}

/******************************************************************************
 Function    : __omci_sql_alarm_checkinst
 Description : 判断是否存在告警表项
 Input Parm  : hi_ushort16 us_meid,
               hi_ushort16 us_instid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_sql_alarm_checkinst(hi_ushort16 us_meid, hi_ushort16 us_instid)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT COUNT(*) as CNT FROM instalarm%u WHERE instid=%u",
			 us_meid, us_instid);

	return hi_omci_sql_countsql(c_sql);
}

/******************************************************************************
 Function    : __omci_sql_alarm_inst_create
 Description : 创建告警表
 Input Parm  : hi_ushort16 us_meid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_sql_alarm_inst_create(hi_ushort16 us_meid)
{
	hi_char8 uc_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;

	HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1,
			 "CREATE TABLE IF NOT EXISTS instalarm%u(instid,bitmap)", us_meid);

	return hi_omci_sql_exec(uc_sql);
}

/******************************************************************************
 Function    : __omci_sql_alarm_inst_insert
 Description : 创建告警表项
 Input Parm  : hi_uint32 ui_meid,
               hi_uint32 ui_instid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_sql_alarm_inst_insert(hi_uint32 ui_meid, hi_uint32 ui_instid)
{
	hi_char8 uc_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1,
			 "INSERT INTO instalarm%u(instid,bitmap)VALUES(%u,0)",
			 ui_meid, ui_instid);

	return hi_omci_sql_exec(uc_sql);
}

/******************************************************************************
 Function    : __omci_sql_alarm_upload_free
 Description : 释放告警上报链表
 Input Parm  : 无
 Output Parm : 无
 Return      : 无
******************************************************************************/
hi_void __omci_sql_alarm_upload_free(hi_void)
{
	hi_list_head *pst_list_pos = HI_NULL;
	hi_list_head *pst_list_next = HI_NULL;
	hi_omci_sql_alarm_list_s *pst_alarm = HI_NULL;

	hi_list_for_each_safe(pst_list_pos, pst_list_next, &gst_alarm_upload.st_listhead) {
		pst_alarm = (hi_omci_sql_alarm_list_s *)hi_list_entry(pst_list_pos, hi_omci_sql_alarm_list_s, st_listhead);
		if (HI_NULL != pst_alarm) {
			hi_list_del(&pst_alarm->st_listhead);
			hi_os_free(pst_alarm);
		}
	}

	gst_alarm_upload.pst_lastlist = HI_NULL;
	gst_alarm_upload.ui_totalnum = 0;

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return;
}

/******************************************************************************
 Function    : __omci_sql_alam_upload_inst
 Description : 查询所有告警
 Input Parm  : hi_uint32 ui_arc 是否上报有ARC标志的告警
               hi_ushort16 us_meid
 Output Parm : 无
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 __omci_sql_alam_upload_inst(hi_uint32 ui_arc, hi_ushort16 us_meid)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;
	hi_char8 **ppc_result = 0;
	hi_omci_sql_alarm_list_s *pst_alarm_upload = HI_NULL;
	hi_uint32 ui_column, ui_row, ui_instindex;
	hi_int32 i_ret;
	hi_ushort16 us_instid;
	hi_ushort16 us_bitmap;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT instid FROM instalarm%u", us_meid);
	i_ret = sqlite3_get_table(g_pst_mibdb, c_sql, &ppc_result, (hi_int32 *)&ui_row, (hi_int32 *)&ui_column, &g_pc_errmsg);
	if (HI_RET_SUCC != i_ret) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(i_ret), ui_column, ui_row, 0, 0);
		return HI_OMCI_SQL_RET(i_ret);
	}

	for (ui_instindex = 1; ui_instindex <= ui_row; ui_instindex++) {
		us_instid = hi_os_strtoul(ppc_result[ui_instindex], 0, 10);

		i_ret = hi_omci_sql_alarm_inst_get(us_meid, us_instid, &us_bitmap);
		if (HI_RET_SUCC != i_ret) {
			__omci_sql_alarm_upload_free();
			sqlite3_free_table(ppc_result);
			hi_omci_lib_systrace(HI_OMCI_SQL_RET(i_ret), ui_column, ui_row, 0, 0);
			return HI_OMCI_SQL_RET(i_ret);
		}

		if (0 == us_bitmap) {
			continue;
		}

		pst_alarm_upload = (hi_omci_sql_alarm_list_s *)hi_os_malloc(sizeof(hi_omci_sql_alarm_list_s));
		if (HI_NULL == pst_alarm_upload) {
			__omci_sql_alarm_upload_free();
			sqlite3_free_table(ppc_result);
			hi_omci_lib_systrace(HI_OMCI_SQL_RET((hi_uint32)HI_RET_MALLOC_FAIL), ui_column, ui_row, 0, 0);
			return HI_OMCI_SQL_RET((hi_int32)HI_RET_MALLOC_FAIL);
		}

		pst_alarm_upload->st_data.us_meid = us_meid;
		pst_alarm_upload->st_data.us_instid = us_instid;
		pst_alarm_upload->st_data.us_bitmap = us_bitmap;
		hi_list_add_tail(&pst_alarm_upload->st_listhead, &gst_alarm_upload.st_listhead);

		gst_alarm_upload.ui_totalnum++;
	}

	sqlite3_free_table(ppc_result);
	hi_omci_lib_systrace(i_ret, 0, 0, 0, 0);
	return i_ret;
}

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_sql_alarm_inst_create
 Description : 创建告警表项
 Input Parm  : hi_ushort16 us_meid,
               hi_ushort16 us_instid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_sql_alarm_inst_create(hi_ushort16 us_meid, hi_ushort16 us_instid)
{
	hi_int32 i_num;
	hi_int32 i_ret;

	i_num = __omci_sql_alarm_checktable(us_meid);
	if (0 == i_num) {
		i_ret = __omci_sql_alarm_inst_create(us_meid);
		HI_OMCI_LIB_FAIL_RET(i_ret);
	}

	i_num = __omci_sql_alarm_checkinst(us_meid, us_instid);
	if (0 == i_num) {
		i_ret = __omci_sql_alarm_inst_insert(us_meid, us_instid);
		HI_OMCI_LIB_FAIL_RET(i_ret);
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_sql_alarm_inst_delete
 Description : 删除告警表项
 Input Parm  : hi_ushort16 us_meid,
               hi_ushort16 us_instid
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_sql_alarm_inst_delete(hi_ushort16 us_meid, hi_ushort16 us_instid)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "DELETE FROM instalarm%u WHERE instid=%u",
			 us_meid, us_instid);

	return hi_omci_sql_exec(c_sql);
}

/******************************************************************************
 Function    : hi_omci_sql_alarm_inst_set
 Description : 设置告警状态
 Input Parm  : hi_ushort16 us_meid,
               hi_ushort16 us_instid,
               hi_ushort16 us_bitmap
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_sql_alarm_inst_set(hi_ushort16 us_meid,
				    hi_ushort16 us_instid, hi_ushort16 us_bitmap)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "UPDATE instalarm%u SET bitmap=%u WHERE instid=%u",
			 us_meid, us_bitmap, us_instid);
	return hi_omci_sql_exec(c_sql);
}

/******************************************************************************
 Function    : hi_omci_sql_alarm_inst_get
 Description : 查询告警状态
 Input Parm  : hi_ushort16 us_meid,
               hi_ushort16 us_instid
 Output Parm : hi_ushort16 *pus_bitmap
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_sql_alarm_inst_get(hi_ushort16 us_meid,
				    hi_ushort16 us_instid, hi_ushort16 *pus_bitmap)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	hi_uint32 ui_data = 0;
	hi_int32 i_ret = 0;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT * FROM instalarm%u WHERE instid=%u",
			 us_meid, us_instid);

	i_ret = hi_omci_sql_getcolumn(c_sql, 1, HI_OMCI_SQL_DB_INT32_E, (hi_uchar8 *)&ui_data, sizeof(hi_uint32));
	HI_OMCI_LIB_FAIL_RET(i_ret);

	*pus_bitmap = (hi_ushort16)ui_data;

	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : hi_omci_sql_alarm_upload
 Description : 查询所有告警
 Input Parm  : hi_uint32 ui_arc 是否上报有ARC标志的告警
 Output Parm : 无
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_sql_alarm_upload(hi_uint32 ui_arc)
{
	hi_char8 c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;
	hi_char8 **ppc_result = 0;
	hi_uint32 ui_column, ui_row, ui_meindex;
	hi_int32 i_ret;
	hi_ushort16 us_meid;

	gst_alarm_upload.ui_totalnum = 0;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT meid FROM %s", HI_OMCI_MIB_ALARMTABLE_NAME);
	i_ret = sqlite3_get_table(g_pst_mibdb, c_sql, &ppc_result, (hi_int32 *)&ui_row, (hi_int32 *)&ui_column, &g_pc_errmsg);
	if (HI_RET_SUCC != i_ret) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(i_ret), ui_column, ui_row, 0, 0);
		return HI_OMCI_SQL_RET(i_ret);
	}

	for (ui_meindex = 1; ui_meindex <= ui_row; ui_meindex++) {
		us_meid = hi_os_strtoul(ppc_result[ui_meindex], 0, 10);

		i_ret = __omci_sql_alam_upload_inst(ui_arc, us_meid);
		if (HI_RET_SUCC != i_ret) {
			hi_omci_lib_systrace(HI_OMCI_SQL_RET(i_ret), ui_column, ui_row, 0, 0);
			continue;
		}
	}

	sqlite3_free_table(ppc_result);
	hi_omci_lib_systrace(i_ret, 0, 0, 0, 0);
	return i_ret;
}

/******************************************************************************
 Function    : hi_omci_sql_alarm_upload_num_get
 Description : 获取上报告警数目
 Input Parm  : 无
 Output Parm : 无
 Return      : 上报告警数目
******************************************************************************/
hi_int32 hi_omci_sql_alarm_upload_num_get(hi_void)
{
	return gst_alarm_upload.ui_totalnum;
}

/******************************************************************************
 Function    : hi_omci_sql_alarm_upload_get
 Description : 获取上报告警实体
 Input Parm  : hi_uint32 ui_seqnum
 Output Parm : hi_void *pv_data
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_sql_alarm_upload_get(hi_void *pv_data, hi_uint32 ui_seqnum)
{
	hi_list_head *pst_alarm_list = gst_alarm_upload.pst_lastlist;
	hi_omci_sql_alarm_list_s *pst_alarm = HI_NULL;
	hi_omci_sql_alarm_data_s *pst_data = (hi_omci_sql_alarm_data_s *)pv_data;

	if (ui_seqnum >= gst_alarm_upload.ui_totalnum) {
		hi_omci_lib_systrace(HI_OMCI_SQL_RET((hi_uint32)HI_RET_INVALID_PARA), ui_seqnum, gst_alarm_upload.ui_totalnum, 0, 0);
		return HI_OMCI_SQL_RET((hi_uint32)HI_RET_INVALID_PARA);
	}

	/* 获取链表的下一个结点 */
	if (HI_NULL == pst_alarm_list) {
		pst_alarm_list = hi_list_getnext(&gst_alarm_upload.st_listhead);
	} else {
		pst_alarm_list = hi_list_getnext(pst_alarm_list);
	}

	/* 更新当前结点 */
	gst_alarm_upload.pst_lastlist = pst_alarm_list;

	if (HI_NULL != pst_alarm_list) {
		pst_alarm = hi_list_entry(pst_alarm_list, hi_omci_sql_alarm_list_s, st_listhead);
	}
	if (HI_NULL == pst_alarm) {
		hi_omci_lib_systrace(HI_RET_OMCI_SQL_NODATA_E, ui_seqnum, 0, 0, 0);
		return HI_RET_OMCI_SQL_NODATA_E;
	}

	pst_data->us_meid = htons(pst_alarm->st_data.us_meid);
	pst_data->us_instid = htons(pst_alarm->st_data.us_instid);
	pst_data->us_bitmap = htons(pst_alarm->st_data.us_bitmap);

	if (ui_seqnum == (gst_alarm_upload.ui_totalnum - 1)) {
		__omci_sql_alarm_upload_free();
	}

	hi_omci_lib_systrace(HI_RET_SUCC, ui_seqnum, gst_alarm_upload.ui_totalnum, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_sql_alarm_upload_init
 Description : 告警查询上报初始化
 Input Parm  : 无
 Output Parm : 无
 Return      : 无
******************************************************************************/
hi_void hi_omci_sql_alarm_upload_init(hi_void)
{
	HI_OS_MEMSET_S(&gst_alarm_upload, sizeof(gst_alarm_upload), 0, sizeof(gst_alarm_upload));
	hi_list_init_head(&gst_alarm_upload.st_listhead);
	hi_omci_init_alarm_seq();
	return;
}

/******************************************************************************
 Function    : hi_omci_sql_alarm_upload_init
 Description : 告警查询上报初始化
 Input Parm  : 无
 Output Parm : 无
 Return      : 无
******************************************************************************/
hi_void hi_omci_sql_alarm_upload_exit(hi_void)
{
	__omci_sql_alarm_upload_free();
	return;
}

