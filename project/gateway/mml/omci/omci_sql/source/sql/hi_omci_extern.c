/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_sql_extern.c
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2012_01_12
  功能描述   : omci sql 提供对外接口的文件
******************************************************************************/
#include "hi_omci_lib.h"
#include "hi_ipc.h"
#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */
static hi_char8 *g_pc_errmsg = HI_NULL;
/*****************************************************************************
 函 数 名  : hi_omci_ext_get_attrlist
 功能描述  : 根据meid获取实体属性信息
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
 输出参数  : hi_omci_mib_attrlist_s *pst_attrlist
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_get_attrlist(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;
	HI_OS_MEMSET_S(pst_attrlist, sizeof(hi_omci_mib_attrlist_s), 0, sizeof(hi_omci_mib_attrlist_s));

	/*get attribute list by meid */
	pst_attrlist->us_meid   = us_meid;
	pst_attrlist->us_instid = us_instid;
	pst_attrlist->uc_opt    = HI_OMCI_ATT_R_W_S_E;
	pst_attrlist->us_mask   = 0xFFFF;

	/*获取属性信息*/
	ui_ret = hi_omci_sql_attr_getlist(pst_attrlist);
	HI_OMCI_LIB_FAIL_RET(ui_ret);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_attr_decode
 功能描述  : 将传入的实例内容解析到pst_attrlist中
 输入参数  : hi_void *pv_data
 输出参数  : hi_omci_mib_attrlist_s *pst_attrlist
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_ext_attr_decode(hi_uchar8 *puc_data, hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uchar8 *puc_pos = puc_data;
	hi_uint32 ui_cnt;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	for (ui_cnt = 0; ui_cnt < pst_attrlist->uc_num; ui_cnt++) {
		pst_attr = &pst_attrlist->st_attr[ui_cnt];
		switch (pst_attr->uc_type) {
		case HI_OMCI_SQL_DATA_UCHAR_E :
			pst_attr->puc_data = puc_pos;
			puc_pos += sizeof(hi_uchar8);
			break;
		case HI_OMCI_SQL_DATA_USHORT_E :
			pst_attr->puc_data = puc_pos;
			*(hi_ushort16 *)pst_attr->puc_data = *(hi_ushort16 *)puc_pos;
			puc_pos += sizeof(hi_ushort16);
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			pst_attr->puc_data = puc_pos;
			*(hi_uint32 *)pst_attr->puc_data = *(hi_uint32 *)puc_pos;
			puc_pos += sizeof(hi_uint32);
			break;
		case HI_OMCI_SQL_DATA_BINARY_E :
		case HI_OMCI_SQL_DATA_TEXT_E :
		case HI_OMCI_SQL_DATA_TABLEATTR_E :
			pst_attr->puc_data = puc_pos;
			puc_pos += pst_attr->uc_size;
			break;
		default:
			break;
		}
	}

	hi_omci_lib_systrace(HI_RET_SUCC, ui_cnt, 0, 0, 0);
	return ;
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_create_inst
 功能描述  : 根据meid instid在数据库中创建实例
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
 输出参数  : hi_void *pv_data
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_create_inst(hi_ushort16 us_meid, hi_ushort16 us_instid)
{
	hi_uint32 ui_ret = HI_RET_SUCC;
	hi_omci_mib_attrlist_s st_attrlist;

	HI_OS_MEMSET_S(&st_attrlist, sizeof(hi_omci_mib_attrlist_s), 0, sizeof(hi_omci_mib_attrlist_s));
	/*实例不存在,创建一个实例*/
	st_attrlist.us_meid   = us_meid;
	st_attrlist.us_instid = us_instid;

	st_attrlist.uc_opt    = HI_OMCI_ATT_R_W_S_E;
	st_attrlist.us_mask   = 0xFFFF;

	ui_ret = hi_omci_sql_attr_getlist(&st_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_os_printf("LINE = %d, FUNC = %s, ui_ret =%d,\n",  __LINE__, __func__, ui_ret);
		hi_omci_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_omci_sql_inst_checktable(us_meid);
	if (0 == ui_ret) {
		ui_ret = hi_omci_sql_inst_create(us_meid);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_printf("LINE = %d, FUNC = %s, ui_ret =%d,\n",  __LINE__, __func__, ui_ret);
			hi_omci_lib_systrace(HI_OMCI_PRO_ERR_DEV_BUSY_E, 0, 0, ui_ret, 0);
			return HI_OMCI_PRO_ERR_DEV_BUSY_E;
		}
	}


	/*check if instance repeat*/
	ui_ret = hi_omci_sql_inst_checkinst(us_meid, us_instid);
	if (0 == ui_ret) {
		st_attrlist.uc_opt = HI_OMCI_ATT_S_E;
		ui_ret = hi_omci_sql_inst_insert(&st_attrlist);
		if (HI_RET_SUCC != ui_ret) {
			hi_os_printf("LINE = %d, FUNC = %s, ui_ret =%d,\n",  __LINE__, __func__, ui_ret);
			hi_omci_systrace(HI_OMCI_PRO_ERR_DEV_BUSY_E, 0, 0, 0, 0);
			return HI_OMCI_PRO_ERR_DEV_BUSY_E;
		}
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_uint32 hi_omci_ext_del_inst(hi_ushort16 us_meid, hi_ushort16 us_instid)
{
	return hi_omci_sql_inst_del(us_meid, us_instid);
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_get_inst
 功能描述  : 根据meid instid从数据库获取实例
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
 输出参数  : hi_void *pv_data
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_get_inst(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_void *pv_data)
{
	hi_uint32 ui_ret;
	hi_omci_mib_attrlist_s st_attrlist;

	ui_ret = hi_omci_ext_get_attrlist(us_meid, us_instid, &st_attrlist);
	if ((HI_RET_SUCC != ui_ret)) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	/*获取实例*/
	ui_ret = hi_omci_sql_inst_get(&st_attrlist, (hi_uchar8 *)((hi_uchar8 *)pv_data + sizeof(hi_omci_me_msg_head_s)));
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_set_inst
 功能描述  : 根据meid instid将pv_data的值设置到数据库实例中
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_set_inst(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_void *pv_data)
{
	/*check parameter*/
	hi_uint32 ui_ret;
	hi_omci_mib_attrlist_s st_attrlist;

	ui_ret = hi_omci_ext_get_attrlist(us_meid, us_instid, &st_attrlist);
	if ((HI_RET_SUCC != ui_ret)) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	/*根据meid从pv_data中解析出数据*/
	hi_omci_ext_attr_decode((hi_uchar8 *)((hi_uchar8 *)pv_data + sizeof(hi_omci_me_msg_head_s)), &st_attrlist);

	/*根据解析出来的数据，更新数据库*/
	ui_ret = hi_omci_sql_inst_set(&st_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_set_tabnum
 功能描述  : 配置实体属性的表项数目
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_index
             hi_uint32 ui_tabnum
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_set_tabnum(hi_ushort16 us_meid, hi_ushort16 us_index, hi_uint32 ui_tabnum)
{
	return hi_omci_sql_attr_tabnum_set(us_meid, us_index, ui_tabnum);
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_get_tabnum
 功能描述  : 根据实体id，表属性索引，获取表项数目
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_index
             hi_uint32 *pui_tabnum
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_get_tabnum(hi_ushort16 us_meid, hi_ushort16 us_index, hi_uint32 *pui_tabnum)
{
	hi_uint32 ui_ret;
	hi_omci_mib_attrlist_s  st_attrlist;

	HI_OS_MEMSET_S(&st_attrlist, sizeof(hi_omci_mib_attrlist_s), 0, sizeof(hi_omci_mib_attrlist_s));
	ui_ret = hi_omci_ext_get_attrlist(us_meid, 0, &st_attrlist);
	if (ui_ret != HI_RET_SUCC) {
		return HI_OMCI_PRO_ERR_PROCESS_ERR_E;
	}

	*pui_tabnum = st_attrlist.st_attr[us_index - 1].us_tabnum;

	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_get_instnum
 功能描述  : get inst count
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_get_instnum(hi_ushort16 us_meid, hi_ushort16 us_instid)
{
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	hi_uint32 ui_instnum = 0;
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "SELECT COUNT(*) as CNT FROM insttable%u WHERE instid=%u",
			 us_meid, us_instid);
	ui_instnum = (hi_ushort16)hi_omci_sql_countsql(c_sql);

	return ui_instnum;
}

hi_uint32 hi_omci_ext_get_instbyme(hi_ushort16 us_meid)
{
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	hi_uint32 ui_instnum = 0;
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "SELECT COUNT(*) as CNT FROM insttable%u", us_meid);
	ui_instnum = (hi_ushort16)hi_omci_sql_countsql(c_sql);

	return ui_instnum;
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_get_install
 功能描述  : 获取ME的所有实体ID
 输入参数  : hi_ushort16 us_meid,   实体ID
             hi_uint32 ui_inlen,    输出空间长度
 输出参数  : hi_uint32 *pui_data,   输出的实例ID
             hi_uint32 *pui_outlen  输出的实例个数
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_get_install(hi_ushort16 us_meid,
				  hi_uint32 *pui_data, hi_uint32 ui_inlen, hi_uint32 *pui_outlen)
{
	hi_char8  c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	hi_uint32 ui_instnum = 0;
	hi_uint32 ui_index;
	hi_uint32 *pui_instid = (hi_uint32 *)pui_data;
	hi_uint32 ui_ret;
	hi_uint32   ui_column, ui_row;
	hi_char8    **ppc_result = 0;

	ui_instnum = hi_omci_ext_get_instbyme(us_meid);
	if (ui_instnum > ui_inlen) {
		ui_instnum = ui_inlen;
	}

	*pui_outlen = ui_instnum;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "SELECT instid FROM insttable%u", us_meid);
	ui_ret = sqlite3_get_table(g_pst_mibdb, c_sql, &ppc_result, (hi_int32 *)&ui_row, (hi_int32 *)&ui_column, &g_pc_errmsg);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_column, ui_row, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}
	ui_row = ui_instnum > ui_row ? ui_row : ui_instnum;
	for (ui_index = 1; ui_index <= ui_row; ui_index++, pui_instid++) {
		*pui_instid = hi_os_strtoul(ppc_result[ui_index], 0, 10);
	}

	sqlite3_free_table(ppc_result);

	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_get_attr
 功能描述  : 根据meid instid以及属性的index从数据库获取属性值
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_ushort16 us_index
             hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_get_attr(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_ushort16 us_index, hi_void *pv_data)
{

	hi_uint32 ui_ret;
	hi_omci_mib_attrlist_s st_attrlist;
	hi_omci_mib_attr_s *pst_attr;
	/*check parameter*/
	ui_ret = hi_omci_ext_get_attrlist(us_meid, us_instid, &st_attrlist);
	if ((HI_RET_SUCC != ui_ret)) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	pst_attr = &st_attrlist.st_attr[us_index - 1];
	switch (pst_attr->uc_type) {
	case HI_OMCI_SQL_DATA_UCHAR_E :
		ui_ret = hi_omci_sql_inst_getcoluchar8(us_meid, us_instid, us_index, (hi_uchar8 *)pv_data);
		break;
	case HI_OMCI_SQL_DATA_USHORT_E :

		ui_ret = hi_omci_sql_inst_getcolshort16(us_meid, us_instid, us_index, (hi_ushort16 *)pv_data);
		break;
	case HI_OMCI_SQL_DATA_UINT_E :
		ui_ret = hi_omci_sql_inst_getcolint32(us_meid, us_instid, us_index, (hi_uint32 *)pv_data);
		break;
	case HI_OMCI_SQL_DATA_BINARY_E :
		ui_ret = hi_omci_sql_inst_getcolblob(us_meid, us_instid, us_index, pv_data, pst_attr->uc_size);
		break;
	case HI_OMCI_SQL_DATA_TEXT_E :
		ui_ret = hi_omci_sql_inst_getcoltext(us_meid, us_instid, us_index, pv_data, pst_attr->uc_size);
		break;
	case HI_OMCI_SQL_DATA_TABLEATTR_E :
		ui_ret = hi_omci_sql_inst_getcolblob(us_meid, us_instid, us_index, pv_data, pst_attr->uc_size * pst_attr->us_tabnum);
		break;
	default:
		break;
	}

	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_set_attr
 功能描述  : 根据meid instid以及属性的index设置属性值
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_ushort16 us_index
             hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_set_attr(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_ushort16 us_index, hi_void *pv_data)
{
	hi_uint32 ui_ret;
	hi_omci_mib_attrlist_s st_attrlist;
	hi_omci_mib_attr_s *pst_attr;

	/*check parameter*/
	ui_ret = hi_omci_ext_get_attrlist(us_meid, us_instid, &st_attrlist);
	if ((HI_RET_SUCC != ui_ret)) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	pst_attr = &st_attrlist.st_attr[us_index - 1];
	switch (pst_attr->uc_type) {
	case HI_OMCI_SQL_DATA_UCHAR_E :
		ui_ret = hi_omci_sql_inst_setcoluchar8(us_meid, us_instid, us_index, *(hi_uchar8 *)pv_data);
		break;
	case HI_OMCI_SQL_DATA_USHORT_E :
		ui_ret = hi_omci_sql_inst_setcolshort16(us_meid, us_instid, us_index, *(hi_ushort16 *)pv_data);
		break;
	case HI_OMCI_SQL_DATA_UINT_E :
		ui_ret = hi_omci_sql_inst_setcolint32(us_meid, us_instid, us_index, *(hi_uint32 *)pv_data);
		break;
	case HI_OMCI_SQL_DATA_BINARY_E :
		ui_ret = hi_omci_sql_inst_setcolblob(us_meid, us_instid, us_index, pv_data, pst_attr->uc_size);
		break;
	case HI_OMCI_SQL_DATA_TEXT_E :
		ui_ret = hi_omci_sql_inst_setcoltext(us_meid, us_instid, us_index, pv_data, pst_attr->uc_size);
		break;
	case HI_OMCI_SQL_DATA_TABLEATTR_E :
		ui_ret = hi_omci_sql_inst_setcolblob(us_meid, us_instid, us_index, pv_data, (pst_attr->uc_size * pst_attr->us_tabnum));
		break;
	default:
		break;
	}

	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_get_instid_byattr
 功能描述  : 根据实体id 以及某个属性的值，获取实例id
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_index
             hi_void *pv_attr
             hi_ushort16 *ps_instid
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_get_instid_byattr(hi_ushort16 us_meid, hi_ushort16 us_index, hi_void *pv_attr,
					hi_ushort16 *ps_instid)
{
	hi_uint32 ui_ret;
	hi_char8     uc_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	sqlite3_stmt *pst_mibstmt = 0;
	hi_omci_mib_attrlist_s st_attrlist;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	/*check parameter*/

	/*获取属性信息*/
	st_attrlist.us_meid = us_meid;
	ui_ret = hi_omci_sql_attr_getlist(&st_attrlist);
	HI_OMCI_LIB_FAIL_RET(ui_ret);

	HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1, "SELECT instid FROM insttable%u WHERE attr%u=?", us_meid,
			 us_index);

	ui_ret = sqlite3_prepare(g_pst_mibdb, uc_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_ret, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	pst_attr  = &st_attrlist.st_attr[us_index - 1];
	switch (pst_attr->uc_type) {
	case HI_OMCI_SQL_DATA_UCHAR_E :
		sqlite3_bind_int(pst_mibstmt, 1, *(hi_uchar8 *)pv_attr);
		break;
	case HI_OMCI_SQL_DATA_USHORT_E :
		sqlite3_bind_int(pst_mibstmt, 1, *(hi_ushort16 *)pv_attr);
		break;
	case HI_OMCI_SQL_DATA_UINT_E :
		sqlite3_bind_int(pst_mibstmt, 1, *(hi_uint32 *)pv_attr);
		break;
	case HI_OMCI_SQL_DATA_BINARY_E :
	case HI_OMCI_SQL_DATA_TEXT_E :
		sqlite3_bind_blob(pst_mibstmt, 1, pv_attr, pst_attr->uc_size, SQLITE_STATIC);
		break;
	case HI_OMCI_SQL_DATA_TABLEATTR_E :
		sqlite3_bind_blob(pst_mibstmt, 1, pv_attr, pst_attr->uc_size * pst_attr->us_tabnum, SQLITE_STATIC);
		break;
	default:
		break;
	}

	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_ROW != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	*ps_instid = (hi_ushort16)sqlite3_column_int(pst_mibstmt, 0);

	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_ext_get_instid_byattr2
 功能描述  : 根据实体id以及某两个属性的值，获取实例id
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_index1
             hi_void *pv_attr1
             hi_ushort16 us_index2
             hi_void *pv_attr2
             hi_ushort16 *ps_instid
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_ext_get_instid_byattr2(hi_ushort16 us_meid, hi_ushort16 us_index1, hi_void *pv_attr1,
		hi_ushort16 us_index2, hi_void *pv_attr2, hi_ushort16 *ps_instid)
{
	hi_uint32  ui_ret;
	hi_char8   uc_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	sqlite3_stmt *pst_mibstmt = 0;
	hi_omci_mib_attrlist_s st_attrlist;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	/*check parameter*/
	/*获取属性信息*/
	st_attrlist.us_meid = us_meid;
	ui_ret = hi_omci_sql_attr_getlist(&st_attrlist);
	HI_OMCI_LIB_FAIL_RET(ui_ret);

	HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1,
			 "SELECT instid FROM insttable%u WHERE attr%u=? and attr%u=?", us_meid, us_index1, us_index2);

	ui_ret = sqlite3_prepare(g_pst_mibdb, uc_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_ret, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}
	pst_attr  = &(st_attrlist.st_attr[us_index1 - 1]);
	switch (pst_attr->uc_type) {
	case HI_OMCI_SQL_DATA_UCHAR_E :
		sqlite3_bind_int(pst_mibstmt, 1, *(hi_uchar8 *)pv_attr1);
		break;
	case HI_OMCI_SQL_DATA_USHORT_E :
		sqlite3_bind_int(pst_mibstmt, 1, *(hi_ushort16 *)pv_attr1);
		break;
	case HI_OMCI_SQL_DATA_UINT_E :
		sqlite3_bind_int(pst_mibstmt, 1, *(hi_uint32 *)pv_attr1);
		break;
	case HI_OMCI_SQL_DATA_BINARY_E :
	case HI_OMCI_SQL_DATA_TEXT_E :
		sqlite3_bind_blob(pst_mibstmt, 1, pv_attr1, pst_attr->uc_size, SQLITE_STATIC);
		break;
	case HI_OMCI_SQL_DATA_TABLEATTR_E :
		sqlite3_bind_blob(pst_mibstmt, 1, pv_attr1, pst_attr->uc_size * pst_attr->us_tabnum, SQLITE_STATIC);
		break;
	default:
		break;
	}

	pst_attr  = &(st_attrlist.st_attr[us_index2 - 1]);
	switch (pst_attr->uc_type) {
	case HI_OMCI_SQL_DATA_UCHAR_E :
		sqlite3_bind_int(pst_mibstmt, 2, *(hi_uchar8 *)pv_attr2);
		break;
	case HI_OMCI_SQL_DATA_USHORT_E :
		sqlite3_bind_int(pst_mibstmt, 2, *(hi_ushort16 *)pv_attr2);
		break;
	case HI_OMCI_SQL_DATA_UINT_E :
		sqlite3_bind_int(pst_mibstmt, 2, *(hi_uint32 *)pv_attr2);
		break;
	case HI_OMCI_SQL_DATA_BINARY_E :
	case HI_OMCI_SQL_DATA_TEXT_E :
		sqlite3_bind_blob(pst_mibstmt, 2, pv_attr2, pst_attr->uc_size, SQLITE_STATIC);
		break;
	case HI_OMCI_SQL_DATA_TABLEATTR_E :
		sqlite3_bind_blob(pst_mibstmt, 2, pv_attr2, pst_attr->uc_size * pst_attr->us_tabnum, SQLITE_STATIC);
		break;
	default:
		break;
	}
	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_ROW != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	*ps_instid = (hi_ushort16)sqlite3_column_int(pst_mibstmt, 0);
	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

hi_uint32 hi_omci_ext_entity_set(hi_uint32 ui_cmdtype, hi_uint32 ui_callback, hi_uchar8 *puc_filename,
				 hi_uchar8 *puc_funcname)
{
	return hi_omci_sql_entity_set(ui_cmdtype, ui_callback, puc_filename, puc_funcname);
}

hi_uint32 hi_omci_ext_entity_del(hi_void *pv_callback)
{
	return hi_omci_sql_entity_del(pv_callback);
}

hi_uint32 hi_omci_ext_ploam_omci_send(hi_void *pv_data, hi_uint32 ui_inlen)
{
	struct hi_gpon_omci_msg msg;
	*(hi_ushort16 *)((hi_uchar8 *)pv_data + HI_OMCI_PROC_MSGSTCONTEXT_LEN + 2) = htons(40);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	msg.data = pv_data;
	msg.len = ui_inlen;
	return HI_IPC_CALL("hi_gpon_send_omci_msg", &msg);
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/*用于库文件加载*/
hi_int32 hi_omci_sql_init()
{
	return HI_RET_SUCC;
}

hi_void hi_omci_sql_exit()
{
	return;
}
