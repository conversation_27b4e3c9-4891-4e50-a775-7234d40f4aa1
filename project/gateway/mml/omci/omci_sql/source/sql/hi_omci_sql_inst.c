/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_sql_entity.c
  版 本 号   : 初稿
  作    者   : l00183967
  生成日期   : D2011_11_04
  功能描述   : 对insttable操作
******************************************************************************/

#include "hi_omci_lib.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

static hi_char8 *g_pc_errmsg = HI_NULL;

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_getcount
 功能描述  : 获取实体实例个数
 输入参数  : hi_ushort16 us_meid
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_getcount(hi_ushort16 us_meid)
{
	hi_char8    c_tablename[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_tablename, sizeof(c_tablename), sizeof(c_tablename) - 1, "insttable%u", us_meid);

	hi_omci_lib_systrace(HI_RET_SUCC, us_meid, 0, 0, 0);
	return hi_omci_sql_counttable(c_tablename);
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_insert
 功能描述  : 插入管理实体实例
 输入参数  : hi_omci_mib_attrlist_s  *pst_attrlist
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_insert(hi_omci_mib_attrlist_s  *pst_attrlist)
{
	hi_uint32    ui_ret, ui_cnt;
	hi_char8     uc_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	sqlite3_stmt *pst_mibstmt = 0;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1,
			 "INSERT INTO insttable%u"
			 "(instid,attr1,attr2,attr3,attr4,attr5,attr6,attr7,attr8,attr9,attr10,attr11,attr12,attr13,attr14,attr15,attr16)"
			 "VALUES(%u,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)",
			 pst_attrlist->us_meid, pst_attrlist->us_instid);
	ui_ret = sqlite3_prepare(g_pst_mibdb, uc_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_ret, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	for (ui_cnt = 0; ui_cnt < pst_attrlist->uc_num; ui_cnt++) {
		ui_ret = hi_omci_sql_attr_check(pst_attrlist, ui_cnt);
		if (HI_RET_SUCC != ui_ret) {
			continue;
		}
		pst_attr  = &pst_attrlist->st_attr[ui_cnt];
		switch (pst_attr->uc_type) {
		case HI_OMCI_SQL_DATA_UCHAR_E :
			sqlite3_bind_int(pst_mibstmt, ui_cnt + 1, *(hi_uchar8 *)pst_attr->puc_data);
			break;
		case HI_OMCI_SQL_DATA_USHORT_E :
			sqlite3_bind_int(pst_mibstmt, ui_cnt + 1, *(hi_ushort16 *)pst_attr->puc_data);
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			sqlite3_bind_int(pst_mibstmt, ui_cnt + 1, *(hi_uint32 *)pst_attr->puc_data);
			break;
		case HI_OMCI_SQL_DATA_BINARY_E :
		case HI_OMCI_SQL_DATA_TEXT_E :
			sqlite3_bind_blob(pst_mibstmt, ui_cnt + 1, pst_attr->puc_data, pst_attr->uc_size, SQLITE_STATIC);
			break;
		case HI_OMCI_SQL_DATA_TABLEATTR_E :
			sqlite3_bind_blob(pst_mibstmt, ui_cnt + 1, pst_attr->puc_data, (pst_attr->uc_size * pst_attr->us_tabnum),
					  SQLITE_STATIC);
			break;
		default:
			break;
		}
	}

	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_DONE != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_clear
 功能描述  : 清除属性链表上面的数据
 输入参数  : hi_omci_mib_attrlist_s *pst_attrlist
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_clear(hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32  ui_col;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	for (ui_col = 0; ui_col < pst_attrlist->uc_num; ui_col++) {
		pst_attr = &pst_attrlist->st_attr[ui_col];
		pst_attr->puc_data = HI_NULL;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_get
 功能描述  : 从实体实例表中获取一条实例数据
             将实例数据按照属性表定义放置在实例内存中
             内存给底层应用使用
 输入参数  : hi_omci_mib_attrlist_s *pst_attrlist
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_get(hi_omci_mib_attrlist_s *pst_attrlist, hi_uchar8 *puc_buf)
{
	hi_uint32      ui_ret, ui_col, ui_value;
	hi_uchar8     *puc_tmp, *puc_pos = puc_buf;
	hi_char8      c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	sqlite3_stmt  *pst_mibstmt = 0;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT * FROM insttable%u WHERE instid=%u",
			 pst_attrlist->us_meid, pst_attrlist->us_instid);

	ui_ret = sqlite3_prepare(g_pst_mibdb, c_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_ROW != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	for (ui_col = 0; ui_col < pst_attrlist->uc_num; ui_col++) {
		ui_ret = hi_omci_sql_attr_check(pst_attrlist, ui_col);
		if (HI_RET_SUCC != ui_ret) {
			continue;
		}
		pst_attr = &pst_attrlist->st_attr[ui_col];
		pst_attr->puc_data = puc_pos;
		HI_OS_MEMSET_S(puc_pos, pst_attr->uc_size, 0, pst_attr->uc_size);
		switch (pst_attr->uc_type) {
		case HI_OMCI_SQL_DATA_UCHAR_E :
			ui_value = sqlite3_column_int(pst_mibstmt, ui_col + HI_OMCI_SQL_INSTTABLE_ATTR1_E);
			*(hi_uchar8 *)puc_pos = (hi_uchar8)ui_value;
			puc_pos += sizeof(hi_uchar8);
			break;
		case HI_OMCI_SQL_DATA_USHORT_E :
			ui_value = sqlite3_column_int(pst_mibstmt, ui_col + HI_OMCI_SQL_INSTTABLE_ATTR1_E);
			*(hi_ushort16 *)puc_pos = (hi_ushort16)ui_value;
			puc_pos += sizeof(hi_ushort16);
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			ui_value = sqlite3_column_int(pst_mibstmt, ui_col + HI_OMCI_SQL_INSTTABLE_ATTR1_E);
			*(hi_uint32 *)puc_pos = (hi_uint32)ui_value;
			puc_pos += sizeof(hi_uint32);
			break;
		case HI_OMCI_SQL_DATA_BINARY_E :
			puc_tmp = (hi_uchar8 *)sqlite3_column_blob(pst_mibstmt, ui_col + HI_OMCI_SQL_INSTTABLE_ATTR1_E);
			if (HI_NULL != puc_tmp) {
				HI_OS_MEMCPY_S(puc_pos, pst_attr->uc_size, puc_tmp, pst_attr->uc_size);
			}
			puc_pos += pst_attr->uc_size;
			break;
		case HI_OMCI_SQL_DATA_TEXT_E :
			puc_tmp = (hi_uchar8 *)sqlite3_column_text(pst_mibstmt, ui_col + HI_OMCI_SQL_INSTTABLE_ATTR1_E);
			if (HI_NULL != puc_tmp) {
				HI_OS_MEMCPY_S(puc_pos, pst_attr->uc_size, puc_tmp, pst_attr->uc_size);
			}
			puc_pos += pst_attr->uc_size;
			break;
		case HI_OMCI_SQL_DATA_TABLEATTR_E :
			puc_tmp = (hi_uchar8 *)sqlite3_column_blob(pst_mibstmt, ui_col + HI_OMCI_SQL_INSTTABLE_ATTR1_E);
			if (HI_NULL != puc_tmp) {
				HI_OS_MEMCPY_S(puc_pos, pst_attr->uc_size, puc_tmp, pst_attr->uc_size);
			}
			puc_pos += pst_attr->uc_size;
			break;
		default:
			break;
		}
	}

	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_getall
 功能描述  : 获取某个meid所有实例
 输入参数  : hi_omci_mib_attrlist_s *pst_attrlist
             hi_uchar8 *puc_buf
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_getall(hi_omci_mib_attrlist_s *pst_attrlist, hi_uchar8 *puc_buf)
{
	hi_ushort16 us_instid;
	hi_uint32   ui_ret = HI_RET_SUCC;
	hi_uint32   ui_column, ui_row, ui_instindx;
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;
	hi_char8    **ppc_result = 0;

	/*query inst table, get content*/
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT instid FROM insttable%u", pst_attrlist->us_meid);
	ui_ret = sqlite3_get_table(g_pst_mibdb, c_sql, &ppc_result, (hi_int32 *)&ui_row, (hi_int32 *)&ui_column, &g_pc_errmsg);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_column, ui_row, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	for (ui_instindx = 1; ui_instindx <= ui_row; ui_instindx++) {
		us_instid = hi_os_strtoul(ppc_result[ui_instindx], 0, 10);
		pst_attrlist->us_instid = us_instid;
		HI_OS_MEMSET_S(puc_buf, pst_attrlist->us_totalsize, 0, pst_attrlist->us_totalsize);

		/*get inst content from db*/
		ui_ret = hi_omci_sql_inst_get(pst_attrlist, puc_buf);
		if (HI_RET_SUCC != ui_ret) {
			break;
		}
		hi_omci_sql_attr_encodehton(pst_attrlist);

		hi_omci_sql_attr_dump(pst_attrlist);
	}

	sqlite3_free_table(ppc_result);
	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_setsql
 功能描述  : 根据实例属性计算得出update语句
 输入参数  : hi_omci_mib_attrlist_s *pst_attrlist
             hi_uchar8 *puc_sql
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_setsql(hi_omci_mib_attrlist_s *pst_attrlist, hi_char8 *pc_sql, hi_uint32 sql_size)
{
	hi_int32  i_len;
	hi_uint32 ui_col, ui_first = 0;
	hi_char8 *pc_pos = pc_sql;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	i_len = HI_OS_SNPRINTF_S(pc_pos, sql_size, sql_size - 1,
				 "UPDATE insttable%u SET ",
				 pst_attrlist->us_meid);
	if (0 >= i_len) {
		hi_omci_lib_systrace(HI_RET_OUTRANG, i_len, 0, 0, 0);
		return HI_RET_OUTRANG;
	}
	pc_pos += i_len;

	for (ui_col = 0; ui_col < pst_attrlist->uc_num; ui_col++) {
		/* 表项数据在各ME业务处理中更新 */
		pst_attr = &pst_attrlist->st_attr[ui_col];
		if ((!(pst_attrlist->us_mask & (HI_ATT_MASK_ONE >> ui_col)))
		    || (pst_attr->uc_type == HI_OMCI_SQL_DATA_TABLEATTR_E)
		    || (HI_NULL == pst_attr->puc_data)) {
			continue;
		}
		if (0 == ui_first) {
			i_len = HI_OS_SNPRINTF_S(pc_pos, sql_size - (pc_pos - pc_sql), sql_size - (pc_pos - pc_sql) - 1,
						 "attr%u=? ", ui_col + 1);
			ui_first++;
		} else {
			i_len = HI_OS_SNPRINTF_S(pc_pos, sql_size - (pc_pos - pc_sql), sql_size - (pc_pos - pc_sql) - 1,
						 ",attr%u=? ", ui_col + 1);
		}
		if (0 >= i_len) {
			hi_omci_lib_systrace(HI_RET_OUTRANG, i_len, 0, 0, 0);
			return HI_RET_OUTRANG;
		}
		pc_pos += i_len;
	}

	/* 没有属性需要更新数据库 */
	if (0 == ui_first) {
		return HI_RET_INVALID_PARA;
	}

	i_len = HI_OS_SNPRINTF_S(pc_pos, sql_size - (pc_pos - pc_sql), sql_size - (pc_pos - pc_sql) - 1,
				 " WHERE instid=%u", pst_attrlist->us_instid);
	if (0 >= i_len) {
		hi_omci_lib_systrace(HI_RET_OUTRANG, i_len, 0, 0, 0);
		return HI_RET_OUTRANG;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_set
 功能描述  : 从实体实例表中获取一条实例数据
             将实例数据按照属性表定义放置在实例内存中
 输入参数  : hi_omci_mib_attrlist_s *pst_attrlist
             hi_void *pv_instcontent
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_set(hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32      ui_ret = HI_RET_SUCC;
	hi_uint32      ui_col, ui_index;
	hi_char8       c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	sqlite3_stmt  *pst_mibstmt = 0;
	hi_omci_mib_attr_s *pst_attr = HI_NULL;

	ui_ret = hi_omci_sql_inst_setsql(pst_attrlist, c_sql, sizeof(c_sql));
	if (HI_RET_SUCC != ui_ret) {
		/* 没有需要配置的属性，返回正确即可 */
		if (HI_RET_INVALID_PARA ==  ui_ret) {
			hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
			return HI_RET_SUCC;
		}

		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = sqlite3_prepare(g_pst_mibdb, c_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	for (ui_index = 0, ui_col = 1; ui_index < pst_attrlist->uc_num; ui_index++) {
		ui_ret = hi_omci_sql_attr_check(pst_attrlist, ui_index);
		if (HI_RET_SUCC != ui_ret) {
			continue;
		}

		/* owen修改: OLT一次下发只有一个或某几个表项
		 * 此处更新表所用的数据不完整
		 * 表项的更新修改为放在各个ME的业务处理中执行
		 */
		pst_attr = &pst_attrlist->st_attr[ui_index];
		if (HI_OMCI_SQL_DATA_TABLEATTR_E == pst_attr->uc_type) {
			continue;
		}

		switch (pst_attr->uc_type) {
		case HI_OMCI_SQL_DATA_UCHAR_E :
			ui_ret = sqlite3_bind_int(pst_mibstmt, ui_col, *(hi_uchar8 *)pst_attr->puc_data);
			break;
		case HI_OMCI_SQL_DATA_USHORT_E :
			ui_ret = sqlite3_bind_int(pst_mibstmt, ui_col, *(hi_ushort16 *)pst_attr->puc_data);
			break;
		case HI_OMCI_SQL_DATA_UINT_E :
			ui_ret = sqlite3_bind_int(pst_mibstmt, ui_col, *(hi_uint32 *)pst_attr->puc_data);
			break;
		case HI_OMCI_SQL_DATA_BINARY_E :
			ui_ret = sqlite3_bind_blob(pst_mibstmt, ui_col, pst_attr->puc_data, pst_attr->uc_size, SQLITE_STATIC);
			break;
		case HI_OMCI_SQL_DATA_TEXT_E :
			ui_ret = sqlite3_bind_text(pst_mibstmt, ui_col, (hi_char8 *)pst_attr->puc_data, pst_attr->uc_size, SQLITE_STATIC);
			break;
		default:
			break;
		}
		ui_col++;
	}

	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_DONE != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_del
 功能描述  : 删除管理实体实例表项
 输入参数  : hi_omci_mib_index_s *pst_index
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_del(hi_ushort16 us_meid, hi_ushort16 us_instid)
{
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "DELETE FROM insttable%u WHERE instid=%u",
			 us_meid, us_instid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_exec(c_sql);
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_getcolint32
 功能描述  : 获取实体实例表中实例数据中一个属性(uint32)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_col
             hi_uint32 *pui_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_getcolint32(hi_ushort16 us_meid, hi_ushort16 us_instid,
				       hi_uchar8 uc_col, hi_uint32 *pui_data)
{
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT * FROM insttable%u WHERE instid=%u",
			 us_meid, us_instid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_getcolumn(c_sql, uc_col, HI_OMCI_SQL_DB_INT32_E, (hi_uchar8 *)pui_data, sizeof(hi_uint32));
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_getcolint32
 功能描述  : 获取实体实例表中实例数据中一个属性(hi_ushort16)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_col
             hi_uint32 *pui_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_getcolshort16(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_ushort16 *pus_data)
{
	hi_uint32 ui_data = 0;
	hi_uint32 ui_ret = 0;

	ui_ret = hi_omci_sql_inst_getcolint32(us_meid, us_instid, uc_col, &ui_data);

	*pus_data = (hi_ushort16)ui_data;

	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_getcoluchar8
 功能描述  : 获取实体实例表中实例数据中一个属性(hi_uchar8)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_col
             hi_uint32 *pui_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_getcoluchar8(hi_ushort16 us_meid, hi_ushort16 us_instid,
					hi_uchar8 uc_col, hi_uchar8 *puc_data)
{
	hi_uint32 ui_data = 0;
	hi_uint32 ui_ret = 0;

	ui_ret = hi_omci_sql_inst_getcolint32(us_meid, us_instid, uc_col, &ui_data);

	*puc_data = (hi_uchar8)ui_data;

	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}


/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_getcolblob
 功能描述  : 获取实体实例表中实例数据中一个属性(二进制格式)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_col
             hi_void *pv_data
             hi_uint32 ui_size
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_getcolblob(hi_ushort16 us_meid, hi_ushort16 us_instid,
				      hi_uchar8 uc_col, hi_void *pv_data, hi_uint32 ui_size)
{
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT * FROM insttable%u WHERE instid=%u",
			 us_meid, us_instid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_getcolumn(c_sql, uc_col, HI_OMCI_SQL_DB_BLOB_E, (hi_uchar8 *)pv_data, ui_size);

}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_getcoltext
 功能描述  : 获取实体实例表中实例数据中一个属性(文本格式)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_col
             hi_void *pv_data
             hi_uint32 ui_size
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_getcoltext(hi_ushort16 us_meid, hi_ushort16 us_instid,
				      hi_uchar8 uc_col, hi_void *pv_data, hi_uint32 ui_size)
{
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT * FROM insttable%u WHERE instid=%u",
			 us_meid, us_instid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_getcolumn(c_sql, uc_col, HI_OMCI_SQL_DB_TEXT_E, (hi_uchar8 *)pv_data, ui_size);

}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_setcolint32
 功能描述  : 获取实体实例表中实例数据中一个属性(uint32)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_col
             hi_uint32 ui_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_setcolint32(hi_ushort16 us_meid, hi_ushort16 us_instid,
				       hi_uchar8 uc_col, hi_uint32 ui_data)
{
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "UPDATE insttable%u SET attr%u=? WHERE instid=%u",
			 us_meid, uc_col, us_instid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_setcolumn(c_sql, HI_OMCI_SQL_DB_INT32_E, &ui_data, sizeof(hi_uint32));
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_setcolshort16
 功能描述  : 获取实体实例表中实例数据中一个属性(hi_ushort16)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_col
             hi_uint32 ui_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_setcolshort16(hi_ushort16 us_meid, hi_ushort16 us_instid,
		hi_uchar8 uc_col, hi_ushort16 us_data)
{
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_inst_setcolint32(us_meid, us_instid, uc_col, (hi_uint32)us_data);
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_setcoluchar8
 功能描述  : 获取实体实例表中实例数据中一个属性(hi_uchar8)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_col
             hi_uint32 ui_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_setcoluchar8(hi_ushort16 us_meid, hi_ushort16 us_instid,
					hi_uchar8 uc_col, hi_uchar8 uc_data)
{
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_inst_setcolint32(us_meid, us_instid, uc_col, (hi_uint32)uc_data);
}


/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_setcolblob
 功能描述  : 获取实体实例表中实例数据中一个属性(二进制格式)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_col
             hi_void *pv_data
             hi_uint32 ui_size
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_setcolblob(hi_ushort16 us_meid, hi_ushort16 us_instid,
				      hi_uchar8 uc_col, hi_void *pv_data, hi_uint32 ui_size)
{
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "UPDATE insttable%u SET attr%u=? WHERE instid=%u",
			 us_meid, uc_col, us_instid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_setcolumn(c_sql, HI_OMCI_SQL_DB_BLOB_E, pv_data, ui_size);
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_setcoltext
 功能描述  : 获取实体实例表中实例数据中一个属性(文本格式)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_col
             hi_void *pv_data
             hi_uint32 ui_size
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_setcoltext(hi_ushort16 us_meid, hi_ushort16 us_instid,
				      hi_uchar8 uc_col, hi_void *pv_data, hi_uint32 ui_size)
{
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "UPDATE insttable%u SET attr%u=? WHERE instid=%u",
			 us_meid, uc_col, us_instid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_setcolumn(c_sql, HI_OMCI_SQL_DB_TEXT_E, pv_data, ui_size);
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_checktable
 功能描述  : 检查动态实体实例表是否已经创建
 输入参数  : hi_ushort16 us_meid
 输出参数  : 无
 返 回 值  : hi_uint32 返回当前条件数据库中数目 0:没有数据
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_checktable(hi_ushort16 us_meid)
{
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT COUNT(*) as CNT FROM sqlite_master where type='table' and name='insttable%u' ",
			 us_meid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_countsql(c_sql);
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_inst_checkinst
 功能描述  : 检查管理实体实例表中该实例是否存在
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
 输出参数  : 无
 返 回 值  : hi_uint32 返回当前条件数据库中数目 0:没有数据
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_checkinst(hi_ushort16 us_meid, hi_ushort16 us_instid)
{
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT COUNT(*) as CNT FROM insttable%u WHERE instid=%u",
			 us_meid, us_instid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_countsql(c_sql);
}

/*****************************************************************************
函 数 名  : hi_omci_sql_inst_check
功能描述  : 管理实体实例表项检查
输入参数  : hi_omci_mib_inst_index_s *pst_index
输出参数  : 无
返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_check(hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32 ui_ret;

	ui_ret = hi_omci_sql_inst_checktable(pst_attrlist->us_meid);
	if (0 == ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_UNKNOWN_ME_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_UNKNOWN_ME_E;
	}

	ui_ret = hi_omci_sql_inst_checkinst(pst_attrlist->us_meid, pst_attrlist->us_instid);
	if (0 == ui_ret) {
		hi_omci_lib_systrace(HI_OMCI_PRO_ERR_UNKNOWN_IN_E, ui_ret, 0, 0, 0);
		return HI_OMCI_PRO_ERR_UNKNOWN_IN_E;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_create_inst
 功能描述  : 创建实例表
 输入参数  : hi_ushort16 us_meid
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_inst_create(hi_ushort16 us_meid)
{
	hi_char8    uc_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;

	HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1,
			 "CREATE TABLE IF NOT EXISTS "
			 "insttable%u(instid,attr1,attr2,attr3,attr4,attr5,attr6,attr7,attr8,attr9, attr10,attr11,attr12,attr13,attr14,attr15,attr16)",
			 us_meid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_exec(uc_sql);
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

