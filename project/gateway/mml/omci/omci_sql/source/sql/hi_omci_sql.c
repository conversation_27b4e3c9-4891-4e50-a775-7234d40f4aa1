/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_sql.c
  版 本 号   : 初稿
  作    者   : t00185260
  生成日期   : D2011_10_13
  功能描述   : sqlite3数据库操作
******************************************************************************/

#include "hi_omci_lib.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


static hi_char8 *g_pc_errmsg = HI_NULL;
sqlite3  *g_pst_mibdb = HI_NULL;

/*****************************************************************************
 函 数 名  : hi_omci_sql_getcolumn
 功能描述  : 获取SQL出来的数据第一行某列数据
             select * from tablename where meid=n and inst=m
 输入参数  : hi_char8 *pc_sql
             hi_uchar8 uc_col
             hi_uchar8 uc_type
             hi_uchar8 *puc_data
             hi_uint32 ui_size
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_getcolumn(hi_char8 *pc_sql, hi_uchar8 uc_col, hi_uchar8 uc_type,
				hi_uchar8 *puc_data, hi_uint32 ui_size)
{
	hi_uint32     ui_ret;
	hi_int32      i_col    = uc_col;
	hi_uchar8     *puc_buf = HI_NULL;
	sqlite3_stmt *pst_mibstmt = 0;

	ui_ret = sqlite3_prepare(g_pst_mibdb, pc_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), uc_col, uc_type, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_ROW != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), uc_col, uc_type, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	switch (uc_type) {
	case HI_OMCI_SQL_DB_INT32_E:
		*(hi_uint32 *)puc_data = sqlite3_column_int(pst_mibstmt, i_col);
		break;
	case HI_OMCI_SQL_DB_BLOB_E:
		puc_buf = (hi_uchar8 *)sqlite3_column_blob(pst_mibstmt, i_col);
		if (HI_NULL != puc_buf) {
			HI_OS_MEMCPY_S(puc_data, ui_size, puc_buf, ui_size);
		}
		break;
	case HI_OMCI_SQL_DB_TEXT_E:
		puc_buf = (hi_uchar8 *)sqlite3_column_text(pst_mibstmt, i_col);
		if (HI_NULL != puc_buf) {
			HI_OS_MEMCPY_S(puc_data, ui_size, puc_buf, ui_size);
		}
		break;
	default:
		break;
	}

	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_setcolumn
 功能描述  : 设置SQL出来的数据第一行某列数据
             update tablename set colname=? where meid=n and inst=m
 输入参数  : hi_char8 *pc_sql
             hi_uchar8 uc_type
             hi_void *pv_data
             hi_uint32 ui_size
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_setcolumn(hi_char8 *pc_sql,  hi_uchar8 uc_type,
				hi_void *pv_data, hi_uint32 ui_size)
{
	hi_uint32     ui_ret;
	sqlite3_stmt *pst_mibstmt = 0;

	ui_ret = sqlite3_prepare(g_pst_mibdb, pc_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), uc_type, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	switch (uc_type) {
	case HI_OMCI_SQL_DB_INT32_E :
		ui_ret = sqlite3_bind_int(pst_mibstmt, 1, *(hi_uint32 *)pv_data);
		break;
	case HI_OMCI_SQL_DB_BLOB_E :
		ui_ret = sqlite3_bind_blob(pst_mibstmt, 1, pv_data, ui_size, SQLITE_STATIC);
		break;
	case HI_OMCI_SQL_DATA_TEXT_E :
		ui_ret = sqlite3_bind_text(pst_mibstmt, 1, (hi_char8 *)pv_data, ui_size, SQLITE_STATIC);
		break;
	default:
		break;
	}

	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_DONE != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), uc_type, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_counttable
 功能描述  : 返回表统计个数
             select count(*) as cnt from %s
 输入参数  : hi_uchar8 *puc_sql
 输出参数  : 无
 返 回 值  : hi_uint32 0:错误或者没有数据 否则为统计数
*****************************************************************************/
hi_uint32 hi_omci_sql_counttable(hi_char8 *pc_tablename)
{
	hi_uint32   ui_ret, ui_cnt;
	hi_int32    i_column, i_row;
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;
	hi_char8    **ppc_result = HI_NULL;

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT COUNT(*) as CNT FROM %s",
			 pc_tablename);

	ui_ret = sqlite3_get_table(g_pst_mibdb, c_sql, &ppc_result, &i_row, &i_column, &g_pc_errmsg);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), i_column, i_row, 0, 0);
		return 0;
	}

	ui_cnt = hi_os_strtoul(ppc_result[0], 0, 10);

	sqlite3_free_table(ppc_result);

	hi_omci_lib_systrace(HI_RET_SUCC, ui_cnt, 0, 0, 0);
	return ui_cnt;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_countsql
 功能描述  : 执行该SQL语句,返回数据row数
             any sql
 输入参数  : hi_char8 *pc_sql
 输出参数  : 无
 返 回 值  : HI_RET_SUCC有数据存在,其它无数据输出
*****************************************************************************/
hi_uint32 hi_omci_sql_countsql(hi_char8 *pc_sql)
{
	hi_uint32   ui_ret;
	hi_int32    i_column  = 0;
	hi_int32    i_row     = 0;
	hi_uint32   ui_count   = 0;
	hi_char8  **ppc_result = HI_NULL;

	ui_ret = sqlite3_get_table(g_pst_mibdb, pc_sql, &ppc_result, &i_row, &i_column, &g_pc_errmsg);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return 0;
	}
	ui_count = hi_os_strtoul(ppc_result[1], 0, 10);
	sqlite3_free_table(ppc_result);
	hi_omci_lib_systrace(HI_RET_SUCC, i_row, 0, 0, 0);
	return ui_count;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_exec
 功能描述  : 执行SQL语句
             any sql
 输入参数  : hi_char8 *pc_sql
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_exec(hi_char8 *pc_sql)
{
	hi_uint32 ui_ret;

	ui_ret = sqlite3_exec(g_pst_mibdb, pc_sql, 0, 0, &g_pc_errmsg);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_ret, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_dbstart
 功能描述  : sqlite3数据库初始化
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_dbstart(hi_void)
{
	hi_uint32  ui_ret;

	ui_ret = sqlite3_open(HI_OMCI_MIB_DBDATABASE_NAME, &g_pst_mibdb);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_debug("%s", sqlite3_errmsg(g_pst_mibdb));
		sqlite3_close(g_pst_mibdb);

		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_dbstop
 功能描述  : sqlite3数据库去初始化
 输入参数  : sqlite3* g_pst_mibdb
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_dbstop(hi_void)
{
	if (g_pc_errmsg != NULL) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
	}
	sqlite3_close(g_pst_mibdb);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

