/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_sql_alarmmsg.c
  版 本 号   : 初稿
  作    者   : y00185833
  生成日期   : D2011_11_04
  功能描述   : 对alarmmsgtable操作
******************************************************************************/

#include "hi_omci_lib.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

static hi_char8 *g_pc_errmsg = HI_NULL;
hi_uint32 hi_omci_sql_del_alarmmsg(hi_ushort16 us_meid, hi_ushort16 us_instid);
/*****************************************************************************
 函 数 名  : hi_omci_sql_check_alarmmsg
 功能描述  : 检查告警表项实例是否存在
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 uc_alarmid
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_check_alarminst(hi_ushort16 us_meid, hi_ushort16 us_instid)
{
	hi_uint32  ui_flag = 0;
	hi_char8   c_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;

	/*参数合法性由前面保证*/
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
			 "SELECT COUNT(*) as CNT FROM alarmmsgtable WHERE meid=%u and instid=%u", us_meid, us_instid);

	ui_flag = hi_omci_sql_countsql(c_sql);
	if (0 == ui_flag) {
		hi_omci_lib_systrace(HI_RET_OMCI_SQL_TABLE_UNEXISTED_E, us_meid, us_instid, ui_flag, 0);
		return (hi_uint32)HI_RET_OMCI_SQL_TABLE_UNEXISTED_E ;
	}

	hi_omci_lib_systrace(HI_RET_OMCI_SQL_TABLE_EXISTED_E, us_meid, us_instid, ui_flag, 0);
	return (hi_uint32)HI_RET_OMCI_SQL_TABLE_EXISTED_E;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_get_alarmmask
 功能描述  : 获取alarm告警表的alarm mask
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8 *puc_alarmmask
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_get_alarmmask(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_uchar8 *puc_alarmmask)
{
	hi_uint32  ui_ret;
	hi_char8   uc_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;

	/*参数合法性由前面保证*/

	HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1,
			 "SELECT alarmmask FROM alarmmsgtable WHERE meid=%u and instid=%u", us_meid, us_instid);

	ui_ret = hi_omci_sql_getcolumn(uc_sql, HI_OMCI_SQL_ALARMMSGTABLE_ALARMID_E, HI_OMCI_SQL_DB_BLOB_E, puc_alarmmask, 28);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(HI_RET_FAIL, us_meid, us_instid, 0, 0);
		return (hi_uint32)HI_RET_FAIL;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_update_alarmmsg
 功能描述  : 更新告警数据库表，添加或刷新表项,若alarm mask为全零，则删除表项
 输入参数  : hi_omci_proc_alarm_msg_item_s * pst_alarmitem
             hi_uchar8 *puc_alarmmask
             hi_uchar8 uc_arc
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_update_alarmmsg(hi_omci_proc_alarm_msg_item_s *pst_alarmitem, hi_uchar8 *puc_alarmmask,
				      hi_uchar8 uc_arc)
{
	hi_uint32  ui_ret;
	hi_char8   uc_sql[HI_OMCI_SQL_MAX_LEN] = {0} ;
	sqlite3_stmt *pst_mibstmt = 0;
	hi_uchar8  uc_emptymask[28] = {0};

	/*若alarm mask为全零，则从alarm表中删除*/
	ui_ret = hi_os_memcmp(puc_alarmmask, uc_emptymask, 28);
	if (0 == ui_ret) {
		hi_omci_sql_del_alarmmsg(pst_alarmitem->us_meid, pst_alarmitem->us_instid);
		return HI_RET_SUCC;
	}

	ui_ret = hi_omci_sql_check_alarminst(pst_alarmitem->us_meid, pst_alarmitem->us_instid);
	if ((hi_uint32)HI_RET_OMCI_SQL_TABLE_UNEXISTED_E == ui_ret) {
		hi_omci_lib_systrace(HI_RET_OMCI_WRITER_DATABSE_E, 0, 0, 0, 0);
		HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1, "INSERT INTO alarmmsgtable VALUES(%u, %u, ?, %u, %u)",
				 pst_alarmitem->us_meid, pst_alarmitem->us_instid, pst_alarmitem->ui_cmdtype, uc_arc);
	} else {
		HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1,
				 "UPDATE alarmmsgtable SET alarmmask=?, cmdtype=%u, arc=%u WHERE meid=%u and instid=%u",
				 pst_alarmitem->ui_cmdtype, uc_arc, pst_alarmitem->us_meid, pst_alarmitem->us_instid);
	}

	ui_ret = sqlite3_prepare(g_pst_mibdb, uc_sql, -1, &pst_mibstmt, 0);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_ret, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	sqlite3_bind_blob(pst_mibstmt, 1, puc_alarmmask, 28, SQLITE_STATIC);
	ui_ret = sqlite3_step(pst_mibstmt);
	if (SQLITE_DONE != ui_ret) {
		sqlite3_finalize(pst_mibstmt);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), 0, 0, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	sqlite3_finalize(pst_mibstmt);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);

	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_del_alarmmsg
 功能描述  : 删除告警表项(预留)
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_uchar8  uc_alarmid
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_del_alarmmsg(hi_ushort16 us_meid, hi_ushort16 us_instid)
{
	hi_char8    uc_sql[HI_OMCI_SQL_MAX_LEN] = {0};


	HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1,
			 "DELETE alarmmsgtable WHERE meid =%u and instid=%u", us_meid, us_instid);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_sql_exec(uc_sql);
}
#if 0
/*****************************************************************************
 函 数 名  : hi_omci_sql_get_alarmmsg
 功能描述  : 获取告警信息
 输入参数  : hi_ushort16 us_meid
             hi_ushort16 us_instid
             hi_omci_mib_alarm_s* pst_mibalarm
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_get_alarmmsg(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_omci_mib_alarm_s *pst_mibalarm)
{
	hi_uint32   ui_ret ;
	hi_uint32   ui_index    = 0;
	hi_int32    ui_column   = 0;
	hi_int32    ui_row      = 0;
	hi_uint32   ui_alarmcmd = 0;
	hi_uchar8   uc_byteno   = 0;
	hi_uchar8   uc_bitno    = 0;
	hi_char8    uc_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	hi_char8    **ppc_result = HI_NULL;

	HI_OS_SNPRINTF_S(uc_sql, HI_OMCI_SQL_MAX_LEN, HI_OMCI_SQL_MAX_LEN - 1
			 "SELECT  * FROM alarmmsgtable WHERE meid =%u and instid=%u and status=1",
			 us_meid, us_instid);

	/*调用SQL 获取告警消息*/
	ui_ret = sqlite3_get_table(g_pst_mibdb, uc_sql, &ppc_result, &ui_row,
				   &ui_column, &g_pc_errmsg);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_column, ui_row, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	/*如果没有警告*/
	if (ui_row == 0) {
		/*释放table内存*/
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), ui_ret, ui_column, ui_row, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	/*填充告警信息*/
	pst_mibalarm->us_meid = us_meid;
	pst_mibalarm->us_instid = us_instid;

	for (ui_index = HI_NUM_2 ; ui_index < ui_column * (ui_row + 1) ;) {
		ui_alarmcmd = hi_os_strtoul(ppc_result[ui_column + ui_index], 0, 10);
		/*获取告警信息掩码位置*/
		uc_byteno = ui_alarmcmd / HI_OMCI_ALM_BYTE_NUM;
		uc_bitno = ui_alarmcmd % HI_OMCI_ALM_BYTE_NUM;

		pst_mibalarm->uc_alarm[ uc_byteno ] |= (HI_ATT_ONE >> uc_bitno);

		ui_index += ui_column;
	}

	/*释放table内存*/
	sqlite3_free_table(ppc_result);

	hi_omci_lib_systrace(HI_RET_SUCC, us_meid, us_instid, ui_column, ui_row);
	return HI_RET_SUCC;
}
#endif
/*****************************************************************************
 函 数 名  : hi_omci_sql_getalarmcallback
 功能描述  : 根据cmdtype从数据库中获取回调函数的地址
 输入参数  : hi_uint32 ui_cmdtype
             hi_uint32* pui_callback
             hi_uint32* pui_num
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_get_alarmcallback(hi_uint32 ui_cmdtype, hi_uint32 *pui_callback, hi_uint32 *pui_num)
{
	hi_uint32   ui_ret ;
	hi_uint32   i;
	hi_int32    i_column = 0;
	hi_int32    i_row    = 0;
	hi_char8    uc_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	hi_char8    **ppc_result = HI_NULL;

	HI_OS_SNPRINTF_S(uc_sql, sizeof(uc_sql), sizeof(uc_sql) - 1, "SELECT address FROM callbacktable WHERE cmdtype=%u",
			 ui_cmdtype);

	/*获取alarm callback表项*/
	ui_ret = sqlite3_get_table(g_pst_mibdb, uc_sql, &ppc_result, &i_row,
				   &i_column, &g_pc_errmsg);
	if (HI_RET_SUCC != ui_ret) {
		sqlite3_free(g_pc_errmsg);
		g_pc_errmsg = NULL;
		sqlite3_free_table(ppc_result);
		hi_omci_lib_systrace(HI_OMCI_SQL_RET(ui_ret), i_column, i_row, 0, 0);
		return HI_OMCI_SQL_RET(ui_ret);
	}

	/*注:ui_row不能大于HI_OMCI_ALARM_NUM，支持HI_OMCI_ALARM_NUM个告警*/
	for (i = 1; (i <= i_row) && (i <= HI_OMCI_ALM_NUM); i++) {
		pui_callback[i - 1] = hi_os_strtoul(ppc_result[i], 0, 10);
	}

	*pui_num = (hi_uint32)((i_row >  HI_OMCI_ALM_NUM) ? HI_OMCI_ALM_NUM : i_row);
	sqlite3_free_table(ppc_result);
	hi_omci_lib_systrace(HI_RET_SUCC, i_row, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_getalarmarc
 功能描述  : 根据arc属性的位置获取arc的值
 输入参数  : hi_void *pv_data
             hi_uchar8 *puc_arc
             hi_uchar8 *puc_arcintvl
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_uint32 hi_omci_sql_get_alarmarc(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_uchar8 *puc_arc,
				   hi_uchar8 *puc_arcintvl)
{
	hi_uint32  ui_ret = HI_RET_SUCC;
	hi_char8   c_sql[HI_OMCI_SQL_MAX_LEN] = {0};
	hi_uchar8  uc_arcpos = 0;

	/*获取arc的属性位置*/
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "SELECT pos FROM attrtable WHERE meid=%u and desc=\"ARC\"",
			 us_meid);

	ui_ret = hi_omci_sql_getcolumn(c_sql, 0, HI_OMCI_SQL_DB_INT32_E, &uc_arcpos, sizeof(hi_uchar8));
	if (HI_RET_SUCC != ui_ret) {
		return (hi_uint32)HI_RET_FAIL;
	}

	/*根据arc的属性位置获取arc的值*/
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "SELECT attr%u FROM insttable%u WHERE instid=%u",
			 uc_arcpos, us_meid, us_instid);

	ui_ret = hi_omci_sql_getcolumn(c_sql, 0, HI_OMCI_SQL_DB_INT32_E, puc_arc, sizeof(hi_uchar8));
	if (HI_RET_SUCC != ui_ret) {
		return (hi_uint32)HI_RET_FAIL;
	}

	if ((*puc_arc != HI_FALSE) && (*puc_arc != HI_TRUE)) {
		*puc_arc = HI_FALSE;
	}

	return (hi_uint32)HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_get_alarmsnapshot
 功能描述  : 获取alarm table的snapshot，以及alarm的数目
 输入参数  : hi_uchar8 uc_regardarc
 输出参数  :  hi_ushort16 *us_alarmnum
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_get_alarmsnapshot(hi_uchar8 uc_regardarc, hi_ushort16 *pus_alarmnum)
{
	hi_uint32   ui_ret;
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	/*清空snapshot表*/
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "DELETE FROM alarmmsgtable_snapshot");
	ui_ret = hi_omci_sql_exec(c_sql);
	if (HI_RET_SUCC != ui_ret) {
		return ui_ret;
	}

	/*根据arc标识获取alarm table的snapshot*/
	if (HI_TRUE == uc_regardarc) {
		HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
				 "INSERT INTO alarmmsgtable_snapshot SELECT * FROM alarmmsgtable WHERE arc=0");
	} else {
		HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1,
				 "INSERT INTO alarmmsgtable_snapshot SELECT * FROM alarmmsgtable");
	}

	ui_ret = hi_omci_sql_exec(c_sql);
	if (HI_RET_SUCC != ui_ret) {
		return ui_ret;
	}

	/*获取snapshot alarm的数目*/
	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "SELECT COUNT(*) as CNT FROM alarmmsgtable_snapshot");
	*pus_alarmnum = (hi_ushort16)hi_omci_sql_countsql(c_sql);

	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_sql_get_alarmitem
 功能描述  : 根据alarmnum从snapshot表中获取alarm item
 输入参数  : hi_uint32   ui_alarmnum
 输出参数  : hi_ushort16 *pus_meid
             hi_ushort16 *pus_instid
             hi_uchar8   *puc_alarmmask
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_sql_get_alarmitem(hi_uint32 ui_alarmnum, hi_ushort16 *pus_meid,  hi_ushort16 *pus_instid,
				    hi_uchar8 *puc_alarmmask)
{
	hi_uint32   ui_ret;
	hi_char8    c_sql[HI_OMCI_SQL_MAX_LEN] = {0};

	HI_OS_SNPRINTF_S(c_sql, sizeof(c_sql), sizeof(c_sql) - 1, "SELECT * FROM alarmmsgtable_snapshot LIMIT 1 OFFSET %u",
			 ui_alarmnum);

	/*获取meid*/
	ui_ret = hi_omci_sql_getcolumn(c_sql, 1, HI_OMCI_SQL_DB_INT32_E, (hi_uchar8 *)pus_meid, sizeof(hi_ushort16));
	if (HI_RET_SUCC != ui_ret) {
		return (hi_uint32)HI_RET_FAIL;
	}

	/*获取instid*/
	ui_ret = hi_omci_sql_getcolumn(c_sql, 2, HI_OMCI_SQL_DB_INT32_E, (hi_uchar8 *)pus_instid, sizeof(hi_ushort16));
	if (HI_RET_SUCC != ui_ret) {
		return (hi_uint32)HI_RET_FAIL;
	}

	/*获取alarm mask*/
	ui_ret = hi_omci_sql_getcolumn(c_sql, 3, HI_OMCI_SQL_DB_BLOB_E, puc_alarmmask, sizeof(hi_uchar8));
	if (HI_RET_SUCC != ui_ret) {
		return (hi_uint32)HI_RET_FAIL;
	}

	return HI_RET_SUCC;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */

