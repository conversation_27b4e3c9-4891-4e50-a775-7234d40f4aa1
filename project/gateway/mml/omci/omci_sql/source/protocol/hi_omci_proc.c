/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  文 件 名   : hi_omci_proc .c
  版 本 号   : 初稿
  作    者   : t00185260
  生成日期   : D2011_10_24
  功能描述   : OMCI消息处理
******************************************************************************/

#include "hi_omci_lib.h"
#include "hi_omci_extend.h"
#include "hi_ipc.h"

#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */

/*OMCI模块全局信息*/
static hi_omci_proc_global_s g_st_globalpara;
static hi_uchar8  g_uc_omci_outbuf[HI_OMCI_PROC_MSG_MAXLEN];
static hi_uchar8  g_uc_omci_lsthigh[HI_OMCI_PROC_MSG_MAXLEN];
static hi_uchar8  g_uc_omci_lstlow[HI_OMCI_PROC_MSG_MAXLEN];
static hi_uchar8  g_uc_omci_reportbuf[HI_OMCI_PROC_MSG_MAXLEN];

#define HI_OMCI_EXTEND_TABLE_COUNT      16

static hi_uint32  g_aui_omci_extend[HI_OMCI_EXTEND_TABLE_COUNT] = {
	HI_OMCI_PRO_ME_EXTEND_VLAN_TAG_E,
	HI_OMCI_PRO_ME_EXT_ONT_ABILITY_E,
	HI_OMCI_PRO_ME_ONT_FLOW_E,
	HI_OMCI_PRO_ME_VOIP_SW_IMAGE_E,
	HI_OMCI_PRO_ME_MULTICASE_TRANSFER_MODE_E,
	HI_OMCI_PRO_ME_E_PPTP_ETH_UNI_E,
	HI_OMCI_PRO_ME_EXT_ONT2_ABILITY_E,
	HI_OMCI_PRO_ME_HWEXTEND_VLAN_TAG_E,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
	0,
};

static uint16_t g_omci_extend_rsp_len[HI_OMCI_PRO_MSG_TYPE_MAX_E] = {
	HI_OMCI_EXT_PRO_NULL_RSP_LEN,
	HI_OMCI_EXT_PRO_NULL_RSP_LEN,
	HI_OMCI_EXT_PRO_NULL_RSP_LEN,
	HI_OMCI_EXT_PRO_NULL_RSP_LEN,
	HI_OMCI_EXT_PRO_CREATE_RSP_LEN, /* mt=4 create */
	HI_OMCI_EXT_PRO_CREATECC_RSP_LEN, /* mt=5 createcc */
	HI_OMCI_EXT_PRO_DELETE_RSP_LEN, /* mt=6 delete */
	HI_OMCI_EXT_PRO_DELETECC_RSP_LEN, /* mt=7 deletecc */
	HI_OMCI_EXT_PRO_SET_RSP_LEN, /* mt=8 set */
	HI_OMCI_EXT_PRO_GET_RSP_LEN, /* mt=9 get */
	HI_OMCI_EXT_PRO_GETCC_RSP_LEN, /* mt=10 getcc */
	HI_OMCI_EXT_PRO_GETALLALARM_RSP_LEN, /* mt=11 get all alarms */
	HI_OMCI_EXT_PRO_GETALLALARMNEXT_RSP_LEN, /* mt=12 get all alarms next */
	HI_OMCI_EXT_PRO_MIBUPLOAD_RSP_LEN, /* mt=13 mib upload */
	HI_OMCI_EXT_PRO_MIBUPLOADNEXT_RSP_LEN, /* mt=14 mib upload next */
	HI_OMCI_EXT_PRO_MIBRESET_RSP_LEN, /* mt=15 mib reset */
	HI_OMCI_EXT_PRO_NULL_RSP_LEN, /* mt=16 alarm */
	HI_OMCI_EXT_PRO_NULL_RSP_LEN, /* mt=17 avc */
	HI_OMCI_EXT_PRO_TEST_RSP_LEN, /* mt=18 test */
	HI_OMCI_EXT_PRO_STARTDOWNLOAD_RSP_LEN, /* mt=19 start software download */
	HI_OMCI_EXT_PRO_DOWNLOADING_RSP_LEN, /* mt=20 download section */
	HI_OMCI_EXT_PRO_ENDDOWNLOAD_RSP_LEN, /* mt=21 end software */
	HI_OMCI_EXT_PRO_ACTIVEIMAGE_RSP_LEN, /* mt=22 activate software */
	HI_OMCI_EXT_PRO_COMMITIMAGE_RSP_LEN, /* mt=23 commit software */
	HI_OMCI_EXT_PRO_SYNCTIME_RSP_LEN, /* mt=24 sync time */
	HI_OMCI_EXT_PRO_REBOOT_RSP_LEN, /* mt=25 reboot */
	HI_OMCI_EXT_PRO_GETNEXT_RSP_LEN, /* mt=26 get next */
	HI_OMCI_EXT_PRO_NULL_RSP_LEN, /* mt=27 test result */
	HI_OMCI_EXT_PRO_GET_CURRDATA_RSP_LEN, /* mt=28 get current data */
	HI_OMCI_EXT_PRO_SET_TABLE_RSP_LEN, /* mt=29 set table */
};

/* alarm sequence number */
static hi_uchar8  g_uc_alarm_seq_num = 1;

extern hi_uchar8  g_uc_debug_mode;
hi_uint32  gui_extend_omci_cnt;

static hi_int32 hi_omci_proc_send_omci_msg(struct hi_gpon_omci_msg *msg)
{
	if (g_uc_debug_mode) {
		return HI_RET_SUCC;
	}
	return HI_IPC_CALL("hi_gpon_send_omci_msg", msg);
}
/*****************************************************************************
 函 数 名  : hi_omci_proc_getlen
 功能描述  : 从消息中获取消息总长度
 输入参数  : hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_getlen(hi_void *pv_data)
{
	hi_ushort16 us_len;
	hi_uchar8 uc_devid = HI_OMCI_PROC_GET_DEVID((hi_uchar8 *)pv_data);

	if (HI_OMCI_PROC_DEVICEID_STD == uc_devid) {
		us_len = HI_OMCI_PROC_HEADLEN_STD + HI_OMCI_PROC_CONTENTLEN_STD;
		hi_omci_lib_systrace(HI_RET_SUCC, us_len, 0, 0, 0);
		return us_len;
	}

	us_len = HI_OMCI_PROC_HEADLEN_EXT + HI_OMCI_PROC_EXT_GET_SIZE((hi_uchar8 *)pv_data);
	hi_omci_lib_systrace(HI_RET_SUCC, us_len, 0, 0, 0);
	return us_len;
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_clearlst
 功能描述  : 清除上一个成功消息
 输入参数  : hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_clearlst(hi_void *pv_data)
{
	hi_ushort16 us_tci;

	us_tci = HI_OMCI_PROC_GET_TCI((hi_uchar8 *)pv_data);
	if (us_tci & HI_OMIC_PROC_TCI_PRI_MASK) {
		HI_OS_MEMSET_S(g_uc_omci_lsthigh, HI_OMCI_PROC_MSG_MAXLEN, 0, HI_OMCI_PROC_MSG_MAXLEN);
	} else {
		HI_OS_MEMSET_S(g_uc_omci_lstlow, HI_OMCI_PROC_MSG_MAXLEN, 0, HI_OMCI_PROC_MSG_MAXLEN);
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_recordlst
 功能描述  : 记录最后一条成功处理消息
 输入参数  : hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_recordlst(hi_void *pv_data)
{
	hi_ushort16 us_tci;
	hi_uint32   ui_len;

	ui_len = hi_omci_proc_getlen(pv_data);

	us_tci = HI_OMCI_PROC_GET_TCI((hi_uchar8 *)pv_data);
	if (us_tci & HI_OMIC_PROC_TCI_PRI_MASK) {
		HI_OS_MEMCPY_S(g_uc_omci_lsthigh, sizeof(g_uc_omci_lsthigh), pv_data, ui_len);
	} else {
		HI_OS_MEMCPY_S(g_uc_omci_lstlow, sizeof(g_uc_omci_lstlow), pv_data, ui_len);
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_resend
 功能描述  : 重发omci消息
 输入参数  : hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_resend(hi_void *pv_data)
{
	hi_ushort16 us_tci;
	hi_void *pv_ackdata = HI_NULL;
	struct hi_gpon_omci_msg msg;
	uint32_t len;

	us_tci = HI_OMCI_PROC_GET_TCI((hi_uchar8 *)pv_data);
	if (us_tci & HI_OMIC_PROC_TCI_PRI_MASK) {
		pv_ackdata = g_uc_omci_lsthigh;
	} else {
		pv_ackdata = g_uc_omci_lstlow;
	}

	if (HI_OMCI_PROC_GET_DEVID(pv_data) == HI_OMCI_PROC_DEVICEID_STD) {
		*(uint16_t *)((uint8_t *)pv_ackdata + HI_OMCI_PROC_MSGSTCONTEXT_LEN + 2) = htons(HI_OMCI_CPCS_SDU_LENGTH);
		len = HI_OMCI_PROC_MSGSTCONTEXT_LEN + HI_OMCI_PROC_MSGSTTAIL_LEN;
	} else {
		len = HI_OMCI_PROC_HEADLEN_EXT + HI_OMCI_PROC_EXTRSP_GET_SIZE(pv_ackdata) + HI_OMCI_EXT_MIC_LEN;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	hi_omci_dbg_pkt(pv_ackdata, HI_OMCI_DBG_DIR_OUT);
	msg.data = pv_ackdata;
	msg.len = len;
	return hi_omci_proc_send_omci_msg(&msg);
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_sendrsp
 功能描述  : 发送omci回复消息
 输入参数  : hi_omci_proc_msg_s *pst_inmsg
             hi_omci_proc_stdrsp_msg_s *pst_outmsg
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_sendrsp(hi_void *pv_data, hi_void *pv_outdata)
{
	hi_omci_proc_msg_type_u u_msgtype;
	struct hi_gpon_omci_msg msg;
	uint32_t rsp_len;

	/*judge if this message need to response*/
	u_msgtype.uc_msgtype = HI_OMCI_PROC_GET_MSGTYPE((hi_uchar8 *)pv_data);
	if (HI_OMCI_NOREQ_ACK == u_msgtype.st_msgtype.uc_ar) {
		hi_omci_proc_clearlst(pv_data);
		hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	HI_OS_MEMCPY_S(pv_outdata, sizeof(hi_omci_proc_msg_head_s), pv_data, sizeof(hi_omci_proc_msg_head_s));

	u_msgtype.st_msgtype.uc_db = HI_OMCI_DB_VALUE;
	u_msgtype.st_msgtype.uc_ar = HI_OMCI_NOREQ_ACK;
	u_msgtype.st_msgtype.uc_ak = HI_OMCI_REQ_ACK;
	HI_OMCI_PROC_SET_MSGTYPE((hi_uchar8 *)pv_outdata, u_msgtype.uc_msgtype);

	if (HI_OMCI_PROC_GET_DEVID(pv_data) == HI_OMCI_PROC_DEVICEID_STD) {
		*(uint16_t *)((uint8_t *)pv_outdata + HI_OMCI_PROC_MSGSTCONTEXT_LEN + 2) = htons(HI_OMCI_CPCS_SDU_LENGTH);
		rsp_len = HI_OMCI_PROC_MSGSTCONTEXT_LEN + HI_OMCI_PROC_MSGSTTAIL_LEN;
	} else {
		rsp_len = HI_OMCI_PROC_HEADLEN_EXT + HI_OMCI_PROC_EXTRSP_GET_SIZE(pv_outdata) + HI_OMCI_EXT_MIC_LEN;
	}

	hi_omci_proc_recordlst(pv_outdata);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	msg.data = pv_outdata;
	msg.len = rsp_len;
	return hi_omci_proc_send_omci_msg(&msg);
}

/******************************************************************************
 Function    : hi_omci_proc_alarm_seq_increase
 Description : alarm sequence number increase
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
static hi_void hi_omci_proc_alarm_seq_increase()
{
	g_uc_alarm_seq_num++;

	if (g_uc_alarm_seq_num == 0) {
		g_uc_alarm_seq_num = 1;
	}
}

/******************************************************************************
 Function    : hi_omci_proc_sendalarm
 Description : 发送告警消息
 Input Parm  : hi_ushort16 us_meid,
               hi_ushort16 us_instid,
               hi_ushort16 us_bitmap
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_proc_sendalarm(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_ushort16 us_bitmap)
{
	hi_omci_proc_msg_head_s *pst_head;
	hi_omci_proc_msg_type_u u_msgtype;
	struct hi_gpon_omci_msg msg;
	hi_void *pv_outdata = g_uc_omci_reportbuf;

	HI_OS_MEMSET_S(pv_outdata, HI_OMCI_PROC_MSG_MAXLEN, 0, HI_OMCI_PROC_MSG_MAXLEN);
	HI_OS_MEMSET_S(&u_msgtype, sizeof(u_msgtype), 0, sizeof(u_msgtype));
	pst_head = (hi_omci_proc_msg_head_s *)pv_outdata;

	u_msgtype.st_msgtype.uc_db = HI_OMCI_DB_VALUE;
	u_msgtype.st_msgtype.uc_ar = HI_OMCI_NOREQ_ACK;
	u_msgtype.st_msgtype.uc_ak = HI_OMCI_NOREQ_ACK;
	u_msgtype.st_msgtype.uc_mt = HI_OMCI_PRO_NOTIFICATIONS_ALARM_E;

	pst_head->uc_deviceid = HI_OMCI_PROC_DEVICEID_STD;
	pst_head->uc_msgtype = u_msgtype.uc_msgtype;
	pst_head->us_entityclass = htons(us_meid);
	pst_head->us_instance = htons(us_instid);

	*(hi_ushort16 *)HI_OMCI_PROC_STD_GET_CONTENT((hi_uchar8 *)pv_outdata) = htons(us_bitmap);
	*(hi_uchar8 *)((hi_uchar8 *)pv_outdata + HI_OMCI_PROC_MSGSTCONTEXT_LEN - 1) = g_uc_alarm_seq_num;

	hi_omci_proc_alarm_seq_increase();

	hi_omci_dbg_pkt(pv_outdata, HI_OMCI_DBG_DIR_OUT);
	msg.data = pv_outdata;
	msg.len = HI_OMCI_PROC_MSGSTCONTEXT_LEN + HI_OMCI_PROC_MSGSTTAIL_LEN;
	return hi_omci_proc_send_omci_msg(&msg);
}

/******************************************************************************
 Function    : hi_omci_proc_sendavc
 Description : 发送AVC消息
 Input Parm  : hi_ushort16 us_meid,
               hi_ushort16 us_instid,
               hi_ushort16 us_bitmap
               hi_void *pv_data
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_proc_sendavc(hi_ushort16 us_meid, hi_ushort16 us_instid,
			      hi_ushort16 us_bitmap, hi_void *pv_data, hi_uint32 ui_len)
{
	hi_omci_proc_msg_head_s *pst_head;
	hi_omci_proc_msg_type_u u_msgtype;
	struct hi_gpon_omci_msg msg;
	hi_void *pv_outdata;
	hi_uint32 ui_size = HI_OMCI_PROC_MSGSTCONTEXT_LEN + HI_OMCI_PROC_MSGSTTAIL_LEN;
	hi_uint32 ui_ret;

	pv_outdata = hi_os_malloc(ui_size);
	if (HI_NULL == pv_outdata) {
		return HI_RET_MALLOC_FAIL;
	}

	HI_OS_MEMSET_S(pv_outdata, ui_size, 0, ui_size);
	HI_OS_MEMSET_S(&u_msgtype, sizeof(u_msgtype), 0, sizeof(u_msgtype));
	pst_head = (hi_omci_proc_msg_head_s *)pv_outdata;

	u_msgtype.st_msgtype.uc_db = HI_OMCI_DB_VALUE;
	u_msgtype.st_msgtype.uc_ar = HI_OMCI_NOREQ_ACK;
	u_msgtype.st_msgtype.uc_ak = HI_OMCI_NOREQ_ACK;
	u_msgtype.st_msgtype.uc_mt = HI_OMCI_PRO_NOTIFICATIONS_AVC_E;

	pst_head->uc_deviceid = HI_OMCI_PROC_DEVICEID_STD;
	pst_head->uc_msgtype = u_msgtype.uc_msgtype;
	pst_head->us_entityclass = htons(us_meid);
	pst_head->us_instance = htons(us_instid);

	*(hi_ushort16 *)HI_OMCI_PROC_STD_GET_CONTENT(pv_outdata) = htons(us_bitmap);

	if (HI_NULL != pv_data) {
		HI_OS_MEMCPY_S(HI_OMCI_PROC_STD_GET_SETCONTENT(pv_outdata), ui_len, pv_data, ui_len);
	}

	hi_omci_dbg_pkt(pv_outdata, HI_OMCI_DBG_DIR_OUT);

	msg.data = pv_outdata;
	msg.len = ui_size;
	ui_ret = hi_omci_proc_send_omci_msg(&msg);
	hi_os_free(pv_outdata);
	return ui_ret;
}

/******************************************************************************
 Function    : hi_omci_proc_sendtestresult
 Description : 发送test result消息
 Input Parm  : hi_ushort16 us_meid,
               hi_ushort16 us_instid,
               hi_ushort16 测试结果数据
 Output Parm : N/A
 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_proc_sendtestresult(hi_ushort16 us_meid, hi_ushort16 us_instid, hi_void *pv_data)
{
	hi_omci_proc_msg_head_s *pst_head;
	hi_omci_proc_msg_type_u u_msgtype;
	struct hi_gpon_omci_msg msg;
	hi_void *pv_outdata = g_uc_omci_outbuf;

	HI_OS_MEMSET_S(&u_msgtype, sizeof(u_msgtype), 0, sizeof(u_msgtype));
	pst_head = (hi_omci_proc_msg_head_s *)pv_outdata;

	u_msgtype.st_msgtype.uc_db = HI_OMCI_DB_VALUE;
	u_msgtype.st_msgtype.uc_ar = HI_OMCI_NOREQ_ACK;
	u_msgtype.st_msgtype.uc_ak = HI_OMCI_NOREQ_ACK;
	u_msgtype.st_msgtype.uc_mt = HI_OMCI_PRO_NOTIFICATIONS_TESTRESULT_E;

	pst_head->uc_deviceid = HI_OMCI_PROC_DEVICEID_STD;
	pst_head->uc_msgtype = u_msgtype.uc_msgtype;
	pst_head->us_entityclass = htons(us_meid);
	pst_head->us_instance = htons(us_instid);

	HI_OS_MEMCPY_S(HI_OMCI_PROC_STD_GET_CONTENT((hi_uchar8 *)pv_outdata), sizeof(g_uc_omci_outbuf), pv_data,
		       HI_OMCI_PROC_MSGSTCONTEXT_LEN);

	hi_omci_dbg_pkt(pv_outdata, HI_OMCI_DBG_DIR_OUT);

	msg.data = pv_outdata;
	msg.len = HI_OMCI_PROC_MSGSTCONTEXT_LEN + HI_OMCI_PROC_MSGSTTAIL_LEN;
	return hi_omci_proc_send_omci_msg(&msg);
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_sendmsg
 功能描述  : 发送omci消息
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_sendmsg(hi_void *pv_data, hi_void *pv_outdata)
{
	hi_omci_proc_msg_type_u  u_msgtype;
	struct hi_gpon_omci_msg msg;
	uint32_t len;

	HI_OS_MEMCPY_S(pv_outdata, sizeof(hi_omci_proc_msg_head_s), pv_data, sizeof(hi_omci_proc_msg_head_s));

	u_msgtype.st_msgtype.uc_db = HI_OMCI_DB_VALUE;
	u_msgtype.st_msgtype.uc_ar = HI_OMCI_NOREQ_ACK;
	u_msgtype.st_msgtype.uc_ak = HI_OMCI_REQ_ACK;
	u_msgtype.st_msgtype.uc_mt = HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data);
	HI_OMCI_PROC_SET_MSGTYPE((hi_uchar8 *)pv_outdata, u_msgtype.uc_msgtype);

	if (HI_OMCI_PROC_GET_DEVID(pv_data) == HI_OMCI_PROC_DEVICEID_STD) {
		*(uint16_t *)((uint8_t *)pv_outdata + HI_OMCI_PROC_MSGSTCONTEXT_LEN + 2) = htons(HI_OMCI_CPCS_SDU_LENGTH);
		len = HI_OMCI_PROC_MSGSTCONTEXT_LEN + HI_OMCI_PROC_MSGSTTAIL_LEN;
	} else {
		len = HI_OMCI_PROC_HEADLEN_EXT + HI_OMCI_PROC_EXTRSP_GET_SIZE(pv_outdata) + HI_OMCI_EXT_MIC_LEN;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	hi_omci_dbg_pkt(pv_outdata, HI_OMCI_DBG_DIR_OUT);
	msg.data = pv_outdata;
	msg.len = len;
	return hi_omci_proc_send_omci_msg(&msg);
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_sendfail
 功能描述  : 发送omci回复消息错误
 输入参数  : hi_omci_proc_msg_s *pst_inmsg
             hi_omci_proc_stdrsp_msg_s *pst_outmsg
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_sendfail(hi_void *pv_data, hi_void *pv_outdata, hi_uchar8 uc_result)
{
	hi_uchar8 uc_devid;
	hi_ushort16 us_size = 1;

	hi_os_printf("\r\n Entity[%04u]\n", HI_OMCI_PROC_GET_ENTITY((hi_uchar8 *)pv_data));
	hi_os_printf("Return fail 0x%x result to OLT .\n", uc_result);

	uc_devid = HI_OMCI_PROC_GET_DEVID((hi_uchar8 *)pv_data);
	if (HI_OMCI_PROC_DEVICEID_STD == uc_devid) {
		HI_OMCI_PROC_STDRSP_SET_RESULT((hi_uchar8 *)pv_outdata, uc_result);
	} else {
		HI_OMCI_PROC_EXTRSP_SET_RESULT((hi_uchar8 *)pv_outdata, uc_result);
		HI_OMCI_PROC_EXTRSP_SET_SIZE((hi_uchar8 *)pv_outdata, us_size);
	}

	hi_omci_lib_systrace(HI_RET_SUCC, uc_devid, 0, 0, 0);
	return hi_omci_proc_sendmsg(pv_data, pv_outdata);
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_checktci
 功能描述  : OMCI消息TCI域检查
 输入参数  : hi_void* pst_msg
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_checktci(hi_void *pv_data)
{
	hi_ushort16 us_lsttci, us_tci;
	hi_ushort16 *pus_lsttci;
	us_tci = HI_OMCI_PROC_GET_TCI((hi_uchar8 *)pv_data);
	if (us_tci & HI_OMIC_PROC_TCI_PRI_MASK) {
		pus_lsttci = (hi_ushort16 *)g_uc_omci_lsthigh;
		us_lsttci = ntohs(*pus_lsttci);
	} else {
		pus_lsttci = (hi_ushort16 *)g_uc_omci_lstlow;
		us_lsttci = ntohs(*pus_lsttci);
	}

	if (us_lsttci != us_tci) {
		hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return HI_RET_SUCC;
	}

	hi_omci_lib_systrace(HI_RET_OMCI_REPEAT_PACKET_E, 0, 0, 0, 0);
	return (hi_uint32)HI_RET_OMCI_REPEAT_PACKET_E;
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_checkmsg
 功能描述  : OMCI消息检查
 输入参数  : hi_void* pv_msg
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_checkmsg(hi_void *pv_data, hi_void *pv_outdata,
				hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uint32   ui_ret;

	/* check repeat*/
	ui_ret = hi_omci_proc_checktci(pv_data);
	if (HI_RET_SUCC != ui_ret) {
		/*repeat send simple ack*/
		hi_omci_proc_resend(pv_data);
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	/* check range */
	if (HI_OMCI_PRO_MSG_TYPE_MAX_E <= HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data)) {
		hi_omci_proc_sendfail(pv_data, pv_outdata, HI_OMCI_PRO_ERR_PARA_ERR_E);
		hi_omci_lib_systrace(HI_RET_OUTRANG, 0, 0, 0, 0);
		return HI_RET_OUTRANG;
	}

	/* check database*/
	ui_ret = hi_omci_mib_checkdb(pv_data, pv_outdata, pst_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_proc_sendfail(pv_data, pv_outdata, ui_ret);
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/*****************************************************************************
 函 数 名  : hi_omci_proc_act
 功能描述  : 执行action动作
 输入参数  : hi_void *pv_data
             hi_void *pv_outdata
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_act(hi_void *pv_data, hi_void *pv_outdata,
			   hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uchar8 uc_devid = HI_OMCI_PROC_GET_DEVID((hi_uchar8 *)pv_data);

	if (HI_OMCI_PROC_DEVICEID_STD == uc_devid) {
		hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
		return hi_omci_mib_std(pv_data, pv_outdata, pst_attrlist);
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_mib_ext(pv_data, pv_outdata, pst_attrlist);
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_actmanage
 功能描述  : 消息动作处理
 输入参数  : hi_void *pv_data
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_actmanage(hi_void *pv_data, hi_void *pv_outdata,
				 hi_omci_mib_attrlist_s *pst_attrlist)
{
	hi_uchar8   uc_msgtype;
	hi_uint32   ui_ret;

	/*消息处理*/
	ui_ret = hi_omci_proc_act(pv_data, pv_outdata, pst_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_proc_sendfail(pv_data, pv_outdata, ui_ret);
		/*debug out*/
		hi_omci_dbg_pkt(pv_outdata, HI_OMCI_DBG_DIR_OUT);
		hi_omci_lib_systrace(ui_ret, ui_ret, 0, 0, 0);
		return ui_ret;
	}

	uc_msgtype = HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data);
	/* MIB counter increase */
	hi_omci_mib_datasync(uc_msgtype);

	/* send rsp message*/
	ui_ret = hi_omci_proc_sendrsp(pv_data, pv_outdata);
	/*debug out*/
	hi_omci_dbg_pkt(pv_outdata, HI_OMCI_DBG_DIR_OUT);

	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}

static hi_uint32 action_downloading(hi_void *pv_data, hi_void *pv_outdata, hi_uchar8 uc_msgtype, hi_uint32 ui_len)
{
	hi_uint32 ui_ret;
	hi_omci_dbg_pkt(pv_data, HI_OMCI_DBG_DIR_IN);
	/* check repeat */
	ui_ret = hi_omci_proc_checktci(pv_data);
	if (HI_RET_SUCC != ui_ret) {
		/* repeat send simple ack */
		hi_omci_proc_resend(pv_data);
		printf("skip repeat upgrade pkt\n");
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	ui_ret = hi_omci_upgrade_loading(pv_data, pv_outdata, ui_len);
	if (HI_RET_SUCC != ui_ret) {
		HI_OS_MEMCPY_S(pv_outdata, sizeof(g_uc_omci_outbuf), pv_data, sizeof(hi_omci_proc_msg_head_s));

		HI_OMCI_PROC_SET_MSGTYPE((hi_uchar8 *)pv_outdata, (uc_msgtype + 32));
		hi_omci_proc_sendfail(pv_data, pv_outdata, HI_OMCI_PRO_ERR_PROCESS_ERR_E);

		return ui_ret;
	}

	uc_msgtype = HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data);
	/* MIB counter increase */
	hi_omci_mib_datasync(uc_msgtype);

	/* send rsp message*/
	ui_ret = hi_omci_proc_sendrsp(pv_data, pv_outdata);
	hi_omci_proc_recordlst(pv_outdata);
	return ui_ret;
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_msg
 功能描述  : OMCI消息处理
 输入参数  : hi_void *pv_data
             hi_uint32 ui_inlen
 输出参数  : 无
 返 回 值  : hi_uint32
*****************************************************************************/
hi_uint32 hi_omci_proc_msg(hi_void *pv_data, hi_uint32 ui_len)
{
	hi_uint32 ui_ret;
	hi_void  *pv_outdata = g_uc_omci_outbuf;
	hi_uchar8 uc_msgtype = 0;
	hi_uint32 ui_i;
	hi_omci_mib_attrlist_s st_attrlist;

	HI_OS_MEMSET_S(pv_outdata, HI_OMCI_PROC_MSG_MAXLEN * sizeof(hi_uchar8), 0, HI_OMCI_PROC_MSG_MAXLEN * sizeof(hi_uchar8));
	HI_OS_MEMSET_S(&st_attrlist, sizeof(hi_omci_mib_attrlist_s), 0, sizeof(hi_omci_mib_attrlist_s));

	uc_msgtype = HI_OMCI_PROC_GET_MT((hi_uchar8 *)pv_data);
	if ((HI_OMCI_PROC_GET_DEVID((uint8_t *)pv_data) == HI_OMCI_PROC_DEVICEID_EXT) &&
	    (uc_msgtype < HI_OMCI_PRO_MSG_TYPE_MAX_E)) {
		HI_OMCI_PROC_EXTRSP_SET_SIZE(pv_outdata, g_omci_extend_rsp_len[uc_msgtype]);
	}
	/*为了提高软件升级速度，升级动作在这里进行处理*/
	if (HI_OMCI_PRO_ACTIONS_DOWNLOADING_E == uc_msgtype)
		return action_downloading(pv_data, pv_outdata, uc_msgtype, ui_len);

	/*debug info*/
	hi_omci_dbg_pkt(pv_data, HI_OMCI_DBG_DIR_IN);

	/*get attribute list by meid */
	st_attrlist.us_meid   = HI_OMCI_PROC_GET_ENTITY((hi_uchar8 *)pv_data);
	st_attrlist.us_instid = HI_OMCI_PROC_GET_INST((hi_uchar8 *)pv_data);
	st_attrlist.uc_opt    = HI_OMCI_ATT_R_W_S_E;
	st_attrlist.us_mask   = HI_OMCI_MIB_ATTRMASK;

	/* 判断是否是extend omci实例 */
	for (ui_i = 0; ui_i < HI_OMCI_EXTEND_TABLE_COUNT; ui_i++) {
		if (g_aui_omci_extend[ui_i] == st_attrlist.us_meid) {
			gui_extend_omci_cnt++;
			break;
		}
	}

	/*如果实体不支持，return OK*/
	ui_ret = hi_omci_sql_entity_check_meid(st_attrlist.us_meid);
	if (HI_RET_SUCC != ui_ret) {
		uc_msgtype = HI_OMCI_PROC_GET_MT(pv_data);
		hi_omci_mib_datasync(uc_msgtype);
		ui_ret = hi_omci_proc_sendrsp(pv_data, pv_outdata);
		/*debug out*/
		hi_omci_debug("Entity[%hu] not support\n", st_attrlist.us_meid);
		hi_omci_dbg_pkt(pv_outdata, HI_OMCI_DBG_DIR_OUT);
		hi_omci_proc_recordlst(pv_outdata);
		return ui_ret;
	}

	ui_ret = hi_omci_sql_attr_getlist(&st_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, st_attrlist.us_totalsize, 0, 0, 0);
		return ui_ret;
	}

	/*check attribute*/
	ui_ret = hi_omci_proc_checkmsg(pv_data, pv_outdata, &st_attrlist);
	if (HI_RET_SUCC != ui_ret) {
		hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
		return ui_ret;
	}

	/*proc action*/
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return hi_omci_proc_actmanage(pv_data, pv_outdata, &st_attrlist);
}

void hi_omci_init_alarm_seq(void)
{
	g_uc_alarm_seq_num = 1;
}

/*****************************************************************************
 函 数 名  : hi_omci_proc_first
 功能描述  : 对第一次接收OMCI进行初始化
 输入参数  : hi_void
 输出参数  : 无
 返 回 值  : hi_void
*****************************************************************************/
hi_void hi_omci_proc_first(hi_void)
{
	static hi_uchar8 uc_status = 0;

	if (0 == uc_status) {
		uc_status++;
		/*operate database*/
	}

	hi_omci_lib_systrace(HI_RET_SUCC, uc_status, 0, 0, 0);
	return;
}

hi_uint32 hi_omci_proc_init(hi_void)
{
	hi_uint32 ui_ret;

	HI_OS_MEMSET_S(&g_st_globalpara, sizeof(hi_omci_proc_global_s),   0, sizeof(hi_omci_proc_global_s));
	HI_OS_MEMSET_S(g_uc_omci_outbuf, HI_OMCI_PROC_MSG_MAXLEN,   0, HI_OMCI_PROC_MSG_MAXLEN);
	HI_OS_MEMSET_S(g_uc_omci_lsthigh, HI_OMCI_PROC_MSG_MAXLEN,  0, HI_OMCI_PROC_MSG_MAXLEN);
	HI_OS_MEMSET_S(g_uc_omci_lstlow, HI_OMCI_PROC_MSG_MAXLEN,   0, HI_OMCI_PROC_MSG_MAXLEN);
	hi_omci_init_alarm_seq();

	ui_ret = hi_omci_mib_init();

	hi_omci_lib_systrace(ui_ret, 0, 0, 0, 0);
	return ui_ret;
}

#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
