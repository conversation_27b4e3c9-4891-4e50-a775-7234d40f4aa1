/******************************************************************************

                  Copyright (C), 2012-2013, HSAN

 ******************************************************************************
  Filename   : hi_omci_libext.c
  Version    : 初稿
  Author     : owen
  Creation   : 2014-1-13
  Description: OMCI加载扩展库
******************************************************************************/

/*****************************************************************************
 *                                INCLUDE                                    *
 *****************************************************************************/
#include <dlfcn.h>
#include "hi_omci_lib.h"
//#include "yajl/yajl_tree.h"

/*****************************************************************************
 *                                LOCAL_DEFINE                               *
 *****************************************************************************/
#define HI_OMCI_LIBEXT_FILEPATH "/etc/omci/hi_omci.json"
#define HI_OMCI_LIBEXT_LD_ENV_NAME "LD_LIBRARY_PATH"

#define HI_OMCI_LIBEXT_PATH_NAME_MAX        256
//#define HI_OMCI_LIBEXT_PATH_FILE_NAME_MAX   (HI_OMCI_LIBEXT_PATH_NAME_MAX + 64)
//#define HI_OMCI_LIBEXT_PATH_SO_TPL          "libhi_%s.so"
#define HI_OMCI_LIBEXT_FUNC_NAME_MAX        64

#define HI_OMCI_LIBEXT_FILE_BUF_SIZE 1024
//#define HI_OMCI_LIBEXT_ERR_BUF_SIZE  1024

/*****************************************************************************
 *                                LOCAL_TYPEDEF                              *
 *****************************************************************************/
typedef struct {
	hi_list_head st_list;
	hi_char8 ac_name[HI_OMCI_LIBEXT_PATH_NAME_MAX];
} hi_omci_libext_path_s;

typedef hi_int32(* hi_omci_libext_construct)(hi_void);

/*****************************************************************************
 *                                LOCAL_VARIABLE                             *
 *****************************************************************************/
static hi_list_head g_st_omci_libext_path_list;

static hi_char8 gac_libname[HI_OMCI_LIBEXT_PATH_NAME_MAX];

/*****************************************************************************
 *                                LOCAL_FUNCTION                             *
 *****************************************************************************/

/******************************************************************************
 Function    : __omci_libext_path_add
 Description : 记录环境变量
 Input Parm  : const hi_char8 *pc_env,
               hi_uint32 ui_begin,
               hi_uint32 ui_end
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
static hi_int32 __omci_libext_path_add(const hi_char8 *pc_env, hi_uint32 ui_begin, hi_uint32 ui_end)
{
	hi_omci_libext_path_s *pst_path;
	hi_uint32 ui_index;
	hi_uint32 ui_cnt;

	if (ui_end == ui_begin) {
		return HI_RET_SUCC;
	}

	pst_path = (hi_omci_libext_path_s *)hi_os_malloc(sizeof(hi_omci_libext_path_s));
	if (pst_path == HI_NULL) {
		hi_omci_lib_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	for (ui_index = ui_begin, ui_cnt = 0; ui_index < ui_end; ui_index++) {
		if (ui_cnt >= (HI_OMCI_LIBEXT_PATH_NAME_MAX - 1)) {
			hi_os_free(pst_path);
			hi_omci_lib_systrace(HI_RET_FAIL, 0, 0, 0, 0);
			return (hi_int32)HI_RET_FAIL;
		}

		pst_path->ac_name[ui_cnt] = pc_env[ui_index];
		ui_cnt++;
	}
	pst_path->ac_name[ui_cnt] = '\0';
	hi_list_add_tail(&pst_path->st_list, &g_st_omci_libext_path_list);

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

/******************************************************************************
 Function    : __omci_libext_env_clr
 Description : 清空环境变量记录
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
static hi_void __omci_libext_env_clr()
{
	hi_omci_libext_path_s *pst_path = HI_NULL;
	hi_omci_libext_path_s *pst_temp = HI_NULL;

	hi_list_for_each_entry_safe(pst_path, pst_temp, &g_st_omci_libext_path_list, st_list) {
		hi_list_del(&pst_path->st_list);
		hi_os_free(pst_path);
	}

	return;
}

/******************************************************************************
 Function    : __omci_libext_env_get
 Description : 获取环境变量
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
static hi_int32 __omci_libext_env_get()
{
	hi_char8 *pc_env;
	hi_uint32 ui_index = 0;
	hi_uint32 ui_begin = 0;
	hi_int32 i_ret;

	hi_list_init_head(&g_st_omci_libext_path_list);

	pc_env = getenv(HI_OMCI_LIBEXT_LD_ENV_NAME);
	if (HI_NULL == pc_env) {
		hi_omci_lib_systrace(HI_RET_FAIL, 0, 0, 0, 0);
		return (hi_int32)HI_RET_FAIL;
	}

	while (pc_env[ui_index] != '\0') {
		if (pc_env[ui_index] == ':') {
			i_ret = __omci_libext_path_add(pc_env, ui_begin, ui_index);
			if (i_ret != HI_RET_SUCC) {
				__omci_libext_env_clr();
				hi_omci_lib_systrace(i_ret, 0, 0, 0, 0);
				return i_ret;
			}

			ui_begin = ui_index + 1;
		}

		ui_index++;
	}

	i_ret = __omci_libext_path_add(pc_env, ui_begin, ui_index);
	if (i_ret != HI_RET_SUCC) {
		__omci_libext_env_clr();
		hi_omci_lib_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
#if 0
/******************************************************************************
 Function    : __omci_libext_init_proc
 Description : 执行库的初始化
 Input Parm  : hi_char8 *pc_fname, 文件名
               hi_char8 *pc_name   模块名
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
static hi_int32 __omci_libext_init_proc(hi_char8 *pc_fname, hi_char8 *pc_name)
{
	hi_void *pv_handler;
	hi_char8 *pc_func;
	hi_omci_libext_construct pf_construct;
	hi_int32 i_ret;

	pv_handler = dlopen(pc_fname, RTLD_NOW);
	if (HI_NULL == pv_handler) {
		hi_omci_lib_systrace(HI_RET_FILE_OPEN_FAIL, 0, 0, 0, 0);
		return HI_RET_FILE_OPEN_FAIL;
	}

	pc_func = hi_os_malloc(HI_OMCI_LIBEXT_FUNC_NAME_MAX);
	if (HI_NULL == pc_func) {
		dlclose(pv_handler);
		hi_omci_lib_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	HI_OS_SNPRINTF_S(pc_func, HI_OMCI_LIBEXT_FUNC_NAME_MAX, HI_OMCI_LIBEXT_FUNC_NAME_MAX - 1, "hi_%s_init", pc_name);
	pf_construct = dlsym(pv_handler, pc_func);

	hi_os_free(pc_func);

	if (HI_NULL == pf_construct) {
		dlclose(pv_handler);
		hi_omci_lib_systrace(HI_RET_FILE_READ_FAIL, 0, 0, 0, 0);
		return HI_RET_FILE_READ_FAIL;
	}

	i_ret = pf_construct();
	if (i_ret != HI_RET_SUCC) {
		dlclose(pv_handler);
		hi_omci_lib_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

	/* 将文件句柄保存 */
	i_ret = hi_omci_sql_entity_extlib_set(pc_name, pv_handler);
	if (i_ret != HI_RET_SUCC) {
		dlclose(pv_handler);
		hi_omci_lib_systrace(i_ret, 0, 0, 0, 0);
		return i_ret;
	}

	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
/******************************************************************************
 Function    : __omci_libext_proc
 Description : 执行扩展库的初始化
 Input Parm  : hi_char8 *pc_name 模块名
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
static hi_int32 __omci_libext_proc(hi_char8 *pc_name)
{
	hi_omci_libext_path_s *pst_path = HI_NULL;
	hi_char8 *pc_fname;
	hi_int32 i_ret;

	pc_fname = (hi_char8 *)hi_os_malloc(HI_OMCI_LIBEXT_PATH_FILE_NAME_MAX);
	if (HI_NULL == pc_fname) {
		hi_omci_lib_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	hi_list_for_each_entry(pst_path, &g_st_omci_libext_path_list, st_list) {
		HI_OS_SNPRINTF_S(pc_fname, HI_OMCI_LIBEXT_PATH_FILE_NAME_MAX, HI_OMCI_LIBEXT_PATH_FILE_NAME_MAX - 1,
				 "%s/"HI_OMCI_LIBEXT_PATH_SO_TPL,
				 pst_path->ac_name, pc_name);
		if (hi_os_access(pc_fname, R_OK) == 0) {
			/* 临时保存库文件名，代函数注册时使用 */
			HI_OS_STRCPY_S(gac_libname, sizeof(gac_libname), pc_name);

			i_ret = __omci_libext_init_proc(pc_fname, pc_name);
			hi_os_free(pc_fname);
			return i_ret;
		}
	}

	hi_os_free(pc_fname);
	hi_omci_lib_systrace(HI_RET_ITEM_NOTEXIST, 0, 0, 0, 0);
	return HI_RET_ITEM_NOTEXIST;
}
/******************************************************************************
 Function    : __omci_libext_parse
 Description : 分析脚本文件，提取要加载的库
 Input Parm  : hi_void *pv_data 脚本文件数据
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
static hi_int32 __omci_libext_parse(hi_void *pv_data)
{
	yajl_val st_root;
	yajl_val st_libs;
	hi_char8 *pac_libs[2] = {"lib", HI_NULL};
	hi_void *pv_errbuf;
	hi_uint32 ui_num;
	hi_uint32 ui_index;
	hi_char8 *pc_libname;
	hi_int32 i_ret;

	pv_errbuf = hi_os_malloc(HI_OMCI_LIBEXT_ERR_BUF_SIZE);
	if (HI_NULL == pv_errbuf) {
		hi_omci_lib_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	st_root = yajl_tree_parse(pv_data, pv_errbuf, HI_OMCI_LIBEXT_ERR_BUF_SIZE);
	if (HI_NULL == st_root) {
		hi_os_free(pv_errbuf);
		hi_omci_lib_systrace(HI_RET_FAIL, 0, 0, 0, 0);
		return HI_RET_FAIL;
	}

	hi_os_free(pv_errbuf);

	st_libs = yajl_tree_get(st_root, (const hi_char8 **)pac_libs, yajl_t_any);
	if (HI_NULL == st_libs) {

		yajl_tree_free(st_root);
		hi_omci_lib_systrace(HI_RET_FAIL, 0, 0, 0, 0);
		return HI_RET_FAIL;
	}

	ui_num = YAJL_GET_ARRAY(st_libs)->len;
	for (ui_index = 0; ui_index < ui_num; ui_index++) {
		pc_libname = YAJL_GET_STRING(YAJL_GET_ARRAY(st_libs)->values[ui_index]);
		i_ret = __omci_libext_proc(pc_libname);
		if (i_ret != HI_RET_SUCC) {
			hi_os_printf("\r\n%s init fail \r\n", pc_libname);
			yajl_tree_free(st_root);
			hi_omci_lib_systrace(HI_RET_FAIL, 0, 0, 0, 0);
			return HI_RET_FAIL;
		} else {
			hi_os_printf("\r\n%s init succ \r\n", pc_libname);
		}
	}

	yajl_tree_free(st_root);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}
#endif

/*****************************************************************************
 *                                PUBLIC_FUNCTION                            *
 *****************************************************************************/

/******************************************************************************
 Function    : hi_omci_libext_name_get
 Description : 获取当前的库文件名
 Input Parm  : 无
 Output Parm : hi_char8 *pc_name 输出的库文件名
 Return      : N/A
******************************************************************************/
/*hi_void hi_omci_libext_name_get(hi_char8 *pc_name, hi_uint32 len)
{
    HI_OS_STRCPY_S(pc_name, len, gac_libname);
    return;
}*/

/*****************************************************************************
 *                                INIT_EXIT                                  *
 *****************************************************************************/
/******************************************************************************
 Function    : hi_omci_libext_init
 Description : 扩展库初始化加载
 Input Parm  : 无
 Output Parm : N/A

 Return      : HI_RET_SUCC/HI_RET_FAIL
******************************************************************************/
hi_int32 hi_omci_libext_init()
{
	FILE *pf_config;
	size_t rd;
	hi_void *pv_buf;
	hi_int32 i_ret;

	HI_OS_MEMSET_S(gac_libname, sizeof(gac_libname), 0, sizeof(gac_libname));

	pf_config = hi_os_fopen(HI_OMCI_LIBEXT_FILEPATH, "r");
	if (HI_NULL == pf_config) {
		//hi_omci_lib_systrace(HI_RET_FILE_OPEN_FAIL, 0, 0, 0, 0);
		return HI_RET_FILE_OPEN_FAIL;
	}

	pv_buf = hi_os_malloc(HI_OMCI_LIBEXT_FILE_BUF_SIZE);
	if (HI_NULL == pv_buf) {
		hi_os_fclose(pf_config);
		hi_omci_lib_systrace(HI_RET_MALLOC_FAIL, 0, 0, 0, 0);
		return HI_RET_MALLOC_FAIL;
	}

	/* 打开配置文件，文件不能大于1K字节 */
	rd = hi_os_fread(pv_buf, 1, HI_OMCI_LIBEXT_FILE_BUF_SIZE - 1, pf_config);
	if ((rd == 0 && !feof(pf_config)) || rd >= HI_OMCI_LIBEXT_FILE_BUF_SIZE - 1) {
		hi_os_free(pv_buf);
		hi_os_fclose(pf_config);
		hi_omci_lib_systrace(HI_RET_FILE_READ_FAIL, 0, 0, 0, 0);
		return HI_RET_FILE_READ_FAIL;
	}

	i_ret = __omci_libext_env_get();
	if (i_ret != HI_RET_SUCC) {
		hi_os_free(pv_buf);
		hi_os_fclose(pf_config);
		hi_omci_lib_systrace(HI_RET_FAIL, 0, 0, 0, 0);
		return (hi_int32)HI_RET_FAIL;
	}
#if 0
	i_ret = __omci_libext_parse(pv_buf);
	if (i_ret != HI_RET_SUCC) {
		__omci_libext_env_clr();
		hi_os_free(pv_buf);
		hi_os_fclose(pf_config);
		hi_omci_lib_systrace(HI_RET_FAIL, 0, 0, 0, 0);
		return HI_RET_FAIL;
	}
#endif
	__omci_libext_env_clr();
	hi_os_free(pv_buf);
	hi_os_fclose(pf_config);
	hi_omci_lib_systrace(HI_RET_SUCC, 0, 0, 0, 0);
	return HI_RET_SUCC;
}

