include $(HI_EXT_CONFIG)
#===============================================================================
# export sub dir
#===============================================================================
HI_SUB_DIR :=
HI_SUB_DIR += source/sql
HI_SUB_DIR += source/mib
HI_SUB_DIR += source/protocol
HI_SUB_DIR += source/upgrade
HI_SUB_DIR += source/log
HI_SUB_DIR += source/diagnose
HI_SUB_DIR += source/init
#===============================================================================
# export lib
#===============================================================================
HI_LOC_LIB += -lhi_basic -lhi_timer -lhi_util -lsqlite3 -lhi_omci_tapi
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/../omci_ctl/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/../omci_tapi/include
HI_LOC_U_INCLUDE += -I$(HI_HISI_LINUX_DIR)/net/pon/include
HI_LOC_U_INCLUDE += -I$(HI_OPENSRC_DIR)/sqlite3/sqlite-autoconf-3081101
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/util/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/ipc/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/timer/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/pon/api/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/pon/sml/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/network/common/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/basic/include/os
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/upgrade/include
HI_LOC_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/cfm/cfm_lib
#
#===============================================================================
# target
#===============================================================================
TARGET 		= libhi_omci_sql.so
TARGET_TYPE	= so

include $(HI_HGW_SCRIPT_DIR)/app.mk
