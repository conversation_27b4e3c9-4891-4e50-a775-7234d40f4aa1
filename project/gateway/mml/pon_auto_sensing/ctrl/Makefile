include $(HI_EXT_CONFIG)
#===============================================================================
# export sub dir
#===============================================================================
HI_SUB_DIR :=
#===============================================================================
# export lib
#===============================================================================
HI_LOC_LIB += -lhi_auto_sensing_adp
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/ipc/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/pon/api/include
HI_LOC_U_INCLUDE += -I$(HI_HISI_LINUX_DIR)/net/pon/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/../adapt
#
#===============================================================================
# target
#===============================================================================
TARGET 		= libhi_auto_sensing.so
TARGET_TYPE	= so

include $(HI_HGW_SCRIPT_DIR)/app.mk

