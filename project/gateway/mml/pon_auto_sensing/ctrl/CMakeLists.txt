include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

set(USERAPP_NAME hi_auto_sensing)

set(USERAPP_TARGET_TYPE so)

set(USERAPP_PRIVATE_SRC
    hi_auto_sensing.c
)

set(USERAPP_PRIVATE_INC
    ../adapt
    ${HGW_FWK_DIR}/include
    ${HGW_BASIC_DIR}/include
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_SERVICE_DIR}/pon/api/include
    ${CONFIG_LINUX_DRV_DIR}/net/pon/include
)

set(USERAPP_PRIVATE_LIB
    hi_auto_sensing_adp
    hi_ipc hi_basic
    hi_owal_ssf openwrt_lib
)

set(USERAPP_PRIVATE_DEFINE)

set(USERAPP_PRIVATE_COMPILE
    ${HGW_COMMON_CFLAGS}
)

build_app_feature()
