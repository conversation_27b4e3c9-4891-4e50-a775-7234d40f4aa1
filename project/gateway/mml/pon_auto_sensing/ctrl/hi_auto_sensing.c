/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create:
 * History:
 */
#include "hi_auto_sensing_adp.h"
#include "hi_ipc.h"
#include "hi_pon_gpon_api.h"
#include "hi_pon_epon_api.h"
#include <pthread.h>

static HI_PTHREAD_T g_pthreadp = 0;
static enum hi_pon_mode g_switch_mode = HI_PON_MODE_RESV;

static hi_void hi_auto_sensing_get_los_state(hi_uint32 *state)
{
    enum hi_pon_mode mode = HI_PON_MODE_RESV;
    enum hi_auto_sensing_adp_type type;
    HI_IPC_CALL("hi_pon_get_mode", &mode, sizeof(mode));
    type = hi_auto_sensing_adp_get_type(mode);
    if (type == HI_AUTO_SENSING_ADP_TYPE_EPON) {
        HI_IPC_CALL("hi_epon_get_los_state", state, sizeof(*state));
        return;
    } else if (type == HI_AUTO_SENSING_ADP_TYPE_GPON) {
        HI_IPC_CALL("hi_gpon_get_los_state", state, sizeof(*state));
    }
}

static hi_int32 hi_auto_sensing_check_los(hi_uint32 check_los_cnt)
{
    hi_uint32 los_state = HI_TRUE;
    static hi_uint32 unlos_cnt = 0;
    hi_auto_sensing_get_los_state(&los_state);
    if (los_state == HI_TRUE) {
        unlos_cnt = 0;
        return HI_RET_FAIL;
    }
    unlos_cnt++;
    if (unlos_cnt < check_los_cnt) {
        return HI_RET_FAIL;
    }
    return HI_RET_SUCC;
}

static hi_void hi_auto_sensing_close_reg(enum hi_pon_mode mode)
{
    enum hi_auto_sensing_adp_type type;
    type = hi_auto_sensing_adp_get_type(mode);
    if (type == HI_AUTO_SENSING_ADP_TYPE_EPON) {
        HI_IPC_CALL("hi_epon_exit");
    } else if (type == HI_AUTO_SENSING_ADP_TYPE_GPON) {
        HI_IPC_CALL("hi_gpon_exit");
    }
}

static hi_void hi_auto_sensing_start_epon_reg(hi_void)
{
    struct hi_epon_para epon_para = {0};
    struct hi_auto_sensing_adp_reg_info info = {0};

    hi_auto_sensing_adp_get_reg_info(&info);
    memcpy_s(epon_para.mac, sizeof(epon_para.mac), info.mac, sizeof(info.mac));
    HI_IPC_CALL("hi_epon_start", &epon_para);
}

static hi_void hi_auto_sensing_start_gpon_reg(hi_void)
{
    struct hi_gpon_para reg_param = {0};
    struct hi_auto_sensing_adp_reg_info info = {0};
    hi_int32 ret = HI_RET_SUCC;
    hi_auto_sensing_adp_get_reg_info(&info);
    ret |= memcpy_s(reg_param.sn, sizeof(reg_param.sn), info.sn, sizeof(info.sn));
    ret |= strncpy_s((char *)reg_param.pwd, sizeof(reg_param.pwd), (char *)info.pwd, sizeof(info.pwd));
    ret |= strncpy_s((char *)reg_param.regid, sizeof(reg_param.regid), (char *)info.regid, sizeof(info.regid));
    HI_IPC_CALL("hi_gpon_start", &reg_param);
    if (ret != HI_RET_SUCC) {
        printf("auto sensing start gpon reg fail");
    }
}

static hi_void hi_auto_sensing_start_reg(enum hi_pon_mode mode)
{
    enum hi_auto_sensing_adp_type type;
    type = hi_auto_sensing_adp_get_type(mode);
    if (type == HI_AUTO_SENSING_ADP_TYPE_EPON) {
        hi_auto_sensing_start_epon_reg();
    } else if (type == HI_AUTO_SENSING_ADP_TYPE_GPON) {
        hi_auto_sensing_start_gpon_reg();
    }
}

static hi_uint32 hi_auto_sensing_check_reg_state(hi_void)
{
    enum hi_pon_mode mode = HI_PON_MODE_RESV;
    enum hi_auto_sensing_adp_type type;
    struct hi_auto_sensing_adp_attr attr = {0};
    hi_uint32 state = HI_FALSE;

    HI_IPC_CALL("hi_pon_get_mode", &mode, sizeof(mode));
    type = hi_auto_sensing_adp_get_type(mode);
    if (type == HI_AUTO_SENSING_ADP_TYPE_EPON) {
        HI_IPC_CALL("hi_epon_get_sensing_flag", &state, sizeof(state));
    } else if (type == HI_AUTO_SENSING_ADP_TYPE_GPON) {
        HI_IPC_CALL("hi_gpon_get_sensing_flag", &state, sizeof(state));
    }
    if (state == HI_FALSE) {
        return HI_FALSE;
    }
    if (g_switch_mode != HI_PON_MODE_RESV) {
        hi_auto_sensing_adp_get_attr(&attr);
        hi_auto_sensing_close_reg(g_switch_mode);
        hi_os_sleep(attr.switch_wait_time);
        HI_IPC_CALL("hi_pon_set_mode", &g_switch_mode, sizeof(g_switch_mode));
        hi_auto_sensing_adp_init_reg(g_switch_mode);
        hi_os_sleep(attr.switch_wait_time);
        hi_auto_sensing_start_reg(g_switch_mode);
        g_switch_mode = HI_PON_MODE_RESV;
        hi_os_sleep(attr.reg_check_time);
    }
    return HI_TRUE;
}

static hi_void hi_pon_auto_sensing_switch(hi_uint32 wait_time, hi_uint32 reg_time)
{
    hi_uint32 capability;
    enum hi_pon_mode mode;
    enum hi_pon_mode prev_mode = HI_PON_MODE_RESV;
    hi_uint32 first_flag = HI_FALSE;
    if (HI_IPC_CALL("hi_pon_get_sensing_capa", &capability, sizeof(capability)) != HI_RET_SUCC) {
        return;
    }
    HI_IPC_CALL("hi_pon_get_mode", &prev_mode, sizeof(prev_mode));
    if ((g_switch_mode == HI_PON_MODE_RESV) && (prev_mode != HI_PON_MODE_RESV)) {
        first_flag = HI_TRUE;
    }
    for (mode = HI_PON_MODE_EPON; mode < HI_PON_MODE_RESV; mode++) {
        if (((capability >> mode) & 0x1) && (((g_switch_mode == HI_PON_MODE_RESV) && (prev_mode != mode))
                                             || (mode > g_switch_mode) || ((g_switch_mode != HI_PON_MODE_RESV) &&
                                                                           ((capability >> (g_switch_mode + 1)) == 0) && (mode != g_switch_mode)))) {
            g_switch_mode = mode;
            break;
        }
    }
    if (mode >= HI_PON_MODE_RESV) {
        return;
    }
    hi_auto_sensing_close_reg(prev_mode);
    hi_os_sleep(wait_time);
    if (first_flag == HI_TRUE) {
        hi_auto_sensing_adp_exit_reg(prev_mode);
        hi_os_sleep(wait_time);
    }
    HI_IPC_CALL("hi_pon_set_mode", &g_switch_mode, sizeof(g_switch_mode));
    hi_auto_sensing_start_reg(g_switch_mode);
    hi_os_sleep(reg_time);
}

static hi_int32 hi_auto_sensing_proc(hi_void)
{
    struct hi_auto_sensing_adp_attr attr = {0};
    while (1) {
        hi_auto_sensing_adp_get_attr(&attr);
        if ((attr.enable == HI_FALSE) ||
            (hi_auto_sensing_check_los(attr.check_los_cnt) != HI_RET_SUCC) ||
            (hi_auto_sensing_check_reg_state() == HI_TRUE)) {
            hi_os_sleep(attr.check_time);
            continue;
        }
        hi_pon_auto_sensing_switch(attr.switch_wait_time, attr.reg_check_time);
        hi_os_sleep(attr.check_time);
    }
    return HI_RET_SUCC;
}

hi_int32 hi_auto_sensing_init()
{
    hi_int32 ret;
    pthread_attr_t attr;
    if (g_pthreadp > 0) {
        return HI_RET_ALREADYINIT;
    }
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    ret = pthread_create(&g_pthreadp, HI_NULL, (hi_os_threadfun_t)hi_auto_sensing_proc, HI_NULL);
    if (ret != HI_RET_SUCC) {
        return ret;
    }
    pthread_attr_destroy(&attr);
    return HI_RET_SUCC;
}

hi_void hi_auto_sensing_exit()
{
    pthread_cancel(g_pthreadp);
    g_pthreadp = 0;
}
