/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create:
 * History:
 */
#ifdef CONFIG_PLATFORM_OPENWRT
#include "uci.h"
#endif
#include "hi_ipc.h"
#include "hi_sysinfo.h"
#include "hi_cfm_api.h"
#include "hi_uci.h"
#include "hi_auto_sensing_adp.h"
static struct hi_auto_sensing_adp_attr g_sensing_attr = {
    .enable = 0,
    .check_time = 1,
    .switch_wait_time = 5,
    .check_los_cnt = 15,
    .reg_check_time = 15,
};
enum hi_auto_sensing_adp_type hi_auto_sensing_adp_get_type(enum hi_pon_mode mode)
{
    switch (mode) {
        case HI_PON_MODE_GPON:
        case HI_PON_MODE_GPON_S:
        case HI_PON_MODE_10GGPON_U2DOT5G:
        case HI_PON_MODE_10GGPON_SYM:
                            return HI_AUTO_SENSING_ADP_TYPE_GPON;
        case HI_PON_MODE_EPON:
        case HI_PON_MODE_10GEPON_1G:
        case HI_PON_MODE_10GEPON_U2DOT5G:
        case HI_PON_MODE_10GEPON_SYM:
            return HI_AUTO_SENSING_ADP_TYPE_EPON;
        default:
            return HI_AUTO_SENSING_ADP_TYPE_RESV;
    }
}

#ifdef CONFIG_PLATFORM_OPENWRT
static hi_int32 hi_auto_sensing_get_mode_str(enum hi_pon_mode mode, char *mode_str, int32_t size)
{
    int32_t loop;
    struct {
        uint32_t idx;
        const char *mode_str;
    } matrix[] = {
        {HI_PON_MODE_GPON,              "gpon"},
        {HI_PON_MODE_GPON_S,            "gpon_s"},
        {HI_PON_MODE_10GGPON_U2DOT5G,   "xgpon_u2d5"},
        {HI_PON_MODE_10GGPON_SYM,       "xgpon_sym"},
        {HI_PON_MODE_EPON,              "epon"},
        {HI_PON_MODE_10GEPON_1G,        "xepon_u1"},
        {HI_PON_MODE_10GEPON_U2DOT5G,   "xepon_u2d5"},
        {HI_PON_MODE_10GEPON_SYM,       "xepon_sym"},
    };

    for (loop = 0; loop < sizeof(matrix)/sizeof(matrix[0]); loop++) {
        if (mode == matrix[loop].idx) {
            if (sprintf_s(mode_str, size, matrix[loop].mode_str) < 0) {
                printf("auto sensing get mode str sprintf error\n");
                return HI_RET_FAIL;
            }
            return HI_RET_SUCC;
        }
    }
    printf("pon mode not support(%u)\n", mode);
    return HI_RET_FAIL;
}

static hi_void hi_auto_sensing_ada_update_cfg_pon(enum hi_pon_mode mode)
{
    char buf[64] = {0};
    const char *path_str = "/etc/config/pon.Adapt.mode";
    char mode_str[16] = {0};

    if (hi_auto_sensing_get_mode_str(mode, mode_str, sizeof(mode_str)) != HI_RET_SUCC)
        return;

    if (sprintf_s(buf, sizeof(buf), "%s=%s", path_str, mode_str) < 0) {
        printf("auto sensing sprintf error\n");
        return;
    }

    if (hi_uci_set(buf) != UCI_OK)
        printf("auto sensing set pon_adapt error\n");

    printf("auto sensing set pon_adapt = %s\n", mode_str);
}
#else
static hi_void hi_auto_sensing_ada_update_sysinfo_mode(enum hi_pon_mode mode)
{
    switch (mode) {
        case HI_PON_MODE_GPON:
            cfmSetObj("sysinfo.pon_adapt", "gpon");
            system("hi_ucm set sysinfo.pon_adapt gpon");
            break;
        case HI_PON_MODE_GPON_S:
            cfmSetObj("sysinfo.pon_adapt", "gpon_s");
            system("hi_ucm set sysinfo.pon_adapt gpon_s");
            break;
        case HI_PON_MODE_10GGPON_U2DOT5G:
            cfmSetObj("sysinfo.pon_adapt", "xgpon_u2d5");
            system("hi_ucm set sysinfo.pon_adapt xgpon_u2d5");
            break;
        case HI_PON_MODE_10GGPON_SYM:
            cfmSetObj("sysinfo.pon_adapt", "xgpon_sym");
            system("hi_ucm set sysinfo.pon_adapt xgpon_sym");
            break;
        case HI_PON_MODE_EPON:
            cfmSetObj("sysinfo.pon_adapt", "epon");
            system("hi_ucm set sysinfo.pon_adapt epon");
            break;
        case HI_PON_MODE_10GEPON_1G:
            cfmSetObj("sysinfo.pon_adapt", "xepon_u1");
            system("hi_ucm set sysinfo.pon_adapt xepon_u1");
            break;
        case HI_PON_MODE_10GEPON_U2DOT5G:
            cfmSetObj("sysinfo.pon_adapt", "xepon_u2d5");
            system("hi_ucm set sysinfo.pon_adapt xepon_u2d5");
            break;
        case HI_PON_MODE_10GEPON_SYM:
            cfmSetObj("sysinfo.pon_adapt", "xepon_sym");
            system("hi_ucm set sysinfo.pon_adapt xepon_sym");
            break;
        default:
            printf("pon mode not support(%u)\n", mode);
            break;
    }
}
#endif
hi_void hi_auto_sensing_adp_init_reg(enum hi_pon_mode mode)
{
    enum hi_auto_sensing_adp_type type;
    type = hi_auto_sensing_adp_get_type(mode);
    if (type >= HI_AUTO_SENSING_ADP_TYPE_RESV)
        return;

    if (type == HI_AUTO_SENSING_ADP_TYPE_GPON)
        HI_IPC_CALL("hi_omci_start");
    else if (type == HI_AUTO_SENSING_ADP_TYPE_EPON)
        HI_IPC_CALL("hi_oam_start");

#ifdef CONFIG_PLATFORM_OPENWRT
    hi_auto_sensing_ada_update_cfg_pon(mode);
#else
    hi_auto_sensing_ada_update_sysinfo_mode(mode);
#endif
}

hi_void hi_auto_sensing_adp_exit_reg(enum hi_pon_mode mode)
{
    enum hi_auto_sensing_adp_type type;
    type = hi_auto_sensing_adp_get_type(mode);
    if (type == HI_AUTO_SENSING_ADP_TYPE_GPON) {
        HI_IPC_CALL("hi_omci_stop");
    } else if (type == HI_AUTO_SENSING_ADP_TYPE_EPON) {
        HI_IPC_CALL("hi_oam_stop");
    }
}

HI_DEF_IPC(hi_auto_sensing_adp_set_attr, struct hi_auto_sensing_adp_attr *, attr)
{
    g_sensing_attr = *attr;
    return HI_RET_SUCC;
}

HI_DEF_IPC(hi_auto_sensing_adp_get_attr, struct hi_auto_sensing_adp_attr *, attr)
{
    *attr = g_sensing_attr;
    return HI_RET_SUCC;
}

HI_DEF_IPC(hi_auto_sensing_adp_get_reg_info, struct hi_auto_sensing_adp_reg_info *, info)
{
    hi_int32 ret = HI_RET_SUCC;
    hi_sysinfo_data_s sysinfo = {0};
    if (HI_IPC_CALL("hi_sysinfo_data_get", &sysinfo) != HI_RET_SUCC) {
        return HI_RET_FAIL;
    }
    ret |= memcpy_s(info->mac, sizeof(info->mac), sysinfo.auc_llid0_mac, sizeof(sysinfo.auc_llid0_mac));
    ret |= memcpy_s(info->sn, sizeof(info->sn), sysinfo.st_auid.auc_sn, sizeof(sysinfo.st_auid.auc_sn));
    ret |= strncpy_s((char *)info->pwd, sizeof(info->pwd), (char *)sysinfo.st_auid.auc_pwd,
                     sizeof(sysinfo.st_auid.auc_pwd));
    ret |= strncpy_s((char *)info->regid, sizeof(info->regid), sysinfo.ac_reg_id, sizeof(info->regid));
    if (ret != HI_RET_SUCC) {
        printf("auto sensing get reg info fail");
    }
    return HI_RET_SUCC;
}
