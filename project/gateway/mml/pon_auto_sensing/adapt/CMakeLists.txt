include(${CONFIG_CMAKE_DIR}/toolchains/${CONFIG_TOOLCHAIN_LINUX_APP}.cmake)

set(USERAPP_NAME hi_auto_sensing_adp)

set(USERAPP_TARGET_TYPE so)

set(USERAPP_PRIVATE_SRC
    hi_auto_sensing_adp.c
)

# 依赖的头文件
set(USERAPP_PRIVATE_INC
    ${HGW_FWK_DIR}/include
    ${HGW_BASIC_DIR}/include
    ${HGW_FWK_DIR}/ipc/include
    ${HGW_FWK_DIR}/uci/include
    ${HGW_SERVICE_DIR}/pon/api/include
    ${HGW_SERVICE_DIR}/dms/cfm/cfm_lib
    ${HGW_SERVICE_DIR}/dms/sysinfo/include
    ${CONFIG_LINUX_DRV_DIR}/net/pon/include
)

set(USERAPP_PRIVATE_LIB
    hi_cfm hi_ipc hi_uci
    hi_owal_ssf openwrt_lib
)

# 自定义宏
set(USERAPP_PRIVATE_DEFINE)

# 自定义编译选项
set(USERAPP_PRIVATE_COMPILE
    ${HGW_COMMON_CFLAGS}
)

build_app_feature()
