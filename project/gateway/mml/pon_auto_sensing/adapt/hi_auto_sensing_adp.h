/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description:
 * Author:
 * Create:
 * History:
 */

#ifndef HI_AUTO_SENSING_ADP_H
#define HI_AUTO_SENSING_ADP_H
#include "hi_uspace.h"
#include "hi_pon_glb_api.h"

#define HI_AUTO_SENDING_PWD_LEN 10
#define HI_AUTO_SENDING_SN_LEN 8
#define HI_AUTO_SENDING_REGID_LEN 36
#define HI_AUTO_SENDING_MAC_LEN 6

enum hi_auto_sensing_adp_type {
    HI_AUTO_SENSING_ADP_TYPE_EPON = 0,
    HI_AUTO_SENSING_ADP_TYPE_GPON,
    HI_AUTO_SENSING_ADP_TYPE_RESV
};

struct hi_auto_sensing_adp_attr {
    hi_uint32 enable;
    hi_uint32 check_time;
    hi_uint32 switch_wait_time;
    hi_uint32 check_los_cnt;
    hi_uint32 reg_check_time;
};

struct hi_auto_sensing_adp_reg_info {
    hi_uchar8 sn[HI_AUTO_SENDING_SN_LEN];
    hi_uchar8 pwd[HI_AUTO_SENDING_PWD_LEN];
    hi_ushort16 resv;
    hi_uchar8 regid[HI_AUTO_SENDING_REGID_LEN];
    hi_uchar8 mac[HI_AUTO_SENDING_MAC_LEN];
    hi_ushort16 resv1;
};

hi_int32 hi_auto_sensing_adp_set_attr(struct hi_auto_sensing_adp_attr *attr);
hi_int32 hi_auto_sensing_adp_get_attr(struct hi_auto_sensing_adp_attr *attr);
hi_int32 hi_auto_sensing_adp_get_reg_info(struct hi_auto_sensing_adp_reg_info *info);
enum hi_auto_sensing_adp_type hi_auto_sensing_adp_get_type(enum hi_pon_mode mode);
hi_void hi_auto_sensing_adp_init_reg(enum hi_pon_mode mode);
hi_void hi_auto_sensing_adp_exit_reg(enum hi_pon_mode mode);
#endif
