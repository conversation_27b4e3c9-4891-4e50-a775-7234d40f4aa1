#===============================================================================
# local varible
#===============================================================================
HI_LOC_INCLUDE += -I$(HI_GATEWAY_DIR)/mml/oam/oam_service/include
HI_LOC_INCLUDE	+= -I$(HI_HISI_BASIC_DIR)/include
HI_LOC_INCLUDE	+= -I$(HI_HISI_BASIC_DIR)/linux
HI_LOC_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/include
HI_LOC_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/ipc/include
HI_LOC_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/msgcenter/include
HI_LOC_INCLUDE += -I$(HI_HISI_LINUX_DIR)/net/pon/include
#===============================================================================
# target
#===============================================================================
TARGET 	     = hi_koam

$(TARGET)-y+= source/hi_kernel_ctcoam_reg.o
$(TARGET)-y+= source/hi_kernel_ctcoam_main.o
$(TARGET)-y+= source/hi_kernel_stdoam_reg.o
$(TARGET)-y+= source/hi_kernel_oam_filelog.o

HI_EXTRA_SYMBOLS +=  $(shell ls | find $(HI_HISI_LINUX_DIR)/net/pon/core -name Module.symvers) \
					 $(shell ls | find $(HI_GATEWAY_DIR)/ssf/msgcenter -name Module.symvers)
include $(HI_HGW_SCRIPT_DIR)/module.mk
