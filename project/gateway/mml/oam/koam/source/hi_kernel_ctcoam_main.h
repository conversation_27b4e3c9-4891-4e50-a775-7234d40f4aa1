/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_kernel_ctcoam_main.h
  Version       : Initial Draft
  Last Modified :
  Description   : hi_kernel_ctcoam_main.c header file
  Function List :
******************************************************************************/
#ifdef __cplusplus
#if __cplusplus
extern "C" {
#endif
#endif /* __cplusplus */


#ifndef __HI_KERNEL_CTCOAM_MAIN_H__
#define __HI_KERNEL_CTCOAM_MAIN_H__

#define hi_kernel_ctcoam_dbg_err_print(fmt, args...) do { \
		if ((hi_ctcoam_get_dbg_flag() >> 0) & 0x1) \
			printk("[CTCOAM ERR]"fmt, ##args); \
	} while (0)

#define hi_kernel_ctcoam_dbg_info_print(fmt, args...) do { \
		if ((hi_ctcoam_get_dbg_flag() >> 1) & 0x1) \
			printk("[CTCOAM INFO]"fmt, ##args); \
	} while (0)

int32_t hi_ctcoam_get_dbg_flag(void);
#endif /* __HI_KERNEL_CTCOAM_MAIN_H__ */


#ifdef __cplusplus
#if __cplusplus
}
#endif
#endif /* __cplusplus */
