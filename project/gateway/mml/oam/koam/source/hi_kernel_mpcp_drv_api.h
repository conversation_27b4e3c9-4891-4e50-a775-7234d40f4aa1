/******************************************************************************

Copyright (c) HiSilicon (Shanghai) Technologies Co., Ltd. 2022-2023. All rights reserved.

 ******************************************************************************
  File Name     : hi_kernel_mpcp_drv_api.h
  Version       : Initial Draft
  Created       : 2013/6/19
  Last Modified :
  Description   : API describtion of mpcp driver
******************************************************************************/
#ifndef __HI_KERNEL_MPCP_DRV_API_H__
#define __HI_KERNEL_MPCP_DRV_API_H__

typedef hi_uint32(*hi_kernel_mpcp_alarm_hook)(hi_uint32 ui_llid, hi_uchar8 uc_state);


hi_int32  hi_kernel_mpcp_stdoamregfailevn_write(hi_uchar8 uc_llidindex);
#define hi_kernel_mpcp_writeregeventproc hi_kernel_mpcp_stdoamregfailevn_write

#endif /* __HI_KERNEL_MPCP_DRV_API_H__ */


