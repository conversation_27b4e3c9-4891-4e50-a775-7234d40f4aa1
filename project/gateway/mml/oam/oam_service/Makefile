include $(HI_EXT_CONFIG)

#===============================================================================
# export sub dir
#===============================================================================
HI_SUB_DIR :=
HI_SUB_DIR += source

#===============================================================================
# export lib
#===============================================================================
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/util/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/basic/include/os
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/sysinfo/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/board/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/optical
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/pon/sml/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/pon/api/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/network/common/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/ipc/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/upgrade/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/network/qos/u_space/include

HI_LOC_INCLUDE += -I$(HI_HISI_LINUX_DIR)/net/pon/include
HI_LOC_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/cfm/cfm_lib
#===============================================================================
# target
#===============================================================================
TARGET 		= libhi_oam_service.so
TARGET_TYPE	= so

include $(HI_HISI_GW_APP_SCRIPT_DIR)/app.mk
