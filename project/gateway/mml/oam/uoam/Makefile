include $(HI_EXT_CONFIG)
#===============================================================================
# export sub dir
#===============================================================================
HI_SUB_DIR :=
HI_SUB_DIR += source

#===============================================================================
# export lib
#===============================================================================
#HI_LOC_LIB :=-lhi_ipc -lhi_hal -lhi_wan -lcJSON
HI_LOC_LIB :=-lhi_ipc -lhi_sml -lhi_oam_service -lhi_basic -lhi_board -lhi_sysinfo -lhi_upgrade
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CUR)/../oam_service/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/timer/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/sysinfo/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/dms/upgrade/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/ipc/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/notifier/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/ssf/ioreactor/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/pon/sml/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/network/common/include
HI_LOC_U_INCLUDE += -I$(HI_GATEWAY_DIR)/service/pon/api/include
HI_LOC_U_INCLUDE += -I$(HI_HISI_LINUX_DIR)/net/pon/include
#
#===============================================================================
# target
#===============================================================================
TARGET 		= libhi_oam.so
TARGET_TYPE	= so

include $(HI_HISI_GW_APP_SCRIPT_DIR)/app.mk


