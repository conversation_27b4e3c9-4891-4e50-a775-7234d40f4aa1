hsan_add_subdirectory(cmcc_smart ${CONFIG_GATEWAY_MML_CMCC_SMART})
hsan_add_subdirectory(cu_smart ${CONFIG_GATEWAY_MML_CU_SMART})
hsan_add_subdirectory(ctc_smart ${CONFIG_GATEWAY_MML_CTC_SMART})
hsan_add_subdirectory(cwmp ${CONFIG_GATEWAY_MML_CWMP})
hsan_add_subdirectory(ctc_smart_rt ${CONFIG_GATEWAY_MML_CTC_SMART_RT})
hsan_add_subdirectory(cmcc_smart_rt ${CONFIG_GATEWAY_MML_CMCC_SMART_RT})
hsan_add_subdirectory(cu_smart_rt ${CONFIG_GATEWAY_MML_CU_SMART_RT})
hsan_add_subdirectory(oam ${CONFIG_GATEWAY_MML_OAM})
hsan_add_subdirectory(omci ${CONFIG_GATEWAY_MML_OMCI})
hsan_add_subdirectory(pon_auto_sensing ${CONFIG_GATEWAY_MML_AUTO_SENSING})

hsan_add_subdirectory(uni_omci ${CONFIG_GATEWAY_MML_UNIPON})

if(CONFIG_GATEWAY_MML_WEB)
    if (CONFIG_PRODUCT_TYPE STREQUAL router)
        add_subdirectory(web_router)
    else()
        add_subdirectory(web)
    endif()
endif()
