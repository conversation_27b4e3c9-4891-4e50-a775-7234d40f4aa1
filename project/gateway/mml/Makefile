include $(HI_EXT_CONFIG)

#===============================================================================
# local varible
#===============================================================================
HI_LOC_U_INCLUDE += -I$(HI_LOC_FWK_DIR)/util/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CML_DIR)/mib/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CML_DIR)/odl/include
HI_LOC_U_INCLUDE += -I$(HI_LOC_CML_DIR)/odlapi/include

#===============================================================================
# sub target
#===============================================================================

ifeq ($(HI_PRODUCT_CHIP_NAME), $(filter $(HI_PRODUCT_CHIP_NAME), luofu))
	hi_subdir-y += web_router
else
	hi_subdir-y += web
endif

hi_subdir-$(HI_BUILD_SOLUTION_GATEWAY_MML_CWMP) += cwmp
hi_subdir-$(HI_BUILD_SOLUTION_GATEWAY_NET_PON) += omci
hi_subdir-$(HI_BUILD_SOLUTION_GATEWAY_NET_PON) += oam
hi_subdir-$(HI_BUILD_SOLUTION_GATEWAY_NET_PON) += pon_auto_sensing
hi_subdir-$(HI_BUILD_SOLUTION_GATEWAY_MML_SMART) += cmcc_smart

include $(HI_HISI_GW_APP_SCRIPT_DIR)/subdir.mk
