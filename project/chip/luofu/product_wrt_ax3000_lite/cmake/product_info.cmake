#===============================================================================
# @brief    cmake product info
# Copyright (c) CompanyNameMagicTag 2022-2022. All rights reserved.
#===============================================================================
set(CONFIG_CHIP_NAME "luofu")
set(CONFIG_CHIP_VERSION "V200")

set(CONFIG_PRODUCT_NAME "wrt_ax3000_lite")
set(CONFIG_PRODUCT_TYPE "router")
set(CONFIG_PRODUCT_VERSION "ChenTang_1.2.13.linux0")

set(CONFIG_FLASH_TYPE "nand")
set(CONFIG_FLASH_PAGE_SIZE 2048)
set(CONFIG_FLASH_BLOCK_SIZE 131072)

set(CONFIG_UBOOT_SIZE_MAX 1048576)

set(CONFIG_UBOOT_ARCH_TYPE arm)
set(CONFIG_LINUX_UBOOT_TYPE "arm")
set(CONFIG_LINUX_KERNEL_TYPE "arm")
set(CONFIG_LINUX_APP_TYPE "arm")
set(CONFIG_HI_MEMTESTER_ARCH_TYPE arm)
set(CONFIG_LINUX_TEXT_OFFSET "0x80608000")
set(CONFIG_ESBC_LINK_ADDR 0x80010000)
set(CONFIG_MEMTESTER_LINK_ADDR 0x11341000)
set(CONFIG_GREENBOX_ARCH_TYPE arm)
set(CONFIG_GREENBOX_LINK_ADDR 0x11343000)
set(CONFIG_OVERLAY_BOARD_TYPE
    ax3000_lite_wrt
    ax3000_lite3_wrt
)
set(CONFIG_DDR_SIZE 0x8000000)
set(CONFIG_DDR_RESV_SIZE 0x500000)
set(CONFIG_FLASH_SHARE_MEM_OFFSET 0x80600000)
set(CONFIG_BOOT_UNSECBOOT ON)
set(CONFIG_BUILD_IMAGE_HEAD ON)
set(CONFIG_LINUX_DEFCONFIG hsan_luofu_ax3000_lite_defconfig)
set(CONFIG_IP_VERSION_PRERT "v250")
set(CONFIG_IP_VERSION_HWDIO "1.0")
set(CONFIG_IP_VERSION_CRYP "2.0")
set(CONFIG_IP_VERSION_LSW_DP_SRC "v250")
set(CONFIG_IP_VERSION_LSW_DP_INC "v250")
set(CONFIG_IP_VERSION_LSW_PFE "v200")
set(CONFIG_IP_VERSION_NET_PIE "v250")
set(CONFIG_IP_VERSION_PCIE "v1")
set(CONFIG_IP_VERSION_PINCTRL "1.0")
set(CONFIG_IP_VERSION_EFUSE "1.0")

set(CONFIG_TOOLCHAIN_ESBC "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_UBOOT "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_LINUX_DRV "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_LINUX_APP "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_ATF "toolchain-hcc_arm64le")
set(CONFIG_TOOLCHAIN_TEE_DRV "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_TEE_SRV "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_HI_MEMTESTER "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_GREENBOX toolchain-arm-mix510-linux)

set(CONFIG_TOOLCHIAN_SEL_HCC_ARM64LE ON)
set(CONFIG_TOOLCHIAN_SEL_ARM_MIX510_LINUX ON)
set(CONFIG_TOOLCHIAN_SEL_ARM_CC_RISCV32_MUSl OFF)
set(CONFIG_TOOLCHIAN_SEL_ARM_XTENSA OFF)

set(CONFIG_LINUX_KERNEL_DIR
    ${OPEN_SOURCE_OPENWRT_BINARY_DIR}/openwrt/build_dir/target-arm-mix510-linux_musl/linux-hisilicon_${CONFIG_CHIP_NAME}/linux-5.10.201
)

set(CONFIG_COMPONENT_VENDORS OFF)
