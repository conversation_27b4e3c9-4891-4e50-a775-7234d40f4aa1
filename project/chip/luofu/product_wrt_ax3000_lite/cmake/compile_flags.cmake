#===============================================================================
# @brief    cmake component list
# Copyright (c) CompanyNameMagicTag 2022-2022. All rights reserved.
#===============================================================================
set(CONFIG_PUBLIC_COMPILE_FLAGS_CPU a9)
set(CONFIG_PUBLIC_COMPILE_FLAGS_FLOAT_ABI soft)
set(CONFIG_PUBLIC_COMPILE_FLAGS_FPU )

add_library(esbc_public_flags INTERFACE)
set(CONFIG_PUBLIC_ESBC_SEC_CFLAGS )
set(CONFIG_PUBLIC_ESBC_SEC_LDFLAGS )
target_compile_options(esbc_public_flags INTERFACE
    -Wall -marm -march=armv7-a
    -mno-unaligned-access
    -fno-aggressive-loop-optimizations
    -Os -mthumb -nostdlib
    ${CONFIG_PUBLIC_ESBC_SEC_CFLAGS}
)
target_link_options(esbc_public_flags INTERFACE
    -nostartfiles -nostdlib -nodefaultlibs
    -static -Bstatic -Ttext ${CONFIG_ESBC_LINK_ADDR}
    ${CONFIG_PUBLIC_ESBC_SEC_LDFLAGS}
)

add_library(uboot_public_flags INTERFACE)
set(CONFIG_PUBLIC_UBOOT_SEC_CFLAGS)
set(CONFIG_PUBLIC_UBOOT_SEC_LDFLAGS )
target_compile_options(uboot_public_flags INTERFACE
    -Wall -Werror -Wstack-protector -Os
    -D__KERNEL__ -D__UBOOT__ -Wstrict-prototypes
    -fno-builtin -ffreestanding -std=gnu11
    -fshort-wchar -fno-strict-aliasing -fno-PIE
    -g -fstack-usage -Werror=date-time -D__ARM__
    -marm -mno-thumb-interwork -mabi=aapcs-linux
    -mword-relocations -fno-pic -mno-unaligned-access
    -ffunction-sections -fdata-sections -fno-common
    -ffixed-r9 -msoft-float -pipe -march=armv7-a
    -D__LINUX_ARM_ARCH__=7 -mtune=generic-armv7-a
    -DCONFIG_DDR_SIZE=${CONFIG_DDR_SIZE}
    -DCONFIG_DDR_RESV_SIZE=${CONFIG_DDR_RESV_SIZE}
    ${CONFIG_PUBLIC_UBOOT_SEC_CFLAGS}
)
target_link_options(uboot_public_flags INTERFACE ${CONFIG_PUBLIC_UBOOT_SEC_LDFLAGS})
#
add_library(linux_public_flags INTERFACE)
set(CONFIG_PUBLIC_LINUX_SEC_CFLAGS -fstack-protector-strong)
set(CONFIG_PUBLIC_LINUX_SEC_LDFLAGS )
target_compile_options(linux_public_flags INTERFACE ${CONFIG_PUBLIC_LINUX_SEC_CFLAGS})
target_link_options(linux_public_flags INTERFACE ${CONFIG_PUBLIC_LINUX_SEC_LDFLAGS})
#
add_library(tee_os_public_flags INTERFACE)
set(CONFIG_PUBLIC_TEE_OS_SEC_CFLAGS )
set(CONFIG_PUBLIC_TEE_OS_SEC_LDFLAGS )
target_compile_options(tee_os_public_flags INTERFACE ${CONFIG_PUBLIC_TEE_OS_SEC_CFLAGS})
target_link_options(tee_os_public_flags INTERFACE ${CONFIG_PUBLIC_TEE_OS_SEC_LDFLAGS})
#
add_library(tee_service_public_flags INTERFACE)
set(CONFIG_PUBLIC_TEE_SRV_SEC_CFLAGS )
set(CONFIG_PUBLIC_TEE_SRV_SEC_LDFLAGS )
target_compile_options(tee_service_public_flags INTERFACE ${CONFIG_PUBLIC_TEE_SRV_SEC_CFLAGS})
target_link_options(tee_service_public_flags INTERFACE ${CONFIG_PUBLIC_TEE_SRV_SEC_LDFLAGS})
#
add_library(atf_public_flags INTERFACE)
set(CONFIG_PUBLIC_ATF_SEC_CFLAGS )
set(CONFIG_PUBLIC_ATF_SEC_LDFLAGS )
target_compile_options(atf_public_flags INTERFACE ${CONFIG_PUBLIC_ATF_SEC_CFLAGS})
target_link_options(atf_public_flags INTERFACE ${CONFIG_PUBLIC_ATF_SEC_LDFLAGS})
#
add_library(app_public_flags INTERFACE)
set(CONFIG_PUBLIC_APP_SEC_CFLAGS -fPIC -Os  -fstack-protector-strong)
set(CONFIG_PUBLIC_APP_SEC_LDFLAGS -Wl,-z,relro  -Wl,-z,now  -Wl,-z,noexecstack)
if(CONFIG_PUBLIC_COMPILE_FLAGS_FPU)
set(CONFIG_FPU_ARGS -mfpu=${CONFIG_PUBLIC_COMPILE_FLAGS_FPU})
endif()
target_compile_options(app_public_flags INTERFACE ${CONFIG_PUBLIC_APP_SEC_CFLAGS}
    -mcpu=cortex-${CONFIG_PUBLIC_COMPILE_FLAGS_CPU}
)
target_link_options(app_public_flags INTERFACE ${CONFIG_PUBLIC_APP_SEC_LDFLAGS})
target_compile_definitions(app_public_flags INTERFACE
    _FORTIFY_SOURCE=2
    CONFIG_PLATFORM_OPENWRT
)

add_library(bin_public_flags INTERFACE)
add_library(so_public_flags INTERFACE)
add_library(a_public_flags INTERFACE)
target_link_options(bin_public_flags INTERFACE -fPIE -pie)

add_library(hi_memtester_public_flags INTERFACE)
target_compile_options(hi_memtester_public_flags INTERFACE
    -Wall -O2 -fno-unwind-tables -fasm
    -march=armv7-a -mgeneral-regs-only
)
target_link_options(hi_memtester_public_flags INTERFACE
    -Bstatic -Ttext ${CONFIG_MEMTESTER_LINK_ADDR}
    -nostartfiles -nodefaultlibs
)
target_compile_definitions(hi_memtester_public_flags INTERFACE
    CONFIG_CHIP_$<UPPER_CASE:${CONFIG_CHIP_NAME}>
)

add_library(ddr_greenbox_public_flags INTERFACE)
target_compile_options(ddr_greenbox_public_flags INTERFACE
    -Wall -O2 -fno-unwind-tables -fasm
    -march=armv7-a
    -mtune=cortex-${CONFIG_PUBLIC_COMPILE_FLAGS_CPU}
    -mno-unaligned-access -mfloat-abi=soft
    -fno-aggressive-loop-optimizations
)
target_link_options(ddr_greenbox_public_flags INTERFACE
    -Bstatic -Ttext ${CONFIG_GREENBOX_LINK_ADDR}
    -nostartfiles
    -nodefaultlibs
)
target_compile_definitions(ddr_greenbox_public_flags INTERFACE
    CONFIG_CHIP_$<UPPER_CASE:${CONFIG_CHIP_NAME}>
)
