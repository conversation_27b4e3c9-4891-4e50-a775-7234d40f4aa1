#===============================================================================
# @brief    cmake mod list
# Copyright (c) CompanyNameMagicTag 2022-2022. All rights reserved.
#===============================================================================

#----SDK_DRIVERS----#
set(CONFIG_LINUX_DRV_BASIC ON)
set(CONFIG_LINUX_DRV_SECURE_C ON)

set(CONFIG_LINUX_DRV_CRG ON)
set(CONFIG_LINUX_DRV_RESET ON)
set(CONFIG_LINUX_DRV_CLK_CORRECTOR ON)
set(CONFIG_LINUX_DRV_CLK_MODIFIER OFF)
set(CONFIG_LINUX_DRV_CLK ON)
set(CONFIG_LINUX_DRV_WDG ON)
set(CONFIG_LINUX_DRV_CRYP OFF)
set(CONFIG_LINUX_DRV_PINCTRL ON)
set(CONFIG_LINUX_DRV_ALSA_DRV OFF)
set(CONFIG_LINUX_DRV_SYNC OFF)
set(CONFIG_LINUX_DRV_EFUSE ON)
set(CONFIG_LINUX_DRV_SYSCTRL ON)
set(CONFIG_LINUX_DRV_PCIE ON)
set(CONFIG_LINUX_DRV_MTD ON)
set(CONFIG_LINUX_DRV_CPU_LOADER OFF)
set(CONFIG_LINUX_DRV_RBT OFF)
set(CONFIG_LINUX_DRV_IPC OFF)
set(CONFIG_LINUX_DRV_DMA OFF)
set(CONFIG_LINUX_DRV_KDF OFF)
set(CONFIG_LINUX_DRV_TRNG OFF)
set(CONFIG_LINUX_DRV_JENT_RNG ON)
set(CONFIG_LINUX_DRV_MMC OFF)
set(CONFIG_LINUX_DRV_CEC OFF)
set(CONFIG_LINUX_DRV_HIFI OFF)
set(CONFIG_LINUX_DRV_IR OFF)
set(CONFIG_LINUX_DRV_NPU OFF)
set(CONFIG_LINUX_DRV_NET_MDIO OFF)
set(CONFIG_LINUX_DRV_NET_PCU OFF)
set(CONFIG_LINUX_DRV_NET_PIE OFF)
set(CONFIG_LINUX_DRV_NET_PIE_CLIPPER ON)
set(CONFIG_LINUX_DRV_NET_PIE_WOC ON)
set(CONFIG_LINUX_DRV_NET_WOE_WIFI OFF)
set(CONFIG_LINUX_DRV_NET_ETH_PHY OFF)
set(CONFIG_LINUX_DRV_NET_ETH_MAC OFF)
set(CONFIG_LINUX_DRV_NET_BOARDCOM_PHY OFF)
set(CONFIG_LINUX_DRV_NET_TEAM OFF)
set(CONFIG_LINUX_DRV_NET_PCS OFF)
set(CONFIG_LINUX_DRV_NET_MII_MISC OFF)
set(CONFIG_LINUX_DRV_NET_PSA OFF)
set(CONFIG_LINUX_DRV_NET_WOE OFF)
set(CONFIG_LINUX_DRV_NET_PON OFF)
set(CONFIG_LINUX_DRV_NET_CHBA OFF)
set(CONFIG_LINUX_DRV_LSW OFF)
set(CONFIG_LINUX_DRV_LSW_WOE OFF)
set(CONFIG_LINUX_DRV_LSW_WOEV2 OFF)
set(CONFIG_LINUX_DRV_LSW_PUB OFF)
set(CONFIG_LINUX_DRV_LSW_PSA OFF)
set(CONFIG_LINUX_DRV_LSW_PFE OFF)
set(CONFIG_LINUX_DRV_LSW_PFE_RT OFF)
set(CONFIG_LINUX_DRV_LSW_DP OFF)
set(CONFIG_LINUX_DRV_LSW_DPU OFF)
set(CONFIG_LINUX_DRV_DYINGGASP OFF)
set(CONFIG_LINUX_DRV_MOTOPWM OFF)
set(CONFIG_LINUX_DRV_LEDPWM OFF)
set(CONFIG_LINUX_DRV_PMUPWM ON)
set(CONFIG_LINUX_DRV_SPMI OFF)
set(CONFIG_LINUX_DRV_THERMAL_TSENSOR OFF)
set(CONFIG_LINUX_DRV_THERMAL_TVSENSOR ON)
set(CONFIG_LINUX_DRV_CPUFREQ ON)
set(CONFIG_LINUX_DRV_LSADC OFF)
set(CONFIG_LINUX_DRV_ACP ON)
set(CONFIG_LINUX_DRV_ASC OFF)

set(CONFIG_LINUX_DRV_PMU OFF)
set(CONFIG_LINUX_DRV_SOUND_ATTR OFF)
set(CONFIG_LINUX_DRV_FASTDIO OFF)
set(CONFIG_LINUX_DRV_USB OFF)
set(CONFIG_LINUX_DRV_UPSPHY OFF)
set(CONFIG_LINUX_DRV_USB2PHY OFF)
set(CONFIG_LINUX_DRV_WLA_ADP OFF)
set(CONFIG_LINUX_DRV_RSTINFO ON)
set(CONFIG_LINUX_DRV_HISAP OFF)
set(CONFIG_LINUX_DRV_PROC OFF)

set(CONFIG_LINUX_DRV_WIFICLK OFF)
set(CONFIG_LINUX_DRV_SYSENV ON)
set(CONFIG_LINUX_DRV_HWDIO OFF)
set(CONFIG_LINUX_DRV_NET_SERDES OFF)
set(CONFIG_LINUX_DRV_YT_PHY OFF)
set(CONFIG_LINUX_DRV_MXL_PHY OFF)

#----SDK_SERVICE----#
set(CONFIG_LINUX_SRV_SSM OFF)
set(CONFIG_LINUX_SRV_WLA OFF)
set(CONFIG_LINUX_SRV_MMTN OFF)
set(CONFIG_LINUX_SRV_CARA OFF)
set(CONFIG_LINUX_SRV_CFE OFF)
set(CONFIG_LINUX_SRV_SWA OFF)
set(CONFIG_LINUX_SRV_VOIP OFF)
set(CONFIG_LINUX_SRV_VOICE OFF)
set(CONFIG_LINUX_SRV_VOICE_VSP OFF)
set(CONFIG_LINUX_SRV_VOICE_H248 OFF)
set(CONFIG_LINUX_SRV_MIRROR OFF)
set(CONFIG_LINUX_SRV_CDEV ON)
set(CONFIG_LINUX_SRV_CAPTURE OFF)
set(CONFIG_LINUX_SRV_DIAGNOSE OFF)
set(CONFIG_LINUX_SRV_HSLINK OFF)

#----SOLUTION_DFT_LINUX_DRIVER----#
set(CONFIG_LINUX_DFT_DRV OFF)
set(CONFIG_LINUX_DFT_DRV_CEC OFF)
set(CONFIG_LINUX_DFT_DRV_CLI OFF)
set(CONFIG_LINUX_DFT_DRV_ETH OFF)
set(CONFIG_LINUX_DFT_DRV_HIFI OFF)
set(CONFIG_LINUX_DFT_DRV_IPC OFF)
set(CONFIG_LINUX_DFT_DRV_KDF OFF)
set(CONFIG_LINUX_DFT_DRV_LSW ON)
set(CONFIG_LINUX_DFT_DRV_LSW_PUB OFF)
set(CONFIG_LINUX_DFT_DRV_LSW_DP OFF)
set(CONFIG_LINUX_DFT_DRV_MMC OFF)
set(CONFIG_LINUX_DFT_DRV_NPU OFF)
set(CONFIG_LINUX_DFT_DRV_PCIE OFF)
set(CONFIG_LINUX_DFT_DRV_PMU OFF)
set(CONFIG_LINUX_DFT_DRV_RBT OFF)
set(CONFIG_LINUX_DFT_DRV_SEC OFF)
set(CONFIG_LINUX_DFT_DRV_SPMI OFF)
set(CONFIG_LINUX_DFT_DRV_SYNC OFF)
set(CONFIG_LINUX_DFT_DRV_TIMER OFF)
set(CONFIG_LINUX_DFT_DRV_TRNG OFF)
set(CONFIG_LINUX_DFT_DRV_USB OFF)
set(CONFIG_LINUX_DFT_DRV_WOE OFF)
set(CONFIG_LINUX_DFT_DRV_GT_ADP OFF)
set(CONFIG_LINUX_DFT_DRV_PON OFF)
set(CONFIG_LINUX_DFT_DRV_CHBA OFF)
set(CONFIG_LINUX_DFT_DRV_PLSW_561X OFF)
set(CONFIG_LINUX_DFT_DRV_LSW_DPU OFF)

#----SOLUTION_DFT_LINUX_SERVICE----#
set(CONFIG_LINUX_DFT_SRV OFF)
set(CONFIG_LINUX_DFT_SRV_CFE OFF)
set(CONFIG_LINUX_DFT_SRV_CFE_ADP OFF)
set(CONFIG_LINUX_DFT_SRV_HSLINK OFF)
set(CONFIG_LINUX_DFT_SRV_WLA OFF)
#linux app
set(CONFIG_LINUX_APP_SECURE_C ON)

#----SOLUTION_DFT_LINUX----#
set(CONFIG_LINUX_DFT_APP OFF)
set(CONFIG_LINUX_DFT_APP_CA OFF)
set(CONFIG_LINUX_DFT_APP_DCC OFF)
set(CONFIG_LINUX_DFT_APP_HUART OFF)
set(CONFIG_LINUX_DFT_APP_IR OFF)
set(CONFIG_LINUX_DFT_APP_MAXPOWER OFF)
set(CONFIG_LINUX_DFT_APP_NPU OFF)
set(CONFIG_LINUX_DFT_APP_SPI OFF)
set(CONFIG_LINUX_DFT_APP_MONITOR OFF)
set(CONFIG_LINUX_DFT_APP_WDG OFF)
set(CONFIG_LINUX_DFT_APP_CLI OFF)
set(CONFIG_LINUX_DFT_APP_SSF OFF)
set(CONFIG_LINUX_DFT_APP_SOUND_ATTR OFF)
set(CONFIG_LINUX_DFT_APP_HISTEN OFF)
set(CONFIG_LINUX_DFT_APP_LED OFF)
set(CONFIG_LINUX_DFT_APP_GADGET_HID OFF)
set(CONFIG_LINUX_DFT_APP_MGN ON)
set(CONFIG_LINUX_DFT_APP_MGN_CLI ON)
#
set(CONFIG_GATEWAY_BASIC ON)
set(CONFIG_GATEWAY_CML OFF)
set(CONFIG_GATEWAY_CML_MIB OFF)
set(CONFIG_GATEWAY_CML_ODL OFF)
set(CONFIG_GATEWAY_CML_ODLAPI OFF)
set(CONFIG_GATEWAY_CML_STORAGE OFF)
set(CONFIG_GATEWAY_MML OFF)
set(CONFIG_GATEWAY_MML_WEB OFF)
set(CONFIG_GATEWAY_ROOTFS ON)
set(CONFIG_GATEWAY_SERVICE ON)
set(CONFIG_GATEWAY_SERVICE_DMS ON)
set(CONFIG_GATEWAY_SERVICE_DMS_CRASHLOG OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_BOARD ON)
set(CONFIG_GATEWAY_SERVICE_DMS_BOB OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_CFM ON)
set(CONFIG_GATEWAY_SERVICE_DMS_CRONTAB OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_DOWNLOAD OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_ENCRYPT ON)
set(CONFIG_GATEWAY_SERVICE_DMS_KEY OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_LED OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_MSGALARM OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_OPTICAL OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_SYS_ENV ON)
set(CONFIG_GATEWAY_SERVICE_DMS_SYSINFO ON)
set(CONFIG_GATEWAY_SERVICE_DMS_SYSMSG OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_SYSLOG OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_SYSSTATS OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_CODETYPE OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_UPGRADE ON)
set(CONFIG_GATEWAY_SERVICE_DMS_WGET_MANAGE OFF)
set(CONFIG_GATEWAY_SERVICE_DMS_OMDIAG OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK ON)
set(CONFIG_GATEWAY_SERVICE_NETWORK_ACCOUNT OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_COMMON OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_EMU OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_LAN OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_LANHOST OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_MULTICAST OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_NETAPP OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_NETMONITOR OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_QOS OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_RA_BIND OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_SAMBA OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_SECURITY OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_UPLOAD OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_WAN OFF)
set(CONFIG_GATEWAY_SERVICE_NETWORK_WLAN ON)
set(CONFIG_GATEWAY_SERVICE_NETWORK_WLAN_BANDSTEERING OFF)
set(CONFIG_GATEWAY_SERVICE_VOICE OFF)
set(CONFIG_GATEWAY_SERVICE_VOICE_VSP OFF)
set(CONFIG_GATEWAY_SERVICE_VOICE_VOIP_KVOIP OFF)
set(CONFIG_GATEWAY_SSF OFF)
set(CONFIG_GATEWAY_NET_PON OFF BIN)
set(CONFIG_GATEWAY_MML_CWMP OFF)
set(CONFIG_GATEWAY_MML_CMCC_SMART OFF)
set(CONFIG_GATEWAY_MML_CU_SMART OFF)
set(CONFIG_GATEWAY_MML_CTC_SMART OFF)
set(CONFIG_GATEWAY_NETWORK_EASYMESH ON)
set(CONFIG_EASYMESH_VERSION R5)
set(CONFIG_EASYMESH_UT OFF)
set(CONFIG_GATEWAY_LACP ON)
set(CONFIG_GATEWAY_SMART_QOS OFF)
set(CONFIG_GATEWAY_VPN OFF)
set(CONFIG_GATEWAY_OPT_AUTH OFF)
set(CONFIG_GATEWAY_VXLAN OFF)
set(CONFIG_GATEWAY_DNSFILTER ON)
set(CONFIG_GATEWAY_USB_STORAGE ON)
set(CONFIG_GATEWAY_SERVICE_EMU_SPEED OFF)
set(CONFIG_GATEWAY_SERVICE_EMU_TURBO_SPEED OFF)
if(CMAKE_BUILD_TYPE MATCHES debug)
    set(CONFIG_GATEWAY_CLI ON)
endif()

# ddr
set(CONFIG_BOOTLOADER_DDR_TRAINING OFF)

# esbc #
set(CONFIG_BOOTLOADER_HI_ESBC OFF)
set(CONFIG_BOOTLOADER_HI_MINI_ESBC OFF)

set(CONFIG_ESBC_MBEDTLS OFF)
set(CONFIG_ESBC_TIMER  OFF)
set(CONFIG_ESBC_UART OFF)
set(CONFIG_ESBC_WDG OFF)
set(CONFIG_ESBC_USB OFF)
set(CONFIG_ESBC_SEC OFF)
set(CONFIG_ESBC_CRYPTO OFF)
set(CONFIG_ESBC_EFUSE OFF)
set(CONFIG_ESBC_FLASH OFF)
set(CONFIG_ESBC_KDF OFF)
set(CONFIG_ESBC_MMC OFF)
set(CONFIG_ESBC_SPI OFF)
set(CONFIG_ESBC_SYSCTRL OFF)

# uboot #
set(CONFIG_BOOTLOADER_UBOOT ON)
set(CONFIG_UBOOT_ENVTOOLS ON)

set(CONFIG_UBOOT_BOOT_TAGS ON)
set(CONFIG_UBOOT_CLK ON)
set(CONFIG_UBOOT_USB OFF)
set(CONFIG_UBOOT_CRG ON)
set(CONFIG_UBOOT_FASTBOOT OFF)
set(CONFIG_UBOOT_FLASH ON)
set(CONFIG_UBOOT_GET_RSTINFO ON)
set(CONFIG_UBOOT_IRQ ON)
set(CONFIG_UBOOT_LSW_PSA OFF)
set(CONFIG_UBOOT_LSW_PFE ON)
set(CONFIG_UBOOT_LSW_DP ON)
set(CONFIG_UBOOT_NET ON)
set(CONFIG_UBOOT_NET_PIE ON)
set(CONFIG_UBOOT_NET_PCU OFF)
set(CONFIG_UBOOT_QSPI OFF)
set(CONFIG_UBOOT_RESET ON)
set(CONFIG_UBOOT_TIMER ON)
set(CONFIG_UBOOT_TRNG OFF)
set(CONFIG_UBOOT_WDT ON)
set(CONFIG_UBOOT_LSADC OFF)
set(CONFIG_UBOOT_MULTIUPG ON)
set(CONFIG_UBOOT_DDR_TRAINING ON)

# uboot dft #
set(CONFIG_UBOOT_DFT_MMC OFF)
set(CONFIG_UBOOT_DFT_QSPI OFF)
set(CONFIG_UBOOT_DFT_UARTCFG OFF)
set(CONFIG_UBOOT_DFT_UPGRATE OFF)
set(CONFIG_UBOOT_DFT_FLASH_WP OFF)
set(CONFIG_UBOOT_DFT_OCRAM OFF)

# vendors #
set(CONFIG_VENDORS_EPHY OFF)
set(CONFIG_VENDORS_CODECS OFF)
set(CONFIG_VENDORS_SLIC OFF)
set(CONIFG_VENDORS_LED OFF)
set(CONFIG_VENDORS_CODEC_AD82010_MONO OFF)
set(CONFIG_VENDORS_CODEC_AD82010_STEREO OFF)
set(CONFIG_VENDORS_CODEC_ES7210 OFF)
set(CONFIG_VENDORS_CODEC_ES7243 OFF)
set(CONFIG_VENDORS_CODEC_RT9119 OFF)
set(CONFIG_VENDORS_PHY_BROADCOM OFF)
set(CONFIG_VENDORS_PHY_MAXLINEAR OFF)
set(CONFIG_VENDORS_PHY_REALTEK OFF)

set(CONFIG_VENDORS_PHY_RTL8386 OFF)
set(CONFIG_VENDORS_PHY_YT OFF)
set(CONFIG_VENDORS_SLIC_K_SPACE OFF)
set(CONFIG_VENDORS_SLIC_U_SPACE OFF)
set(CONFIG_VENDORS_SLIC_K_SPACE_LANTIQ OFF)

set(CONFIG_VENDORS_SLIC_K_SPACE_SILABS OFF)
set(CONFIG_VENDORS_SLIC_K_SPACE_ZARLINK OFF)

# ddr_greenbox
set(CONFIG_DDR_GREENBOX_DFT OFF)

# optee dft #
set(CONFIG_OPTEE_DFT OFF)

# itrustee #
set(CONFIG_ITRUSTEE_DRV_TRNG OFF)
set(CONFIG_ITRUSTEE_DRV_CRYPT OFF)

# itrustee dft #
set(CONFIG_ITRUSTEE_DFT OFF)

# hosttool #
set(CONFIG_HOSTTOOL_RAND_GEN ON)
set(CONFIG_HOSTTOOL_ADDECC ON)
set(CONFIG_HOSTTOOL_CLITRANS ON)
set(CONFIG_HOSTTOOL_OS_ENC_TEE ON)
set(CONFIG_HOSTTOOL_SEC_BOOT_IMG_PACK ON)
set(CONFIG_HOSTTOOL_BOOT_PACK ON)
set(CONFIG_HOSTTOOL_RSA_SIGN ON)
set(CONFIG_HOSTTOOL_FILEADDCRC ON)
set(CONFIG_HOSTTOOL_RSA_GEN ON)
set(CONFIG_HOSTTOOL_OS_ENC ON)

# harmony #
set(CONFIG_HARMONY_L0_RISCV OFF)

# hisap #
set(CONFIG_PLATFORM_HISAP OFF)
set(CONFIG_PLATFORM_HISAP_SOURCE OFF)
set(CONFIG_PLATFORM_HISAP_SOURCE_CORE OFF)

# liteos drivers #
set(CONFIG_LITEOS_DRV OFF)
set(CONFIG_LITEOS_DRV_BASIC OFF)
set(CONFIG_LITEOS_DRV_CLIC OFF)
set(CONFIG_LITEOS_DRV_CPUCYCLE OFF)
set(CONFIG_LITEOS_DRV_CRG OFF)
set(CONFIG_LITEOS_DRV_DDR OFF)
set(CONFIG_LITEOS_DRV_DMAC OFF)
set(CONFIG_LITEOS_DRV_DYING_GASP OFF)
set(CONFIG_LITEOS_DRV_GPIO OFF)
set(CONFIG_LITEOS_DRV_HISAP OFF)
set(CONFIG_LITEOS_DRV_HRTIMER OFF)
set(CONFIG_LITEOS_DRV_I2C OFF)
set(CONFIG_LITEOS_DRV_IPC OFF)
set(CONFIG_LITEOS_DRV_IOMUX OFF)
set(CONFIG_LITEOS_DRV_LPC OFF)
set(CONFIG_LITEOS_DRV_LSADC OFF)
set(CONFIG_LITEOS_DRV_MEMORY OFF)
set(CONFIG_LITEOS_DRV_PLIC OFF)
set(CONFIG_LITEOS_DRV_PMU OFF)
set(CONFIG_LITEOS_DRV_POWER OFF)
set(CONFIG_LITEOS_DRV_PSRAM OFF)
set(CONFIG_LITEOS_DRV_SEC OFF)
set(CONFIG_LITEOS_DRV_SENSOR OFF)
set(CONFIG_LITEOS_DRV_SPI OFF)
set(CONFIG_LITEOS_DRV_SPMI OFF)
set(CONFIG_LITEOS_DRV_SYNC OFF)
set(CONFIG_LITEOS_DRV_SYSCTRL OFF)
set(CONFIG_LITEOS_DRV_TIMER OFF)
set(CONFIG_LITEOS_DRV_UART OFF)
set(CONFIG_LITEOS_DRV_WDG OFF)

# liteos service #
set(CONFIG_LITEOS_SRV OFF)
set(CONFIG_LITEOS_SRV_MNTN OFF)
set(CONFIG_LITEOS_SRV_SSM OFF)
set(CONFIG_LITEOS_SRV_THERMAL OFF)

# upgrade package #
set(CONFIG_UPGRADE_PACKAGE ON)

# open_source #

set(CONFIG_OPENSRC_UBOOT ON)
set(CONFIG_OPENSRC_VERSION_UBOOT "2022.07")
set(CONFIG_OPENSRC_SRC_UBOOT "local")

set(CONFIG_OPENSRC_OPENWRT ON)
set(CONFIG_OPENSRC_VERSION_OPENWRT "22.03.06")
set(CONFIG_OPENSRC_SRC_OPENWRT "local")

# rootfs #
set(CONFIG_KERNEL_INITRAMFS_SOURCE_DIR ${CONFIG_INSTALL_DIR}/tmpfs)
set(CONFIG_ROOTFS_TYPE squashfs_ubi)
set(CONFIG_ROOTFS_UID 0)
set(CONFIG_ROOTFS_GID 0)

# sdk_linux_drv 专用宏
set(CONFIG_PUBLIC_LINUX_DRV_CHIP_DEF
    HI_LSW_VER=0x250
)

if(CONFIG_LINUX_DRV_NET_PIE_WOC)
    list(APPEND CONFIG_PUBLIC_LINUX_SRV_CHIP_DEF HI_WOE_ON_CPU)
    list(APPEND CONFIG_PUBLIC_LINUX_DRV_CHIP_DEF HI_WOE_ON_CPU)
endif()

set(CONFIG_FWK OFF)
