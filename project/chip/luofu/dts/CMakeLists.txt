set(LIST_PRIVATE_DTS_DEFINEZTIONS
    -DCONFIG_LUOFU -DHI_DDR_SIZE=${CONFIG_DDR_SIZE}
    -DHI_DDR_RESV_SIZE=${CONFIG_DDR_RESV_SIZE}
    -DHI_FLASH_SHARE_MEM_START=${CONFIG_FLASH_SHARE_MEM_OFFSET}
)

configure_file(
    ${CONFIG_CHIP_NAME}_${CONFIG_PRODUCT_NAME}.dtsi
    ${CONFIG_CHIP_NAME}.dtsi COPYONLY
)
configure_file(
    ${CONFIG_CHIP_NAME}_partition_${CONFIG_PRODUCT_NAME}.dtsi
    ${CONFIG_CHIP_NAME}_partition.dtsi COPYONLY
)

include(${CONFIG_CMAKE_DIR}/compile_dts.cmake)
