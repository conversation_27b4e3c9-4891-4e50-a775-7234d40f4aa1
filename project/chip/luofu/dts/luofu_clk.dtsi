/*
 * dts file for Hisilicon luofu Development Board
 *
 * Copyright (C) 2022-09-07, Hisilicon Ltd.
 *
 */

/ {
	apb_clk: apb_clk{
		compatible = "fixed-clock";
		#clock-cells = <0x0>;
		clock-frequency = <*********>;
		clock-output-names = "apb_clk";
	};

	ahb_clk: ahb_clk{
		compatible = "fixed-clock";
		#clock-cells = <0x0>;
		clock-frequency = <*********>;
		clock-output-names = "ahb_clk";
	};

	gemacN_clk: gemacN_clk{
		compatible = "fixed-clock";
		#clock-cells = <0x0>;
		clock-frequency = <*********>;
		clock-output-names = "gemacN_clk";
	};

	lsw_dp_clk: lsw_dp_clk{
		compatible = "fixed-clock";
		#clock-cells = <0x0>;
		clock-frequency = <*********>;
		clock-output-names = "lsw_dp_clk";
	};

	lsw_pfe_clk: lsw_pfe_clk{
		compatible = "fixed-clock";
		#clock-cells = <0x0>;
		clock-frequency = <*********>;
		clock-output-names = "lsw_pfe_clk";
	};

	osc_clk: fixed_clk0@20000000 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <20000000>;
		clock-output-names = "osc";
	};

	efuse_ring_clk: fixed_clk1@20000000 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <20000000>;
		clock-output-names = "efuse_ring_clk";
	};

	corrector_clk: corrector_fixed_clk@*********0 {
		compatible = "fixed-clock";
		#clock-cells = <0x0>;
		clock-frequency = <CPU_FREQ_NORMAL>;
		clock-output-names = "corrector_clk";
	};

	twd_clk: fixed_factor_clk@0 {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&corrector_clk>;
		clock-mult = <1>;
		clock-div = <4>;
	};

	top_clk: clk@14880000 {
		compatible = "hsan,clk", "syscon", "simple-mfd";
		reg = <0x14880000 0x1000>;

		cpu_pll: clk_pll1@0198 {
			compatible = "hsan,clk-pll";
			#clock-cells = <0>;
			clocks = <&osc_clk>;
			clock-names = "ref";
			ctrl-offset = <0x198>;
			status-offset = <0x90>;
			status-bit = <30>;
			clock-output-names = "cpu-clk";
		};

		lsw_pll: clk_pll2@01e0 {
			compatible = "hsan,clk-pll";
			#clock-cells = <0>;
			clocks = <&osc_clk>;
			clock-names = "ref";
			ctrl-offset = <0x1e0>;
			status-offset = <0x90>;
			status-bit = <27>;
			clock-output-names = "lsw-clk";
		};

		cpu_clk: cpu_mux@01388 {
			compatible = "hsan,clk-mux";
			#clock-cells = <0>;
			reg-offset = <0x138>;
			mask = <0x8>;
			table = <0 1>;
			clocks = <&cpu_pll>, <&lsw_pll>;
			clock-output-names = "cpu-clk-mux";
		};

		sfc_clk: clk_gate@001400 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&apb_clk>;
			clock-output-names = "sfc_clk";
			reg-offset = <0x14>;
			bit = <0>;
		};

		led_pwm: clk_gate@001414 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&apb_clk>;
			clock-output-names = "led_pwm";
			reg-offset = <0x14>;
			bit = <14>;
		};

		i2c0_clk: clk_gate2@001424 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&apb_clk>;
			clock-output-names = "i2c0_clk";
			reg-offset = <0x14>;
			bit = <24>;
		};

		gpio0_clk: clk_gate1@001420 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x14>;
			bit = <20>;
			clocks = <&apb_clk>;
			clock-output-names = "gpio0_clk";
		};

		gpio1_clk: clk_gate2@001421 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x14>;
			bit = <21>;
			clocks = <&apb_clk>;
			clock-output-names = "gpio1_clk";
		};

		pcie0_clk: clk_gate@002012 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <12>;
			clocks = <&apb_clk>;
			clock-output-names = "pcie0_clk";
		};

		pcie1_clk: clk_gate@002013 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <13>;
			clocks = <&apb_clk>;
			clock-output-names = "pcie1_clk";
		};

		pie_clk0: clk_gate@002025 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&ahb_clk>;
			clock-output-names = "pie_clk0";
			reg-offset = <0x20>;
			bit = <25>;
		};

		gemac_clk0: clk_gate@002019 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&gemacN_clk>;
			clock-output-names = "gemac_clk0";
			reg-offset = <0x20>;
			bit = <19>;
		};

		gemac_clk1: clk_gate@002020 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&gemacN_clk>;
			clock-output-names = "gemac_clk1";
			reg-offset = <0x20>;
			bit = <20>;
		};

		gemac_clk2: clk_gate@002021 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&gemacN_clk>;
			clock-output-names = "gemac_clk2";
			reg-offset = <0x20>;
			bit = <21>;
		};

		gemac_clk3: clk_gate@002022 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&gemacN_clk>;
			clock-output-names = "gemac_clk3";
			reg-offset = <0x20>;
			bit = <22>;
		};

		gemac_clk4: clk_gate@002023 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&gemacN_clk>;
			clock-output-names = "gemac_clk4";
			reg-offset = <0x20>;
			bit = <23>;
		};

		mdio_clk0: clk_gate@002003 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&gemacN_clk>;
			clock-output-names = "mdio_clk0";
			reg-offset = <0x20>;
			bit = <3>;
		};

		lsw_dp_clk0: clk_gate@002000 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&lsw_dp_clk>;
			clock-output-names = "lsw_dp_clk0";
			reg-offset = <0x20>;
			bit = <0>;
		};

		lsw_pfe_clk0: clk_gate@002001 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&lsw_pfe_clk>;
			clock-output-names = "lsw_pfe_clk0";
			reg-offset = <0x20>;
			bit = <1>;
		};

		efuse_clk: efuse_mux@0138 {
			compatible = "hsan,clk-mux";
			#clock-cells = <0>;
			reg-offset = <0x138>;
			mask = <0x2>;
			table = <0 1>;
			clocks = <&efuse_ring_clk>, <&osc_clk>;
			clock-output-names = "efuse-clk-mux";
		};
	};
};
