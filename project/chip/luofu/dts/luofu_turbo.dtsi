/*
 * dts file for Hisilicon Development Board
 *
 * Copyright (C) 2023-03-23, Hisilicon Ltd.
 *
 */

&cpu0_opp_table_ss{
	opp-1400000000 {
		opp-hz = /bits/ 64 <1400000000>;
		opp-microvolt-slow = <990000>;
		opp-microvolt-typical = <990000>;
		opp-microvolt-fast = <990000>;
		clock-latency-ns = <4840000>;
	};
	opp-1600000000 {
		opp-hz = /bits/ 64 <1600000000>;
		opp-microvolt-slow = <1040000>;
		opp-microvolt-typical = <1040000>;
		opp-microvolt-fast = <1040000>;
		clock-latency-ns = <4840000>;
	};
};

/ {
	global_timer: global_timer@10180200 {
		compatible = "arm,cortex-a9-global-timer";
		reg = <0x10180200 0x20>;
		interrupts = <1 11 0x301>;
		clocks = <&twd_clk>;
	};

	twd_clk_modifier: clk_modifier@0 {
		compatible = "hsan,hsan-clk-modifier";
		clocks = <&corrector_clk>;

		freq_div = <1>;
	};
};