/*
 * dtsi file for Hisilicon luofu Development Board
 *
 * Copyright (C) 2022-11-03, Hisilicon(shanghai) Ltd.
 *
 */

/ {
	efuse {
		compatible = "simple-mfd";
		ranges;

		efuse0: ns {
			compatible = "hsan,efuse";
			#address-cells = <1>;
			#size-cells = <1>;
			efusecon-phandle = <&efuse_ctl>;
			efusedata-phandle = <&efuse_data>;
			efuseclksrc-phandle = <&efuse_clk_src>;
			resets = <&top_reset 0x34 0>;
			clocks = <&efuse_ring_clk>, <&efuse_clk>, <&osc_clk>;
			clock-names = "ring_clk", "mux", "osc_clk";
			read-only;

			nvmem-cells = <&basic_dieid>, <&ext_diedid>,
					<&osc_ring_svt_freq>, <&bootrom_forbid>,
					<&bootcli_forbid>, <&osc_ring_lvt_freq>,
					<&osc_ring_hvt_freq>, <&rgmii0_tune_efuse>,
					<&power_grade>, <&hw_dbios_en>,
					<&tsensor_trim_offset>, <&vsensor_trim_offset>,
					<&ldo_trim_sel>, <&ldo_trim>,
					<&usb2_trim_offset>, <&cs_chip>,
					<&kgd_size>, <&kgd_type>,
					<&kgd_dieid>, <&ft_crc>;

			nvmem-cell-names = "basic_dieid", "ext_diedid",
					   "osc_ring_svt_freq", "bootrom_forbid",
					   "bootcli_forbid", "osc_ring_lvt_freq",
					   "osc_ring_hvt_freq", "rgmii0_tune_efuse",
					   "power_grade", "hw_dbios_en",
					   "tsensor_trim_offset", "vsensor_trim_offset",
					   "ldo_trim_sel", "ldo_trim",
					   "usb2_trim_offset", "cs_chip",
					   "kgd_size", "kgd_type",
					   "kgd_dieid", "ft_crc";

			/* Byte0 ~ Byte19 */
			basic_dieid: basic_dieid {
				reg = <0x0 0x14>;
			};

			/* Byte20 ~ Byte31 */
			ext_diedid: ext_diedid {
				reg = <0x14 0x6>;
			};
			osc_ring_svt_freq: osc_ring_svt_freq {
				reg = <0x1A 0x2>;
				bits = <0 12>;
			};
			bootrom_forbid: bootrom_forbid {
				reg = <0x1B 0x1>;
				bits = <4 1>;
			};
			bootcli_forbid: bootcli_forbid {
				reg = <0x1B 0x1>;
				bits = <5 1>;
			};
			osc_ring_lvt_freq: osc_ring_lvt_freq {
				reg = <0x1C 0x2>;
				bits = <0 12>;
			};
			osc_ring_hvt_freq: osc_ring_hvt_freq {
				reg = <0x1D 0x2>;
				bits = <4 12>;
			};
			rgmii0_tune_efuse: rgmii0_tune_efuse {
				reg = <0x1F 0x1>;
				bits = <0 4>;
			};
			power_grade: power_grade {
				reg = <0x1F 0x1>;
				bits = <4 2>;
			};
			hw_dbios_en: hw_dbios_en {
				reg = <0x1F 0x1>;
				bits = <6 1>;
			};
			tsensor_trim_offset: tsensor_trim_offset {
				reg = <0x20 0x2>;
				bits = <0 10>;
			};
			vsensor_trim_offset: vsensor_trim_offset {
				reg = <0x21 0x2>;
				bits = <2 10>;
			};
			ldo_trim_sel: ldo_trim_sel {
				reg = <0x22 0x1>;
				bits = <4 1>;
			};
			ldo_trim: ldo_trim {
				reg = <0x22 0x2>;
				bits = <5 4>;
			};
			usb2_trim_offset: usb2_trim_offset {
				reg = <0x23 0x1>;
				bits = <1 5>;
			};
			cs_chip: cs_chip {
				reg = <0x23 0x1>;
				bits = <6 1>;
			};
			kgd_size: kgd_size {
				reg = <0x23 0x1>;
				bits = <7 1>;
			};
			kgd_type: kgd_type {
				reg = <0x24 0x1>;
				bits = <0 2>;
			};
			kgd_dieid: kgd_dieid {
				reg = <0x28 0xE>;
				bits = <0 106>;
			};
			ft_crc: ft_crc {
				reg = <0x3F 0x1>;
			};
		};

		efuse1: chip_key {
			compatible = "hsan,efuse";
			#address-cells = <1>;
			#size-cells = <1>;
			efusecon-phandle = <&efuse_ctl>;
			efusedata-phandle = <&efuse_data>;
			efuseclksrc-phandle = <&efuse_clk_src>;
			resets = <&top_reset 0x34 0>;
			clocks = <&efuse_ring_clk>, <&efuse_clk>, <&osc_clk>;
			clock-names = "ring_clk", "mux", "osc_clk";

			nvmem-cells = <&msk>, <&huk>,
					<&jak>;

			nvmem-cell-names = "msk", "huk",
					   "jak";

			msk: msk {
				reg = <0x40 0x20>;
			};
			huk: huk {
				reg = <0x60 0x20>;
			};
			jak: jak {
				reg = <0x80 0x20>;
			};
		};

		efuse2: sec_ctrl {
			compatible = "hsan,efuse";
			#address-cells = <1>;
			#size-cells = <1>;
			efusecon-phandle = <&efuse_ctl>;
			efusedata-phandle = <&efuse_data>;
			efuseclksrc-phandle = <&efuse_clk_src>;
			resets = <&top_reset 0x34 0>;
			clocks = <&efuse_ring_clk>, <&efuse_clk>, <&osc_clk>;
			clock-names = "ring_clk", "mux", "osc_clk";

			nvmem-cells = <&pubkh>, <&sec_boot_en>,
					<&jatg_auth>, <&jatg_efuse_forbid>,
					<&product_id>;

			nvmem-cell-names = "pubkh", "sec_boot_en",
					   "jatg_auth", "jatg_efuse_forbid",
					   "product_id";

			pubkh: pubkh {
				reg = <0xA0 0x20>;
			};
			sec_boot_en: sec_boot_en {
				reg = <0xC0 0x1>;
				bits = <0 1>;
			};
			jatg_auth: jatg_auth {
				reg = <0xC0 0x1>;
				bits = <3 2>;
			};
			jatg_efuse_forbid: jatg_efuse_forbid {
				reg = <0xC0 0x1>;
				bits = <7 1>;
			};
			product_id: product_id {
				reg = <0xC1 0x4>;
			};
		};

		efuse3: online {
			compatible = "hsan,efuse";
			#address-cells = <1>;
			#size-cells = <1>;
			efusecon-phandle = <&efuse_ctl>;
			efusedata-phandle = <&efuse_data>;
			efuseclksrc-phandle = <&efuse_clk_src>;
			resets = <&top_reset 0x34 0>;
			clocks = <&efuse_ring_clk>, <&efuse_clk>, <&osc_clk>;
			clock-names = "ring_clk", "mux", "osc_clk";

			nvmem-cells = <&revokedkeymask>, <&blnvcounter>,
					<&blnvcounter_st>, <&osnvcounter>,
					<&osnvcounter_st>;

			nvmem-cell-names = "revokedkeymask","blnvcounter",
					   "blnvcounter_st", "osnvcounter",
					   "osnvcounter_st";

			revokedkeymask: revokedkeymask {
				reg = <0xD0 0x4>;
			};
			blnvcounter: blnvcounter {
				reg = <0xD4 0x4>;
				bits = <0 31>;
			};
			blnvcounter_st: blnvcounter_st {
				reg = <0xD7 0x1>;
				bits = <7 1>;
			};
			osnvcounter: osnvcounter {
				reg = <0xD8 0x1C>;
				bits = <0 233>;
			};
			osnvcounter_st: osnvcounter_st {
				reg = <0xF3 0x1>;
				bits = <7 1>;
			};
		};
	};
};
