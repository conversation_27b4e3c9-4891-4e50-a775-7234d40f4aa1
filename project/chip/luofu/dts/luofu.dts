/*
 * dts file for Hisilicon luofu Development Board
 *
 * Copyright (C) 2021-07-29, Hisilicon Ltd.
 *
 */

/dts-v1/;

#include "luofu.h"
#include "luofu_clk.dtsi"
#include "luofu_rst.dtsi"
#include "luofu-pinctrl.dtsi"
#include "luofu-efuse.dtsi"
#include <dt-bindings/gpio/gpio.h>

/ {
	compatible = "hsan-luofu";
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&gic>;

	aliases {
		console = &uart1;
		serial0 = &uart1;
		serial1 = &uart0;
	};

	cpus {
		#address-cells = <0x1>;
		#size-cells = <0x0>;
		enable-method = "hisilicon,hsan_smp";

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0x0>;
			clocks = <&cpu_pll>, <&cpu_clk>, <&lsw_pll>;
			clock-names = "pclk", "mux", "aclk";
			operating-points-v2 = <&cpu0_opp_table_ss>;
			cpu-supply = <&cpu_supply>;
			nvmem-cells = <&osc_ring_svt_freq>;
			nvmem-cell-names = "avs";
			hpm-range = <0 1660>, <1661 1929>, <1930 0xffffffff>;
		};

		cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0x1>;
			clocks = <&cpu_pll>, <&cpu_clk>, <&lsw_pll>;
			clock-names = "pclk", "mux", "aclk";
			operating-points-v2 = <&cpu0_opp_table_ss>;
			cpu-supply = <&cpu_supply>;
			nvmem-cells = <&osc_ring_svt_freq>;
			nvmem-cell-names = "avs";
			hpm-range = <0 1660>, <1661 1929>, <1930 0xffffffff>;
		};
	};

	cpu0_opp_table_ss: opp_table0 {
		compatible = "operating-points-v2";
		opp-shared;

		opp-800000000 {
			opp-hz = /bits/ 64 <800000000>;
			opp-microvolt-slow = <900000>;
			opp-microvolt-typical = <900000>;
			opp-microvolt-fast = <900000>;
			clock-latency-ns = <4840000>;
		};
		opp-1000000000 {
			opp-hz = /bits/ 64 <1000000000>;
			opp-microvolt-slow = <900000>;
			opp-microvolt-typical = <900000>;
			opp-microvolt-fast = <900000>;
			clock-latency-ns = <4840000>;
			opp-suspend;
		};
		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt-slow = <970000>;
			opp-microvolt-typical = <920000>;
			opp-microvolt-fast = <860000>;
			clock-latency-ns = <4840000>;
		};
	};

	twd_clk_corrector: clk_corrector@0 {
		compatible = "hsan,hsan-clk-corrector";
		clocks = <&corrector_clk>;
	};

	rstinfo@0x14880000 {
		compatible = "hsan,rstinfo";
		reg = <0x14880000 0x1000>;
		reg-offset = <0x90>;
		record_addr = <0x10100C10>;

		rstinfo-mask = <0x1C000>;
		rstinfo-offset = <0xE>;
		rstinfo-type = <0x3>;
		rstinfo-item = "hard";
	};

	gic: interrupt-controller {
		compatible = "arm,cortex-a9-gic";
		#interrupt-cells = <0x3>;
		interrupt-controller;
		/* gic uboot init feature */
		off-secure-status;
		/* gic dist base, gic cpu base */
		reg = <0x10181000 0x1000>,
			<0x10180100 0x100>;
	};

	local_timer: local_timer@10180600 {
		compatible = "arm,cortex-a9-twd-timer";
		reg = <0x10180600 0x20>;
		interrupts = <1 13 0x301>;
		clocks = <&twd_clk>;

		load-reg-offset = <0x0>;
		count-reg-offset = <0x4>;
		contrl-reg-offset = <0x8>;
		status-reg-offset = <0xc>;
	};

	timer0: timer@10104000 {
		compatible = "arm,sp804";
		reg = <0x10104000 0x1000>;
		interrupts = <1 47 0x4>; //79
		/* timer00 & timer01 */
		clocks = <&apb_clk>;
	};

	L2: l2-cache {
		compatible = "arm,pl310-cache";
		reg = <0x7f000000 0x1000>;
		cache-unified;
		cache-level = <2>;
		cache-way-size-sel = <0x1>;
		l2c_aux_mask = <0xFFB0FFFE>;
		l2c_aux_val = <0x430001>;
	};

	scu@0x10180000 {
		compatible = "arm,cortex-a9-scu";
		reg = <0x10180000 0x1000>;
	};

	memory {
		device_type = "memory";
		reg = <HI_LINUX_DDR_MEMORY_START HI_LINUX_DDR_MEMORY_SIZE>;
	};

	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;

		flashinfo_reserved: flashinfo@0x80800000 {
			compatible = "hsan,flashinfo_reserved";
			reg = <HI_FLASH_SHARE_MEM_START HI_FLASH_SHARE_MEM_SIZE>;
			atag-offset = <0x1000>;
		};
	};

	crg@14880000 {
			compatible = "hsan,crg";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x14880000 0x1000>;
			resume-offset = <0x38>;
			reboot-offset = <0x0>;
			reboot-base-index = <0x0>;
			core-info = <22 24>, <23 25>; // <reset bit, clk bit> for core 0,1
			softrst_val0 = <0x51162100>;
			softrst_val1 = <0xAEE9DEFF>;
			clr-wdg-offset = <0x70>;
			set-wdg-offset = <0x50>;
			switch-wdg-offset = <0x64>;
	};

	watchdog {
		compatible = "hsan,hsan-watchdog";
		hwver = <2>;
		reg = <0x14880000 0x1000>;
		timeout-sec = <30>;
		timeout-min = <1>;
		timeout-max = <512>;
		interrupts = <0 2 4>;
		reset-val0 = <0x55AA5A5A>;
		reset-val1 = <0xAA55A5A5>;
		stop-val0 = <0xABCD5116>;
		stop-val1 = <0xED574447>;
		enable-val0 = <0x4F4E5452>;
		enable-val1 = <0x12345116>;
		set-offset = <0x50>;
		en-offset = <0x0064>;
		clr-offset = <0x0070>;
		int-status-offset = <0x0100>;
	};

	sysctrl: system-controller@10100000 {
		compatible = "hsan,sysctrl", "syscon";
		#address-cells = <1>;
		#size-cells = <1>;
		reg = <0x10100000 0x1000>;
		smp-offset = <0xc00>;
	};
	sysenv {
		compatible = "hsan,sysenv";
		syscon = <&sysctrl>;
		bootreg-offset = <0x0c08>;
	};

	uart0: uart0@0x1010e000 {
		compatible = "snps,dw-apb-uart";
		bus_id = "uart0";
		reg = <0x1010e000 0x1000>;
		clocks = <&apb_clk>;
		clock-names = "apb_pclk";
		reg-shift = <0x2>;
		interrupts = <0x0 0x2d 0x4>;
	};

	uart1: uart1@0x1010f000 {
		compatible = "snps,dw-apb-uart";
		bus_id = "uart1";
		reg = <0x1010f000 0x1000>;
		clocks = <&apb_clk>;
		clock-names = "apb_pclk";
		reg-shift = <0x2>;
		interrupts = <0x0 0x2e 0x4>;
	};

	i2c0: i2c0@0x10111000 {
		compatible = "snps,designware-i2c";
		reg = <0x10111000 0x1000>;
		clock-frequency=<100000>;
		clocks = <&i2c0_clk>;
		resets = <&top_reset 0x2c 24>;
		interrupts = <0 44 4>; //76
	};

	tvsensor0: tvsensor@14900500 {
		compatible = "hsan,tvsensor";
		hwver = <TVSENSOR_HWVER>;
		reg = <0x14900500 0x14>;
		interrupts = <0 0 4>;
		#thermal-sensor-cells = <0>;
		trim = <0 0>;
		int-base-offset = <0xC>;
		addition-en;
		efuse-trim-en;
	};

	thermal-zones {
		cls0: cls0 {
			polling-delay = <1000>;
			polling-delay-passive = <100>;
			sustainable-power = <2000>;
			thermal-sensors = <&tvsensor0>;

			trips {
				alert0: trip-point@0 {
					temperature = <115000>;
					hysteresis = <3000>;
					type = "passive";
				};
				critical: trip-point@2 {
					temperature = <125000>;
					hysteresis = <3000>;
					type = "critical";
				};
			};
			cooling-maps {
				map0 {
					trip = <&alert0>;
					cooling-device = <&net_cdev 0 3>;
					contribution = <100>;
				};
			};
		};
	};

	net_cdev: cooling_dev0 {
		compatible = "hsan,net-cdev";
		car-rate-table = <0 500000 125000 32000>; /* kbps */
		#cooling-cells = <2>;
		dev-names = "eth0", "eth1", "eth2", "eth3", "eth4";
	};

	pmupwm: pwm@1 {
		compatible = "hsan,pmu-pwm";
		reg = <0x14900800 0x4>;
		clocks = <&osc_clk>;
		#pwm-cells = <2>; /* <index period> */
		npwm = <1>;
	};

	cpu_supply: reg@0 {
		compatible = "pwm-regulator";
		pwms = <&pmupwm 0 10000>;
		regulator-min-microvolt = <740000>;
		regulator-max-microvolt = <1040000>;
		regulator-name = "vdd_logic";
		pwm-dutycycle-unit = <100>;
		pwm-dutycycle-range = <100 0>; /* */
	};

	peri_gpio0: gpio0@0x10106000 {
		compatible = "snps,dw-apb-gpio";
		reg = <0x10106000 0x1000>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&gpio0_clk>;
		resets = <&top_reset 0x2c 20>;

		porta: gpio-port@0 {
			compatible = "snps,dw-apb-gpio-port";
			bank-name = "porta";
			gpio-controller;
			#gpio-cells = <2>;
			snps,nr-gpios = <32>;
			gpio-base = <0>;
			reg = <0>;
			interrupt-controller;
			#interrupt-cells = <2>;
			interrupt-parent = <&gic>;
			interrupts = <0 49 4>;
		};
	};

	peri_gpio1: gpio1@0x10107000 {
		compatible = "snps,dw-apb-gpio";
		reg = <0x10107000 0x1000>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&gpio1_clk>;
		resets = <&top_reset 0x2c 21>;

		portb: gpio-port@0 {
			compatible = "snps,dw-apb-gpio-port";
			bank-name = "portb";
			gpio-controller;
			#gpio-cells = <2>;
			snps,nr-gpios = <5>;
			gpio-base = <32>;
			reg = <0>;
			interrupt-controller;
			#interrupt-cells = <2>;
			interrupt-parent = <&gic>;
			interrupts = <0 50 4>;
		};
	};

	fmc: fmc@10a20000 {
		compatible = "hsan,fmc";
		bus_id = "fmc";
		reg = <0x10a20000 0x1000>,
			<0x1c000000 0x100000>;
		spi_cs = <0x1>;
		clocks = <&sfc_clk>;
		clock-names = "sfc_clk";
		resets = <&top_reset 0x2c 0>;
		reset-names = "sfc_rst";
		enable-quad-mode;

	};

	pcie0: pcie@0x10160000 {
		compatible = "hsan,pcie", "hsan,acp-pcie0";
		hwver = <0x10>;
		channel = <0>; // physical channel
		linux,pci-domain = <0>;
		capability = <0x1>; // bit0:pcie1.1, bit1:pcie2.0
		//dma-coherent;

		reg = <0x10160000 0x1000>,
			<0x10161000 0x3000>,
			<0x50000000 0x1000>,
			<0x40000000 0x2000000>,
			<0x48000000 0x800000>;
		reg-names = "dbi", "misc", "cfg", "mem", "io";

		interrupts = <0 59 4>,
			     <0 69 4>;
		interrupt-names = "radm", "linkdown";

		bus-range = <0 10>;
		ranges;

		iatu_rc = <0x0 0x4 0x80000000 0x50000000 0x0 0x57FFFFFF 0x0 0x0>, // cfg
			  <0x1 0x0 0x80000000 0x40000000 0x0 0x47FFFFFF 0x40000000 0x0>, // mem
			  <0x2 0x2 0x80000000 0x48000000 0x0 0x4FFFFFFF 0x48000000 0x0>; // io
		iatu_ep = <0x2 0x0 0x80000000 0x30000000 0x0 0x307FFFFF 0xab000000 0x0>;

		clocks = <&pcie0_clk>;
		clock-names = "pcie_clk";

		resets = <&top_reset 0x34 12>,
				<&top_reset 0x34 13>,
				<&top_reset 0x34 14>,
				<&top_reset 0x34 15>;
		reset-names = "apb_rst","pcs_rst","phy_rst","ctrl_rst";

		gpio-delay = <50>; // ms
		pcie-gpios;
		acp-gate = <0x10100174 0x2000000 0x0>; // attr_base attr_mask attr_val
	};

	pcie1: pcie@0x10164000 {
		compatible = "hsan,pcie", "hsan,acp-pcie1";
		hwver = <0x10>;
		channel = <1>; // physical channel
		linux,pci-domain = <1>;
		capability = <0x1>; // bit0:pcie1.1, bit1:pcie2.0
		//dma-coherent;

		reg = <0x10164000 0x1000>,
			<0x10165000 0x3000>,
			<0x68000000 0x1000>,
			<0x58000000 0x2000000>,
			<0x60000000 0x800000>;
		reg-names = "dbi", "misc", "cfg", "mem", "io";

		interrupts = <0 63 4>,
			     <0 70 4>;
		interrupt-names = "radm", "linkdown";

		bus-range = <0 10>;
		ranges;

		iatu_rc = <0x0 0x4 0x80000000 0x68000000 0x0 0x6FFFFFFF 0x0 0x0>, // cfg
			  <0x1 0x0 0x80000000 0x58000000 0x0 0x5FFFFFFF 0x58000000 0x0>, // mem
			  <0x2 0x2 0x80000000 0x60000000 0x0 0x67FFFFFF 0x60000000 0x0>; // io
		iatu_ep = <0x2 0x0 0x80000000 0x30000000 0x0 0x307FFFFF 0xab000000 0x0>;

		clocks = <&pcie1_clk>;
		clock-names = "pcie_clk";

		resets = <&top_reset 0x34 16>,
				<&top_reset 0x34 17>,
				<&top_reset 0x34 18>,
				<&top_reset 0x34 19>;
		reset-names = "apb_rst","pcs_rst","phy_rst","ctrl_rst";

		gpio-delay = <50>; // ms
		pcie-gpios;
		acp-gate = <0x10100174 0x4000000 0x0>; // attr_base attr_mask attr_val
	};

	pie: pie@0x10a70000 {
		compatible = "hsan,pie", "hsan,acp-pie";
		reg = <0x10a70000 0x10000>;
		interrupts = <0 88 4>,
			     <0 89 4>,
			     <0 90 4>,
			     <0 91 4>;
		clocks = <&pie_clk0>;
		resets = <&top_reset 0x30 25>;
		hwver = <0x250>;
		tx_ring = <12 4>; // <2^ring_size, ch_num>
		rx_ring = <10 2048>, // <2^ring_size, frame_len>
			<10 2048>,
			<10 2048>,
			<10 2048>;
		woc_tx_ring = <12 4>;
		woc_rx_ring = <10 2048>,
			<10 2048>,
			<10 2048>,
			<10 2048>;
		dma-coherent;
		acp-gate = <0x10100174 0x1000000 0x0>; // attr_base attr_mask attr_val
		acp-pre-cfg-attr = <0x1010015c 0xFFFFFFFF 0x1B1B1B1B>,
			<0x10100160 0xFFFFFFFF 0x16161717>;
	};

	iomux {
		compatible = "hsan,iomux";
		reg = <0x14900000 0x1000>;
		iomux-jtag-sel-offset = <0x108>;
		iomux-jtag-sel-value = <0x1>;
	};

	lsw_woe {
		compatible = "hsan,lsw_woe";
		hwver = <0x251>;
		interrupts = <0 84 4>, <0 83 4>, <0 82 4>, <0 81 4>, <0 80 4>; // 116 115 114
		interrupt-names = "band0_comp_irq", "band1_comp_irq", "band2_comp_irq", "band0_txba", "band1_txba";
		dma-coherent;
	};
	woe_dev {
		compatible = "hsan,woe_dev";
		hwver = <0x300>;
		port = <1>;
		dma-coherent;
	};
	woe_wifi {
		compatible = "hsan,woe_wifi";
		dma-coherent;
	};

	gemac0: gemac@0x14300000 {
		compatible = "hsan,mac";
		reg = <0x14300000 0x10000>;
		clocks = <&gemac_clk0>;
		resets = <&top_reset 0x30 13>, <&top_reset 0x30 14>;
		reset-names = "mac_logic_reset", "mac_reset";
		status = "okay";
		phy-handle = <&gephy0>;
		phy-mode = "rgmii";
		cnt-clr = <0x14980004 0>; // reg bit
		port= <8>;
	};

	gemac1: gemac@0x14380000 {
		compatible = "hsan,mac";
		reg = <0x14380000 0x10000>;
		clocks = <&gemac_clk1>;
		resets = <&top_reset 0x30 15>, <&top_reset 0x30 16>;
		reset-names = "mac_logic_reset", "mac_reset";
		status = "okay";
		phy-handle = <&gephy1>;
		phy-mode = "gmii";
		cnt-clr = <0x14980004 1>; // reg bit
		port= <9>;
	};

	gemac2: gemac@0x14400000 {
		compatible = "hsan,mac";
		reg = <0x14400000 0x10000>;
		clocks = <&gemac_clk2>;
		resets = <&top_reset 0x30 17>, <&top_reset 0x30 18>;
		reset-names = "mac_logic_reset", "mac_reset";
		status = "okay";
		phy-handle = <&gephy2>;
		phy-mode = "gmii";
		cnt-clr = <0x14980004 2>; // reg bit
		port= <10>;
	};

	gemac3: gemac@0x14480000 {
		compatible = "hsan,mac";
		reg = <0x14480000 0x10000>;
		clocks = <&gemac_clk3>;
		resets = <&top_reset 0x30 19>, <&top_reset 0x30 20>;
		reset-names = "mac_logic_reset", "mac_reset";
		status = "okay";
		phy-handle = <&gephy3>;
		phy-mode = "gmii";
		cnt-clr = <0x14980004 3>; // reg bit
		port= <11>;
	};

	gemac4: gemac@0x14500000 {
		compatible = "hsan,mac";
		reg = <0x14500000 0x10000>;
		clocks = <&gemac_clk4>;
		resets = <&top_reset 0x30 21>, <&top_reset 0x30 22>;
		reset-names = "mac_logic_reset", "mac_reset";
		status = "okay";
		phy-handle = <&gephy4>;
		phy-mode = "gmii";
		cnt-clr = <0x14980004 4>; // reg bit
		port= <12>;
	};

	mdio0 {
		compatible = "hsan,mdio";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x14000000 0x1000>;

		clocks = <&mdio_clk0>;
		resets = <&top_reset 0x30 6>, <&top_reset 0x30 26>, <&top_reset 0x30 27>,
			<&top_reset 0x30 28>, <&top_reset 0x30 29>, <&top_reset 0x30 30>,
			<&top_reset 0x30 31>;
		reset-names = "mdio_reset", "gephyall_reset", "gephy0_reset",
				"gephy1_reset", "gephy2_reset", "gephy3_reset",
				"gephy4_reset";

		gephy0: ethernet-phy@0 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0x1>;
			max-speed = <1000>;
		};

		gephy1: ethernet-phy@1 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0x2>;
			max-speed = <1000>;
		};

		gephy2: ethernet-phy@2 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0x3>;
			max-speed = <1000>;
		};

		gephy3: ethernet-phy@3 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0x4>;
			max-speed = <1000>;
		};

		gephy4: ethernet-phy@4 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0x5>;
			max-speed = <1000>;
		};
	};

	lsw_dp: lsw_dp {
		compatible = "hsan,dp";
		reg = <HI_LSW_DP_QUEUE_RSV_BUF_START HI_LSW_DP_QUEUE_RSV_BUF_SIZE>,
		      <HI_LSW_DP_FLOW_RSV_BUF_START HI_LSW_DP_FLOW_RSV_BUF_SIZE>,
		      <0x11000000 0x80000>, <0x11280000 0x80000>, <0x11200000 0x80000>,
		      <0x11100000 0x80000>, <0x11080000 0x80000>;
		reg-names = "queue", "flow",
		            "dp", "eps", "eqm",
		            "fam", "ips";
		clocks = <&lsw_dp_clk0>;
		resets = <&top_reset 0x30 0>, <&top_reset 0x30 1>;
		reset-names = "dp_logic", "dp_srst";
		port_bitmap = <0xF01 0x1FF0>;
		hwver = <0x250>;
	};

	lsw_pfe {
		compatible = "hsan,pfe";
		reg = <0x12100000 0x80000>, <0x12180000 0x80000>, <0x12280000 0x80000>,
		      <0x12380000 0x80000>, <0x12400000 0x80000>, <0x12080000 0x80000>,
		      <0x11180000 0x80000>, <0x12000000 0x80000>, <0x11380000 0x80000>, <0x12480000 0x80000>;
		reg-names = "ifc", "l2", "napt",
		            "mcc", "opp", "pa",
		            "pe", "pfe_glb", "prbs", "pub";
		clocks = <&lsw_pfe_clk0>;
		resets = <&top_reset 0x30 3>, <&top_reset 0x30 4>;
		reset-names = "pfe_logic", "pfe_srst";
		hwver = <0x250>;
	};

	efuse_clk_src: efuse_clk_src {
		compatible = "syscon";
		reg = <0x14880138 0x1>;  //clock source
	};

	efuse_ctl: efuse_control_reg {
		compatible = "syscon";
		reg = <0x14900140 0x20>;  //ctrl address
	};

	efuse_data: efuse_data_reg {
		compatible = "syscon";
		reg = <0x14948000 0x100>;  //data address
	};

	jent-rng {
		compatible = "hsan,jent-rng";
	};

	ledpwm0: ledpwm@14900280 {
		compatible = "hsan,ledpwm";
		reg = <0x14900110 0x4>, <0x1490011C 0x4>, <0x14900280 0x8>, <0x14944000 0x200>;
		reg-names = "oen", "loop", "cfg", "steptable";
		clocks = <&led_pwm>;
		nledpwm = <2>;
		#ledpwm-cells = <1>; /* <index> */
		label = "peri0";
		resets = <&top_reset 0x2C 14>;
	};
};

#include "luofu.dtsi"
#include "luofu_turbo.dtsi"
#include "luofu_partition.dtsi"
