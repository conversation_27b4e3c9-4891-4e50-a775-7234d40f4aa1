#################################################################
#
include $(HI_SCRIPT_ENV_PATH)
CHIP_CONFIG := CONFIG_$(shell echo $(HI_PRODUCT_CHIP_NAME)$(HI_CHIP_VERSION) | tr '[a-z]' '[A-Z]')
#
HI_CURRENT_DIR =$(shell pwd)
DTC_FLAGS := -O dtb -I dts
DTC_FLAGS += -p 1024 -i. -@ \
	-i$(HI_KERNEL_PATH)/$(HI_KERNEL_VERSION)/include/
#

.PHONY:pkg_prebuild
pkg_prebuild:

.PHONY:pkg_config
pkg_config:

.PHONY: pkg_build
pkg_build:
	@echo "---------------dts build------------------------"
	########overlay build
	@for board in $(HI_BOARD_TYPE); do cpp -Wp,-MD,$$board.pre.tmp -nostdinc -I. -I$(HI_KERNEL_PATH)/$(HI_KERNEL_VERSION)/include/ -undef -D__DTS__\
		-D$(CHIP_CONFIG) -x assembler-with-cpp -o $$board.dts.tmp $${board}_overlay.dts; done;
	@for board in $(HI_BOARD_TYPE); do dtc $(DTC_FLAGS) -a 4 -o $${board}.dtbo $$board.dts.tmp; done;
	@python ${HI_TOOLS_DIR}/dtbotool.py ${HI_CURRENT_DIR} ${HI_BOARD_TYPE}

	#########dtb build
	@cpp -Wp,-MD,$(HI_PRODUCT_CHIP_NAME).pre.tmp -nostdinc -I. -I$(HI_KERNEL_PATH)/$(HI_KERNEL_VERSION)/include/ -undef -D__DTS__\
		-D$(CHIP_CONFIG) -x assembler-with-cpp -o $(HI_PRODUCT_CHIP_NAME).dts.tmp $(HI_PRODUCT_CHIP_NAME).dts
	@dtc $(DTC_FLAGS) -o $(HI_PRODUCT_CHIP_NAME).dtb $(HI_PRODUCT_CHIP_NAME).dts.tmp

	@rm -f $(HI_CURRENT_DIR)/*.tmp

.PHONY:pkg_install
pkg_install:
	@cp -f $(HI_DTS_PATH)/$(HI_PRODUCT_CHIP_NAME).dtb $(HI_INSTALL_IMAGE_DIR)/$(HI_PRODUCT_CHIP_NAME).dtb

.PHONY: pkg_clean
pkg_clean:
	@rm -f $(HI_DTS_PATH)/$(HI_PRODUCT_CHIP_NAME).dtb
	@rm -f $(HI_DTS_PATH)/$(HI_PRODUCT_CHIP_NAME).*.tmp

.PHONY:pkg_delete
pkg_delete:
	@rm -f $(HI_INSTALL_IMAGE_DIR)/$(HI_PRODUCT_CHIP_NAME).dtb

