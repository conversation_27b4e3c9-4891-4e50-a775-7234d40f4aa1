/*
 * dtsi file for Hisilicon（Shanghai） luofu Development Board
 *
 * Copyright (C) 2021-10-22, Hisilicon Ltd.
 *
 */

 / {
	pinctrl_peri:pinctrl@0x14900000 {
		compatible = "hsan,luofu-peri-pinctrl";
		reg = <0x14900100 0x3c>, <0x14940000 0x100>;
		reg-names = "mux", "cfg";

		jtag_gpio2_default_state: jtag_gpio2_default_state {
			jtag_gpio2_pmux {
				function = "jtag_gpio2";
				groups = "jtag_gpio2_grp";
			};
		};

		jtag_gpio3_default_state: jtag_gpio3_default_state {
			jtag_gpio3_pmux {
				function = "jtag_gpio3";
				groups = "jtag_gpio3_grp";
			};
		};

		jtag_gpio4_default_state: jtag_gpio4_default_state {
			jtag_gpio4_pmux {
				function = "jtag_gpio4";
				groups = "jtag_gpio4_grp";
			};
		};

		jtag_gpio5_default_state: jtag_gpio5_default_state {
			jtag_gpio5_pmux {
				function = "jtag_gpio5";
				groups = "jtag_gpio5_grp";
			};
		};

		spi {
			spi_default_state: spi_default_state {
				spi_pmux {
					function = "spi";
					groups = "spi_grp";
				};
			};

			spi_sleep_state: spi_sleep_state {
				spi_pmux {
					function = "spi";
					groups = "spi_grp";
				};
			};
		};
		pmupwm_default_state: pmupwm_default_state {
			pmupwm_pmux {
				function = "avs";
				groups = "avs_grp";
			};
		};
		led0_1_default_state: led0_1_default_state {
			led0_0_pmux {
				function = "led0_1";
				groups = "led0_1_grp";
			};
		};
		led1_1_default_state: led1_1_default_state {
			led1_1_pmux {
				function = "led1_1";
				groups = "led1_1_grp";
			};
		};
	};
};
