/*
 * dts file for Hisilicon luofu Development Board
 *
 * Copyright (C) 2022-09-07, Hisilicon Ltd.
 *
 */

/dts-v1/;
/plugin/;

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include "luofu.h"

/ {
	compatible = "hsan-luofu";
	board_id = <0x00000000>;
	vendor_id = "hsan";

	fragment@0 {
		target-path = "/";
		__overlay__ {
			board_id = <0x00000000>;
			vendor_id = "hsan";
			board_name = "ax3000";

			keys: keys {
				compatible = "gpio-keys";
				reset {
					label = "reset";
					gpios = <&porta 7 GPIO_ACTIVE_LOW>;
					linux,code = <KEY_RESTART>;
				};
				wps {
					label = "wps";
					gpios = <&porta 28 GPIO_ACTIVE_LOW>;
					linux,code = <KEY_WPS_BUTTON>;
				};
			};

			gpio-leds {
				compatible = "gpio-leds";
				power_led {
					label = "led_power";
					gpios = <&porta 24 GPIO_ACTIVE_LOW>;
					deflate-state = "off";
				};
				wifi1_led {
					label = "led_wifi1";
					gpios = <&porta 25 GPIO_ACTIVE_LOW>;
					deflate-state = "off";
				};
				wifi2_led {
					label = "led_wifi2";
					gpios = <&porta 0 GPIO_ACTIVE_LOW>;
					deflate-state = "off";
				};
				wifi3_led {
					label = "led_wifi3";
					gpios = <&porta 1 GPIO_ACTIVE_LOW>;
					deflate-state = "off";
				};
			};
		};
	};

	fragment@1 {
		target = <&pcie0>;
		__overlay__ {
			capability = <0x3>;
			gpio-delay = <50>;
			pcie-gpios = <&porta 20 GPIO_ACTIVE_HIGH>, <&porta 21 GPIO_ACTIVE_HIGH>,
					<&porta 23 GPIO_ACTIVE_HIGH>; /* new ax3000 */
			//pcie-gpios = <&porta 2 GPIO_ACTIVE_HIGH>, <&porta 10 GPIO_ACTIVE_HIGH>,
					//<&porta 11 GPIO_ACTIVE_HIGH>, <&porta 9 GPIO_ACTIVE_HIGH>; /* old ax3000 */
		};
	};

	fragment@2 {
		target = <&pcie1>;
		__overlay__ {
			capability = <0x3>;
			gpio-delay = <50>;
			pcie-gpios;
		};
	};

	fragment@3 {
		target = <&gemac0>;
		__overlay__ {
			status = "disabled";
		};
	};

	fragment@4 {
		target = <&gephy0>;
		__overlay__ {
			status = "disabled";
		};
	};

	fragment@5 {
		target = <&lsw_dp>;
		__overlay__ {
			reg = <HI_LSW_DP_KERNEL_QUEUE_RSV_BUF_START HI_LSW_DP_QUEUE_RSV_BUF_SIZE>,
				<HI_LSW_DP_KERNEL_FLOW_RSV_BUF_START HI_LSW_DP_FLOW_RSV_BUF_SIZE>,
				<0x11000000 0x80000>, <0x11280000 0x80000>, <0x11200000 0x80000>,
				<0x11100000 0x80000>, <0x11080000 0x80000>;
		};
	};

	fragment@ledpwm0 {
		target = <&ledpwm0>;
		__overlay__ {
			pinctrl-names = "led0_1", "led1_1";
			pinctrl-0 = <&led0_1_default_state>;
			pinctrl-1 = <&led1_1_default_state>;
		};
	};

	fragment@pmupwm {
		target = <&pmupwm>;
		__overlay__ {
			pinctrl-names = "enable";
			pinctrl-0 = <&pmupwm_default_state>;
		};
	};

	fragment@pinctrl_peri {
		target = <&pinctrl_peri>;
		__overlay__ {
			pinctrl-names = "default";
			pinctrl-0 = <&jtag_gpio2_default_state &jtag_gpio3_default_state \
				&jtag_gpio4_default_state &jtag_gpio5_default_state>;
		};
	};
};
