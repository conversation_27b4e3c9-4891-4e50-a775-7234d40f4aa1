/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: header files
 * Author: hsan
 * Create: 2022-10-12
 */

#ifndef LUOFU_H
#define LUOFU_H

#include <linux/sizes.h>

/* MEMORY: DDR */
#define HI_DDR_START_ADDR  0x80000000

/* must 2M aligned */
#define HI_LSW_DP_RSV_BUF_SIZE HI_DDR_RESV_SIZE

/* 4M */
#define HI_LSW_DP_QUEUE_RSV_BUF_SIZE 0x400000
#define HI_LSW_DP_FLOW_RSV_BUF_SIZE (HI_DDR_RESV_SIZE - HI_LSW_DP_QUEUE_RSV_BUF_SIZE)

/* U-Boot stage lsw dp reserved buf position */
#define HI_LSW_DP_QUEUE_RSV_BUF_START (HI_DDR_START_ADDR + HI_DDR_SIZE - HI_LSW_DP_RSV_BUF_SIZE)
#define HI_LSW_DP_FLOW_RSV_BUF_START (HI_LSW_DP_QUEUE_RSV_BUF_START + HI_LSW_DP_QUEUE_RSV_BUF_SIZE)

/* Kernel/Linux stage lsw dp reserved buf position */
#define HI_LSW_DP_KERNEL_QUEUE_RSV_BUF_START HI_DDR_START_ADDR
#define HI_LSW_DP_KERNEL_FLOW_RSV_BUF_START (HI_LSW_DP_KERNEL_QUEUE_RSV_BUF_START + HI_LSW_DP_QUEUE_RSV_BUF_SIZE)
#define HI_FLASH_SHARE_MEM_SIZE   0x4000

#define HI_LINUX_DDR_MEMORY_START (HI_DDR_START_ADDR + HI_DDR_RESV_SIZE)
#define HI_LINUX_DDR_MEMORY_SIZE  (HI_DDR_SIZE - HI_DDR_RESV_SIZE)

/* MEMORY: FLASH */
#define HI_MTD_SIZE        SZ_128M

/* partition table */
#define HI_ESBC_SIZE        SZ_256K
#define HI_UBOOTA_SIZE      SZ_1M
#define HI_UBOOTB_SIZE      SZ_1M
/* 需同步修改uboot env */
#define HI_ENVA_SIZE        SZ_256K
#define HI_ENVB_SIZE        SZ_256K
#define HI_FAC_SIZE         SZ_2M
#define HI_CFGA_SIZE        SZ_2M
#define HI_CFGB_SIZE        SZ_2M
#define HI_LOG_SIZE         (SZ_4M + SZ_256K)
#define HI_PSTORE_SIZE      SZ_256K

#define HI_KERNELA_SIZE     (SZ_8M - SZ_2M)
#define HI_KERNELB_SIZE     (SZ_8M - SZ_2M)

#define HI_ROOTFSA_SIZE     (SZ_32M + SZ_16M - SZ_2M - SZ_1M)
#define HI_ROOTFSB_SIZE     (SZ_32M + SZ_16M - SZ_2M - SZ_1M)

#define HI_OTHER_SIZE       (HI_MTD_SIZE - \
				(HI_ESBC_SIZE + \
				HI_ENVA_SIZE + HI_ENVB_SIZE + \
				HI_UBOOTA_SIZE +  HI_UBOOTB_SIZE  + \
				HI_FAC_SIZE + HI_CFGA_SIZE + \
				HI_CFGB_SIZE + HI_LOG_SIZE + HI_PSTORE_SIZE+ \
				HI_KERNELA_SIZE + HI_KERNELB_SIZE + \
				HI_ROOTFSA_SIZE + HI_ROOTFSB_SIZE))

#define HI_ESBC_OFFSET      0x0
#define HI_UBOOTA_OFFSET    (HI_ESBC_OFFSET + HI_ESBC_SIZE)
#define HI_UBOOTB_OFFSET    (HI_UBOOTA_OFFSET + HI_UBOOTA_SIZE)
#define HI_ENVA_OFFSET      (HI_UBOOTB_OFFSET + HI_UBOOTB_SIZE)
#define HI_ENVB_OFFSET      (HI_ENVA_OFFSET + HI_ENVA_SIZE)
#define HI_FAC_OFFSET       (HI_ENVB_OFFSET + HI_ENVB_SIZE)
#define HI_CFGA_OFFSET      (HI_FAC_OFFSET + HI_FAC_SIZE)
#define HI_CFGB_OFFSET      (HI_CFGA_OFFSET + HI_CFGA_SIZE)
#define HI_LOG_OFFSET       (HI_CFGB_OFFSET + HI_CFGB_SIZE)
#define HI_PSTORE_OFFSET    (HI_LOG_OFFSET + HI_LOG_SIZE)
#define HI_KERNELA_OFFSET   (HI_PSTORE_OFFSET + HI_PSTORE_SIZE)
#define HI_KERNELB_OFFSET   (HI_KERNELA_OFFSET + HI_KERNELA_SIZE)
#define HI_ROOTFSA_OFFSET   (HI_KERNELB_OFFSET + HI_KERNELB_SIZE)
#define HI_ROOTFSB_OFFSET   (HI_ROOTFSA_OFFSET + HI_ROOTFSA_SIZE)
#define HI_OTHER_OFFSET     (HI_ROOTFSB_OFFSET + HI_ROOTFSB_SIZE)

/* HARDWARE VERSION */
#define TVSENSOR_HWVER 3

/* cpufreq */
#define CPU_FREQ_NORMAL 1000000000
#define CPU_FREQ_BOOST 1200000000

#endif /* LUOFU_H */
