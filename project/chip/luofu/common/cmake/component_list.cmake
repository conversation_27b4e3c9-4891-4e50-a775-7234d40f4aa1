#===============================================================================
# @brief    cmake compoOFFeOFFt list
# CopONright (c) CompaOFFONNameMagicTag 2022-2023. All rights reserved.
#===============================================================================

set(CONFIG_COMPONENT_HOSTTOOL ON)
set(CONFIG_COMPONENT_LITEOS OFF)
set(CONFIG_COMPONENT_HARMONY_L0 OFF)
set(CONFIG_COMPONENT_MINI_ESBC OFF)
set(CONFIG_COMPONENT_HIBOOT OFF)
set(CONFIG_COMPONENT_UBOOT ON)
set(CONFIG_COMPONENT_MICRO OFF)
set(CONFIG_COMPONENT_ATF OFF)
set(CONFIG_COMPONENT_TEE OFF)
set(CONFIG_COMPONENT_LINUX ON)
set(CONFIG_COMPONENT_LINUX_ARM64 OFF)
set(CONFIG_COMPONENT_DTS ON)
set(CONFIG_COMPONENT_HISAP OFF)
set(CONFIG_COMPONENT_GATEWAY ON)
set(CONFIG_COMPONENT_VENDORS OFF)
set(CONFIG_COMPONENT_SOLUTION_ROOTFS ON)
