#===============================================================================
# @brief    cmake board parameter info
# Copyright (c) Hisilicon 2022-2023. All rights reserved.
#===============================================================================

# -- DDR_DEFUALT_FREQ -- #
# DDR3_400M # 0xdfdf0400
# DDR3_667M # 0xdfdf0667
# DDR3_781M # 0xdfdf0781
# DDR3_800M # 0xdfdf0800
# DDR3_850M # 0xdfdf0850
# DDR3_933M # 0xdfdf0933

set(CONFIG_DEFAULT_DDRFREQ 0xdfdf0781)

# -- SOFT_WATCH_DOG -- #
set(CONFIG_SOFT_WATCHDOG_THRESH_TURBO 50)
set(CONFIG_SOFT_WATCHDOG_THRESH_DEBUG 10)
set(CONFIG_SOFT_WATCHDOG_THRESH_RELEASE 10)

set(CONFIG_SOFT_WATCHDOG_EN_TURBO 0)
set(CONFIG_SOFT_WATCHDOG_EN_DEBUG 1)
set(CONFIG_SOFT_WATCHDOG_EN_RELEASE 1)

# -- QUEUE_BUFFER -- #
set(CONFIG_QUEUE_BUFFER_TURBO 30000)
set(CONFIG_QUEUE_BUFFER_DEBUG 5000)
set(CONFIG_QUEUE_BUFFER_RELEASE 5000)

# -- ROOTFS_USER -- #
set(CONFIG_ROOTFS_UID 1000)
set(CONFIG_ROOTFS_GID 1000)
