#===============================================================================
# @brief    cmake product info
# Copyright (c) Hisilicon 2024-2024. All rights reserved.
#===============================================================================
set(CONFIG_CHIP_NAME "emei")
set(CONFIG_CHIP_VERSION "v500")

set(CONFIG_PRODUCT_NAME "wrt_g5")
set(CONFIG_PRODUCT_VERSION "ChenTang_1.2.13.linux0")

set(CONFIG_FLASH_TYPE "spi")
set(CONFIG_FLASH_PAGE_SIZE 2048)
set(CONFIG_FLASH_BLOCK_SIZE 131072)
set(CONFIG_FLASH_PARTS_NAME wrt_hgu)

set(CONFIG_UBOOT_SIZE_MAX 1048576)

set(CONFIG_UBOOT_ARCH_TYPE "arm")
set(CONFIG_LINUX_KERNEL_TYPE "arm")
set(CONFIG_LINUX_APP_TYPE "arm")
set(CONFIG_DDR_SIZE "128M")
set(CONFIG_LINUX_TEXT_OFFSET "0x80008000")
set(CONFIG_ESBC_LINK_ADDR 0x11345100)
set(CONFIG_OVERLAY_BOARD_TYPE
    vendor5_gpon7_wuyi_wrt
    hsan_gpon7_wuyi_wrt
)

set(CONFIG_BOOT_UNSECBOOT ON)
set(CONFIG_BUILD_IMAGE_HEAD ON)
set(CONFIG_IP_VERSION_HWDIO "1.0")
set(CONFIG_IP_VERSION_CRYP "2.0")
set(CONFIG_IP_VERSION_LSW_DP_SRC "v200")
set(CONFIG_IP_VERSION_LSW_DP_INC "v200")
set(CONFIG_IP_VERSION_LSW_PFE "v200")
set(CONFIG_IP_VERSION_PCIE "v1")
set(CONFIG_IP_VERSION_NET_PON_GLB "v200")
set(CONFIG_IP_VERSION_NET_PON_MAC "v200")
set(CONFIG_IP_VERSION_EFUSE "1.0")
set(CONFIG_IP_VERSION_PINCTRL "1.0")

set(CONFIG_TOOLCHAIN_ESBC "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_UBOOT "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_LINUX_DRV "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_LINUX_APP "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_TEE_DRV "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_TEE_SRV "toolchain-arm-mix510-linux")
set(CONFIG_TOOLCHAIN_MVN "toolchain-apache-maven")

set(CONFIG_TOOLCHIAN_SEL_ARM_MIX510_LINUX ON)
set(CONFIG_TOOLCHIAN_SEL_APACHE_MAVEN OFF)

set(CONFIG_KERNEL_INITRAMFS_SOURCE_DIR ${CONIFG_INSTALL_TMPFS_DIR})
set(CONFIG_LINUX_KERNEL_DIR
    ${OPEN_SOURCE_OPENWRT_BINARY_DIR}/openwrt/build_dir/target-arm-mix510-linux_musl/linux-hisilicon_${CONFIG_CHIP_NAME}/linux-5.10.201
)
