#===============================================================================
# @brief    cmake board parameter info
# Copyright (c) Hisilicon 2022-2023. All rights reserved.
#===============================================================================

# -- CPU_DEFUALT_FREQ -- #
set(CONFIG_CPU_FREQ_DEBUG OFF)
#  800M # 0xcaca0800
#  900M # 0xcaca0900
# 1000M # 0xcaca1000
# 1100M # 0xcaca1100
# 1200M # 0xcaca1200
set(CONFIG_DEFAULT_CPU_FREQ_TURBO 0xcaca1000)
set(CONFIG_DEFAULT_CPU_FREQ_DEBUG 0xcaca0800)
set(CONFIG_DEFAULT_CPU_FREQ_RELEASE 0xcaca0800)


# -- DDR_DEFUALT_FREQ -- #
set(CONFIG_DDR_TYPE DDR3)
set(CONFIG_DDR_FREQ_DEBUG ON)
# DDR_333M # 0xdfdf0333
# DDR_667M # 0xdfdf0667
# DDR_781M # 0xdfdf0781
# DDR_800M # 0xdfdf0800
# DDR_850M # 0xdfdf0850
# DDR_933M # 0xdfdf0933
set(CONFIG_DEFAULT_DDR_FREQ_TURBO 0xdfdf0850)
set(CONFIG_DEFAULT_DDR_FREQ_DEBUG 0xdfdf0781)
set(CONFIG_DEFAULT_DDR_FREQ_RELEASE 0xdfdf0781)


# -- CLK_FREQ -- #
#  666Mhz 166000000
#  800Mhz 200000000
#  900Mhz 225000000
# 1000Mhz 250000000
# 1200Mhz 300000000
set(CONFIG_DEFAULT_CLKFREQ_TURBO 250000000)
set(CONFIG_DEFAULT_CLKFREQ_DEBUG 200000000)
set(CONFIG_DEFAULT_CLKFREQ_RELEASE 200000000)

# -- SOFT_WATCH_DOG -- #
set(CONFIG_SOFT_WATCHDOG_THRESH_TURBO 50)
set(CONFIG_SOFT_WATCHDOG_THRESH_DEBUG 10)
set(CONFIG_SOFT_WATCHDOG_THRESH_RELEASE 10)

set(CONFIG_SOFT_WATCHDOG_EN_TURBO 0)
set(CONFIG_SOFT_WATCHDOG_EN_DEBUG 1)
set(CONFIG_SOFT_WATCHDOG_EN_RELEASE 1)

# -- QUEUE_BUFFER -- #
set(CONFIG_QUEUE_BUFFER_TURBO 12000)

# -- ROOTFS_USER -- #
set(CONFIG_ROOTFS_UID 1000)
set(CONFIG_ROOTFS_GID 1000)
