#include "emei.h"
/ {
	osc_clk: fixed_clk@25000000 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <25000000>;
		clock-output-names = "osc";
	};

	arm_timer_clk: arm_timer_clk {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <DEFAULT_CLKFREQ>;
	};

	apb_clk: fixed_clk@100000000 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <100000000>;
		clock-output-names = "apb-clk";
	};

	fixed_clk50m: clk@50000000 {
		compatible = "fixed-clock";
		#clock-cells = <0x0>;
		clock-frequency = <50000000>;
		clock-output-names = "fixed_clk50m";
	};

	fixed_hwdio_clk: clk@49125000 {
		compatible = "fixed-clock";
		#clock-cells = <0x0>;
		clock-frequency = <49125000>;
		clock-output-names = "fixed_hwdio_clk";
	};

	top_crg: clk@14880000 {
		compatible = "syscon", "simple-mfd";
		reg = <0x14880000 0x1000>;
	};

	ecs_crg: clk@148a0000 {
		compatible = "hsan,clk", "syscon", "simple-mfd";
		reg = <0x148A0000 0x1000>;

		uart0_clk: clk_gate@001410 {
                        compatible = "hsan,clk-gate";
                        #clock-cells = <0>;
                        clocks = <&apb_clk>;
                        clock-output-names = "uart0_clk";
                        reg-offset = <0x14>;
                        bit = <10>;
                };

		sfc_clk: clk_gate@001400 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&apb_clk>;
			clock-output-names = "sfc_clk";
			reg-offset = <0x14>;
			bit = <0>;
		};

		hwdio_clk: hwdioclk_gate@001403 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&fixed_hwdio_clk>;
			clock-output-names = "hwdio_clk";
			reg-offset = <0x14>;
			bit = <3>;
		};

		spi1_clk: clk_gate@001409 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&apb_clk>;
			clock-output-names = "spi1_clk";
			reg-offset = <0x14>;
			bit = <9>;
		};

		pie_clk0: clk_gate@002025 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "pie_clk0";
			reg-offset = <0x20>;
			bit = <25>;
		};

		dmac_clk: clk_gate@002011 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			clocks = <&arm_timer_clk>;
			clock-output-names = "dmac_clk";
			reg-offset = <0x20>;
			bit = <11>;
		};

		pcie0_clk: clk_gate@002013 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <13>;
			clocks = <&apb_clk>;
			clock-output-names = "pcie0_clk";
		};

		pcie1_clk: clk_gate@002014 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <14>;
			clocks = <&apb_clk>;
			clock-output-names = "pcie1_clk";
		};

		port1_usb2_clk: clk_gate@002012 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <12>;
			clocks = <&apb_clk>;
			clock-output-names = "port1_usb2_clk";
		};

		port0_usb_apb_clk: clk_gate@002031 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <31>;
			clocks = <&apb_clk>;
			clock-output-names = "port0_usb_apb_clk";
		};

		port0_usb_ctrl_clk: clk_gate@002030 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <30>;
			clocks = <&apb_clk>;
			clock-output-names = "port0_usb_ctrl_clk";
		};

		port0_usb3_phy_clk: clk_gate@002018 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <18>;
			clocks = <&apb_clk>;
			clock-output-names = "port0_usb3_phy_clk";
		};

		port0_usb2_phy_clk: clk_gate@002016 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <16>;
			clocks = <&apb_clk>;
			clock-output-names = "port0_usb2_phy_clk";
		};
	};

	clk@14890000 {
		compatible = "hsan,clk", "syscon", "simple-mfd";
		reg = <0x14890000 0x2000>;

		lsw_dp_clk0: periclk_gate@002000 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <0>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "lsw_dp_clk0";
		};

		lsw_pfe_clk0: periclk_gate@002001 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <1>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "lsw_pfe_clk0";
		};

		lsw_clk2: periclk_gate@002002 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x20>;
			bit = <2>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "lsw_clk2";
		};

		gemac_clk0: itfclk_gate@102005 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <5>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "gemac_clk0";
		};

		gemac_clk1: itfclk_gate@102006 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <6>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "gemac_clk1";
		};

		gemac_clk2: itfclk_gate@102007 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <7>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "gemac_clk2";
		};

		gemac_clk3: itfclk_gate@102008 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <8>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "gemac_clk3";
		};

		gemac_clk4: itfclk_gate@102009 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <9>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "gemac_clk4";
		};

		gemac_clk8: itfclk_gate@102026 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <26>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "gemac_clk8";
		};

		mdio_clk0: itfclk_gate@102014 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <14>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "mdio_clk0";
		};

		mdio_clk1: itfclk_gate@102015 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <15>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "mdio_clk1";
		};

		pcs_clk0: itfclk_gate@102027 {
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <27>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "pcs_clk0";
		};

		gmac_clk0: itfclk_gate@102000{
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <0>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "gmac_clk0";
		};

		emac_clk0: itfclk_gate@102002{
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <2>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "emac_clk0";
		};

		pon_global_clk0: itfclk_gate@102029{
			compatible = "hsan,clk-gate";
			#clock-cells = <0>;
			reg-offset = <0x1020>;
			bit = <29>;
			clocks = <&fixed_clk50m>;
			clock-output-names = "pon_global_clk0";
		};
	};
};
