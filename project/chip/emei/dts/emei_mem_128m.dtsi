/*
 * Copyright (C) 2025-06-18, HiSilicon (Shanghai) Ltd.
 *
 * dts file for Hisilicon emei Development Board
 *
 */

#define HI_FLASH_INFO_SIZE 0x4000
#define HI_FLASH_INFO_START 0x80000000
#define HI_LSW_DP_SHARE_BUF0_SIZE  0x400000
#define HI_LSW_DP_SHARE_BUF1_SIZE  0x200000
#define HI_LSW_DP_SHARE_BUF0_START 0x87A00000
#define HI_LSW_DP_SHARE_BUF1_START (HI_LSW_DP_SHARE_BUF0_START + HI_LSW_DP_SHARE_BUF0_SIZE)
#define HI_VOIP_RSV_BUF_SIZE      0x100000
#define HI_VOIP_RSV_BUF_START     0x87900000

&pie {
	tx_ring = <12 2>; // <2^ring_size, ch_num>
	rx_ring = <8 2048>, // <2^ring_size, frame_len>
		<8 2048>;
	woc_tx_ring = <12 2>;
	woc_rx_ring = <9 2048>,
		<9 2048>;
};

&flashinfo_reserved {
	reg = <HI_FLASH_INFO_START HI_FLASH_INFO_SIZE>;
	atag-offset = <0x1000>;
};

&lswdpinfo_reserved {
	reg = <HI_LSW_DP_SHARE_BUF0_START (HI_LSW_DP_SHARE_BUF0_SIZE + HI_LSW_DP_SHARE_BUF1_SIZE)>;
};

&voipinfo_reserved {
	reg = <HI_VOIP_RSV_BUF_START HI_VOIP_RSV_BUF_SIZE>;
};

&lsw_dp {
	reg = <HI_LSW_DP_SHARE_BUF0_START HI_LSW_DP_SHARE_BUF0_SIZE>,<HI_LSW_DP_SHARE_BUF1_START HI_LSW_DP_SHARE_BUF1_SIZE>,
		  <0x11000000 0x80000>, <0x11280000 0x80000>, <0x11200000 0x80000>,
		  <0x11100000 0x80000>, <0x11080000 0x80000>;
};

&voip {
	dsp-phyaddr = <HI_VOIP_RSV_BUF_START>;
};
