string(TOUPPER ${CONFIG_BOOT_VER_TYPE} BOOT_VER_TYPE_U)
set(LIST_PRIVATE_DTS_DEFINEZTIONS
    -DCONFIG_EMEIV500
    -DDEFAULT_CLKFREQ=${CONFIG_DEFAULT_CLKFREQ_${BOOT_VER_TYPE_U}}
    -I${CMAKE_CURRENT_BINARY_DIR}
    -I${CMAKE_CURRENT_SOURCE_DIR}
)
set(MEM_POSTFIX_NAME "common")
if(CONFIG_DDR_SIZE)
string(TOLOWER ${CONFIG_DDR_SIZE} MEM_POSTFIX_NAME)
endif()
configure_file(
    emei_mem_${MEM_POSTFIX_NAME}.dtsi
    emei_mem.dtsi COPYONLY
)
configure_file(
    ${CONFIG_CHIP_NAME}_partition_${CONFIG_FLASH_PARTS_NAME}.dtsi
    ${CONFIG_CHIP_NAME}_partition.dtsi COPYONLY
)

include(${CONFIG_CMAKE_DIR}/compile_dts.cmake)
