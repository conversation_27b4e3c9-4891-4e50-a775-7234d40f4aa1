/*
 * Copyright (c) Hisilicon(Shanghai) Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: header files
 * Author: hsan
 * Create: 2022-10-12
 */

#ifndef EMEI_H
#define EMEI_H

#include <linux/sizes.h>

#ifndef DEFAULT_CLKFREQ
#define DEFAULT_CLKFREQ 200000000
#endif

/* MEMORY: DDR */
#define HI_DDR_START_ADDR  0x80000000
#define HI_DDR_SIZE        SZ_512M

#define HI_FLASH_SHARE_MEM_START  HI_DDR_START_ADDR
#define HI_FLASH_SHARE_MEM_SIZE   0x4000
#define HI_LINUX_DDR_MEMORY_START HI_DDR_START_ADDR
#define HI_LINUX_DDR_MEMORY_SIZE  HI_DDR_SIZE

/* MEMORY: FLASH */
#define HI_MTD_SIZE        SZ_256M

/* partition table */
#define HI_UBOOT_SIZE       SZ_1M
/* 需同步修改uboot env */
#define HI_ENVA_SIZE        SZ_256K
#define HI_ENVB_SIZE        SZ_256K
#define HI_FAC_SIZE         SZ_2M
#define HI_CFGA_SIZE        SZ_2M
#define HI_CFGB_SIZE        SZ_2M
#define HI_LOG_SIZE         (SZ_4M + SZ_1M - SZ_256K)
#define HI_PSTORE_SIZE      SZ_256K
#define HI_KERNELA_SIZE     (SZ_4M + SZ_2M)
#define HI_KERNELB_SIZE     (SZ_4M + SZ_2M)

#define HI_UBOOT_OFFSET      0x0
#define HI_ENVA_OFFSET       (HI_UBOOT_OFFSET + HI_UBOOT_SIZE)
#define HI_ENVB_OFFSET       (HI_ENVA_OFFSET + HI_ENVA_SIZE)
#define HI_FAC_OFFSET        (HI_ENVB_OFFSET + HI_ENVB_SIZE)
#define HI_CFGA_OFFSET       (HI_FAC_OFFSET + HI_FAC_SIZE)
#define HI_CFGB_OFFSET       (HI_CFGA_OFFSET + HI_CFGA_SIZE)
#define HI_LOG_OFFSET        (HI_CFGB_OFFSET + HI_CFGB_SIZE)
#define HI_PSTORE_OFFSET     (HI_LOG_OFFSET + HI_LOG_SIZE)
#define HI_KERNELA_OFFSET    (HI_PSTORE_OFFSET + HI_PSTORE_SIZE)
#define HI_KERNELB_OFFSET    (HI_KERNELA_OFFSET + HI_KERNELA_SIZE)

/* TVSensor */
#ifdef CONFIG_EMEIV100
#define TVSENSOR_HWVER 1
#elif defined(CONFIG_EMEIV500)
#define TVSENSOR_HWVER 2
#endif

#endif /* EMEI_H */
