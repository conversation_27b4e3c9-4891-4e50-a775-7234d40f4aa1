/*
 * dts file for Hisilicon xiling Development Board
 *
 * Copyright (C) 2023-10-11, Hisilicon Ltd.
 *
 */

#include "emei.h"

#define HI_ROOTFSA_SIZE     (SZ_64M + SZ_8M)
#define HI_ROOTFSB_SIZE     (SZ_64M + SZ_8M)
#define HI_FWKA_SIZE        (SZ_16M)
#define HI_FWKB_SIZE        (SZ_16M)
#define HI_APP_SIZE         (HI_MTD_SIZE - \
				(HI_UBOOT_SIZE + \
				HI_ENVA_SIZE + HI_ENVB_SIZE + \
				HI_FAC_SIZE +  HI_CFGA_SIZE + \
				HI_CFGB_SIZE + HI_LOG_SIZE + HI_PSTORE_SIZE+ \
				HI_KERNELA_SIZE + HI_KERNELB_SIZE + \
				HI_ROOTFSA_SIZE + HI_ROOTFSB_SIZE + \
				HI_FWKA_SIZE + HI_FWKB_SIZE))
#define HI_ROOTFSA_OFFSET    (HI_KERNELB_OFFSET + HI_KERNELB_SIZE)
#define HI_ROOTFSB_OFFSET    (HI_ROOTFSA_OFFSET + HI_ROOTFSA_SIZE)
#define HI_FWKA_OFFSET       (HI_ROOTFSB_OFFSET + HI_ROOTFSB_SIZE)
#define HI_FWKB_OFFSET       (HI_FWKA_OFFSET + HI_FWKA_SIZE)
#define HI_APP_OFFSET        (HI_FWKB_OFFSET + HI_FWKB_SIZE)

&fmc {
	partitions {
		compatible = "fixed-partitions";
		#address-cells = <1>;
		#size-cells = <1>;

		partition@0 {
			label = "uboot";
			reg = <HI_UBOOT_OFFSET HI_UBOOT_SIZE>;
		};
		partition@1 {
			label = "enva";
			reg = <HI_ENVA_OFFSET HI_ENVA_SIZE>;
		};
		partition@2 {
			label = "envb";
			reg = <HI_ENVB_OFFSET HI_ENVB_SIZE>;
		};
		partition@3 {
			label = "fac";
			reg = <HI_FAC_OFFSET HI_FAC_SIZE>;
		};
		partition@4 {
			label = "cfga";
			reg = <HI_CFGA_OFFSET HI_CFGA_SIZE>;
		};
		partition@5 {
			label = "cfgb";
			reg = <HI_CFGB_OFFSET HI_CFGB_SIZE>;
		};
		partition@6 {
			label = "log";
			reg = <HI_LOG_OFFSET HI_LOG_SIZE>;
		};
		partition@7 {
			label = "pstore";
			reg = <HI_PSTORE_OFFSET HI_PSTORE_SIZE>;
		};
		partition@8 {
			label = "kernela";
			reg = <HI_KERNELA_OFFSET HI_KERNELA_SIZE>;
		};
		partition@9 {
			label = "kernelb";
			reg = <HI_KERNELB_OFFSET HI_KERNELB_SIZE>;
		};
		partition@10 {
			label = "rootfsa";
			reg = <HI_ROOTFSA_OFFSET HI_ROOTFSA_SIZE>;
		};
		partition@11 {
			label = "rootfsb";
			reg = <HI_ROOTFSB_OFFSET HI_ROOTFSB_SIZE>;
		};
		partition@12 {
			label = "fwka";
			reg = <HI_FWKA_OFFSET HI_FWKA_SIZE>;
		};
		partition@13 {
			label = "fwkb";
			reg = <HI_FWKB_OFFSET HI_FWKB_SIZE>;
		};
		partition@14 {
			label = "app";
			reg = <HI_APP_OFFSET HI_APP_SIZE>;
		};
	};
};
