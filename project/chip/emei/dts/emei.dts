/*
 * dts file for Hisilicon emei Development Board
 *
 * Copyright (C) 2016, Hisilicon Ltd.
 *
 */

/dts-v1/;
#include <dt-bindings/gpio/gpio.h>
#include "emei.h"
#include "emei_clk.dtsi"
#include "emei-pinctrl.dtsi"
/ {
	compatible = "hsan-emei";
	#address-cells = <1>;
	#size-cells = <1>;
	interrupt-parent = <&gic>;

	aliases {
		console = &uart1;
		serial0 = &uart1;
		serial1 = &uart0;
	};

	chosen {
		bootargs = "noinitrd cma=0 console=ttyS0,115200 earlycon maxcpus=2 nr_cpus=2 additional_cpus=1";
		linux,stdout-path = &uart1;
		tick-timer = &local_timer;
	};

	cpus {
		#address-cells = <0x1>;
		#size-cells = <0x0>;
		enable-method = "hisilicon,hsan_smp";

		cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0x0>;
		};

		cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a9";
			reg = <0x1>;
		};
	};

	twd_clk_modifier: clk_modifier@0 {
		compatible = "hsan,hsan-clk-modifier";
		clocks = <&arm_timer_clk>;

		freq_div = <4>;
		set_freq_addr = <0x10100c04>;
	};

	gic: interrupt-controller {
		compatible = "arm,cortex-a9-gic";
		#interrupt-cells = <0x3>;
		interrupt-controller;
		/* gic uboot init feature */
		off-secure-status;
		/* gic dist base, gic cpu base */
		reg = <0x10181000 0x1000>,
			<0x10180100 0x100>;
	};

	scu@0x10180000 {
		compatible = "arm,cortex-a9-scu";
		reg = <0x10180000 0x1000>;
	};

	local_timer: local_timer@10180600 {
		compatible = "arm,cortex-a9-twd-timer";
		reg = <0x10180600 0x20>;
		interrupts = <1 13 0x301>;
		clocks = <&arm_timer_clk>;

		load-reg-offset = <0x0>;
		count-reg-offset = <0x4>;
		contrl-reg-offset = <0x8>;
		status-reg-offset = <0xc>;
	};

	timer0: timer@10104000 {
		compatible = "arm,sp804";
		reg = <0x10104000 0x1000>;
		interrupts = <1 47 0x4>; //79
		/* timer00 & timer01 */
		clocks = <&apb_clk>;
	};

	global_timer: global_timer@10180200 {
		compatible = "arm,cortex-a9-global-timer";
		reg = <0x10180200 0x20>;
		interrupts = <1 11 0x301>;
		clocks = <&arm_timer_clk>;
	};

	memory {
		device_type = "memory";
		reg = <HI_LINUX_DDR_MEMORY_START HI_LINUX_DDR_MEMORY_SIZE>;
	};

	reset_top: reset@0 {
		compatible = "hsan,hsan-reset";
		#reset-cells = <2>;
		value = <0x0>;
	};

	reset_lsw: reset@1 {
		compatible = "hsan,hsan-reset";
		#reset-cells = <2>;
		value = <0x1>;
	};

	reset_ecs: reset@2 {
		compatible = "hsan,hsan-reset";
		#reset-cells = <2>;
		value = <0x2>;
	};

	crg@0x14880000 {
		compatible = "hsan,crg";
		reg = <0x14880000 0x1000>, //top crg
			<0x14890000 0x2000>, //lsw crg
			<0x148a0000 0x1000>; //ecs crg

		reboot-offset = <0x0>;
		reboot-base-index = <0x0>;
		resume-offset = <0x38>;
		core-info = <8 12>, <9 13>;
		softrst_val0 = <0x51162100>;
		softrst_val1 = <0xAEE9DEFF>;
	};

	watchdog {
		compatible = "hsan,hsan-watchdog";
		hwver = <1>;
		reg = <0x14880000 0x1000>;
		timeout-sec = <30>;
		timeout-min = <1>;
		timeout-max = <512>;
		interrupts = <0 2 4>;
		reset-val0 = <0x55AA5A5A>;
		reset-val1 = <0xAA55A5A5>;
		stop-val0 = <0xABCD5116>;
		stop-val1 = <0xED574447>;
		enable-val0 = <0x4F4E5452>;
		enable-val1 = <0x12345116>;
		set-offset = <0x50>;
		en-offset = <0x0064>;
		clr-offset = <0x0070>;
		int-status-offset = <0x0100>;
	};

	rstinfo@0x14880000 {
		compatible = "hsan,rstinfo";
		reg = <0x14880000 0x1000>;
		reg-offset = <0x90>;
		record_addr = <0x10100C10>;

		rstinfo-mask = <0xC000>;
		rstinfo-offset = <0xE>;
		rstinfo-type = <0x3>;
		rstinfo-item = "hard";
	};

	dying_gasp {
		compatible = "hsan,dying_gasp";
		reg = <0x14900558 0x8>;
		interrupts = <0 1 4>;  //33
	};

	gpio0: gpio@0x10106000 {
		compatible = "snps,dw-apb-gpio";
		reg = <0x10106000 0x1000>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&apb_clk>;
		resets = <&reset_ecs 0x2c 7>;

		porta: gpio-port@0 {
			compatible = "snps,dw-apb-gpio-port";
			bank-name = "porta";
			gpio-controller;
			#gpio-cells = <2>;
			snps,nr-gpios = <32>;
			gpio-base = <0>;
			reg = <0>;
			interrupt-controller;
			#interrupt-cells = <2>;
			interrupt-parent = <&gic>;
			interrupts = <0 49 4>;
		};
	};

	gpio1: gpio@0x10107000 {
		compatible = "snps,dw-apb-gpio";
		reg = <0x10107000 0x1000>;
		#address-cells = <1>;
		#size-cells = <0>;
		clocks = <&apb_clk>;
		resets = <&reset_ecs 0x2c 8>;

		portb: gpio-port@0 {
			compatible = "snps,dw-apb-gpio-port";
			bank-name = "portb";
			gpio-controller;
			#gpio-cells = <2>;
			snps,nr-gpios = <20>;
			gpio-base = <32>;
			reg = <0>;
			interrupt-controller;
			#interrupt-cells = <2>;
			interrupt-parent = <&gic>;
			interrupts = <0 50 4>;
		};
	};

	uart1: uart@0x1010f000 {
		compatible = "snps,dw-apb-uart";
		bus_id = "uart1";
		reg = <0x1010f000 0x1000>;
		clocks = <&apb_clk>;
		clock-names = "apb_pclk";
		reg-shift = <0x2>;
		interrupts = <0x0 0x2e 0x4>;
	};

	uart0: uart@0x1010e000 {
		compatible = "snps,dw-apb-uart";
		bus_id = "uart0";
		reg = <0x1010e000 0x1000>;
		clocks = <&uart0_clk>;
		clock-names = "apb_pclk";
		reg-shift = <0x2>;
		interrupts = <0x0 0x2d 0x4>;
	};

	i2c0: i2c0@0x10111000 {
		compatible = "snps,designware-i2c";
		reg = <0x10111000 0x1000>;
		clock-frequency=<100000>;
		clocks = <&apb_clk>;
		resets = <&reset_top 0x2c 26>;
		interrupts = <0 44 4>; //76
	};

	tvsensor0: tvsensor@0x14900500 {
		compatible = "hsan,tvsensor";
		hwver = <TVSENSOR_HWVER>;
		reg = <0x14900500 0x100>;
		interrupts = <0 0 4>;
		#thermal-sensor-cells = <0>;
		int-base-offset = <0x60>;
		efuse-trim-en;
		trim = <1 2000>;
	};

	thermal-zones {
		cls0: cls0 {
			polling-delay = <1000>;
			polling-delay-passive = <100>;
			sustainable-power = <2000>;
			thermal-sensors = <&tvsensor0>;

			trips {
				alert0: trip-point@0 {
					temperature = <110000>;
					hysteresis = <3000>;
					type = "passive";
				};
				critical: trip-point@2 {
					temperature = <125000>;
					hysteresis = <3000>;
					type = "critical";
				};
			};
			cooling-maps {
				map0 {
					trip = <&alert0>;
					cooling-device = <&net_cdev 0 3>;
					contribution = <100>;
				};
			};
		};
	};

	net_cdev: cooling_dev0 {
		compatible = "hsan,net-cdev";
		car-rate-table = <0 500000 125000 32000>; /* kbps */
		#cooling-cells = <2>;
		dev-names = "eth0", "eth1", "eth2", "eth3", "eth4", "pon";
	};

	L2: l2-cache {
		compatible = "arm,pl310-cache";
		reg = <0x7f000000 0x1000>;
		cache-unified;
		cache-level = <2>;
		cache-way-size-sel = <0x1>;
		l2c_aux_mask = <0xFFB0FFFE>;
		l2c_aux_val = <0x430001>;
	};

	pie: pie {
		compatible = "hsan,pie", "hsan,acp-pie";
		reg = <0x10a70000 0x10000>;
		interrupts = <0 88 4>,
			     <0 89 4>,
			     <0 90 4>,
			     <0 91 4>;
		clocks = <&pie_clk0>;
		resets = <&reset_ecs 0x30 1>;
		hwver = <0x200>;
		dma-coherent;
		acp-gate = <0x10100174 0x1000000 0x0>; // attr_base attr_mask attr_val
		acp-pre-cfg-attr = <0x1010015c 0xFFFFFFFF 0x1B1B1B1B>,
			<0x10100160 0xFFFFFFFF 0x16161717>;
	};

	gemac0: gemac@0 {
		compatible = "hsan,mac";
		reg = <0x14300000 0x10000>;
		clocks = <&gemac_clk0>;
		resets = <&reset_lsw 0x101c 3>, <&reset_lsw 0x101c 2>;
		reset-names = "mac_logic_reset", "mac_reset";
		status = "okay";
		phy-handle = <&gephy0>;
		phy-mode = "rgmii";
		cnt-clr = <0x14000104 8>; // reg bit
		port= <8>;
	};

	gemac1: gemac@1 {
		compatible = "hsan,mac";
		reg = <0x14380000 0x10000>;
		clocks = <&gemac_clk1>;
		resets = <&reset_lsw 0x101c 5>, <&reset_lsw 0x101c 4>;
		reset-names = "mac_logic_reset", "mac_reset";
		status = "okay";
		phy-handle = <&gephy1>;
		phy-mode = "gmii";
		cnt-clr = <0x14000104 9>; // reg bit
		port= <9>;
	};

	gemac2: gemac@2 {
		compatible = "hsan,mac";
		reg = <0x14400000 0x10000>;
		clocks = <&gemac_clk2>;
		resets = <&reset_lsw 0x101c 7>, <&reset_lsw 0x101c 6>;
		reset-names = "mac_logic_reset", "mac_reset";
		status = "okay";
		phy-handle = <&gephy2>;
		phy-mode = "gmii";
		cnt-clr = <0x14000104 10>; // reg bit
		port= <10>;
	};

	gemac3: gemac@3 {
		compatible = "hsan,mac";
		reg = <0x14480000 0x10000>;
		clocks = <&gemac_clk3>;
		resets = <&reset_lsw 0x101c 9>, <&reset_lsw 0x101c 8>;
		reset-names = "mac_logic_reset", "mac_reset";
		status = "okay";
		phy-handle = <&gephy3>;
		phy-mode = "gmii";
		cnt-clr = <0x14000104 11>; // reg bit
		port= <11>;
	};

	gemac4: gemac@4 {
		compatible = "hsan,mac";
		reg = <0x14500000 0x10000>;
		clocks = <&gemac_clk4>;
		resets = <&reset_lsw 0x101c 11>, <&reset_lsw 0x101c 10>;
		reset-names = "mac_logic_reset", "mac_reset";
		status = "disabled";
		phy-mode = "rgmii";
		cnt-clr = <0x14000104 12>; // reg bit
		port= <12>;
	};

	gemac8: gemac@8 {
		compatible = "hsan,mac";
		reg = <0x14d80000 0x10000>;
		clocks = <&gemac_clk8>;
		resets = <&reset_lsw 0x101c 25>, <&reset_lsw 0x101c 24>;
		reset-names = "mac_logic_reset", "mac_reset";
		phy-handle = <&gephy5>;
		phy-mode = "sgmii";
		cnt-clr = <0x14000104 6>; // reg bit
		port = <0>;
		sgmii = <&pcs0>, <&ponlink10t28v1>, <&mii_misc0>;
		2500base-x = <&pcs0>, <&ponlink10t28v1>, <&mii_misc0>;
	};

	pcs0: pcs@0 {
		compatible = "hsan,pcs";
		reg = <0x14e00000 0x100>;
		clocks = <&pcs_clk0>;
		resets = <&reset_lsw 0x1018 16>, <&reset_lsw 0x1018 15>;
		reset-names = "gemac5_pcs_logic_reset", "gemac5_pcs_reset";
		auto-negotiation = <1>;
		rx-tbi-order = <0>; // 0:nor 1:inverse order
	};

	mii_misc0: mii_misc@0 {
		compatible = "hsan,mii_misc";
		reg = <0x14890138 0x4>;
		default-mask = <0x10000>;
		default-value = <0x0>;
		speed-mask = <0x10000>;
		speed-value = <0x0 0x0 0x0 0x10000>;
		speed-type = <10 100 1000 2500>;
	};

	pon_global: pon_global@0 {
		compatible = "hsan,pon_global";
		reg = <0x14200000 0x8000>, <0x14000000 0x1030>, <0x14280000 0x30>,
		      <0x11000000 0x10>, <0x12440800 0x2000>,
		      <0x14180000 0x1034>, <0x148800a0 0x4>;
		reg-names = "eps_pon", "itf_glb", "nni_sdsif",
			    "dp", "opp", "ips_pon", "pd_reset";
		clocks = <&pon_global_clk0>;
		clock-names = "xps_share";
		resets = <&reset_lsw 0x1018 20>, <&reset_lsw 0x1018 18>,
			 <&reset_lsw 0x1018 17>, <&reset_lsw 0x1018 19>,
			 <&reset_lsw 0x1018 21>;
		reset-names = "sdsif", "sds_rx", "sds_tx",
			      "sdsif_logic", "sdsif_ptr";
		auto_sensing = <3>;
	};

	gmac: gmac@0 {
		compatible = "hsan,gmac";
		reg = <0x14080000 0x10000>, <0x14090000 0x10000>, <0x140a0000 0x10000>,
		      <0x140b0000 0x10000>, <0x140c0000 0x10000>,<0x140d0000 0x10000>;
		reg-names = "ugmac_sys", "ugmac_line", "dgtc_sys",
			    "dgtc_line", "dgem_sys", "gpon_ext";
		interrupts = <0 109 4>, // 141
			     <0 110 4>, // 142
			     <0 111 4>, // 143
			     <0 102 4>, // 134
			     <0 112 4>; // 144
		clocks = <&gmac_clk0>;
		resets = <&reset_lsw 0x1018 0>, <&reset_lsw 0x1018 1>;
		reset-names = "gmac_reset", "gmac_logic_reset";
		tx_dly = <15>;
		tx_ext = <2>;
	};

	emac: emac@0 {
		compatible = "hsan,emac";
		reg = <0x14100000 0x1000>;
		clocks = <&emac_clk0>;
		interrupts = <0 108 4>, // 140
			     <0 110 4>, // 142
			     <0 111 4>, // 143
			     <0 102 4>; // 134
		resets = <&reset_lsw 0x1018 4>, <&reset_lsw 0x1018 5>;
		reset-names = "emac_reset", "emac_logic_reset";
	};

	mdio0: mdio0 {
		compatible = "hsan,mdio";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x14600000 0x1000>;

		clocks = <&mdio_clk0>;
		resets = <&reset_lsw 0x101c 12>, <&reset_top 0x34 21>, <&reset_top 0x34 22>,
			<&reset_top 0x34 23>, <&reset_top 0x34 25>, <&reset_top 0x34 26>;
		reset-names = "mdio_reset", "gephyall_reset", "gephy0_reset",
				"gephy1_reset", "gephy2_reset", "gephy3_reset";

		gephy0: ethernet-phy@1 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0x1>;
			max-speed = <1000>;
		};

		gephy1: ethernet-phy@2 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0x2>;
			max-speed = <1000>;
		};

		gephy2: ethernet-phy@3 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0x3>;
			max-speed = <1000>;
		};

		gephy3: ethernet-phy@4 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <0x4>;
			max-speed = <1000>;
		};
	};

	mdio1: mdio1 {
		compatible = "hsan,mdio";
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0x14680000 0x1000>;

		clocks = <&mdio_clk1>;
		resets = <&reset_lsw 0x101c 14>;
		reset-names = "mdio_reset";

		gephy5: ethernet-phy@6 {
			compatible = "ethernet-phy-ieee802.3-c45";
			reg = <0x1>;
			max-speed = <2500>;
			reset-gpios;
			reset-assert-us = <1000>;
			reset-deassert-us = <1000>;
		};
	};

	lsw_dp: lsw_dp {
		compatible = "hsan,dp";
		reg-names = "queue", "flow",
		            "dp", "eps", "eqm",
		            "fam", "ips";
		clocks = <&lsw_dp_clk0>;
		resets = <&reset_lsw 0x30 25>, <&reset_lsw 0x30 0>;
		reset-names = "dp_logic", "dp_srst", "pfe_logic", "pfe_srst";
		port_bitmap = <0xFF0F01 0xFF8>;
		hwver = <0x200>;
	};

	lsw_pfe {
		compatible = "hsan,pfe";
		reg = <0x12100000 0x80000>, <0x12180000 0x80000>, <0x12200000 0x80000>, <0x12280000 0x80000>,
		      <0x12300000 0x80000>, <0x12380000 0x80000>, <0x12400000 0x80000>, <0x12080000 0x80000>,
		      <0x12600000 0x80000>, <0x12000000 0x80000>, <0x11380000 0x80000>, <0x12480000 0x80000>,
		      <0x12500000 0x10000>;
		reg-names = "ifc", "l2", "l3", "napt",
		            "ofc", "mcc", "opp", "pa",
		            "pe", "pfe_glb", "prbs", "pub", "ptc";
		clocks = <&lsw_pfe_clk0>;
		resets = <&reset_lsw 0x30 26>, <&reset_lsw 0x30 2>;
		reset-names = "pfe_logic", "pfe_srst";
		hwver = <0x200>;
	};

	ponlinkt28v1: ponlinkt28v1@0 {
		compatible = "hsan,ponlinkt28v1";
		reg = <0x14A80000 0x10000>,
			<0x14880118 0x4>;
		reg-names = "pon_serdes", "nnimode";
		resets = <&reset_top 0x34 19>, <&reset_top 0x14c 18>, <&reset_top 0x14c 17>;
		reset-names = "ahb", "macropwrdb", "grstb";
		nnimode-mask = <0xf>;
		nnimode = <0x0 0x1 0x6 0x7>;
		nnimode-names = "GPON_1G2D5G", "EPON_1G1G", "GE_1G1G", "GE_2D5G2D5G";
		port = <0>;
		ds = <0>;
	};

	ponlink10t28v1: ponlink10t28v1@10 {
		compatible = "hsan,ponlink10t28v1";
		reg = <0x14A80000 0x81000>, /* cp base max 0x3fffe */
			<0x14880118 0x4>;
		reg-names = "pon_serdes", "nnimode";
		resets = <&reset_top 0x34 19>, <&reset_top 0x14c 18>, <&reset_top 0x14c 17>;
		reset-names = "ahb", "macropwrdb", "grstb";
		nnimode-mask = <0xf>;
		nnimode = <0x0 0x1 0x6 0x7>;
		nnimode-names = "GPON_1G2D5G", "EPON_1G1G", "GE_1G1G", "GE_2D5G2D5G";
		port = <0>;
		ds = <0>;
		clk_in0 = <0x0 25000000>; /* 25000000 */
		hwver = <0x20>;
		ponlink_fw = "lsw/ponlink10zv100.bin";
	};

	sysctrl: system-controller@10100000 {
		compatible = "hsan,sysctrl", "syscon";
		reg = <0x10100000 0x1000>;
		smp-offset = <0xc00>;
	};
	sysenv {
		compatible = "hsan,sysenv";
		syscon = <&sysctrl>;
		bootreg-offset = <0x0c08>;
	};
	reserved-memory {
		#address-cells = <1>;
		#size-cells = <1>;
		ranges;
		mem_reserved: mem {
			compatible = "hsan,mem_reserved";
			no-map;
		};
		flashinfo_reserved: flashinfo {
			compatible = "hsan,flashinfo_reserved";
		};
		lswdpinfo_reserved: lswdpinfo {
			compatible = "hsan,lswdpinfo_reserved";
			no-map;
		};
		voipinfo_reserved: voipinfo {
			compatible = "hsan,voipinfo_reserved";
			no-map;
		};
	};

	fmc: fmc@0x10a20000 {
		compatible = "hsan,fmc";
		bus_id = "fmc";
		reg = <0x10a20000 0x10000>,
			  <0x1c000000 0x100000>;
		spi_cs = <0x1>;
		clocks = <&sfc_clk>;
		clock-names = "sfc_clk";
		resets = <&reset_ecs 0x30 20>,
			<&reset_ecs 0x30 30>;
		reset-names = "sfc_rst";
	};

	dmac: dmac@0x15200000 {
		compatible = "hsan,dmac";
		hwver = <1>;
		reg = <0x15200000 0x1000>;
		interrupts = <0 58 4>; // 90
		#dma-cells = <3>;  // dir slave_id burst_len
		clocks = <&dmac_clk>;
		resets = <&reset_ecs 0x34 2>;
		reset-names = "dma_reset";
		int_tc_clr_offset = <0x3c>;
		int_err_clr_offset = <0x40>;
	};

	hwdio: hwdio-controller@0x15240000 {
		compatible = "hsan,hwdio";
		reg = <0x15240000 0x1000>,
			<0x148A0000 0x1000>,
			<0x14880000 0x1000>;
		clk-source-offset = <0x1c0>;
		hwpll-offset = <0x1ac>;
		hwpll-pd-val = <0x81032088>;
		hwpll-val = <0x81032008>;
		clk-sel = <3>; /* 0:gpon; 1:epon; 3:osc */

		clocks = <&hwdio_clk>;
		clock-names = "hwdio_clk";

		resets = <&reset_ecs 0x2c 19>,
			<&reset_ecs 0x30 28>,
			<&reset_ecs 0x30 29>;
		reset-names = "hw_scr_srst_n","hw_logic_srst_n","hw_srst_n";
	};

	spi1: spi1@0x10114000 {
		compatible = "snps,dw-apb-ssi";
		reg = <0x10114000 0x1000>;
		interrupts = <0 75 4>; //107
		#address-cells = <1>;
		#size-cells = <0>;
		num-cs = <8>;
		clocks = <&spi1_clk>;
		resets = <&reset_ecs 0x2c 0>;
	};

	voip: voip {
		compatible = "hsan,voip";
		interrupts = <0 37 4>, // 69
			<0 84 4>, // 116
			<0 83 4>, // 115
			<0 82 4>, // 114
			<0 81 4>, // 113
			<0 80 4>, // 112
			<0 79 4>; // 111
		dsp-image = "/tmp/voip/zsp_min.out";
	};

	wificlk {
		compatible = "hsan,emei-wificlk";
		syscon-top-crg = <&top_crg>;
		syscon-ecs-crg = <&ecs_crg>;
		#address-cells = <1>;
		#size-cells = <0>;

		pcie0_wificlk: port@0 {
			#phy-cells = <0>;
			index = <0>;
			io-ds = <2>;
			io-oe = <1>;
			clk-ctrl = <0>;
		};

		pcie1_wificlk: port@1 {
			#phy-cells = <0>;
			index = <1>;
			io-ds = <2>;
			io-oe = <1>;
			clk-ctrl = <0>;
		};
	};

	pcie0: pcie@0x10160000 {
		compatible = "hsan,pcie", "hsan,acp-pcie0";
		hwver = <0x10>;
		channel = <0>; // physical channel
		linux,pci-domain = <0>;
		capability = <0x3>; // bit0:pcie1.1, bit1:pcie2.0
		//dma-coherent;

		reg = <0x10160000 0x1000>,
			<0x10161000 0x3000>,
			<0x50000000 0x1000>,
			<0x40000000 0x2000000>,
			<0x48000000 0x800000>;
		reg-names = "dbi", "misc", "cfg", "mem", "io";

		interrupts = <0 59 4>,
			     <0 69 4>;
		interrupt-names = "radm", "linkdown";

		bus-range = <0 10>;
		ranges;

		iatu_rc = <0x0 0x4 0x80000000 0x50000000 0x0 0x57FFFFFF 0x0 0x0>, // cfg
			  <0x1 0x0 0x80000000 0x40000000 0x0 0x47FFFFFF 0x40000000 0x0>, // mem
			  <0x2 0x2 0x80000000 0x48000000 0x0 0x4FFFFFFF 0x48000000 0x0>; // io
		iatu_ep = <0x2 0x0 0x80000000 0x30000000 0x0 0x307FFFFF 0xab000000 0x0>;

		clocks = <&pcie0_clk>;
		clock-names = "pcie_clk";

		phys = <&pcie0_wificlk>;
		phy-names = "phy";

		resets = <&reset_ecs 0x34 6>,
			 <&reset_ecs 0x34 30>,
			 <&reset_ecs 0x34 8>,
			 <&reset_ecs 0x2C 18>;
		reset-names = "apb_rst","pcs_rst","phy_rst","ctrl_rst";

		gpio-delay = <50>; // ms
		pcie-gpios;
		acp-gate = <0x10100174 0x2000000 0x0>; // attr_base attr_mask attr_val
	};

	pcie1: pcie@0x10164000 {
		compatible = "hsan,pcie", "hsan,acp-pcie1";
		hwver = <0x10>;
		channel = <1>; // physical channel
		linux,pci-domain = <1>;
		capability = <0x3>; // bit0:pcie1.1, bit1:pcie2.0
		//dma-coherent;

		reg = <0x10164000 0x1000>,
			<0x10165000 0x3000>,
			<0x68000000 0x1000>,
			<0x58000000 0x2000000>,
			<0x60000000 0x800000>;
		reg-names = "dbi", "misc", "cfg", "mem", "io";

		interrupts = <0 63 4>,
			     <0 70 4>;
		interrupt-names = "radm", "linkdown";

		bus-range = <0 10>;
		ranges;

		iatu_rc = <0x0 0x4 0x80000000 0x68000000 0x0 0x6FFFFFFF 0x0 0x0>, // cfg
			  <0x1 0x0 0x80000000 0x58000000 0x0 0x5FFFFFFF 0x58000000 0x0>, // mem
			  <0x2 0x2 0x80000000 0x60000000 0x0 0x67FFFFFF 0x60000000 0x0>; // io
		iatu_ep = <0x2 0x0 0x80000000 0x30000000 0x0 0x307FFFFF 0xab000000 0x0>;

		clocks = <&pcie1_clk>;
		clock-names = "pcie_clk";

		phys = <&pcie1_wificlk>;
		phy-names = "phy";

		resets = <&reset_ecs 0x34 7>,
			 <&reset_ecs 0x34 31>,
			 <&reset_ecs 0x34 9>,
			 <&reset_ecs 0x2C 22>;
		reset-names = "apb_rst","pcs_rst","phy_rst","ctrl_rst";

		gpio-delay = <50>; // ms
		pcie-gpios;
		acp-gate = <0x10100174 0x4000000 0x0>; // attr_base attr_mask attr_val
	};

	usb0ctrl: usb0ctrl {
		compatible = "hsan,emei-usb";
		clock-names = "usb2phy";
		clocks = <&port1_usb2_clk>;

		reset-names = "usb2phy",
			      "usb2phy_port",
			      "usb_ctrl";
		resets = <&reset_ecs 0x2c 12>,
			<&reset_ecs 0x2c 13>,
			<&reset_ecs 0x30 17>;
	};

	usb1ctrl: usb1ctrl {
		compatible = "hsan,emei-usb";
		reg = <0x10168000 0x1000>,
			<0x10600000 0x10000>;
		usb3-ctrl;
		clock-names = "usb2phy",
			      "usb3phy",
			      "ctrl",
			      "apb";
		clocks = <&port0_usb2_phy_clk>,
			<&port0_usb3_phy_clk>,
			<&port0_usb_ctrl_clk>,
			<&port0_usb_apb_clk>;
		reset-names = "usb2phy",
			      "usb2phy_port",
			      "usb3phy",
			      "usb3phy_port",
			      "usb_apb",
			      "usb_ctrl";
		resets = <&reset_ecs 0x2c 15>,
			<&reset_ecs 0x2c 16>,
			<&reset_ecs 0x2c 28>,
			<&reset_ecs 0x2c 29>,
			<&reset_ecs 0x1ac 0>,
			<&reset_ecs 0x2C 14>;
	};

	iomux {
		compatible = "hsan,iomux";
		reg = <0x14900000 0x1000>;
		iomux-jtag-sel-offset = <0x110>;
		iomux-jtag-sel-value = <0x3>;
	};

	usb_ohci@0x10a50000{
		reg = <0x10a50000 0x10000>;
		compatible = "generic-ohci";
		dma-coherent;
		interrupts = <0 35 4>; //67
	};

	usb_ehci@0x10a40000{
		reg = <0x10a40000 0x10000>;
		compatible = "generic-ehci", "hsan,acp-usb2";
		dma-coherent;
		interrupts = <0 36 4>; //68
		acp-gate = <0x10100174 0x10000000 0x0>; // attr_base attr_mask attr_val
		acp-pre-cfg-attr = <0x1010012c 0xffffffff 0x1f1f1f1f>;
	};

	usb_xhci{
		reg = <0x10600000 0x100000>;
		compatible = "xhci-platform", "hsan,acp-usb3";
		dma-coherent;
		interrupts = <0 39 4>; //71
		acp-gate = <0x10100174 0x8000000 0x0>; // attr_base attr_mask attr_val
		usb2-lpm-disable;
	};

	jent-rng {
		compatible = "hsan,jent-rng";
	};
};

#include "emei_mem.dtsi"
#include "emei_partition.dtsi"
