/*
 * dtsi file for Hisilicon emei Development Board
 *
 * Copyright (C) 2021-10-22, Hisilicon Ltd.
 *
 */

/ {
	pinctrl_peri: pinctrl@0x14900000 {
			compatible = "hsan,emei-peri-pinctrl";
			reg = <0x14900100 0x7c>, <0x14940000 0x100>;
			reg-names = "mux", "cfg";

			jtag_gpio {
				jtag_gpio23_default_state: jtag_gpio23_default_state {
					jtag_gpio23_pmux {
						function = "jtag_gpio23";
						groups = "jtag_gpio23_grp";
					};
				};

				jtag_gpio24_default_state: jtag_gpio24_default_state {
					jtag_gpio24_pmux {
						function = "jtag_gpio24";
						groups = "jtag_gpio24_grp";
					};
				};

				jtag_gpio25_default_state: jtag_gpio25_default_state {
					jtag_gpio25_pmux {
						function = "jtag_gpio25";
						groups = "jtag_gpio25_grp";
					};
				};

				jtag_gpio26_default_state: jtag_gpio26_default_state {
					jtag_gpio26_pmux {
						function = "jtag_gpio26";
						groups = "jtag_gpio26_grp";
					};
				};

				jtag_gpio27_default_state: jtag_gpio27_default_state {
					jtag_gpio27_pmux {
						function = "jtag_gpio27";
						groups = "jtag_gpio27_grp";
					};
				};
			};

			eth_led {
				eth0_led_default_state: eth0_led_pmux {
					function = "eth_act_led0";
					groups = "eth_act_led0_grp";
				};

				eth1_led_default_state: eth1_led_pmux {
					function = "eth_link_led1";
					groups = "eth_link_led1_grp";
				};

				eth2_led_default_state: eth2_led_pmux {
					function = "eth_link_led2";
					groups = "eth_link_led2_grp";
					};

				eth3_led_default_state: eth3_led_pmux {
					function = "eth_link_led3";
					groups = "eth_link_led3_grp";
				};
			};

			spi0 {
					spi0_default_state: spi0_default_state {
						spi0_pmux {
							function = "spi0";
							groups = "spi0_grp";
						};
					};

					spi0_sleep_state: spi0_sleep_state {
						spi0_pmux {
							function = "spi0";
							groups = "spi0_grp";
						};
					};
			};

			spi1 {
					spi1_default_state: spi1_default_state {
						spi1_pmux {
							function = "spi1";
							groups = "spi1_grp";
						};
					};

					spi1_sleep_state: spi1_sleep_state {
						spi1_pmux {
							function = "spi1";
							groups = "spi1_grp";
						};
					};
			};

			pon_sd {
					pon_sd_default_state: pon_sd_default_state {
						pon_sd_pmux {
							function = "pon_sd";
							groups = "pon_sd_grp";
						};
					};
			};

			i2c0 {
					i2c0_default_state: i2c0_default_state {
						i2c0_pmux {
							function = "i2c0";
							groups = "i2c0_grp";
						};
					};
			};

			pon_tx_en_default_state: pon_tx_en_default_state {
				pon_tx_en_pmux {
					function = "pon_tx_en";
					groups = "pon_tx_en_grp";
				};
			};

			pon_tx_en_off_default_state: pon_tx_en_off_default_state {
				pon_tx_en_off_pmux {
					function = "pon_tx_en_off";
					groups = "pon_tx_en_off_grp";
				};
			};

			pon_tx_dis_default_state: pon_tx_dis_default_state {
				pon_tx_dis_pmux {
					function = "pon_tx_dis";
					groups = "pon_tx_dis_grp";
				};
			};

			mdio1 {
					mdio1_default_state: mdio1_default_state {
						mdio1_pmux {
							function = "mdio1";
							groups = "mdio1_grp";
						};
					};
			};

			pcm0 {
					pcm0_default_state: pcm0_default_state {
						pcm0_pmux {
							function = "pcm0";
							groups = "pcm0_grp";
						};
					};
			};

			pcm1 {
					pcm1_default_state: pcm1_default_state {
						pcm1_pmux {
							function = "pcm1";
							groups = "pcm1_grp";
						};
					};
			};
	};
};
