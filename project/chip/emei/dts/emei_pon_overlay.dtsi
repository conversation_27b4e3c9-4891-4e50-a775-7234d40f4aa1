/*
 * dts overlay for HiSilicon (Shanghai) emei pon Development Board
 *
 * Copyright (C) 2024-10-22, HiSilicon (Shanghai) Ltd.
 *
 */
#include <dt-bindings/gpio/gpio.h>

/ {
	fragment@pon_glb {
		target = <&pon_global>;
		__overlay__ {
			pinctrl-names = "tx_en_1", "tx_en_0", "sd";
			pinctrl-0 = <&pon_tx_en_default_state>, <&pon_sd_default_state>;
			pinctrl-1 = <&pon_tx_en_off_default_state>, <&pon_sd_default_state>;
			pinctrl-2 = <&pon_sd_default_state>;
			pon-gpios = <&porta 20 GPIO_ACTIVE_LOW>;
		};
	};
};
