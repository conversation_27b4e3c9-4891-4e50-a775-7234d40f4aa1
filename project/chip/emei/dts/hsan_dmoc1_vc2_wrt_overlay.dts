/*
 * dts file for Hisilicon emei Development Board
 *
 * Copyright (C) 2022-09-07, Hisilicon Ltd.
 *
 */

/dts-v1/;
/plugin/;

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include "emei.h"
#include "emei_pon_overlay.dtsi"

/ {
	compatible = "hsan-emei";
	board_id = <0x00000002>;
	vendor_id = "hsan";

	fragment@0 {
		target-path = "/";
		__overlay__ {
			board_id = <0x00000002>;
			vendor_id = "hsan";
			board_name = "hgu";

			keys {
				compatible = "gpio-keys";
				reset_key {
					label = "reset_key";
					gpios = <&porta 27 GPIO_ACTIVE_LOW>;
					linux,code = <KEY_RESTART>;
				};
				wlan_key {
					label = "wlan_key";
					gpios = <&portb 18 GPIO_ACTIVE_LOW>;
					linux,code = <KEY_WLAN>;
				};
				wps_key {
					label = "wps_key";
					gpios = <&portb 19 GPIO_ACTIVE_LOW>;
					linux,code = <KEY_WPS_BUTTON>;
				};
			};

			gpio-leds {
				compatible = "gpio-leds";
				power_led {
					label = "led_power";
					gpios = <&porta 12 GPIO_ACTIVE_LOW>;
					deflate-state = "off";
				};
				pon_led {
					label = "led_pon";
					gpios = <&porta 11 GPIO_ACTIVE_LOW>;
					deflate-state = "off";
				};
				los_led {
					label = "led_los";
					gpios = <&porta 1 GPIO_ACTIVE_LOW>;
					deflate-state = "off";
				};
				wifi1_led {
					label = "led_wifi1";
					gpios = <&porta 0 GPIO_ACTIVE_LOW>;
					deflate-state = "off";
				};
			};
		};
	};

	fragment@1 {
		target = <&pcie0>;
		__overlay__ {
			capability = <0x3>;
			gpio-delay = <50>;
			pcie-gpios = <&porta 3 GPIO_ACTIVE_HIGH>, /* PCIE_RST */
					 <&porta 10 GPIO_ACTIVE_HIGH>, /* RF_WL_EN */
					 <&porta 4 GPIO_ACTIVE_HIGH>; /* RF_0V9_EN */
		};
	};

	fragment@2 {
		target = <&pcie1>;
		__overlay__ {
			capability = <0x3>;
			gpio-delay = <50>;
			pcie-gpios;
		};
	};

	fragment@3 {
		target = <&ponlinkt28v1>;
		__overlay__ {
			status = "disabled";
		};
	};

	fragment@4 {
		target = <&ponlink10t28v1>;
		__overlay__ {
			status = "okay";
		};
	};

	fragment@5 {
		target = <&spi1>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;
			slicdev {
				compatible = "hsan,slic-spidev";
				reg = <6>;
				spi-max-frequency = <4096000>;
				zsi;
				rst-gpios = <&portb 17 GPIO_ACTIVE_HIGH>; //gpio49
				gpio-delay = <50>;
				spi-cpha;
				spi-cpol;
				spi-cs-high;
			};
		};
	};

	fragment@6 {
		target = <&hwdio>;
		__overlay__ {
			pinctrl-names = "default";
			pinctrl-0 = <&pcm0_default_state>;

			interface-mode = <1>; /* 0:hw; 1:zsi */
			clk-sel = <3>; /* 0:gpon; 1:epon; 3:osc */
			clk-mode = <1>; /* 0:input clk; 1:output clk */
			clk-rate = <0>; /* 0:8MHz; 1:4MHz; 2:1MHz; 4:512KHz */
			rx-edge = <1>; /* 0:up edge; 1:down edge */
			tx-edge = <0>; /* 0:up edge; 1:down edge */
			rx-align = <0>; /* value[0,1023] */
			tx-align = <0>; /* value[0,1023] */
			fs-edge = <0>; /* 0:up edge; 1:down edge */
			fs-level = <1>; /* 0:low level; 1:hight level */
			fs-rate = <1>; /* sample rate 0:8KHz; 1:16KHz */
			fs-mode = <1>; /* 0:normal mode; 1:8KHz mode */
			pcm-wide = <1>; /* 0:8bit; 1:16bit */
		};
	};

	fragment@7 {
		target = <&gephy0>;
		__overlay__ {
			/* 0x3183/0: 0 ohm, 0x3181/1: 3.3ohm
			 * 0x31b1/0: traditional transformer, 0x31b1/1: separating transformer */
			phy-addr = <0x3181 0x3183 0x3192 0x31b1 0x31b2 0x31b5 0x31b6 0x31b7 0x31b8>;
			phy-val = <0x1 0x1 0x38 0x1 0x38 0x12 0x64 0x0 0x0>; // hsan_emei_board 0

			/* led-polar
			 * 00:on-drive LED low, off-drive LED high
			 * 01:on-drive LED high, off-drive LED low
			 * 10:on-drive LED low, off-tristate LED high
			 * 11:on-drive LED high, off-tristate LED high */
			/* led-freq
			 * eg:led-freq = <200>;Blink period is 200ms */

			led-polar = <0x0>;
			led-mode = <0x1>; /* single_led 1, double_led 2 */
			pinctrl-names = "default";
			pinctrl-0 = <&eth0_led_default_state>;
		};
	};

	fragment@8 {
		target = <&gephy1>;
		__overlay__ {
			led-mode = <1>;
			led-polar = <0>;
			pinctrl-names = "default";
			pinctrl-0 = <&eth1_led_default_state>;
		};
	};

	fragment@9 {
		target = <&gephy2>;
		__overlay__ {
			led-mode = <1>;
			led-polar = <0>;
			pinctrl-names = "default";
			pinctrl-0 = <&eth2_led_default_state>;
		};
	};

	fragment@10 {
		target = <&gephy3>;
		__overlay__ {
			led-mode = <1>;
			led-polar = <0>;
			pinctrl-names = "default";
			pinctrl-0 = <&eth3_led_default_state>;
		};
	};

	fragment@11 {
		target = <&gephy5>;
		__overlay__ {
			status = "disabled";
		};
	};

	fragment@12 {
		target = <&gemac8>;
		__overlay__ {
			status = "disabled";
		};
	};

	fragment@13 {
		target-path = "/memory";
		#address-cells = <1>;
		#size-cells = <1>;
		__overlay__ {
			reg = <HI_LINUX_DDR_MEMORY_START SZ_512M>;
		};
	};

	fragment@14 {
		target = <&mdio1>;
		__overlay__ {
			status = "disabled";
		};
	};

	fragment@i2c0 {
		target = <&i2c0>;
		__overlay__ {
			pinctrl-names = "default";
			pinctrl-0 = <&i2c0_default_state>;
		};
	};

	fragment@pinctrl_peri {
		target = <&pinctrl_peri>;
		__overlay__ {
			pinctrl-names = "default";
			pinctrl-0 = <&jtag_gpio23_default_state &jtag_gpio24_default_state \
				&jtag_gpio25_default_state &jtag_gpio27_default_state>;
		};
	};
};
